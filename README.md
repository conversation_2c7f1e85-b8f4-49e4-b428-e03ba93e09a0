# Discord Bot - NestJS Migration

🚀 **Complete Discord Bot Backend Migration from Next.js to NestJS**

This project represents a full architectural migration from a Next.js-based Discord bot backend to a modern, scalable NestJS application with proper separation of concerns, dependency injection, and production-ready patterns.

## 🏗️ Architecture Overview

### **Core Modules**
- **Core Module**: Database, Security, Configuration
- **Discord Module**: Bot services, Events, Commands, Utils  
- **API Module**: REST endpoints, Authentication, Admin panel
- **Agents Module**: AI-powered Discord agents with scheduling
- **Features Module**: Discord bot features (Music, Gaming, Economy, etc.)

### **Key Technologies**
- **NestJS** - Modern Node.js framework with dependency injection
- **Necord** - Discord.js integration for NestJS
- **TypeORM** - Database ORM with PostgreSQL
- **JWT & Passport** - Authentication and authorization
- **Class Validator** - Request validation
- **Swagger** - API documentation
- **Jest** - Testing framework

## 📁 Project Structure

```
src/
├── main.ts                           # Application bootstrap
├── app.module.ts                     # Root module
├── core/                             # Core infrastructure
│   ├── core.module.ts
│   ├── database/                     # Database layer
│   │   ├── database.module.ts
│   │   ├── database.service.ts
│   │   └── entities/                 # TypeORM entities
│   │       ├── base.entity.ts
│   │       ├── user.entity.ts
│   │       ├── guild.entity.ts
│   │       ├── session.entity.ts
│   │       ├── user-relationship.entity.ts
│   │       ├── agent-interaction.entity.ts
│   │       ├── personal-growth-plan.entity.ts
│   │       ├── agent-memory.entity.ts
│   │       └── ai-agent-config.entity.ts
│   ├── security/                     # Security services
│   │   ├── security.module.ts
│   │   ├── encryption.service.ts
│   │   ├── session.service.ts
│   │   └── user.service.ts
│   └── config/
│       └── config.validation.ts
├── discord/                          # Discord bot module
│   ├── discord.module.ts
│   ├── discord.service.ts            # Main bot service
│   ├── events/
│   │   └── discord-events.service.ts # Event handlers
│   ├── commands/
│   │   └── discord-commands.service.ts # Slash commands
│   └── utils/
│       └── discord-utils.service.ts  # Utility functions
├── api/                              # REST API module
│   ├── api.module.ts
│   ├── auth/                         # Authentication
│   │   ├── auth.module.ts
│   │   ├── auth.controller.ts
│   │   ├── auth.service.ts
│   │   ├── strategies/
│   │   └── guards/
│   ├── guilds/                       # Guild management
│   ├── admin/                        # Admin panel
│   ├── whop/                         # Whop integration
│   └── health/                       # Health checks
│       ├── health.module.ts
│       └── health.controller.ts
├── agents/                           # AI agents module
│   ├── agents.module.ts
│   ├── agents.service.ts
│   ├── integration/
│   │   ├── ai-agent-integration.service.ts
│   │   ├── scheduler.service.ts
│   │   └── channel-routing.service.ts
│   └── types/                        # AI agent implementations
│       ├── personal-growth-coach.ts
│       ├── intake-specialist.ts
│       └── progress-tracker.ts
└── features/                         # Discord features
    ├── features.module.ts
    ├── welcome/
    ├── music/
    ├── gaming/
    ├── leveling/
    ├── economy/
    ├── moderation/
    ├── utility/
    └── starboard/
```

## 🚀 Getting Started

### **Prerequisites**
- Node.js >= 18.17.0
- Redis database
- Discord bot token and OAuth credentials

### **Installation**

```bash
# Install dependencies  
npm install

# Copy environment file
cp .env.example .env

# Configure your environment variables
# Edit .env with your database URL, Discord tokens, etc.

# Redis will be automatically configured on startup

# Start development server
npm run dev
```

### **Environment Variables**

```env
# Discord Configuration
DISCORD_TOKEN=your_discord_bot_token
BOT_CLIENT_ID=your_bot_client_id
BOT_CLIENT_SECRET=your_bot_client_secret

# Redis Database
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Security Keys (Generate with: openssl rand -hex 32)
USER_ENCRYPTION_KEY=your_64_char_hex_key
SESSION_ENCRYPTION_KEY=your_64_char_hex_key  
CSRF_ENCRYPTION_KEY=your_64_char_hex_key

# Application
PORT=8080
NODE_ENV=development
WEB_URL=http://localhost:3000

# JWT (Optional - for API authentication)
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=24h

# AI Configuration (Optional)
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key
```

## 📊 Database Schema

The application uses TypeORM with PostgreSQL. Key entities include:

- **User**: Discord users with OAuth tokens and preferences
- **Guild**: Discord servers with configuration
- **Session**: Secure session management with encryption
- **UserRelationship**: User connections and relationships  
- **AgentInteraction**: AI agent conversation history
- **PersonalGrowthPlan**: User growth plans and goals
- **AgentMemory**: AI agent memory and context
- **AIAgentConfig**: Per-guild AI agent configuration

## 🤖 Discord Bot Features

### **Dev On Demand System** 🚀
A comprehensive developer marketplace with Whop.com integration for secure payments:

**Client Commands:**
- `/dev-setup` - Initialize the system (Admin only)
- `/dev-request` - Submit development requests
- `/dev-payment` - Set up secure escrow payments
- `/dev-requests` - View your active requests

**Developer Commands:**
- `/dev-browse` - Browse available projects
- `/dev-accept` - Accept project assignments
- `/dev-milestone` - Complete project milestones

**Features:**
- 🔒 Secure escrow payments via Whop.com
- 🎯 Milestone-based project management
- 👥 Membership verification (Client/Developer tiers)
- 📊 Real-time project tracking
- 💰 Automated payment releases

### **Core Slash Commands**
- `/ping` - Test bot responsiveness
- `/status` - Get detailed bot status
- `/help` - Display available commands

### **AI Agents**
- **Personal Growth Coach** - Personalized development guidance
- **Intake Specialist** - User onboarding and assessment
- **Progress Tracker** - Goal tracking and milestone monitoring

### **Event Handling**
- Guild join/leave management
- Member join events with welcome messages
- Automatic database synchronization
- Error handling and logging

## 🔐 Security Features

### **Session Management**
- AES-256-GCM encryption for sensitive data
- Secure session tokens with expiration
- Device fingerprinting for tamper detection
- CSRF protection

### **Authentication**
- Discord OAuth2 integration
- JWT-based API authentication
- Role-based access control
- Permission caching with rate limiting

## 🛠️ Development

### **Available Scripts**

```bash
# Development
npm run dev             # Start with hot reload (shorthand)
npm run start:dev       # Start with hot reload (full command)
npm run start:debug     # Start with debugging

# Building
npm run build           # Build for production
npm run start:prod      # Start production server

# Database
npm run migration:generate  # Generate new migration
npm run migration:run      # Run pending migrations
npm run schema:sync        # Sync schema (dev only)

# Testing
npm run test            # Run unit tests
npm run test:e2e        # Run integration tests
npm run test:cov        # Run with coverage

# Code Quality
npm run lint            # Lint code
npm run format          # Format code
```

### **API Documentation**

Once running, visit:
- **Swagger UI**: `http://localhost:8080/docs`
- **Health Check**: `http://localhost:8080/api/health`

## 🔄 Migration from Next.js

This project migrates from a Next.js-based architecture to NestJS:

### **Migration Benefits**
- ✅ **Proper Service Lifecycle**: Discord bot runs as persistent service
- ✅ **Modular Architecture**: Clear separation of concerns
- ✅ **Dependency Injection**: Clean service relationships
- ✅ **Type Safety**: Full TypeScript with decorators
- ✅ **Testing**: Built-in testing framework
- ✅ **Scalability**: Microservice-ready architecture
- ✅ **Performance**: Better resource management

### **Migrated Services**
- Database layer (pg → TypeORM)
- Authentication & session management
- Discord bot services
- AI agent integration
- API endpoints
- Security & encryption services

## 🚀 Deployment

### **Docker Deployment**

```bash
# Build image
docker build -t discord-bot-nestjs .

# Run container
docker run -p 8080:8080 --env-file .env discord-bot-nestjs
```

### **Production Configuration**

```bash
# Install PM2 for process management
npm install -g pm2

# Start with PM2
pm2 start ecosystem.config.js --env production

# Monitor processes  
pm2 monit
```

## 📈 Monitoring & Health Checks

### **Health Endpoints**
- `GET /api/health` - Comprehensive health check
- `GET /api/health/simple` - Simple status check

### **Monitoring**
- Database connection status
- Discord bot connection status
- Memory usage tracking
- Performance metrics
- Error logging with structured format

## 🧪 Testing

```bash
# Unit tests
npm run test

# Integration tests  
npm run test:e2e

# Coverage report
npm run test:cov
```

## 📝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Check the [Issues](issues) page
- Review the API documentation at `/docs`
- Check health status at `/api/health`

---

**Built with ❤️ using NestJS, TypeORM, and Discord.js**