#!/bin/bash

# EnergeX Build Monitor - Startup Script
# Initializes comprehensive build monitoring and performance tracking

set -e

echo "🚀 Starting EnergeX Build Monitor System..."
echo "═══════════════════════════════════════════"

# Change to project directory
cd "$(dirname "$0")/.."

# Check if Node.js and npm are available
if ! command -v node &> /dev/null; then
    echo "❌ Error: Node.js is not installed"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo "❌ Error: npm is not installed"
    exit 1
fi

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Compile TypeScript if needed
if [ ! -d "dist" ] || [ "src" -nt "dist" ]; then
    echo "🔨 Compiling TypeScript..."
    npm run build:tsc || echo "⚠️  Build has errors, but continuing with monitoring..."
fi

# Start the build monitor
echo ""
echo "🔍 Launching Build Monitor Dashboard..."
echo "───────────────────────────────────────────"
echo "Features:"
echo "• Real-time error tracking"
echo "• Progress monitoring"
echo "• Performance analytics"
echo "• Agent coordination"
echo "• Interactive dashboard"
echo ""

# Run the build monitor
npm run monitor:build

echo ""
echo "✅ Build Monitor session ended"