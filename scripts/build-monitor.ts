#!/usr/bin/env ts-node

import { NestFactory } from '@nestjs/core';
import { MonitoringModule } from '../src/core/monitoring/monitoring.module';
import { BuildMonitorService } from '../src/core/monitoring/build-monitor.service';
import { DashboardService } from '../src/core/monitoring/dashboard.service';
import { BuildAnalyzerService } from '../src/core/monitoring/build-analyzer.service';
import { Logger } from '@nestjs/common';
import * as readline from 'readline';

class BuildMonitorCLI {
  private logger = new Logger('BuildMonitorCLI');
  private app: any;
  private buildMonitorService: BuildMonitorService;
  private dashboardService: DashboardService;
  private buildAnalyzerService: BuildAnalyzerService;
  private rl: readline.Interface;

  async initialize() {
    console.log('🚀 Initializing Build Monitor System...\n');

    try {
      // Create NestJS application with monitoring module
      this.app = await NestFactory.createApplicationContext(MonitoringModule);
      
      // Get services
      this.buildMonitorService = this.app.get(BuildMonitorService);
      this.dashboardService = this.app.get(DashboardService);
      this.buildAnalyzerService = this.app.get(BuildAnalyzerService);

      // Initialize readline interface
      this.rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
      });

      console.log('✅ Build Monitor System initialized successfully!\n');
      
      // Start monitoring
      await this.startMonitoring();
      
    } catch (error) {
      console.error('❌ Failed to initialize Build Monitor:', error.message);
      process.exit(1);
    }
  }

  private async startMonitoring() {
    console.log('📊 ENERGEX BUILD MONITOR - REAL-TIME TRACKING');
    console.log('═'.repeat(60));
    console.log();
    
    // Display initial status
    await this.displayCurrentStatus();
    
    // Set up periodic updates
    const updateInterval = setInterval(async () => {
      await this.displayCurrentStatus();
    }, 30000); // Update every 30 seconds
    
    // Interactive commands
    this.displayCommands();
    this.setupCommandHandler();

    // Handle graceful shutdown
    process.on('SIGINT', async () => {
      clearInterval(updateInterval);
      await this.shutdown();
    });
  }

  private async displayCurrentStatus() {
    try {
      const metrics = await this.buildMonitorService.getCurrentMetrics();
      const agentStatus = await this.buildMonitorService.getAgentCoordinationStatus();
      
      // Clear console and display header
      console.clear();
      console.log('🔍 ENERGEX BUILD MONITOR - LIVE DASHBOARD');
      console.log('═'.repeat(60));
      console.log(`⏰ ${new Date().toLocaleString()}`);
      console.log();

      // Build Status
      console.log('📊 BUILD STATUS:');
      console.log(`   Total Errors: ${metrics.totalErrors} ${this.getTrendIndicator(metrics.totalErrors)}`);
      console.log(`   Files Modified: ${metrics.modifiedFiles}`);
      console.log(`   Progress: ${this.calculateProgress(metrics)}%`);
      console.log(`   Build Time: ${(metrics.buildTime / 1000).toFixed(2)}s`);
      console.log();

      // Error Breakdown
      if (Object.keys(metrics.errorsByType).length > 0) {
        console.log('🎯 ERROR BREAKDOWN:');
        Object.entries(metrics.errorsByType)
          .sort(([, a], [, b]) => (b as number) - (a as number))
          .slice(0, 5)
          .forEach(([type, count]) => {
            console.log(`   ${type}: ${count} ${this.getErrorTypeIndicator(type)}`);
          });
        console.log();
      }

      // Agent Coordination
      console.log('🤖 AGENT COORDINATION:');
      console.log(`   Active Agents: ${agentStatus.activeAgents}/${agentStatus.totalAgents}`);
      console.log(`   Completed Tasks: ${agentStatus.completedTasks}`);
      console.log(`   Overall Progress: ${agentStatus.overallProgress.toFixed(1)}%`);
      console.log();

      // Progress Bar
      this.displayProgressBar(agentStatus.overallProgress);
      console.log();

      // Recent milestones
      await this.displayRecentMilestones();
      
      // Performance stats
      await this.displayPerformanceStats();

      // Commands
      console.log('═'.repeat(60));
      console.log('Commands: [r]eport | [a]nalysis | [d]ashboard | [e]xport | [q]uit');
      console.log('═'.repeat(60));

    } catch (error) {
      this.logger.error(`Failed to display status: ${error.message}`);
    }
  }

  private calculateProgress(metrics: any): number {
    if (metrics.modifiedFiles === 0) return 100;
    return Math.round((metrics.completedFiles / metrics.modifiedFiles) * 100);
  }

  private getTrendIndicator(errors: number): string {
    const history = this.buildMonitorService.getHistoricalData(5);
    if (history.length < 2) return '📊';
    
    const recent = history[history.length - 1].totalErrors;
    const previous = history[history.length - 2].totalErrors;
    
    if (recent < previous) return '📉 ↓';
    if (recent > previous) return '📈 ↑';
    return '➡️ →';
  }

  private getErrorTypeIndicator(errorType: string): string {
    const indicators = {
      'syntax_comma': '🔤',
      'syntax_error': '🔤',
      'undefined_reference': '❓',
      'missing_import': '📦',
      'type_mismatch': '🔀',
      'missing_property': '🏷️'
    };
    return indicators[errorType] || '⚠️';
  }

  private displayProgressBar(progress: number, width: number = 40) {
    const filled = Math.round((progress / 100) * width);
    const empty = width - filled;
    const bar = '█'.repeat(filled) + '░'.repeat(empty);
    console.log(`🚀 OVERALL PROGRESS: [${bar}] ${progress.toFixed(1)}%`);
  }

  private async displayRecentMilestones() {
    const history = this.buildMonitorService.getHistoricalData(10);
    if (history.length < 2) return;

    console.log('🎯 RECENT MILESTONES:');
    
    const latest = history[history.length - 1];
    const baseline = history[0];
    const reduction = baseline.totalErrors - latest.totalErrors;
    
    if (reduction > 0) {
      console.log(`   ✅ Reduced ${reduction} errors since monitoring started`);
    }
    
    if (latest.totalErrors === 0) {
      console.log('   🎉 ALL ERRORS RESOLVED! Build successful!');
    } else if (latest.totalErrors < 100) {
      console.log('   🎯 Less than 100 errors remaining!');
    }
    
    console.log();
  }

  private async displayPerformanceStats() {
    const history = this.buildMonitorService.getHistoricalData();
    if (history.length === 0) return;

    const avgBuildTime = history.reduce((sum, h) => sum + h.buildTime, 0) / history.length;
    const monitoringDuration = Date.now() - history[0].timestamp.getTime();
    const minutes = Math.round(monitoringDuration / (1000 * 60));

    console.log('📈 PERFORMANCE STATS:');
    console.log(`   Avg Build Time: ${(avgBuildTime / 1000).toFixed(2)}s`);
    console.log(`   Monitoring Duration: ${minutes} minutes`);
    console.log(`   Data Points: ${history.length}`);
    console.log();
  }

  private displayCommands() {
    // Commands are shown in displayCurrentStatus
  }

  private setupCommandHandler() {
    this.rl.on('line', async (input) => {
      const command = input.trim().toLowerCase();
      
      switch (command) {
        case 'r':
        case 'report':
          await this.showDetailedReport();
          break;
        case 'a':
        case 'analysis':
          await this.showAnalysis();
          break;
        case 'd':
        case 'dashboard':
          await this.openDashboard();
          break;
        case 'e':
        case 'export':
          await this.exportData();
          break;
        case 'q':
        case 'quit':
          await this.shutdown();
          break;
        case 'help':
          this.showHelp();
          break;
        default:
          console.log('Unknown command. Type "help" for available commands.');
      }
    });
  }

  private async showDetailedReport() {
    console.log('\n📋 DETAILED PROGRESS REPORT');
    console.log('═'.repeat(40));
    
    const report = await this.buildMonitorService.generateProgressReport();
    console.log(report);
    console.log('\nPress Enter to continue...');
  }

  private async showAnalysis() {
    console.log('\n🔍 COMPREHENSIVE BUILD ANALYSIS');
    console.log('═'.repeat(40));
    
    try {
      const analysis = await this.buildAnalyzerService.performComprehensiveAnalysis();
      
      console.log('ERROR REDUCTION:');
      console.log(`  Hourly Rate: ${analysis.errorReduction.hourly} errors/hour`);
      console.log(`  Total Reduced: ${analysis.errorReduction.total} (${analysis.errorReduction.percentage}%)`);
      console.log();
      
      console.log('PREDICTIONS:');
      console.log(`  Estimated Completion: ${analysis.predictions.estimatedCompletion.toLocaleString()}`);
      console.log(`  Remaining Effort: ${analysis.predictions.remainingEffort}`);
      console.log(`  Risk Level: ${analysis.predictions.riskAssessment.toUpperCase()}`);
      console.log();
      
      console.log('TOP RECOMMENDATIONS:');
      analysis.recommendations.priority.slice(0, 3).forEach((rec, i) => {
        console.log(`  ${i + 1}. ${rec}`);
      });
      
    } catch (error) {
      console.log(`Failed to generate analysis: ${error.message}`);
    }
    
    console.log('\nPress Enter to continue...');
  }

  private async openDashboard() {
    console.log('\n🖥️  SAVING HTML DASHBOARD');
    console.log('═'.repeat(30));
    
    try {
      const dashboardPath = await this.dashboardService.saveDashboardHTML('/tmp/energex-dashboard.html');
      console.log(`Dashboard saved to: ${dashboardPath}`);
      console.log('Open the file in your browser to view the interactive dashboard.');
      console.log('WebSocket server running on port 3001 for real-time updates.');
    } catch (error) {
      console.log(`Failed to save dashboard: ${error.message}`);
    }
    
    console.log('\nPress Enter to continue...');
  }

  private async exportData() {
    console.log('\n📤 EXPORTING MONITORING DATA');
    console.log('═'.repeat(35));
    
    try {
      const [metricsPath, analysisPath] = await Promise.all([
        this.buildMonitorService.exportMetricsData(),
        this.buildAnalyzerService.exportAnalysis()
      ]);
      
      console.log(`Metrics exported to: ${metricsPath}`);
      console.log(`Analysis exported to: ${analysisPath}`);
      console.log('Export completed successfully!');
    } catch (error) {
      console.log(`Failed to export data: ${error.message}`);
    }
    
    console.log('\nPress Enter to continue...');
  }

  private showHelp() {
    console.log('\n📖 AVAILABLE COMMANDS');
    console.log('═'.repeat(25));
    console.log('r, report    - Show detailed progress report');
    console.log('a, analysis  - Show comprehensive analysis');
    console.log('d, dashboard - Save HTML dashboard');
    console.log('e, export    - Export monitoring data');
    console.log('q, quit      - Exit monitoring system');
    console.log('help         - Show this help message');
    console.log('\nPress Enter to continue...');
  }

  private async shutdown() {
    console.log('\n🛑 Shutting down Build Monitor System...');
    
    try {
      // Stop services
      this.buildMonitorService.stopMonitoring();
      this.dashboardService.shutdown();
      
      // Close readline
      this.rl.close();
      
      // Close NestJS app
      if (this.app) {
        await this.app.close();
      }
      
      console.log('✅ Shutdown complete. Goodbye!');
      process.exit(0);
    } catch (error) {
      console.error('❌ Error during shutdown:', error.message);
      process.exit(1);
    }
  }
}

// Start the CLI if this script is run directly
if (require.main === module) {
  const cli = new BuildMonitorCLI();
  cli.initialize().catch(error => {
    console.error('Failed to start Build Monitor CLI:', error);
    process.exit(1);
  });
}

export default BuildMonitorCLI;