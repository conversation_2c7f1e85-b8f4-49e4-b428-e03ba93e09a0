name: 🔄 Continuous Integration\n\non:\n  push:\n    branches: [ master, backend, develop ]\n  pull_request:\n    branches: [ master, backend ]\n  workflow_dispatch:\n\nenv:\n  NODE_VERSION: '18.17.0'\n  PNPM_VERSION: '8.15.0'\n\njobs:\n  # Fast quality gates for immediate feedback\n  quality-gates:\n    name: 🚦 Quality Gates\n    runs-on: ubuntu-latest\n    timeout-minutes: 10\n    outputs:\n      should-run-tests: ${{ steps.changes.outputs.should-test }}\n      cache-key: ${{ steps.cache-key.outputs.key }}\n    \n    steps:\n      - name: 📥 Checkout code\n        uses: actions/checkout@v4\n        with:\n          fetch-depth: 2\n      \n      - name: 🔍 Detect changes\n        id: changes\n        uses: dorny/paths-filter@v2\n        with:\n          filters: |\n            should-test:\n              - 'src/**'\n              - 'test/**'\n              - 'package.json'\n              - 'pnpm-lock.yaml'\n              - 'tsconfig.json'\n              - 'jest.config.js'\n              - '.env.test'\n      \n      - name: 🔧 Setup Node.js\n        uses: actions/setup-node@v4\n        with:\n          node-version: ${{ env.NODE_VERSION }}\n      \n      - name: 📦 Setup pnpm\n        uses: pnpm/action-setup@v2\n        with:\n          version: ${{ env.PNPM_VERSION }}\n      \n      - name: 🔑 Generate cache key\n        id: cache-key\n        run: |\n          echo \"key=node-modules-${{ runner.os }}-${{ hashFiles('pnpm-lock.yaml') }}\" >> $GITHUB_OUTPUT\n      \n      - name: 💾 Cache dependencies\n        uses: actions/cache@v3\n        with:\n          path: |\n            ~/.pnpm-store\n            node_modules\n          key: ${{ steps.cache-key.outputs.key }}\n          restore-keys: |\n            node-modules-${{ runner.os }}-\n      \n      - name: 📦 Install dependencies\n        run: pnpm install --frozen-lockfile --prefer-offline\n      \n      - name: 🔍 Lint code\n        run: pnpm lint\n      \n      - name: 🔍 Type check\n        run: pnpm build\n      \n      - name: 🔒 Security audit\n        run: pnpm audit --audit-level high\n        continue-on-error: true\n      \n      - name: 📊 Bundle analysis\n        run: |\n          echo \"Bundle size analysis would go here\"\n          # Could add bundle size analysis tools\n\n  # Parallel test execution\n  unit-tests:\n    name: 🧪 Unit Tests\n    runs-on: ubuntu-latest\n    needs: quality-gates\n    if: needs.quality-gates.outputs.should-run-tests == 'true'\n    timeout-minutes: 15\n    \n    strategy:\n      matrix:\n        test-group: [core, features, discord, api]\n    \n    steps:\n      - name: 📥 Checkout code\n        uses: actions/checkout@v4\n      \n      - name: 🔧 Setup Node.js\n        uses: actions/setup-node@v4\n        with:\n          node-version: ${{ env.NODE_VERSION }}\n      \n      - name: 📦 Setup pnpm\n        uses: pnpm/action-setup@v2\n        with:\n          version: ${{ env.PNPM_VERSION }}\n      \n      - name: 💾 Restore dependencies\n        uses: actions/cache@v3\n        with:\n          path: |\n            ~/.pnpm-store\n            node_modules\n          key: ${{ needs.quality-gates.outputs.cache-key }}\n          restore-keys: |\n            node-modules-${{ runner.os }}-\n      \n      - name: 📦 Install dependencies\n        run: pnpm install --frozen-lockfile --prefer-offline\n      \n      - name: 🧪 Run unit tests\n        run: |\n          case \"${{ matrix.test-group }}\" in\n            \"core\")\n              pnpm test --testPathPattern=\"src/core/\" --coverage --coverageDirectory=\"coverage/core\"\n              ;;\n            \"features\")\n              pnpm test --testPathPattern=\"src/features/\" --coverage --coverageDirectory=\"coverage/features\"\n              ;;\n            \"discord\")\n              pnpm test --testPathPattern=\"src/discord/\" --coverage --coverageDirectory=\"coverage/discord\"\n              ;;\n            \"api\")\n              pnpm test --testPathPattern=\"src/api/\" --coverage --coverageDirectory=\"coverage/api\"\n              ;;\n          esac\n        env:\n          NODE_ENV: test\n          CI: true\n      \n      - name: 📊 Upload coverage\n        uses: codecov/codecov-action@v3\n        with:\n          file: ./coverage/${{ matrix.test-group }}/lcov.info\n          flags: ${{ matrix.test-group }}\n          name: ${{ matrix.test-group }}-coverage\n          fail_ci_if_error: false\n      \n      - name: 📁 Upload test artifacts\n        uses: actions/upload-artifact@v3\n        if: always()\n        with:\n          name: unit-test-results-${{ matrix.test-group }}\n          path: |\n            coverage/${{ matrix.test-group }}/\n            test-results/\n          retention-days: 7\n\n  integration-tests:\n    name: 🔗 Integration Tests\n    runs-on: ubuntu-latest\n    needs: quality-gates\n    if: needs.quality-gates.outputs.should-run-tests == 'true'\n    timeout-minutes: 20\n    \n    services:\n      postgres:\n        image: postgres:15-alpine\n        env:\n          POSTGRES_USER: test_user\n          POSTGRES_PASSWORD: test_pass\n          POSTGRES_DB: discord_bot_test\n        options: >-\n          --health-cmd pg_isready\n          --health-interval 10s\n          --health-timeout 5s\n          --health-retries 5\n        ports:\n          - 5432:5432\n    \n    steps:\n      - name: 📥 Checkout code\n        uses: actions/checkout@v4\n      \n      - name: 🔧 Setup Node.js\n        uses: actions/setup-node@v4\n        with:\n          node-version: ${{ env.NODE_VERSION }}\n      \n      - name: 📦 Setup pnpm\n        uses: pnpm/action-setup@v2\n        with:\n          version: ${{ env.PNPM_VERSION }}\n      \n      - name: 💾 Restore dependencies\n        uses: actions/cache@v3\n        with:\n          path: |\n            ~/.pnpm-store\n            node_modules\n          key: ${{ needs.quality-gates.outputs.cache-key }}\n      \n      - name: 📦 Install dependencies\n        run: pnpm install --frozen-lockfile --prefer-offline\n      \n      - name: 🗄️ Setup test database\n        run: |\n          pnpm db:push\n        env:\n          DATABASE_URL: postgresql://test_user:test_pass@localhost:5432/discord_bot_test\n      \n      - name: 🔗 Run integration tests\n        run: pnpm test:integration --coverage\n        env:\n          NODE_ENV: test\n          DATABASE_URL: postgresql://test_user:test_pass@localhost:5432/discord_bot_test\n          CI: true\n      \n      - name: 📊 Upload coverage\n        uses: codecov/codecov-action@v3\n        with:\n          file: ./coverage/lcov.info\n          flags: integration\n          name: integration-coverage\n          fail_ci_if_error: false\n\n  e2e-tests:\n    name: 🎭 E2E Tests\n    runs-on: ubuntu-latest\n    needs: [quality-gates, unit-tests]\n    if: needs.quality-gates.outputs.should-run-tests == 'true' && github.event_name == 'pull_request'\n    timeout-minutes: 30\n    \n    services:\n      postgres:\n        image: postgres:15-alpine\n        env:\n          POSTGRES_USER: e2e_user\n          POSTGRES_PASSWORD: e2e_pass\n          POSTGRES_DB: discord_bot_e2e\n        options: >-\n          --health-cmd pg_isready\n          --health-interval 10s\n          --health-timeout 5s\n          --health-retries 5\n        ports:\n          - 5432:5432\n    \n    steps:\n      - name: 📥 Checkout code\n        uses: actions/checkout@v4\n      \n      - name: 🔧 Setup Node.js\n        uses: actions/setup-node@v4\n        with:\n          node-version: ${{ env.NODE_VERSION }}\n      \n      - name: 📦 Setup pnpm\n        uses: pnpm/action-setup@v2\n        with:\n          version: ${{ env.PNPM_VERSION }}\n      \n      - name: 💾 Restore dependencies\n        uses: actions/cache@v3\n        with:\n          path: |\n            ~/.pnpm-store\n            node_modules\n          key: ${{ needs.quality-gates.outputs.cache-key }}\n      \n      - name: 📦 Install dependencies\n        run: pnpm install --frozen-lockfile --prefer-offline\n      \n      - name: 🗄️ Setup E2E database\n        run: |\n          pnpm db:push\n        env:\n          DATABASE_URL: postgresql://e2e_user:e2e_pass@localhost:5432/discord_bot_e2e\n      \n      - name: 🎭 Run E2E tests\n        run: pnpm test:e2e\n        env:\n          NODE_ENV: e2e\n          DATABASE_URL: postgresql://e2e_user:e2e_pass@localhost:5432/discord_bot_e2e\n          CI: true\n          # Note: E2E tests will use mocked Discord API\n      \n      - name: 📁 Upload E2E artifacts\n        uses: actions/upload-artifact@v3\n        if: always()\n        with:\n          name: e2e-test-results\n          path: |\n            coverage/e2e-report/\n            test-results/\n            logs/\n          retention-days: 14\n\n  performance-tests:\n    name: ⚡ Performance Tests\n    runs-on: ubuntu-latest\n    needs: [quality-gates, unit-tests]\n    if: needs.quality-gates.outputs.should-run-tests == 'true' && github.event_name == 'pull_request'\n    timeout-minutes: 15\n    \n    steps:\n      - name: 📥 Checkout code\n        uses: actions/checkout@v4\n      \n      - name: 🔧 Setup Node.js\n        uses: actions/setup-node@v4\n        with:\n          node-version: ${{ env.NODE_VERSION }}\n      \n      - name: 📦 Setup pnpm\n        uses: pnpm/action-setup@v2\n        with:\n          version: ${{ env.PNPM_VERSION }}\n      \n      - name: 💾 Restore dependencies\n        uses: actions/cache@v3\n        with:\n          path: |\n            ~/.pnpm-store\n            node_modules\n          key: ${{ needs.quality-gates.outputs.cache-key }}\n      \n      - name: 📦 Install dependencies\n        run: pnpm install --frozen-lockfile --prefer-offline\n      \n      - name: ⚡ Run performance tests\n        run: pnpm test:performance\n        env:\n          NODE_ENV: test\n          CI: true\n      \n      - name: 📊 Performance regression check\n        run: |\n          echo \"Performance regression analysis would go here\"\n          # Could integrate with tools like Lighthouse CI or custom benchmarks\n\n  security-scan:\n    name: 🔒 Security Scan\n    runs-on: ubuntu-latest\n    needs: quality-gates\n    timeout-minutes: 15\n    \n    steps:\n      - name: 📥 Checkout code\n        uses: actions/checkout@v4\n      \n      - name: 🔍 Run Snyk security scan\n        uses: snyk/actions/node@master\n        env:\n          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}\n        with:\n          args: --severity-threshold=high\n        continue-on-error: true\n      \n      - name: 🔍 Run CodeQL analysis\n        uses: github/codeql-action/analyze@v2\n        if: github.event_name == 'pull_request'\n        with:\n          languages: typescript\n        continue-on-error: true\n      \n      - name: 🔒 Check for secrets\n        uses: trufflesecurity/trufflehog@main\n        with:\n          path: ./\n          base: ${{ github.event.repository.default_branch }}\n          head: HEAD\n        continue-on-error: true\n\n  # Merge coverage reports and create final report\n  coverage-report:\n    name: 📊 Coverage Report\n    runs-on: ubuntu-latest\n    needs: [unit-tests, integration-tests]\n    if: always() && needs.quality-gates.outputs.should-run-tests == 'true'\n    \n    steps:\n      - name: 📥 Checkout code\n        uses: actions/checkout@v4\n      \n      - name: 📁 Download coverage artifacts\n        uses: actions/download-artifact@v3\n        with:\n          path: coverage-artifacts\n      \n      - name: 📊 Merge coverage reports\n        run: |\n          mkdir -p merged-coverage\n          # Merge coverage reports from different test groups\n          echo \"Merging coverage reports...\"\n          # This would use a tool like nyc or jest to merge reports\n      \n      - name: 📈 Coverage threshold check\n        run: |\n          echo \"Checking coverage thresholds...\"\n          # Implement coverage threshold validation\n      \n      - name: 💬 Comment PR with coverage\n        if: github.event_name == 'pull_request'\n        uses: actions/github-script@v6\n        with:\n          script: |\n            const coverage = 85.5; // This would be calculated from actual coverage\n            const body = `## 📊 Test Coverage Report\\n\\n` +\n              `- **Overall Coverage**: ${coverage}%\\n` +\n              `- **Unit Tests**: ✅ Passed\\n` +\n              `- **Integration Tests**: ✅ Passed\\n` +\n              `- **Performance**: ✅ Within thresholds\\n\\n` +\n              `[View detailed report](${context.payload.pull_request.html_url}/checks)`;\n            \n            github.rest.issues.createComment({\n              issue_number: context.issue.number,\n              owner: context.repo.owner,\n              repo: context.repo.repo,\n              body: body\n            });\n\n  # Final status check\n  ci-success:\n    name: ✅ CI Success\n    runs-on: ubuntu-latest\n    needs: [quality-gates, unit-tests, integration-tests, security-scan, coverage-report]\n    if: always()\n    \n    steps:\n      - name: ✅ Check overall success\n        run: |\n          if [[ \"${{ needs.quality-gates.result }}\" == \"success\" && \n                \"${{ needs.unit-tests.result }}\" == \"success\" && \n                \"${{ needs.integration-tests.result }}\" == \"success\" && \n                \"${{ needs.security-scan.result }}\" != \"failure\" ]]; then\n            echo \"✅ All CI checks passed!\"\n            exit 0\n          else\n            echo \"❌ Some CI checks failed\"\n            exit 1\n          fi\n      \n      - name: 🎉 Success notification\n        if: success() && github.event_name == 'pull_request'\n        run: |\n          echo \"🎉 CI pipeline completed successfully!\"\n          echo \"📊 All tests passed with sufficient coverage\"\n          echo \"🔒 Security checks completed\"\n          echo \"⚡ Performance within acceptable limits\"\n\n# Workflow dispatch inputs for manual runs\nworkflow_dispatch:\n  inputs:\n    test_type:\n      description: 'Type of tests to run'\n      required: true\n      default: 'all'\n      type: choice\n      options:\n      - all\n      - unit\n      - integration\n      - e2e\n      - performance\n    coverage_threshold:\n      description: 'Minimum coverage threshold'\n      required: false\n      default: '80'\n      type: string