{"compilerOptions": {"target": "ES2020", "module": "commonjs", "outDir": "./dist", "rootDir": "./src", "strict": false, "skipLibCheck": true, "skipDefaultLibCheck": true, "noEmitOnError": false, "allowJs": true, "checkJs": false, "noImplicitAny": false, "noImplicitReturns": false, "noImplicitThis": false, "strictNullChecks": false, "strictBindCallApply": false, "strictFunctionTypes": false, "strictPropertyInitialization": false, "noPropertyAccessFromIndexSignature": false, "suppressImplicitAnyIndexErrors": true, "suppressExcessPropertyErrors": true, "allowUnreachableCode": true, "allowUnusedLabels": true, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "noImplicitOverride": false, "noFallthroughCasesInSwitch": false, "noUncheckedIndexedAccess": false, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": false, "resolveJsonModule": true, "isolatedModules": false, "declaration": false, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "sourceMap": false, "incremental": false, "baseUrl": "./", "moduleResolution": "node", "paths": {"@/*": ["src/*"], "@/core/*": ["src/core/*"], "@/discord/*": ["src/discord/*"], "@/api/*": ["src/api/*"], "@/agents/*": ["src/agents/*"], "@/features/*": ["src/features/*"], "@/common/*": ["src/common/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.spec.ts", "**/*.test.ts", "test", "coverage"], "ts_node": {"esm": false, "files": true}}