{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "target": "ES2020", "lib": ["ES2020", "DOM"], "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "tsBuildInfoFile": "./dist/.tsbuildinfo", "skipLibCheck": true, "skipDefaultLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "strictFunctionTypes": false, "strictPropertyInitialization": false, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": false, "strict": false, "exactOptionalPropertyTypes": false, "noImplicitReturns": false, "noUncheckedIndexedAccess": false, "noImplicitOverride": false, "noPropertyAccessFromIndexSignature": false, "ignoreDeprecations": "5.0", "noEmitOnError": false, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "allowJs": true, "paths": {"@/*": ["src/*"], "@/core/*": ["src/core/*"], "@/discord/*": ["src/discord/*"], "@/api/*": ["src/api/*"], "@/agents/*": ["src/agents/*"], "@/features/*": ["src/features/*"], "@/common/*": ["src/common/*"], "@/types/*": ["src/types/*"]}, "typeRoots": ["node_modules/@types", "src/types"]}, "include": ["src/**/*", "test/**/*", "src/types/**/*"], "exclude": ["node_modules", "dist", "**/*.spec.ts", "**/*.test.ts", "coverage", "logs", ".next", "build", "temp*", "scripts/*.js"], "ts-node": {"esm": true, "files": true}}