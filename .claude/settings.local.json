{"permissions": {"allow": ["mcp__discord-mcp__get_server_info", "mcp__discord-mcp__get_automod_rules", "mcp__discord-mcp__find_channel", "mcp__discord-mcp__list_channels", "mcp__discord-mcp__create_automod_rule", "mcp__discord-mcp__send_message", "mcp__discord-mcp__read_messages", "mcp__discord-mcp__delete_automod_rule", "mcp__discord-mcp__get_roles", "mcp__discord-mcp__add_reaction", "mcp__discord-mcp__join_voice_channel", "mcp__discord-mcp__leave_voice_channel", "mcp__ide__getDiagnostics", "mcp__discord-mcp__create_text_channel", "mcp__discord-mcp__edit_channel_advanced", "mcp__discord-mcp__delete_channel", "mcp__discord-mcp__get_members", "mcp__discord-mcp__create_role", "mcp__discord-mcp__delete_role", "mcp__neon__list_projects", "mcp__neon__run_sql", "mcp__neon__describe_table_schema", "mcp__discord-mcp__get_channel_structure", "mcp__discord-mcp__list_channels_in_category", "mcp__discord-mcp__discord_manage", "mcp__discord-mcp__delete_message", "mcp__deepwiki__read_wiki_contents", "mcp__deep<PERSON><PERSON>__ask_question"], "deny": []}}