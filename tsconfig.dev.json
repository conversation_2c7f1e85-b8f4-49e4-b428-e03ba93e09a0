{
  "extends": "./tsconfig.json",
  "compilerOptions": {
    "sourceMap": true,
    "declaration": false,
    "removeComments": false,
    "incremental": true,
    "tsBuildInfoFile": "./dist/.dev-tsbuildinfo",
    "skipLibCheck": true,
    "skipDefaultLibCheck": true,
    "noEmitOnError": false,
    
    // More lenient type checking for development
    "strict": false,
    "noImplicitAny": false,
    "strictNullChecks": false,
    "strictFunctionTypes": false,
    "strictBindCallApply": false,
    "strictPropertyInitialization": false,
    "noImplicitReturns": false,
    "noImplicitThis": false,
    "noImplicitOverride": false,
    "exactOptionalPropertyTypes": false,
    "noUncheckedIndexedAccess": false,
    "noPropertyAccessFromIndexSignature": false,
    
    // Suppress common errors during development
    "suppressImplicitAnyIndexErrors": true,
    "suppressExcessPropertyErrors": true,
    
    // Performance optimizations
    "assumeChangesOnlyAffectDirectDependencies": true,
    "disableSizeLimit": true
  },
  "compileOnSave": false,
  "include": [
    "src/**/*"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "coverage",
    "logs",
    ".next",
    "build", 
    "temp*",
    "scripts/*.js",
    "**/*.spec.ts",
    "**/*.test.ts",
    "test/**/*"
  ]
}