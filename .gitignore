# Dependencies
/node_modules
/.pnp
.pnp.js
pnpm-lock.yaml*
package-lock.json
yarn.lock
dashboard@*

# Build artifacts
/dist/
/build/
*.tsbuildinfo
tsconfig.build.json

# Environment variables
.env
.env*.local
.env.production
.env.development

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
backend.log
bot.log
bot-final.log
bot-test.log
build_errors.txt
build_output.txt
build_test_output.txt
output.log
output.txt

# Testing
/coverage
jest.config.js
jest.setup.js

# Next.js (if used for frontend)
/.next/
/out/
next-env.d.ts
next

# Operating System
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDEs and editors
.vscode/
.idea/
.kiro/
*.swp
*.swo
*~

# Certificates
*.pem
*.key
*.cert

# Cache directories
.cache/
.parcel-cache/

# Database
*.sqlite
*.sqlite3
*.db

# Temporary files
*.tmp
*.temp
temp_migration.sql

# Docker
.dockerignore

# Production/deployment
.vercel
.augment/
.pixi/

# Generated files
cookies.txt
featurecommands.txt
guild-channels-list.txt

# Scripts and utilities
convert_banner_green.py
clear-invalid-cookie.js
bulk_fix_*.sh
fix-*.js
fix-*.py
fix_*.sh
final_cleanup_script.sh

# Development tools
.windsurfrules

# Backups
/backups/

# Panel errors and screenshots
/panel-errors/

# Documentation (uncomment if you want to exclude certain docs)
# *.md

# Zone Identifier files (Windows)
*.Zone.Identifier

# Sevalla specific
sevalla-*.yml
sevalla-*.js
SEVALLA_*.txt
sevalla.json