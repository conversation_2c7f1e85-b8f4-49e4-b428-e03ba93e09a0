#!/bin/bash

# Extract all TS1005 errors with precise file locations
echo "🔍 Extracting all TS1005 syntax errors..."

# Create output directory
mkdir -p error_analysis

# Get detailed build output
npm run build --stats-error-details 2>&1 > full_build.log

# Extract comma errors with file locations
echo "📝 Processing comma errors..."
grep -B3 -A1 "TS1005.*',' expected" full_build.log | \
grep -E "(ERROR in|TS1005)" | \
sed 's/\[1m\[31mERROR\[39m\[22m in \[1m//g' | \
sed 's/\[39m\[22m//g' | \
sed 's/\[1m\[tsl\] \[1m\[31mERROR\[39m\[22m\[1m in //g' | \
sed 's/(\([0-9]*\),\([0-9]*\))/:\1:\2/g' > error_analysis/comma_errors.txt

# Extract semicolon errors with file locations  
echo "📝 Processing semicolon errors..."
grep -B3 -A1 "TS1005.*';' expected" full_build.log | \
grep -E "(ERROR in|TS1005)" | \
sed 's/\[1m\[31mERROR\[39m\[22m in \[1m//g' | \
sed 's/\[39m\[22m//g' | \
sed 's/\[1m\[tsl\] \[1m\[31mERROR\[39m\[22m\[1m in //g' | \
sed 's/(\([0-9]*\),\([0-9]*\))/:\1:\2/g' > error_analysis/semicolon_errors.txt

# Generate frequency analysis
echo "📊 Generating frequency analysis..."
echo "=== COMMA ERROR FREQUENCY BY FILE ===" > error_analysis/frequency_report.txt
grep -E "^/.*\.ts:" error_analysis/comma_errors.txt | cut -d: -f1 | sort | uniq -c | sort -nr >> error_analysis/frequency_report.txt

echo "" >> error_analysis/frequency_report.txt
echo "=== SEMICOLON ERROR FREQUENCY BY FILE ===" >> error_analysis/frequency_report.txt
grep -E "^/.*\.ts:" error_analysis/semicolon_errors.txt | cut -d: -f1 | sort | uniq -c | sort -nr >> error_analysis/frequency_report.txt

# Count totals
comma_count=$(grep -c "TS1005.*',' expected" full_build.log)
semicolon_count=$(grep -c "TS1005.*';' expected" full_build.log)
total_count=$((comma_count + semicolon_count))

echo "✅ Extraction complete!"
echo "📊 Found $comma_count comma errors"
echo "📊 Found $semicolon_count semicolon errors" 
echo "📊 Total: $total_count TS1005 errors"
echo "📁 Results saved in error_analysis/"