// Test file to verify Discord.js type fixes
import { Role, GuildMember } from 'discord.js';

// Test the specific fix for rolesToAdd array type inference
function testRoleArrayTyping() {
  // This should not throw "Type 'Role' is not assignable to parameter of type 'never'"
  const rolesToAdd: Role[] = [];
  
  // Simulate the Discord.js role object
  const mockRole = {} as Role;
  
  // This push should work without type errors
  rolesToAdd.push(mockRole);
  
  // This map should work without "Property 'name' does not exist on type 'never'"
  const roleNames = rolesToAdd.map(r => r.name);
  
  return { rolesToAdd, roleNames };
}

// Test member role management
function testMemberRoleManagement(member: GuildMember) {
  const rolesToAdd: Role[] = [];
  
  // Simulate role fetching from cache
  const roleId = 'some-role-id';
  const role = member.guild.roles.cache.get(roleId);
  
  if (role && !member.roles.cache.has(roleId)) {
    rolesToAdd.push(role); // This should work without type errors
  }
  
  return rolesToAdd;
}

export { testRoleArrayTyping, testMemberRoleManagement };