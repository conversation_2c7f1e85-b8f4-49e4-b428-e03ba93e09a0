/**
 * Global TypeScript declarations for Discord Bot application
 * This file contains type definitions for external libraries and global types
 */

declare global {
  namespace NodeJS {
    interface ProcessEnv {
      // Discord Configuration
      DISCORD_TOKEN: string;
      DISCORD_CLIENT_ID: string;
      DISCORD_CLIENT_SECRET: string;
      DISCORD_GUILD_ID: string;
      D<PERSON>CORD_REDIRECT_URI: string;

      // Database Configuration
      DATABASE_URL: string;
      REDIS_URL: string;
      REDIS_HOST: string;
      REDIS_PORT: string;
      REDIS_PASSWORD: string;

      // API Configuration
      OPENAI_API_KEY: string;
      ANTHROPIC_API_KEY: string;
      JWT_SECRET: string;
      
      // Server Configuration
      PORT: string;
      NODE_ENV: 'development' | 'production' | 'test';
      
      // Whop Integration
      WHOP_API_KEY: string;
      WHOP_CLIENT_ID: string;
      WHOP_CLIENT_SECRET: string;
      
      // NGROK Configuration
      NGROK_AUTHTOKEN: string;
      NGROK_DOMAIN: string;
      
      // Monitoring
      PROMETHEUS_ENABLED: string;
      HEALTH_CHECK_ENABLED: string;
      
      // Security
      RATE_LIMIT_TTL: string;
      RATE_LIMIT_LIMIT: string;
    }
  }

  // jQuery type definitions (if used anywhere)
  interface JQueryStatic {
    (selector: string): JQuery;
    (element: Element): JQuery;
    (html: string, context?: Document): JQuery;
  }

  interface JQuery {
    // Common jQuery methods
    addClass(className: string): JQuery;
    removeClass(className: string): JQuery;
    attr(name: string): string;
    attr(name: string, value: string | number): JQuery;
    html(): string;
    html(htmlString: string): JQuery;
    text(): string;
    text(textString: string): JQuery;
    val(): any;
    val(value: any): JQuery;
    click(handler?: () => void): JQuery;
    on(events: string, handler: () => void): JQuery;
    off(events?: string): JQuery;
    find(selector: string): JQuery;
  }

  // Global jQuery variable
  declare const $: JQueryStatic;
  declare const jQuery: JQueryStatic;

  // Global types for common Discord.js extensions
  interface DiscordUser {
    id: string;
    username: string;
    discriminator: string;
    avatar: string | null;
    bot?: boolean;
    system?: boolean;
    mfa_enabled?: boolean;
    banner?: string | null;
    accent_color?: number | null;
    locale?: string;
    verified?: boolean;
    email?: string | null;
    flags?: number;
    premium_type?: number;
    public_flags?: number;
  }

  interface DiscordGuild {
    id: string;
    name: string;
    icon: string | null;
    icon_hash?: string | null;
    splash: string | null;
    discovery_splash: string | null;
    owner?: boolean;
    owner_id: string;
    permissions?: string;
    region?: string | null;
    afk_channel_id: string | null;
    afk_timeout: number;
    widget_enabled?: boolean;
    widget_channel_id?: string | null;
    verification_level: number;
    default_message_notifications: number;
    explicit_content_filter: number;
    roles: any[];
    emojis: any[];
    features: string[];
    mfa_level: number;
    application_id: string | null;
    system_channel_id: string | null;
    system_channel_flags: number;
    rules_channel_id: string | null;
    max_presences?: number | null;
    max_members?: number;
    vanity_url_code: string | null;
    description: string | null;
    banner: string | null;
    premium_tier: number;
    premium_subscription_count?: number;
    preferred_locale: string;
    public_updates_channel_id: string | null;
    max_video_channel_users?: number;
    approximate_member_count?: number;
    approximate_presence_count?: number;
    welcome_screen?: any;
    nsfw_level: number;
    stickers?: any[];
    premium_progress_bar_enabled: boolean;
  }

  // AI/LLM Types
  interface AIResponse {
    success: boolean;
    data?: any;
    error?: string;
    usage?: {
      prompt_tokens?: number;
      completion_tokens?: number;
      total_tokens?: number;
    };
  }

  interface ChatMessage {
    role: 'system' | 'user' | 'assistant';
    content: string;
    timestamp?: Date;
  }

  // Database types
  interface DatabaseEntity {
    id: string | number;
    created_at: Date;
    updated_at: Date;
  }

  // Redis types
  interface RedisConfig {
    host: string;
    port: number;
    password?: string;
    db?: number;
    retryDelayOnFailover?: number;
    maxRetriesPerRequest?: number;
    enableOfflineQueue?: boolean;
  }

  // Panel types
  interface PanelConfig {
    type: string;
    title: string;
    description?: string;
    components: any[];
    actions?: any[];
    permissions?: string[];
  }

  interface PanelAction {
    id: string;
    type: 'button' | 'select' | 'modal';
    label: string;
    style?: string;
    customId: string;
    handler: string;
  }

  // Agent types
  interface AgentContext {
    userId: string;
    guildId: string;
    channelId: string;
    messageId?: string;
    timestamp: Date;
    sessionId?: string;
  }

  interface AgentResponse {
    content: string;
    embeds?: any[];
    components?: any[];
    ephemeral?: boolean;
    files?: any[];
  }

  // Cache types
  interface CacheEntry<T = any> {
    data: T;
    timestamp: number;
    ttl: number;
  }

  // Monitoring types
  interface MetricData {
    name: string;
    value: number;
    labels?: Record<string, string>;
    timestamp?: Date;
  }

  interface HealthCheck {
    status: 'healthy' | 'unhealthy' | 'degraded';
    message?: string;
    details?: Record<string, any>;
    timestamp: Date;
  }

  // Security types
  interface SecurityContext {
    userId: string;
    roles: string[];
    permissions: string[];
    ip?: string;
    userAgent?: string;
  }

  interface RateLimit {
    limit: number;
    remaining: number;
    reset: number;
    resetTime: Date;
  }

  // Whop Integration types
  interface WhopUser {
    id: string;
    email: string;
    username: string;
    profile_pic_url?: string;
    social_accounts?: any[];
  }

  interface WhopMembership {
    id: string;
    user: WhopUser;
    plan: any;
    status: 'active' | 'past_due' | 'canceled' | 'trialing';
    valid: boolean;
    cancel_at_period_end: boolean;
    current_period_start: number;
    current_period_end: number;
  }

  // Multi-tenancy types
  interface Tenant {
    id: string;
    name: string;
    domain: string;
    settings: Record<string, any>;
    features: string[];
    limits: Record<string, number>;
  }

  interface Organization {
    id: string;
    name: string;
    tenantId: string;
    settings: Record<string, any>;
    members: string[];
  }

  // Utility types for better type safety
  type Nullable<T> = T | null;
  type Optional<T> = T | undefined;
  type RequiredKeys<T> = {
    [K in keyof T]-?: {} extends Pick<T, K> ? never : K;
  }[keyof T];
  type OptionalKeys<T> = {
    [K in keyof T]-?: {} extends Pick<T, K> ? K : never;
  }[keyof T];

  // Generic API Response type
  interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    error?: string;
    message?: string;
    timestamp: Date;
  }

  // Event types
  interface DomainEvent {
    type: string;
    payload: any;
    timestamp: Date;
    aggregateId: string;
    version: number;
  }

  // Configuration types
  interface AppConfig {
    database: {
      url: string;
      type: 'postgres' | 'mysql' | 'sqlite' | 'redis';
      ssl?: boolean;
      logging?: boolean;
    };
    redis: {
      host: string;
      port: number;
      password?: string;
      db?: number;
    };
    discord: {
      token: string;
      clientId: string;
      guildId: string;
    };
    security: {
      jwtSecret: string;
      rateLimiting: {
        ttl: number;
        limit: number;
      };
    };
  }

  // Module augmentation for Express if needed
  namespace Express {
    interface Request {
      user?: any;
      tenant?: Tenant;
      rateLimit?: RateLimit;
      startTime?: number;
    }
  }
}

// Export empty object to make this file a module
export {};