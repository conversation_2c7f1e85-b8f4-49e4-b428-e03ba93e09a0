/**
 * API and service-related type definitions
 * This file contains comprehensive type definitions for API endpoints, services, and DTOs
 */

// Base API response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: ApiError;
  message?: string;
  timestamp: Date;
  requestId?: string;
  version?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, any>;
  stack?: string;
  timestamp: Date;
}

// Authentication and authorization types
export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  tokenType: 'Bearer';
}

export interface JwtPayload {
  sub: string; // user ID
  username: string;
  discordId: string;
  roles: string[];
  permissions: string[];
  guildId?: string;
  iat: number;
  exp: number;
  jti?: string;
}

export interface AuthUser {
  id: string;
  discordId: string;
  username: string;
  discriminator: string;
  avatar?: string;
  email?: string;
  roles: string[];
  permissions: string[];
  isPremium: boolean;
  guildMemberships: Array<{
    guildId: string;
    roles: string[];
    joinedAt: Date;
  }>;
}

// Discord OAuth types
export interface DiscordOAuthResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  refresh_token: string;
  scope: string;
}

export interface DiscordUser {
  id: string;
  username: string;
  discriminator: string;
  avatar?: string;
  bot?: boolean;
  system?: boolean;
  mfa_enabled?: boolean;
  banner?: string;
  accent_color?: number;
  locale?: string;
  verified?: boolean;
  email?: string;
  flags?: number;
  premium_type?: number;
  public_flags?: number;
}

// Guild management API types
export interface GuildDto {
  id: string;
  discordId: string;
  name: string;
  icon?: string;
  ownerId: string;
  memberCount: number;
  isPremium: boolean;
  premiumTier: number;
  features: string[];
  settings: {
    prefix: string;
    aiEnabled: boolean;
    moderationEnabled: boolean;
    welcomeEnabled: boolean;
    welcomeChannelId?: string;
    logChannelId?: string;
    adminRoleIds: string[];
    moderatorRoleIds: string[];
  };
  statistics: {
    totalMessages: number;
    activeUsers: number;
    commandsUsed: number;
    aiInteractions: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateGuildDto {
  discordId: string;
  name: string;
  ownerId: string;
  settings?: Partial<GuildDto['settings']>;
}

export interface UpdateGuildDto {
  name?: string;
  settings?: Partial<GuildDto['settings']>;
  features?: string[];
}

// User management API types
export interface UserDto {
  id: string;
  discordId: string;
  username: string;
  discriminator: string;
  avatar?: string;
  email?: string;
  isPremium: boolean;
  premiumSince?: Date;
  level: number;
  experience: number;
  credits: number;
  preferences: {
    notifications: boolean;
    aiEnabled: boolean;
    privacyLevel: 'public' | 'friends' | 'private';
    language: string;
    timezone: string;
    theme: 'light' | 'dark' | 'auto';
  };
  statistics: {
    totalCommands: number;
    aiInteractions: number;
    messagesCount: number;
    lastSeen: Date;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateUserDto {
  discordId: string;
  username: string;
  discriminator: string;
  avatar?: string;
  email?: string;
}

export interface UpdateUserDto {
  username?: string;
  discriminator?: string;
  avatar?: string;
  email?: string;
  preferences?: Partial<UserDto['preferences']>;
}

// AI Agent API types
export interface AiAgentConfigDto {
  id: string;
  guildId: string;
  agentType: string;
  name: string;
  description: string;
  enabled: boolean;
  model: string;
  systemPrompt: string;
  personality: string;
  parameters: {
    temperature: number;
    maxTokens: number;
    topP: number;
    frequencyPenalty: number;
    presencePenalty: number;
    stopSequences: string[];
  };
  rateLimits: {
    perUser: number;
    global: number;
  };
  permissions: {
    allowedChannels: string[];
    allowedRoles: string[];
    blockedUsers: string[];
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateAiAgentConfigDto {
  guildId: string;
  agentType: string;
  name: string;
  description: string;
  model: string;
  systemPrompt: string;
  personality?: string;
  parameters?: Partial<AiAgentConfigDto['parameters']>;
  rateLimits?: Partial<AiAgentConfigDto['rateLimits']>;
  permissions?: Partial<AiAgentConfigDto['permissions']>;
}

export interface UpdateAiAgentConfigDto {
  name?: string;
  description?: string;
  enabled?: boolean;
  model?: string;
  systemPrompt?: string;
  personality?: string;
  parameters?: Partial<AiAgentConfigDto['parameters']>;
  rateLimits?: Partial<AiAgentConfigDto['rateLimits']>;
  permissions?: Partial<AiAgentConfigDto['permissions']>;
}

export interface AiChatRequest {
  message: string;
  channelId: string;
  guildId: string;
  userId: string;
  context?: Record<string, any>;
  model?: string;
  parameters?: {
    temperature?: number;
    maxTokens?: number;
    topP?: number;
  };
}

export interface AiChatResponse {
  response: string;
  model: string;
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  processingTime: number;
  sessionId?: string;
  context?: Record<string, any>;
}

// Panel management API types
export interface PanelDto {
  id: string;
  guildId: string;
  channelId: string;
  panelType: string;
  messageId: string;
  title: string;
  description?: string;
  config: Record<string, any>;
  components: Array<{
    type: 'button' | 'select' | 'modal';
    id: string;
    label: string;
    style?: string;
    customId: string;
    options?: Array<{
      label: string;
      value: string;
      description?: string;
      emoji?: string;
    }>;
  }>;
  permissions?: string[];
  isActive: boolean;
  deployedBy: string;
  statistics: {
    interactions: number;
    uniqueUsers: number;
    lastInteraction?: Date;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface CreatePanelDto {
  guildId: string;
  channelId: string;
  panelType: string;
  title: string;
  description?: string;
  config: Record<string, any>;
  permissions?: string[];
  autoUpdate?: boolean;
}

export interface UpdatePanelDto {
  title?: string;
  description?: string;
  config?: Record<string, any>;
  permissions?: string[];
  isActive?: boolean;
}

export interface PanelInteractionDto {
  id: string;
  panelId: string;
  userId: string;
  interactionType: string;
  data: Record<string, any>;
  responseTime: number;
  success: boolean;
  errorMessage?: string;
  timestamp: Date;
}

// Support system API types
export interface SupportTicketDto {
  id: string;
  userId: string;
  guildId: string;
  channelId?: string;
  title: string;
  description: string;
  category: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'open' | 'in_progress' | 'waiting' | 'resolved' | 'closed';
  assignedTo?: string;
  assignedAt?: Date;
  resolvedAt?: Date;
  resolution?: string;
  satisfactionRating?: number;
  feedback?: string;
  tags: string[];
  attachments: string[];
  history: Array<{
    action: string;
    by: string;
    at: Date;
    data?: Record<string, any>;
  }>;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateSupportTicketDto {
  title: string;
  description: string;
  category: string;
  priority?: 'low' | 'medium' | 'high' | 'critical';
  tags?: string[];
  attachments?: string[];
}

export interface UpdateSupportTicketDto {
  title?: string;
  description?: string;
  category?: string;
  priority?: 'low' | 'medium' | 'high' | 'critical';
  status?: 'open' | 'in_progress' | 'waiting' | 'resolved' | 'closed';
  assignedTo?: string;
  resolution?: string;
  tags?: string[];
}

// Analytics and metrics API types
export interface AnalyticsDto {
  period: {
    start: Date;
    end: Date;
  };
  guilds: {
    total: number;
    active: number;
    new: number;
    premium: number;
  };
  users: {
    total: number;
    active: number;
    new: number;
    premium: number;
  };
  interactions: {
    commands: number;
    aiChats: number;
    panelInteractions: number;
    supportTickets: number;
  };
  performance: {
    averageResponseTime: number;
    uptime: number;
    errorRate: number;
  };
  resources: {
    memoryUsage: number;
    cpuUsage: number;
    diskUsage: number;
    networkTraffic: number;
  };
  ai: {
    totalRequests: number;
    totalTokens: number;
    averageTokensPerRequest: number;
    modelUsage: Record<string, number>;
    costEstimate: number;
  };
}

export interface MetricsQueryDto {
  metric: string;
  start: Date;
  end: Date;
  granularity?: 'minute' | 'hour' | 'day' | 'week' | 'month';
  filters?: Record<string, any>;
  groupBy?: string[];
}

export interface MetricsResponse {
  metric: string;
  data: Array<{
    timestamp: Date;
    value: number;
    labels?: Record<string, string>;
  }>;
  total?: number;
  average?: number;
  min?: number;
  max?: number;
}

// Health check API types
export interface HealthCheckResponse {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: Date;
  uptime: number;
  version: string;
  environment: string;
  checks: {
    database: HealthCheckDetails;
    redis: HealthCheckDetails;
    discord: HealthCheckDetails;
    ai: HealthCheckDetails;
    external: HealthCheckDetails;
  };
  resources: {
    memory: {
      used: number;
      total: number;
      percentage: number;
    };
    cpu: {
      usage: number;
    };
    disk: {
      used: number;
      total: number;
      percentage: number;
    };
  };
}

export interface HealthCheckDetails {
  status: 'healthy' | 'degraded' | 'unhealthy';
  responseTime?: number;
  message?: string;
  lastChecked: Date;
  details?: Record<string, any>;
}

// Webhook and event types
export interface WebhookDto {
  id: string;
  name: string;
  url: string;
  secret?: string;
  events: string[];
  isActive: boolean;
  lastTriggered?: Date;
  failureCount: number;
  maxRetries: number;
  retryInterval: number;
  headers?: Record<string, string>;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateWebhookDto {
  name: string;
  url: string;
  secret?: string;
  events: string[];
  headers?: Record<string, string>;
  maxRetries?: number;
  retryInterval?: number;
}

export interface WebhookPayload {
  event: string;
  timestamp: Date;
  data: Record<string, any>;
  signature?: string;
  version: string;
}

// File upload types
export interface FileUploadDto {
  id: string;
  filename: string;
  originalName: string;
  mimetype: string;
  size: number;
  url: string;
  uploadedBy: string;
  uploadedAt: Date;
  isPublic: boolean;
  metadata?: Record<string, any>;
}

export interface FileUploadRequest {
  file: Buffer;
  filename: string;
  mimetype: string;
  isPublic?: boolean;
  metadata?: Record<string, any>;
}

// Notification types
export interface NotificationDto {
  id: string;
  userId: string;
  type: string;
  title: string;
  message: string;
  data?: Record<string, any>;
  isRead: boolean;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  channels: ('discord' | 'email' | 'push' | 'sms')[];
  scheduledFor?: Date;
  sentAt?: Date;
  readAt?: Date;
  expiresAt?: Date;
  createdAt: Date;
}

export interface CreateNotificationDto {
  userId: string;
  type: string;
  title: string;
  message: string;
  data?: Record<string, any>;
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  channels?: ('discord' | 'email' | 'push' | 'sms')[];
  scheduledFor?: Date;
  expiresAt?: Date;
}

// Search and filtering types
export interface SearchQuery {
  query?: string;
  filters?: Record<string, any>;
  sort?: {
    field: string;
    direction: 'asc' | 'desc';
  };
  pagination?: {
    page: number;
    limit: number;
  };
  include?: string[];
  exclude?: string[];
}

export interface SearchResult<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasMore: boolean;
  aggregations?: Record<string, any>;
  suggestions?: string[];
  executionTime: number;
}

// Rate limiting types
export interface RateLimitInfo {
  limit: number;
  remaining: number;
  reset: Date;
  retryAfter?: number;
}

export interface RateLimitConfig {
  windowMs: number;
  max: number;
  message?: string;
  standardHeaders?: boolean;
  legacyHeaders?: boolean;
  keyGenerator?: (req: any) => string;
  skip?: (req: any) => boolean;
  onLimitReached?: (req: any, res: any) => void;
}

// Cache types
export interface CacheConfig {
  ttl: number;
  max?: number;
  updateAgeOnGet?: boolean;
  updateAgeOnHas?: boolean;
  allowStale?: boolean;
  noDeleteOnStaleGet?: boolean;
}

export interface CacheEntry<T = any> {
  value: T;
  ttl: number;
  created: Date;
  accessed: Date;
  hits: number;
}

// Service interfaces
export interface BaseService<T, CreateDto, UpdateDto> {
  create(data: CreateDto): Promise<T>;
  findById(id: string): Promise<T | null>;
  findAll(query?: SearchQuery): Promise<PaginatedResponse<T>>;
  update(id: string, data: UpdateDto): Promise<T>;
  delete(id: string): Promise<boolean>;
}

export interface UserService extends BaseService<UserDto, CreateUserDto, UpdateUserDto> {
  findByDiscordId(discordId: string): Promise<UserDto | null>;
  updateExperience(userId: string, amount: number): Promise<UserDto>;
  updateCredits(userId: string, amount: number): Promise<UserDto>;
  getLeaderboard(type: 'level' | 'credits', limit?: number): Promise<UserDto[]>;
}

export interface GuildService extends BaseService<GuildDto, CreateGuildDto, UpdateGuildDto> {
  findByDiscordId(discordId: string): Promise<GuildDto | null>;
  updateSettings(guildId: string, settings: Partial<GuildDto['settings']>): Promise<GuildDto>;
  getStatistics(guildId: string): Promise<AnalyticsDto>;
}

export interface AiService {
  chat(request: AiChatRequest): Promise<AiChatResponse>;
  generateCompletion(prompt: string, model?: string, parameters?: any): Promise<string>;
  analyzeImage(imageUrl: string, prompt?: string): Promise<string>;
  generateImage(prompt: string, options?: any): Promise<string>;
  transcribeAudio(audioUrl: string): Promise<string>;
  translateText(text: string, targetLanguage: string): Promise<string>;
  summarizeText(text: string, maxLength?: number): Promise<string>;
  extractKeywords(text: string): Promise<string[]>;
  classifyText(text: string, categories: string[]): Promise<string>;
}

// Validation schemas (for use with class-validator)
export interface ValidationSchema {
  [key: string]: {
    required?: boolean;
    type?: 'string' | 'number' | 'boolean' | 'array' | 'object' | 'date';
    min?: number;
    max?: number;
    pattern?: RegExp;
    enum?: any[];
    custom?: (value: any) => boolean | string;
  };
}

// Error handling types
export interface ErrorDetails {
  code: string;
  message: string;
  field?: string;
  value?: any;
  constraints?: string[];
}

export interface ValidationError extends Error {
  errors: ErrorDetails[];
  statusCode: number;
}

export interface ServiceError extends Error {
  code: string;
  statusCode: number;
  details?: Record<string, any>;
}

// Configuration types
export interface ApiConfig {
  port: number;
  host: string;
  cors: {
    origin: string[];
    credentials: boolean;
  };
  rateLimit: RateLimitConfig;
  auth: {
    jwt: {
      secret: string;
      expiresIn: string;
      refreshExpiresIn: string;
    };
    discord: {
      clientId: string;
      clientSecret: string;
      redirectUri: string;
      scopes: string[];
    };
  };
  features: {
    swagger: boolean;
    metrics: boolean;
    healthCheck: boolean;
    logging: boolean;
  };
}

// Export utility types for better development experience
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequireFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type ExcludeFields<T, K extends keyof T> = Omit<T, K>;

export type PickFields<T, K extends keyof T> = Pick<T, K>;