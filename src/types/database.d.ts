/**
 * Database-related type definitions
 * This file contains comprehensive type definitions for database entities, repositories, and operations
 */

// Base repository interface
export interface BaseRepository<T extends BaseEntity> {
  create(data: Partial<T>): Promise<T>;
  findById(id: string): Promise<T | null>;
  findOne(criteria: Partial<T>): Promise<T | null>;
  findMany(criteria?: Partial<T>, options?: QueryOptions): Promise<T[]>;
  update(id: string, data: Partial<T>): Promise<T>;
  delete(id: string): Promise<boolean>;
  count(criteria?: Partial<T>): Promise<number>;
  exists(criteria: Partial<T>): Promise<boolean>;
}

// Query options for database operations
export interface QueryOptions {
  limit?: number;
  offset?: number;
  orderBy?: {
    field: string;
    direction: 'ASC' | 'DESC';
  };
  include?: string[];
  select?: string[];
}

// Database entity base interface
export interface BaseEntity {
  id: string;
  created_at: Date;
  updated_at: Date;
  deleted_at?: Date;
}

// User entity types
export interface UserEntity extends BaseEntity {
  discord_id: string;
  username: string;
  discriminator: string;
  avatar?: string;
  email?: string;
  is_premium: boolean;
  premium_since?: Date;
  level: number;
  experience: number;
  credits: number;
  last_seen: Date;
  preferences: Record<string, any>;
  settings: UserSettings;
  api_keys?: UserApiKey[];
  panel_states?: UserPanelState[];
  relationships?: UserRelationship[];
  agent_interactions?: AgentInteraction[];
  agent_memories?: AgentMemory[];
  chat_sessions?: AiChatSession[];
}

export interface UserSettings {
  notifications: boolean;
  ai_enabled: boolean;
  privacy_level: 'public' | 'friends' | 'private';
  language: string;
  timezone: string;
  theme: 'light' | 'dark' | 'auto';
}

// Guild entity types
export interface GuildEntity extends BaseEntity {
  discord_id: string;
  name: string;
  icon?: string;
  owner_id: string;
  prefix: string;
  settings: GuildSettings;
  features: string[];
  is_premium: boolean;
  premium_tier: number;
  member_count: number;
  last_activity: Date;
  ai_configs?: AiAgentConfig[];
  channel_configs?: AiChannelConfig[];
  panel_deployments?: PanelDeployment[];
  announcements?: Announcement[];
}

export interface GuildSettings {
  ai_enabled: boolean;
  moderation_enabled: boolean;
  welcome_enabled: boolean;
  welcome_channel_id?: string;
  log_channel_id?: string;
  admin_role_ids: string[];
  moderator_role_ids: string[];
  auto_role_ids: string[];
  banned_words: string[];
  rate_limits: Record<string, number>;
  permissions: Record<string, string[]>;
}

// AI-related entity types
export interface AiAgentConfig extends BaseEntity {
  guild_id: string;
  agent_type: string;
  name: string;
  description: string;
  enabled: boolean;
  model: string;
  system_prompt: string;
  personality: string;
  temperature: number;
  max_tokens: number;
  top_p: number;
  frequency_penalty: number;
  presence_penalty: number;
  stop_sequences: string[];
  context_length: number;
  rate_limit_per_user: number;
  rate_limit_global: number;
  allowed_channels: string[];
  allowed_roles: string[];
  blocked_users: string[];
  config: Record<string, any>;
}

export interface AiChannelConfig extends BaseEntity {
  channel_id: string;
  guild_id: string;
  enabled: boolean;
  agent_config_id: string;
  auto_respond: boolean;
  require_mention: boolean;
  response_chance: number;
  custom_instructions: string;
  message_history_limit: number;
  context_awareness: boolean;
  learning_enabled: boolean;
  moderation_enabled: boolean;
  logging_enabled: boolean;
}

export interface AiChatSession extends BaseEntity {
  user_id: string;
  guild_id: string;
  channel_id: string;
  agent_type: string;
  model: string;
  context: Record<string, any>;
  message_count: number;
  token_usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  started_at: Date;
  ended_at?: Date;
  is_active: boolean;
}

export interface AgentInteraction extends BaseEntity {
  user_id: string;
  agent_type: string;
  interaction_type: string;
  input: string;
  output: string;
  context: Record<string, any>;
  tokens_used: number;
  processing_time: number;
  satisfaction_rating?: number;
  feedback?: string;
  session_id?: string;
}

export interface AgentMemory extends BaseEntity {
  user_id: string;
  agent_type: string;
  memory_type: 'conversation' | 'preference' | 'context' | 'fact';
  key: string;
  value: any;
  importance: number;
  decay_rate: number;
  last_accessed: Date;
  access_count: number;
  expires_at?: Date;
  tags: string[];
  metadata: Record<string, any>;
}

// Panel system entity types
export interface PanelDeployment extends BaseEntity {
  guild_id: string;
  channel_id: string;
  panel_type: string;
  message_id: string;
  config: Record<string, any>;
  is_active: boolean;
  last_interaction: Date;
  interaction_count: number;
  deployed_by: string;
  auto_update: boolean;
  version: string;
}

export interface PanelAnalytics extends BaseEntity {
  panel_deployment_id: string;
  event_type: string;
  user_id: string;
  interaction_data: Record<string, any>;
  response_time: number;
  success: boolean;
  error_message?: string;
  metadata: Record<string, any>;
}

export interface UserPanelState extends BaseEntity {
  user_id: string;
  guild_id: string;
  panel_type: string;
  state: Record<string, any>;
  step: number;
  progress: number;
  last_interaction: Date;
  is_complete: boolean;
  expires_at?: Date;
}

// Content management entity types
export interface DynamicContent extends BaseEntity {
  type: string;
  identifier: string;
  guild_id?: string;
  title: string;
  content: string;
  metadata: Record<string, any>;
  version: number;
  is_active: boolean;
  created_by: string;
  updated_by: string;
  expires_at?: Date;
  tags: string[];
}

export interface Announcement extends BaseEntity {
  guild_id: string;
  title: string;
  content: string;
  type: 'general' | 'maintenance' | 'feature' | 'emergency';
  priority: number;
  target_roles: string[];
  target_channels: string[];
  scheduled_for?: Date;
  published_at?: Date;
  expires_at?: Date;
  is_published: boolean;
  is_pinned: boolean;
  author_id: string;
  metadata: Record<string, any>;
}

// Community feature entity types
export interface CommunityEvent extends BaseEntity {
  guild_id: string;
  name: string;
  description: string;
  type: string;
  start_time: Date;
  end_time: Date;
  location?: string;
  channel_id?: string;
  max_participants?: number;
  current_participants: number;
  organizer_id: string;
  tags: string[];
  requirements: string[];
  rewards: Record<string, any>;
  status: 'draft' | 'published' | 'active' | 'completed' | 'cancelled';
  metadata: Record<string, any>;
}

export interface CreativeShowcase extends BaseEntity {
  user_id: string;
  guild_id: string;
  title: string;
  description: string;
  category: string;
  content_type: 'image' | 'video' | 'audio' | 'text' | 'link';
  content_url: string;
  thumbnail_url?: string;
  tags: string[];
  likes: number;
  views: number;
  comments: number;
  is_featured: boolean;
  is_approved: boolean;
  approved_by?: string;
  approved_at?: Date;
  metadata: Record<string, any>;
}

export interface EducationalResource extends BaseEntity {
  title: string;
  description: string;
  category: string;
  difficulty_level: 'beginner' | 'intermediate' | 'advanced';
  content_type: 'article' | 'video' | 'tutorial' | 'course' | 'book';
  content_url?: string;
  author: string;
  tags: string[];
  rating: number;
  rating_count: number;
  completion_time?: number;
  prerequisites: string[];
  learning_objectives: string[];
  is_free: boolean;
  price?: number;
  language: string;
  last_updated: Date;
  metadata: Record<string, any>;
}

// Support system entity types
export interface SupportTicket extends BaseEntity {
  user_id: string;
  guild_id: string;
  channel_id?: string;
  title: string;
  description: string;
  category: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'open' | 'in_progress' | 'waiting' | 'resolved' | 'closed';
  assigned_to?: string;
  assigned_at?: Date;
  resolved_at?: Date;
  resolution?: string;
  satisfaction_rating?: number;
  feedback?: string;
  tags: string[];
  metadata: Record<string, any>;
}

// Specialized feature entity types
export interface TradingMarket extends BaseEntity {
  guild_id: string;
  name: string;
  description: string;
  category: string;
  symbol: string;
  current_price: number;
  change_24h: number;
  volume_24h: number;
  market_cap?: number;
  supply?: number;
  is_active: boolean;
  data_source: string;
  last_updated: Date;
  metadata: Record<string, any>;
}

export interface WealthCreation extends BaseEntity {
  user_id: string;
  guild_id: string;
  strategy_name: string;
  description: string;
  category: string;
  risk_level: 'low' | 'medium' | 'high';
  expected_return: number;
  time_horizon: string;
  minimum_investment: number;
  resources: string[];
  milestones: Array<{
    name: string;
    target: number;
    achieved: boolean;
    date?: Date;
  }>;
  progress: number;
  status: 'planning' | 'active' | 'paused' | 'completed';
  created_by: string;
  metadata: Record<string, any>;
}

export interface AiMastery extends BaseEntity {
  user_id: string;
  guild_id: string;
  skill_name: string;
  category: string;
  current_level: number;
  experience_points: number;
  total_practice_time: number;
  completed_exercises: number;
  achievements: string[];
  learning_path: Array<{
    step: number;
    name: string;
    completed: boolean;
    score?: number;
    date?: Date;
  }>;
  strengths: string[];
  areas_for_improvement: string[];
  next_milestone: string;
  mentor_feedback?: string;
  metadata: Record<string, any>;
}

export interface NetworkingBusiness extends BaseEntity {
  user_id: string;
  guild_id: string;
  business_name: string;
  description: string;
  industry: string;
  stage: 'idea' | 'startup' | 'growth' | 'established';
  team_size: number;
  location?: string;
  website?: string;
  social_links: Record<string, string>;
  skills_offered: string[];
  skills_needed: string[];
  collaboration_interests: string[];
  achievements: string[];
  goals: string[];
  is_seeking_partners: boolean;
  is_offering_mentorship: boolean;
  contact_preferences: Record<string, any>;
  metadata: Record<string, any>;
}

export interface GamingEntertainment extends BaseEntity {
  guild_id: string;
  event_name: string;
  description: string;
  game_type: string;
  platform: string;
  max_participants?: number;
  current_participants: number;
  scheduled_time: Date;
  duration?: number;
  entry_fee?: number;
  prize_pool?: number;
  rules: string[];
  requirements: string[];
  organizer_id: string;
  participants: Array<{
    user_id: string;
    joined_at: Date;
    score?: number;
    rank?: number;
  }>;
  status: 'upcoming' | 'active' | 'completed' | 'cancelled';
  results?: Record<string, any>;
  metadata: Record<string, any>;
}

// User relationship and social features
export interface UserRelationship extends BaseEntity {
  user_id: string;
  target_user_id: string;
  relationship_type: 'friend' | 'blocked' | 'mentor' | 'mentee' | 'partner';
  status: 'pending' | 'accepted' | 'rejected' | 'ended';
  initiated_by: string;
  accepted_at?: Date;
  ended_at?: Date;
  end_reason?: string;
  metadata: Record<string, any>;
}

export interface UserApiKey extends BaseEntity {
  user_id: string;
  name: string;
  key_hash: string;
  permissions: string[];
  expires_at?: Date;
  last_used?: Date;
  usage_count: number;
  is_active: boolean;
  created_for: string;
  metadata: Record<string, any>;
}

// Personal development entity types
export interface PersonalGrowthPlan extends BaseEntity {
  user_id: string;
  title: string;
  description: string;
  category: string;
  goals: Array<{
    id: string;
    title: string;
    description: string;
    target_date: Date;
    is_completed: boolean;
    completed_at?: Date;
    progress: number;
    milestones: Array<{
      name: string;
      completed: boolean;
      date?: Date;
    }>;
  }>;
  habits: Array<{
    id: string;
    name: string;
    frequency: string;
    streak: number;
    longest_streak: number;
    last_completed?: Date;
    is_active: boolean;
  }>;
  reflections: Array<{
    date: Date;
    content: string;
    mood: number;
    lessons_learned: string[];
  }>;
  progress_metrics: Record<string, any>;
  coach_notes?: string;
  next_review_date: Date;
  is_active: boolean;
  metadata: Record<string, any>;
}

// Session management
export interface Session extends BaseEntity {
  session_id: string;
  user_id: string;
  type: 'web' | 'api' | 'bot';
  ip_address?: string;
  user_agent?: string;
  expires_at: Date;
  data: Record<string, any>;
  last_activity: Date;
  is_active: boolean;
}

// Multi-tenancy support
export interface Organization extends BaseEntity {
  name: string;
  slug: string;
  description?: string;
  logo?: string;
  website?: string;
  settings: Record<string, any>;
  features: string[];
  limits: Record<string, number>;
  billing_email: string;
  subscription_tier: string;
  subscription_expires: Date;
  is_active: boolean;
  created_by: string;
}

// System management
export interface PanelCleanup extends BaseEntity {
  guild_id: string;
  channel_id: string;
  cleanup_type: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  items_processed: number;
  items_removed: number;
  started_at?: Date;
  completed_at?: Date;
  error_message?: string;
  metadata: Record<string, any>;
}

// Repository interfaces for each entity
export interface UserRepository extends BaseRepository<UserEntity> {
  findByDiscordId(discordId: string): Promise<UserEntity | null>;
  findByApiKey(apiKey: string): Promise<UserEntity | null>;
  updateExperience(userId: string, amount: number): Promise<UserEntity>;
  updateCredits(userId: string, amount: number): Promise<UserEntity>;
  getActiveUsers(limit?: number): Promise<UserEntity[]>;
}

export interface GuildRepository extends BaseRepository<GuildEntity> {
  findByDiscordId(discordId: string): Promise<GuildEntity | null>;
  updateSettings(guildId: string, settings: Partial<GuildSettings>): Promise<GuildEntity>;
  getPremiumGuilds(): Promise<GuildEntity[]>;
  getActiveGuilds(limit?: number): Promise<GuildEntity[]>;
}

export interface AgentRepository extends BaseRepository<AgentInteraction> {
  findByUserId(userId: string, limit?: number): Promise<AgentInteraction[]>;
  findByAgentType(agentType: string, limit?: number): Promise<AgentInteraction[]>;
  getAverageRating(agentType: string): Promise<number>;
  getUsageStats(timeframe?: string): Promise<Record<string, any>>;
}

export interface SessionRepository extends BaseRepository<Session> {
  findBySessionId(sessionId: string): Promise<Session | null>;
  deleteExpired(): Promise<number>;
  getUserSessions(userId: string): Promise<Session[]>;
  invalidateUserSessions(userId: string): Promise<void>;
}

// Query builder interface for complex queries
export interface QueryBuilder<T> {
  select(fields: (keyof T)[]): QueryBuilder<T>;
  where(field: keyof T, operator: '=' | '!=' | '>' | '<' | '>=' | '<=' | 'IN' | 'NOT IN' | 'LIKE', value: any): QueryBuilder<T>;
  whereIn(field: keyof T, values: any[]): QueryBuilder<T>;
  whereNotNull(field: keyof T): QueryBuilder<T>;
  whereNull(field: keyof T): QueryBuilder<T>;
  orderBy(field: keyof T, direction?: 'ASC' | 'DESC'): QueryBuilder<T>;
  limit(count: number): QueryBuilder<T>;
  offset(count: number): QueryBuilder<T>;
  join<U>(table: string, localField: keyof T, foreignField: keyof U): QueryBuilder<T>;
  leftJoin<U>(table: string, localField: keyof T, foreignField: keyof U): QueryBuilder<T>;
  groupBy(field: keyof T): QueryBuilder<T>;
  having(field: string, operator: string, value: any): QueryBuilder<T>;
  execute(): Promise<T[]>;
  first(): Promise<T | null>;
  count(): Promise<number>;
}

// Database connection and transaction types
export interface DatabaseConnection {
  query<T>(sql: string, params?: any[]): Promise<T[]>;
  execute(sql: string, params?: any[]): Promise<{ affectedRows: number; insertId?: number }>;
  transaction<T>(callback: (connection: DatabaseConnection) => Promise<T>): Promise<T>;
  close(): Promise<void>;
}

export interface TransactionContext {
  connection: DatabaseConnection;
  rollback(): Promise<void>;
  commit(): Promise<void>;
}

// Migration types
export interface Migration {
  version: string;
  name: string;
  up(connection: DatabaseConnection): Promise<void>;
  down(connection: DatabaseConnection): Promise<void>;
}

export interface MigrationRunner {
  run(): Promise<void>;
  rollback(steps?: number): Promise<void>;
  status(): Promise<Array<{ version: string; name: string; executed: boolean }>>;
}