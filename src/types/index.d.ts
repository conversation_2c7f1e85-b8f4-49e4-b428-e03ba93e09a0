/**
 * Main type definitions index file
 * This file exports all custom type definitions for the Discord bot application
 */

// Import and re-export global types
import './global';
import './discord-extensions';
import './database';
import './api';

// Additional common type utilities
export type Awaited<T> = T extends PromiseLike<infer U> ? U : T;
export type NonNullable<T> = T extends null | undefined ? never : T;
export type Partial<T> = {
  [P in keyof T]?: T[P];
};
export type Required<T> = {
  [P in keyof T]-?: T[P];
};
export type Readonly<T> = {
  readonly [P in keyof T]: T[P];
};
export type Pick<T, K extends keyof T> = {
  [P in K]: T[P];
};
export type Omit<T, K extends keyof T> = Pick<T, Exclude<keyof T, K>>;

// Common utility types for the application
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type DeepRequired<T> = {
  [P in keyof T]-?: T[P] extends object ? DeepRequired<T[P]> : T[P];
};

export type NestedKeyOf<ObjectType extends object> = {
  [Key in keyof ObjectType & (string | number)]: ObjectType[Key] extends object
    ? `${Key}` | `${Key}.${NestedKeyOf<ObjectType[Key]>}`
    : `${Key}`;
}[keyof ObjectType & (string | number)];

// Brand types for better type safety
export type Brand<T, TBrand> = T & { __brand: TBrand };

export type UserId = Brand<string, 'UserId'>;
export type GuildId = Brand<string, 'GuildId'>;
export type ChannelId = Brand<string, 'ChannelId'>;
export type MessageId = Brand<string, 'MessageId'>;
export type RoleId = Brand<string, 'RoleId'>;

// Function utility types
export type AsyncFunction<T extends any[] = any[], R = any> = (...args: T) => Promise<R>;
export type SyncFunction<T extends any[] = any[], R = any> = (...args: T) => R;
export type AnyFunction<T extends any[] = any[], R = any> = AsyncFunction<T, R> | SyncFunction<T, R>;

// Event emitter types
export type EventMap = Record<string, any>;
export type EventKey<T extends EventMap> = string & keyof T;
export type EventReceiver<T> = (params: T) => void;

// Database transaction types
export type TransactionCallback<T> = () => Promise<T>;
export type TransactionOptions = {
  timeout?: number;
  isolationLevel?: 'READ_UNCOMMITTED' | 'READ_COMMITTED' | 'REPEATABLE_READ' | 'SERIALIZABLE';
};

// HTTP method types
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'HEAD' | 'OPTIONS';

// Discord interaction types
export type InteractionType = 'slash-command' | 'button' | 'select-menu' | 'modal' | 'context-menu';

// Configuration types
export type Environment = 'development' | 'staging' | 'production' | 'test';

// Logging levels
export type LogLevel = 'error' | 'warn' | 'info' | 'debug' | 'verbose';

// Permission types
export type Permission = string;
export type PermissionLevel = 'user' | 'moderator' | 'admin' | 'owner' | 'developer';

// AI model types
export type AIModel = 'gpt-3.5-turbo' | 'gpt-4' | 'gpt-4-turbo' | 'claude-3-haiku' | 'claude-3-sonnet' | 'claude-3-opus';

// Panel component types
export type PanelComponentType = 'button' | 'select' | 'modal' | 'embed' | 'text-input' | 'number-input';

// Status types
export type Status = 'active' | 'inactive' | 'pending' | 'completed' | 'failed' | 'cancelled';

// Priority types
export type Priority = 'low' | 'normal' | 'high' | 'urgent' | 'critical';

// Notification types
export type NotificationChannel = 'discord' | 'email' | 'push' | 'sms' | 'webhook';

// File types
export type FileType = 'image' | 'video' | 'audio' | 'document' | 'archive' | 'code' | 'other';

// Currency types
export type Currency = 'USD' | 'EUR' | 'GBP' | 'JPY' | 'CAD' | 'AUD' | 'CHF' | 'CNY' | 'INR' | 'BTC' | 'ETH';

// Time zone types
export type TimeZone = string; // e.g., 'America/New_York', 'Europe/London', 'Asia/Tokyo'

// Language types
export type Language = 'en' | 'es' | 'fr' | 'de' | 'it' | 'pt' | 'ru' | 'ja' | 'ko' | 'zh' | 'ar' | 'hi';

// Color types
export type Color = `#${string}` | `rgb(${number}, ${number}, ${number})` | `rgba(${number}, ${number}, ${number}, ${number})`;

// URL types
export type URL = `http://${string}` | `https://${string}`;

// Email types
export type Email = `${string}@${string}`;

// Phone number types
export type PhoneNumber = `+${string}`;

// UUID types
export type UUID = `${string}-${string}-${string}-${string}-${string}`;

// Timestamp types
export type Timestamp = number; // Unix timestamp
export type ISO8601 = string; // ISO 8601 date string

// JSON types
export type JSONPrimitive = string | number | boolean | null;
export type JSONValue = JSONPrimitive | JSONObject | JSONArray;
export type JSONObject = { [key: string]: JSONValue };
export type JSONArray = JSONValue[];

// Generic result types
export type Result<T, E = Error> = 
  | { success: true; data: T }
  | { success: false; error: E };

export type Maybe<T> = T | null | undefined;

// Paginated data types
export type PaginatedData<T> = {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrevious: boolean;
  };
};

// Cache key types
export type CacheKey = string;
export type CacheTTL = number; // seconds

// Feature flag types
export type FeatureFlag = {
  name: string;
  enabled: boolean;
  conditions?: Record<string, any>;
  rolloutPercentage?: number;
};

// A/B test types
export type ABTestVariant = 'A' | 'B' | 'control';

// Analytics event types
export type AnalyticsEvent = {
  name: string;
  properties?: Record<string, JSONValue>;
  userId?: UserId;
  sessionId?: string;
  timestamp: Date;
};

// Webhook types
export type WebhookEvent = string;
export type WebhookPayload = Record<string, JSONValue>;

// Subscription types
export type SubscriptionTier = 'free' | 'premium' | 'enterprise';
export type SubscriptionStatus = 'active' | 'cancelled' | 'past_due' | 'unpaid' | 'trialing';

// Rate limit types
export type RateLimitStrategy = 'fixed-window' | 'sliding-window' | 'token-bucket' | 'leaky-bucket';

// Security types
export type SecurityLevel = 'low' | 'medium' | 'high' | 'critical';
export type ThreatLevel = 'none' | 'low' | 'medium' | 'high' | 'critical';

// Content moderation types
export type ModerationAction = 'warn' | 'mute' | 'kick' | 'ban' | 'delete';
export type ModerationReason = string;

// Audit log types
export type AuditAction = 'create' | 'read' | 'update' | 'delete' | 'execute' | 'access' | 'login' | 'logout';

// Health check types
export type HealthStatus = 'healthy' | 'degraded' | 'unhealthy' | 'unknown';

// Performance metric types
export type PerformanceMetric = {
  name: string;
  value: number;
  unit: string;
  timestamp: Date;
  tags?: Record<string, string>;
};

// Error types
export type ErrorCode = string;
export type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical';

// Configuration validation types
export type ConfigSchema = Record<string, {
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  required?: boolean;
  default?: any;
  validate?: (value: any) => boolean | string;
}>;

// Task queue types
export type TaskPriority = 'low' | 'normal' | 'high' | 'critical';
export type TaskStatus = 'pending' | 'running' | 'completed' | 'failed' | 'retrying';

// Backup types
export type BackupType = 'full' | 'incremental' | 'differential';
export type BackupStatus = 'pending' | 'running' | 'completed' | 'failed';

// Deployment types
export type DeploymentEnvironment = 'development' | 'staging' | 'production';
export type DeploymentStatus = 'pending' | 'deploying' | 'deployed' | 'failed' | 'rolled_back';

export {};