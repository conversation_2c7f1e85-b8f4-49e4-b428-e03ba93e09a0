/**
 * Discord.js type extensions and augmentations
 * This file extends Discord.js types with custom properties and methods
 */

import { 
  Client, 
  Guild, 
  User, 
  GuildMember, 
  TextChannel, 
  VoiceChannel, 
  CategoryChannel,
  CommandInteraction,
  ButtonInteraction,
  SelectMenuInteraction,
  ModalSubmitInteraction,
  Message,
  Embed,
  ActionRowBuilder,
  ButtonBuilder,
  StringSelectMenuBuilder
} from 'discord.js';

declare module 'discord.js' {
  interface Client {
    // Custom properties
    isReady: boolean;
    startTime: Date;
    commandsLoaded: boolean;
    
    // Custom methods
    getGuildById(guildId: string): Guild | undefined;
    getUserById(userId: string): User | undefined;
    getChannelById(channelId: string): TextChannel | VoiceChannel | CategoryChannel | undefined;
    
    // Statistics
    getStats(): {
      guilds: number;
      users: number;
      channels: number;
      uptime: number;
    };
  }

  interface Guild {
    // Custom properties for bot functionality
    botSettings?: {
      prefix: string;
      aiEnabled: boolean;
      moderationEnabled: boolean;
      welcomeChannelId?: string;
      logChannelId?: string;
      adminRoleIds: string[];
      moderatorRoleIds: string[];
    };
    
    // Premium features
    premiumFeatures?: {
      tier: number;
      features: string[];
      expiresAt?: Date;
    };
    
    // Custom methods
    hasPermission(userId: string, permission: string): boolean;
    getConfigValue(key: string): any;
    setConfigValue(key: string, value: any): Promise<void>;
  }

  interface User {
    // Custom properties
    botData?: {
      level: number;
      experience: number;
      credits: number;
      lastSeen: Date;
      preferences: Record<string, any>;
    };
    
    // Premium status
    isPremium?: boolean;
    premiumSince?: Date;
    
    // Custom methods
    addExperience(amount: number): Promise<void>;
    addCredits(amount: number): Promise<void>;
    getPreference(key: string): any;
    setPreference(key: string, value: any): Promise<void>;
  }

  interface GuildMember {
    // Custom properties
    joinedBot?: Date;
    totalMessages?: number;
    warnings?: number;
    
    // Methods
    hasRole(roleId: string): boolean;
    addWarning(reason: string): Promise<void>;
    clearWarnings(): Promise<void>;
  }

  interface TextChannel {
    // AI Channel properties
    aiEnabled?: boolean;
    aiModel?: string;
    aiPersonality?: string;
    systemPrompt?: string;
    
    // Panel properties
    panelType?: string;
    panelConfig?: Record<string, any>;
    lastPanelUpdate?: Date;
    
    // Custom methods
    sendAIResponse(prompt: string, context?: any): Promise<Message>;
    deployPanel(panelType: string, config: any): Promise<void>;
    updatePanel(config: any): Promise<void>;
  }

  interface CommandInteraction {
    // Custom properties for context
    guildData?: any;
    userData?: any;
    permissions?: string[];
    
    // Utility methods
    replyEphemeral(content: string): Promise<void>;
    replyWithEmbed(embed: Embed): Promise<void>;
    hasPermission(permission: string): boolean;
  }

  interface ButtonInteraction {
    // Panel context
    panelType?: string;
    panelData?: any;
    
    // Custom methods
    updatePanel(newData: any): Promise<void>;
    closePanel(): Promise<void>;
  }

  interface SelectMenuInteraction {
    // Panel context
    panelType?: string;
    panelData?: any;
    
    // Custom methods
    updatePanelFromSelection(values: string[]): Promise<void>;
  }

  interface ModalSubmitInteraction {
    // Panel context
    panelType?: string;
    panelData?: any;
    
    // Custom methods
    processPanelSubmission(formData: Record<string, string>): Promise<void>;
  }

  interface Message {
    // AI context
    isAIResponse?: boolean;
    aiModel?: string;
    processingTime?: number;
    tokens?: {
      prompt: number;
      completion: number;
      total: number;
    };
    
    // Panel context
    isPanelMessage?: boolean;
    panelType?: string;
    panelId?: string;
    
    // Custom methods
    addAIContext(model: string, tokens: any, processingTime: number): void;
    markAsPanelMessage(panelType: string, panelId: string): void;
  }
}

// Extended interaction types for better type safety
export interface ExtendedCommandInteraction extends CommandInteraction {
  guildData: any;
  userData: any;
  permissions: string[];
  
  replyEphemeral(content: string): Promise<void>;
  replyWithEmbed(embed: Embed): Promise<void>;
  hasPermission(permission: string): boolean;
}

export interface ExtendedButtonInteraction extends ButtonInteraction {
  panelType: string;
  panelData: any;
  
  updatePanel(newData: any): Promise<void>;
  closePanel(): Promise<void>;
}

export interface ExtendedSelectMenuInteraction extends SelectMenuInteraction {
  panelType: string;
  panelData: any;
  
  updatePanelFromSelection(values: string[]): Promise<void>;
}

export interface ExtendedModalSubmitInteraction extends ModalSubmitInteraction {
  panelType: string;
  panelData: any;
  
  processPanelSubmission(formData: Record<string, string>): Promise<void>;
}

// Component builder types
export interface CustomActionRow extends ActionRowBuilder {
  addCustomButton(button: ButtonBuilder): this;
  addCustomSelectMenu(selectMenu: StringSelectMenuBuilder): this;
}

// AI Integration types
export interface AIChannelConfig {
  channelId: string;
  guildId: string;
  enabled: boolean;
  model: string;
  systemPrompt: string;
  personality: string;
  maxTokens: number;
  temperature: number;
  topP: number;
  frequencyPenalty: number;
  presencePenalty: number;
  stopSequences: string[];
  userInstructions?: string;
  contextLength: number;
  rateLimitPerUser: number;
  rateLimitGlobal: number;
  allowedUsers: string[];
  allowedRoles: string[];
  blockedUsers: string[];
  blockedWords: string[];
  moderationEnabled: boolean;
  loggingEnabled: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Panel system types
export interface PanelInteractionContext {
  interaction: CommandInteraction | ButtonInteraction | SelectMenuInteraction | ModalSubmitInteraction;
  panelType: string;
  panelId: string;
  userId: string;
  guildId: string;
  channelId: string;
  data: Record<string, any>;
  permissions: string[];
  timestamp: Date;
}

export interface PanelComponent {
  type: 'button' | 'select' | 'modal' | 'embed';
  id: string;
  label?: string;
  style?: any;
  options?: any[];
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  emoji?: string;
  url?: string;
  customId: string;
}

export interface PanelDefinition {
  type: string;
  title: string;
  description?: string;
  color?: number;
  thumbnail?: string;
  image?: string;
  author?: {
    name: string;
    iconURL?: string;
  };
  footer?: {
    text: string;
    iconURL?: string;
  };
  fields?: Array<{
    name: string;
    value: string;
    inline?: boolean;
  }>;
  components: PanelComponent[];
  permissions?: string[];
  cooldown?: number;
  ephemeral?: boolean;
  persistent?: boolean;
  autoDelete?: number;
  metadata?: Record<string, any>;
}

// Database entity base types
export interface BaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
}

export interface TimestampedEntity {
  created_at: Date;
  updated_at: Date;
}

// Redis key patterns
export type RedisKeyPattern = 
  | `user:${string}`
  | `guild:${string}`
  | `channel:${string}`
  | `session:${string}`
  | `cache:${string}`
  | `lock:${string}`
  | `queue:${string}`;

// Event system types
export interface BotEvent {
  name: string;
  type: 'discord' | 'system' | 'user';
  data: any;
  timestamp: Date;
  source: string;
  handled: boolean;
}

export interface EventHandler<T = any> {
  handle(event: BotEvent): Promise<void>;
  canHandle(event: BotEvent): boolean;
  priority: number;
}

// Monitoring and metrics types
export interface BotMetrics {
  commands: {
    total: number;
    successful: number;
    failed: number;
    averageResponseTime: number;
  };
  messages: {
    total: number;
    aiResponses: number;
    averageLength: number;
  };
  users: {
    active: number;
    new: number;
    returning: number;
  };
  guilds: {
    total: number;
    active: number;
  };
  system: {
    uptime: number;
    memory: {
      used: number;
      total: number;
    };
    cpu: number;
  };
}

// Agent system types
export interface AgentCapability {
  name: string;
  description: string;
  enabled: boolean;
  config: Record<string, any>;
}

export interface AgentPersona {
  name: string;
  description: string;
  personality: string;
  expertise: string[];
  tone: string;
  responseStyle: string;
  capabilities: AgentCapability[];
}

export interface AgentSession {
  id: string;
  userId: string;
  agentType: string;
  persona: string;
  context: Record<string, any>;
  history: Array<{
    role: 'user' | 'assistant';
    content: string;
    timestamp: Date;
  }>;
  startedAt: Date;
  lastActivity: Date;
  isActive: boolean;
}

// Export all types for easier imports
export * from 'discord.js';