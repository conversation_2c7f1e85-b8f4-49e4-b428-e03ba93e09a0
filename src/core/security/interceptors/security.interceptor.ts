import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontex<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  BadRequestException,
  UnauthorizedException,
  ForbiddenException,
  TooManyRequestsException,
} from '@nestjs/common';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { Request, Response } from 'express';
import {
  EnhancedRequest,
  SecurityContext,
  SecurityError,
  SECURITY_CONSTANTS,
} from '../../../api/auth/types/security.types';
import { AuthService } from '../../../api/auth/auth.service';
import { EncryptionService } from '../encryption.service';

@Injectable()
export class SecurityInterceptor implements NestInterceptor {
  private readonly logger = new Logger(SecurityInterceptor.name);

  constructor(
    private readonly authService: AuthService,
    private readonly encryptionService: EncryptionService
  ) {}

  async intercept(
    context: ExecutionContext,
    next: <PERSON><PERSON><PERSON><PERSON>,
  ): Promise<Observable<any>> {
    const request = context.switchToHttp().getRequest<EnhancedRequest>();
    const response = context.switchToHttp().getResponse<Response>();
    
    const startTime = Date.now();
    const requestId = this.generateRequestId();
    
    try {
      // Add request ID for tracing
      request.headers['x-request-id'] = requestId;
      
      // Perform pre-request security checks
      await this.performPreRequestSecurity(request, response);
      
      // Set security headers
      this.setSecurityHeaders(response);
      
      // Continue with request processing
      return next.handle().pipe(
        tap(() => {
          // Log successful request
          this.logRequest(request, response, Date.now() - startTime, 'success');
        }),
        catchError((error) => {
          // Log failed request
          this.logRequest(request, response, Date.now() - startTime, 'error', error);
          
          // Handle security-related errors
          return throwError(() => this.handleSecurityError(error, request));
        })
      );
      
    } catch (error) {
      this.logger.error('Security interceptor error:', error);
      this.logRequest(request, response, Date.now() - startTime, 'error', error);
      
      return throwError(() => this.handleSecurityError(error, request));
    }
  }

  // ========================
  // Pre-Request Security Checks
  // ========================

  private async performPreRequestSecurity(
    request: EnhancedRequest,
    response: Response
  ): Promise<void> {
    // 1. Input validation and sanitization
    await this.validateAndSanitizeInput(request);
    
    // 2. Rate limiting checks
    await this.checkRateLimits(request);
    
    // 3. CSRF protection (for state-changing operations)
    await this.validateCSRF(request);
    
    // 4. Content type validation
    this.validateContentType(request);
    
    // 5. Request size validation
    this.validateRequestSize(request);
    
    // 6. IP-based security checks
    await this.performIPSecurityChecks(request);
    
    // 7. Device fingerprinting
    await this.generateDeviceFingerprint(request);
  }

  private async validateAndSanitizeInput(request: EnhancedRequest): Promise<void> {
    const dangerousPatterns = [
      /<script[^>]*>.*?<\/script>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /eval\s*\(/gi,
      /expression\s*\(/gi,
      /<iframe[^>]*>.*?<\/iframe>/gi,
      /<object[^>]*>.*?<\/object>/gi,
      /<embed[^>]*>/gi,
    ];

    const checkForDangerousContent = (obj: any, path = ''): void => {
      if (typeof obj === 'string') {
        for (const pattern of dangerousPatterns) {
          if (pattern.test(obj)) {
            this.logger.warn(`Dangerous content detected in ${path}: ${pattern}`);
            throw new BadRequestException(`Invalid input detected`);
          }
        }
      } else if (typeof obj === 'object' && obj !== null) {
        for (const [key, value] of Object.entries(obj)) {
          checkForDangerousContent(value, `${path}.${key}`);
        }
      }
    };

    // Check query parameters
    if (request.query) {
      checkForDangerousContent(request.query, 'query');
    }

    // Check request body
    if (request.body) {
      checkForDangerousContent(request.body, 'body');
    }

    // Check URL parameters
    if (request.params) {
      checkForDangerousContent(request.params, 'params');
    }
  }

  private async checkRateLimits(request: EnhancedRequest): Promise<void> {
    const identifier = this.getRateLimitIdentifier(request);
    const endpoint = `${request.method}:${request.route?.path || request.path}`;
    
    // Different rate limits for different endpoint types
    let limit = 100; // Default: 100 requests per minute
    let windowMs = 60 * 1000; // 1 minute
    
    if (endpoint.includes('/auth/')) {
      limit = SECURITY_CONSTANTS.RATE_LIMITS.AUTH_ATTEMPTS;
      windowMs = 15 * 60 * 1000; // 15 minutes for auth endpoints
    } else if (endpoint.includes('/api/')) {
      limit = SECURITY_CONSTANTS.RATE_LIMITS.API_CALLS;
      windowMs = 60 * 60 * 1000; // 1 hour for API calls
    }

    const rateLimit = await this.authService.checkRateLimit(
      identifier,
      endpoint,
      limit,
      windowMs
    );

    if (!rateLimit.allowed) {
      this.logger.warn(`Rate limit exceeded for ${identifier} on ${endpoint}`);
      throw new TooManyRequestsException({
        message: 'Rate limit exceeded',
        retryAfter: rateLimit.resetTime,
        remaining: rateLimit.remaining
      });
    }

    // Attach rate limit info to request
    request.rateLimitInfo = {
      limit,
      remaining: rateLimit.remaining,
      resetTime: rateLimit.resetTime
    };
  }

  private async validateCSRF(request: EnhancedRequest): Promise<void> {
    // Skip CSRF validation for safe methods
    if (['GET', 'HEAD', 'OPTIONS'].includes(request.method)) {
      return;
    }

    // Skip for API token authentication
    const authHeader = request.get('Authorization');
    if (authHeader?.startsWith('Bearer ')) {
      return; // API tokens don't require CSRF protection
    }

    const csrfToken = request.get('X-CSRF-Token') || request.body?.csrfToken;
    const sessionId = request.session?.sessionId;

    if (!csrfToken) {
      this.logger.warn('CSRF token missing for state-changing operation');
      throw new ForbiddenException('CSRF token required');
    }

    const isValidCSRF = await this.authService.validateCsrfToken(csrfToken, sessionId);
    
    if (!isValidCSRF) {
      this.logger.warn(`Invalid CSRF token for session: ${sessionId}`);
      throw new ForbiddenException('Invalid CSRF token');
    }
  }

  private validateContentType(request: EnhancedRequest): void {
    // Skip for GET requests
    if (request.method === 'GET') {
      return;
    }

    const contentType = request.get('Content-Type');
    const allowedTypes = [
      'application/json',
      'application/x-www-form-urlencoded',
      'multipart/form-data',
      'text/plain'
    ];

    if (contentType && !allowedTypes.some(type => contentType.includes(type))) {
      this.logger.warn(`Invalid content type: ${contentType}`);
      throw new BadRequestException('Invalid content type');
    }
  }

  private validateRequestSize(request: EnhancedRequest): void {
    const contentLength = request.get('Content-Length');
    
    if (contentLength) {
      const size = parseInt(contentLength, 10);
      const maxSize = 10 * 1024 * 1024; // 10MB
      
      if (size > maxSize) {
        this.logger.warn(`Request too large: ${size} bytes`);
        throw new BadRequestException('Request entity too large');
      }
    }
  }

  private async performIPSecurityChecks(request: EnhancedRequest): Promise<void> {
    const clientIP = request.ip || request.connection.remoteAddress;
    
    if (!clientIP) {
      this.logger.warn('Unable to determine client IP address');
      return;
    }

    // Check for known malicious IPs (placeholder - implement actual blacklist)
    const blacklistedIPs = ['*********']; // Placeholder
    
    if (blacklistedIPs.includes(clientIP)) {
      this.logger.warn(`Blocked request from blacklisted IP: ${clientIP}`);
      throw new ForbiddenException('Access denied');
    }

    // Check for rapid requests from same IP
    const recentRequestCount = await this.getRecentRequestCount(clientIP);
    if (recentRequestCount > 1000) { // 1000 requests in last minute
      this.logger.warn(`Potential DDoS from IP: ${clientIP}`);
      throw new TooManyRequestsException('Too many requests from this IP');
    }
  }

  private async generateDeviceFingerprint(request: EnhancedRequest): Promise<void> {
    const userAgent = request.get('User-Agent') || '';
    const acceptLanguage = request.get('Accept-Language') || '';
    const acceptEncoding = request.get('Accept-Encoding') || '';
    const clientIP = request.ip || '';

    const fingerprint = this.encryptionService.generateDeviceFingerprint(
      userAgent + acceptLanguage + acceptEncoding,
      clientIP
    );

    request.deviceFingerprint = fingerprint;
  }

  // ========================
  // Response Security Headers
  // ========================

  private setSecurityHeaders(response: Response): void {
    const headers = SECURITY_CONSTANTS.HEADERS.SECURITY;

    // Set all security headers
    Object.entries(headers).forEach(([header, value]) => {
      response.setHeader(header, value);
    });

    // Content Security Policy
    response.setHeader(
      'Content-Security-Policy',
      "default-src 'self'; " +
      "script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://unpkg.com; " +
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; " +
      "font-src 'self' https://fonts.gstatic.com; " +
      "img-src 'self' data: https:; " +
      "connect-src 'self' https://discord.com https://api.github.com; " +
      "frame-ancestors 'none';"
    );

    // Additional security headers
    response.setHeader('X-Request-ID', response.getHeader('x-request-id') || 'unknown');
    response.setHeader('X-Response-Time', Date.now().toString());
  }

  // ========================
  // Error Handling
  // ========================

  private handleSecurityError(error: any, request: EnhancedRequest): Error {
    // Log security violation
    this.authService.logSecurityEvent(
      'SECURITY_VIOLATION',
      'error',
      {
        error: error.message,
        endpoint: request.path,
        method: request.method,
        ip: request.ip,
        userAgent: request.get('User-Agent'),
        user: request.user?.username
      }
    );

    // Return appropriate error based on type
    if (error instanceof BadRequestException ||
        error instanceof UnauthorizedException ||
        error instanceof ForbiddenException ||
        error instanceof TooManyRequestsException) {
      return error;
    }

    // Generic security error
    return new ForbiddenException('Security validation failed');
  }

  // ========================
  // Utility Methods
  // ========================

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getRateLimitIdentifier(request: EnhancedRequest): string {
    // Prefer user ID if authenticated, otherwise use IP
    if (request.user?.id) {
      return `user:${request.user.id}`;
    }
    
    return `ip:${request.ip || 'unknown'}`;
  }

  private async getRecentRequestCount(ip: string): Promise<number> {
    // TODO: Implement Redis-based request counting
    return 0; // Placeholder
  }

  private logRequest(
    request: EnhancedRequest,
    response: Response,
    duration: number,
    status: 'success' | 'error',
    error?: any
  ): void {
    const logData = {
      requestId: request.headers['x-request-id'],
      method: request.method,
      path: request.path,
      ip: request.ip,
      userAgent: request.get('User-Agent'),
      user: request.user?.username,
      duration: `${duration}ms`,
      status: response.statusCode || (error ? 'error' : 'unknown'),
      error: error?.message
    };

    if (status === 'error') {
      this.logger.error('Request failed', logData);
    } else {
      this.logger.debug('Request completed', logData);
    }
  }
}