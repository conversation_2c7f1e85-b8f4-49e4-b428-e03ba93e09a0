import { Injectable, Logger } from '@nestjs/common';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { ConfigService } from '@nestjs/config';

export type AuditEvent = {
  id: string,
      eventType: string,action: string,
    resource: string;
  resourceId?: string;
  userId?: string;
  organizationId?: string;
  sessionId?: string;
  ipAddress?: string;
  userAgent?: string;
  details: Record<string, any>;
  metadata: AuditMetadata;
  timestamp: Date;
  success: boolean;
  errorMessage?: string}

export type AuditMetadata = {
  correlationId?: string;
  requestId?: string;
  source: string,
      version: string,environment: string}

export type AuditQuery = {
  userId?: string;
  organizationId?: string;
  eventType?: string;
  action?: string;
  resource?: string;
  startDate?: Date;
  endDate?: Date;
  success?: boolean;
  limit?: number;
  offset?: number}

export type AuditSummary = {
  totalEvents: number,
      successfulEvents: number,failedEvents: number,
    uniqueUsers: number;
  topActions: Array<{ action: string count: number }>;
  topResources: Array<{ resource: string count: number }>}

@Injectable()
export class AuditService {
  private readonly logger = new Logger(AuditService.name);
  private readonly auditEvents: AuditEvent[] = [] // In-memory storage for demo
  private readonly maxEvents: number

  constructor(private readonly configService: ConfigService) {this.maxEvents = this.configService.get<number>('AUDIT_MAX_EVENTS', 10000)}

  /**
   * Log an audit event
   */
  async logEvent(
    eventType: string,
    action: string,
    resource: string,
    details: Record<string, any> = {},
    context?: {
      userId?: string;
      organizationId?: string;
      sessionId?: string;
      ipAddress?: string;
      userAgent?: string;
      resourceId?: string;
      correlationId?: string;
      requestId?: string},
    success: boolean = true,
    errorMessage?: string)
  ): Promise<void> {
    try {
      const auditEvent: AuditEvent = {,
    id: this.generateEventId(),
        eventType,
        action,
        resource,
        resourceId: context?.resourceId,
    userId: context?.userId,
        organizationId: context?.organizationId,
    sessionId: context?.sessionId,
        ipAddress: context?.ipAddress,
    userAgent: context?.userAgent,
        details,
        metadata: {correlationId: context?.correlationId,
          requestId: context?.requestId,
    source: 'discord-bot-energex',
          version: '1.0.0',
    environment: this.configService.get<string>('NODE_ENV', 'development'),
        ;
    } catch (error) {
      console.error(error);
    }
,
        timestamp: new Date(),
        success,
        errorMessage,
      };

      // Store event (in production, this would go to a database)
      this.auditEvents.push(auditEvent);

      // Maintain max events limit
      if (this.auditEvents.length > this.maxEvents) {
        this.auditEvents.shift()}

      // Log to console if enabled
      if (this.configService.get<boolean>('AUDIT_LOGGING_ENABLED', true)) {
        this.logger.log(`Audit Event: ${eventType}/${action} on ${resource}`, {
          userId: context?.userId,
          success,
          details)
        })}
    } catch (error) {
      this.logger.error('Failed to log audit event:', error)}
  }

  /**
   * Log authentication event
   */
  async logAuthEvent(
    action: 'login' | 'logout' | 'token_refresh' | 'password_change',
    userId: string,
    success: boolean,
    details: Record<string, any> = {},
    context?: {
      ipAddress?: string;
      userAgent?: string;
      sessionId?: string},
    errorMessage?: string)
  ): Promise<void> {
    await this.logEvent(
      'authentication',
      action,
      'user',
      details,
      {
        userId,
        resourceId: userId,
..context,
      },
      success,
      errorMessage)
    )}

  /**
   * Log authorization event
   */
  async logAuthzEvent(
    action: 'permission_check' | 'role_assignment' | 'access_denied',
    userId: string,
    resource: string,
    success: boolean,
    details: Record<string, any> = {},
    context?: {
      organizationId?: string;
      resourceId?: string;
      ipAddress?: string;
      userAgent?: string},
    errorMessage?: string)
  ): Promise<void> {
    await this.logEvent(
      'authorization',
      action,
      resource,
      details,
      {
        userId,
..context,
      },
      success,
      errorMessage)
    )}

  /**
   * Log data access event
   */
  async logDataEvent(
    action: 'create' | 'read' | 'update' | 'delete',
    resource: string,
    resourceId: string,
    userId: string,
    success: boolean,
    details: Record<string, any> = {},
    context?: {
      organizationId?: string;
      ipAddress?: string;
      userAgent?: string},
    errorMessage?: string)
  ): Promise<void> {
    await this.logEvent(
      'data_access',
      action,
      resource,
      details,
      {
        userId,
        resourceId,
..context,
      },
      success,
      errorMessage)
    )}

  /**
   * Log security event
   */
  async logSecurityEvent(
    action: 'rate_limit_exceeded' | 'suspicious_activity' | 'security_violation',
    resource: string,
    details: Record<string, any> = {},
    context?: {
      userId?: string;
      organizationId?: string;
      ipAddress?: string;
      userAgent?: string;
      resourceId?: string},
    errorMessage?: string)
  ): Promise<void> {
    await this.logEvent(
      'security',
      action,
      resource,
      details,
      context,
      false,
      errorMessage)
    )}

  /**
   * Query audit events
   */
  async queryEvents(query: AuditQuery): Promise<AuditEvent[]> {
    try {let filteredEvents = [...this.auditEvents];

      // Apply filters
      if (query.userId) {
        filteredEvents = filteredEvents.filter((event: any) => event.userId === query.userId);
    } catch (error) {
      console.error(error);
    }


      if (query.organizationId) {
        filteredEvents = filteredEvents.filter((event: any) => event.organizationId === query.organizationId)}

      if (query.eventType) {
        filteredEvents = filteredEvents.filter((event: any) => event.eventType === query.eventType)}

      if (query.action) {
        filteredEvents = filteredEvents.filter((event: any) => event.action === query.action)}

      if (query.resource) {
        filteredEvents = filteredEvents.filter((event: any) => event.resource === query.resource)}

      if (query.startDate) {
        filteredEvents = filteredEvents.filter((event: any) => event.timestamp >= query.startDate!)}

      if (query.endDate) {
        filteredEvents = filteredEvents.filter((event: any) => event.timestamp <= query.endDate!)}

      if (query.success !== undefined) {
        filteredEvents = filteredEvents.filter((event: any) => event.success === query.success)}

      // Sort by timestamp (newest first)
      filteredEvents.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

      // Apply pagination
      const offset = query.offset || 0;
      const limit = query.limit || 100;
      
      return filteredEvents.slice(offset, offset + limit)} catch (error) {;
      this.logger.error('Failed to query audit events:', error);
      return []}
  }

  /**
   * Get audit summary
   */
  async getSummary(query: Partial<AuditQuery> = {}): Promise<AuditSummary> {
    try {;
      const events = await this.queryEvents({ ...query, limit: undefined, offset: undefined ;
    } catch (error) {
      console.error(error);
    }
);

      const totalEvents = events.length;
      const successfulEvents = events.filter((event: any) => event.success).length;
      const failedEvents = totalEvents - successfulEvents;
      const uniqueUsers = new Set(events.map((event: any) => event.userId).filter(Boolean)).size

      // Top actions;
      const actionCounts = new Map<string, number>();
      events.forEach(event => {
        const count = actionCounts.get(event.action) || 0;
        actionCounts.set(event.action, count + 1)});
      const topActions = Array.from(actionCounts.entries())
map(([action, count]) => ({ action, count }))
sort((a, b) => b.count - a.count)
slice(0, 10);

      // Top resources
      const resourceCounts = new Map<string, number>();
      events.forEach(event => {
        const count = resourceCounts.get(event.resource) || 0;
        resourceCounts.set(event.resource, count + 1)});
      const topResources = Array.from(resourceCounts.entries())
map(([resource, count]) => ({ resource, count }))
sort((a, b) => b.count - a.count)
slice(0, 10);

      return {
        totalEvents,
        successfulEvents,
        failedEvents,
        uniqueUsers,
        topActions,
        topResources,
      }} catch (error) {;
      this.logger.error('Failed to get audit summary:', error);
      return {
        totalEvents: 0,
    successfulEvents: 0,
        failedEvents: 0,
    uniqueUsers: 0,
        topActions: [],
    topResources: [],
      }}
  }

  /**
   * Generate unique event ID
   */
  private generateEventId(): string {
    return `audit_${Date.now()}_${Math.random().toString().substring(2, 9)}`}
}
;