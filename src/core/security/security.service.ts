import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EncryptionService } from './encryption.service';
import { RbacService } from './rbac.service';
import { SessionService } from './session.service';
import { UserService } from './user.service';
import { AuditService } from './audit.service';
import { SecurityEventService } from './security-event.service';
import { ValidationService } from './validation.service';

// Core Security Types
export type SecurityLevel = 'low' | 'medium' | 'high' | 'critical';

export type SecurityContext = {
  userId?: string;
  organizationId?: string;
  roles: string[];
  permissions: string[];
  sessionId?: string;
  ipAddress?: string;
  userAgent?: string;
  timestamp: Date;
  securityLevel: SecurityLevel;
  deviceFingerprint?: string;
  geoLocation?: GeoLocation;
  riskScore: number;
};

export type GeoLocation = {
  country?: string;
  region?: string;
  city?: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
};

export type EncryptionConfig = {
  algorithm: EncryptionAlgorithm;
  keySize: number;
  mode: EncryptionMode;
  padding?: string;
  ivLength: number;
};

export type EncryptionAlgorithm = 'AES-256-GCM' | 'AES-256-CBC' | 'ChaCha20-Poly1305' | 'RSA-OAEP';
export type EncryptionMode = 'GCM' | 'CBC' | 'CTR' | 'ECB';
export type HashAlgorithm = 'SHA-256' | 'SHA-512' | 'PBKDF2' | 'bcrypt' | 'scrypt' | 'argon2';

export type EncryptionResult = {
  encryptedData: string;
  iv: string;
  authTag?: string;
  algorithm: EncryptionAlgorithm;
  keyId?: string;
};

export type DecryptionRequest = {
  encryptedData: string;
  iv: string;
  authTag?: string;
  algorithm: EncryptionAlgorithm;
  keyId?: string;
  context?: string;
};

export type AccessControlMatrix = {
  userId: string;
  organizationId?: string;
  resources: Map<string, ResourcePermissions>;
  globalPermissions: string[];
  restrictions: SecurityRestriction[];
  expiresAt?: Date;
};

export type ResourcePermissions = {
  resourceId: string;
  resourceType: string;
  permissions: string[];
  conditions?: AccessCondition[];
  inheritedFrom?: string;
};

export type AccessCondition = {
  type: 'ip_range' | 'time_window' | 'device_type' | 'location' | 'mfa_required';
  operator: 'equals' | 'not_equals' | 'in' | 'not_in' | 'between';
  value: any;
  description?: string;
};

export type SecurityRestriction = {
  type: 'rate_limit' | 'ip_block' | 'time_restriction' | 'mfa_required';
  parameters: Record<string, any>;
  expiresAt?: Date;
  reason?: string;
};

export type SecurityEvent = {
  id: string;
  type: SecurityEventType;
  severity: SecurityLevel;
  category: SecurityEventCategory;
  userId?: string;
  organizationId?: string;
  ipAddress?: string;
  userAgent?: string;
  deviceFingerprint?: string;
  geoLocation?: GeoLocation;
  details: Record<string, any>;
  metadata: SecurityEventMetadata;
  timestamp: Date;
  resolved: boolean;
  resolvedAt?: Date;
  resolvedBy?: string;
  responseActions: string[];
};

export type SecurityEventType = 
  | 'authentication_failure'
  | 'authorization_denied'
  | 'suspicious_login'
  | 'rate_limit_exceeded'
  | 'data_breach_attempt'
  | 'malicious_payload'
  | 'privilege_escalation'
  | 'account_lockout'
  | 'session_hijacking'
  | 'brute_force_attack'
  | 'sql_injection_attempt'
  | 'xss_attempt'
  | 'csrf_violation'
  | 'invalid_token'
  | 'encryption_failure'
  | 'audit_log_tampering'
  | 'configuration_change'
  | 'vulnerability_detected';

export type SecurityEventCategory = 
  | 'authentication'
  | 'authorization' 
  | 'data_protection'
  | 'network_security'
  | 'application_security'
  | 'infrastructure_security'
  | 'compliance'
  | 'incident_response';

export type SecurityEventMetadata = {
  correlationId?: string;
  requestId?: string;
  source: string;
  version: string;
  environment: string;
  riskScore: number;
  threat_indicators: string[];
  automated_response: boolean;
};

export type VulnerabilityAssessment = {
  id: string;
  scanId: string;
  target: string;
  targetType: 'application' | 'network' | 'infrastructure' | 'code';
  vulnerabilities: Vulnerability[];
  riskScore: number;
  scanDate: Date;
  scanDuration: number;
  scanner: string;
  scannerVersion: string;
  status: 'completed' | 'in_progress' | 'failed' | 'cancelled';
};

export type Vulnerability = {
  id: string;
  type: VulnerabilityType;
  severity: SecurityLevel;
  cvssScore?: number;
  cveId?: string;
  title: string;
  description: string;
  affectedComponent: string;
  location?: string;
  remediation: RemediationInfo;
  exploitable: boolean;
  falsePositive: boolean;
  riskAccepted: boolean;
  discoveryDate: Date;
  lastSeen: Date;
  status: VulnerabilityStatus;
};

export type VulnerabilityType = 
  | 'sql_injection'
  | 'xss'
  | 'csrf'
  | 'authentication_bypass'
  | 'authorization_flaw'
  | 'information_disclosure'
  | 'buffer_overflow'
  | 'insecure_deserialization'
  | 'xml_external_entities'
  | 'broken_access_control'
  | 'security_misconfiguration'
  | 'insecure_cryptographic_storage'
  | 'insufficient_transport_protection'
  | 'unvalidated_input'
  | 'race_condition'
  | 'path_traversal'
  | 'command_injection'
  | 'ldap_injection'
  | 'weak_authentication'
  | 'session_management';

export type VulnerabilityStatus = 
  | 'open'
  | 'confirmed'
  | 'in_progress'
  | 'resolved'
  | 'false_positive'
  | 'risk_accepted'
  | 'duplicate';

export type RemediationInfo = {
  priority: 'immediate' | 'high' | 'medium' | 'low';
  effort: 'minimal' | 'low' | 'medium' | 'high';
  steps: string[];
  references: string[];
  estimatedFixTime?: number;
  automatedFix?: boolean;
};

export type SecurityValidationRule = {
  id: string;
  name: string;
  type: 'input' | 'output' | 'authentication' | 'authorization' | 'encryption';
  category: string;
  pattern?: string | RegExp;
  validator?: (value: any) => boolean;
  sanitizer?: (value: any) => any;
  errorMessage: string;
  severity: SecurityLevel;
  enabled: boolean;
};

export type ValidationResult = {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
  sanitizedValue?: any;
};

export type ValidationError = {
  field: string;
  rule: string;
  message: string;
  severity: SecurityLevel;
  value?: any;
};

export type ValidationWarning = {
  field: string;
  rule: string;
  message: string;
  suggestion?: string;
};

export type ThreatIntelligence = {
  id: string;
  type: 'ip' | 'domain' | 'hash' | 'url' | 'email';
  value: string;
  category: 'malware' | 'phishing' | 'botnet' | 'apt' | 'spam';
  confidence: number;
  source: string;
  firstSeen: Date;
  lastSeen: Date;
  tags: string[];
  context?: Record<string, any>;
};

export type SecurityMetrics = {
  totalEvents: number;
  criticalEvents: number;
  resolvedEvents: number;
  averageResponseTime: number;
  topThreats: Array<{ type: string; count: number }>;
  vulnerabilityCount: Record<SecurityLevel, number>;
  authenticationSuccessRate: number;
  authorizationSuccessRate: number;
  encryptionCoverage: number;
  complianceScore: number;
  riskScore: number;
  generatedAt: Date;
};

export type SecurityConfiguration = {
  encryption: EncryptionConfig;
  authentication: {
    sessionTimeout: number;
    maxLoginAttempts: number;
    lockoutDuration: number;
    requireMfa: boolean;
    passwordPolicy: PasswordPolicy;
  };
  rateLimiting: {
    windowMs: number;
    maxRequests: number;
    skipSuccessfulRequests: boolean;
    skipFailedRequests: boolean;
  };
  auditLogging: {
    enabled: boolean;
    level: 'minimal' | 'standard' | 'comprehensive';
    retention: number;
    piiRedaction: boolean;
  };
  vulnerability: {
    scanInterval: number;
    autoFix: boolean;
    riskThreshold: SecurityLevel;
    notificationThreshold: SecurityLevel;
  };
};

export type PasswordPolicy = {
  minLength: number;
  maxLength: number;
  requireUppercase: boolean;
  requireLowercase: boolean;
  requireNumbers: boolean;
  requireSpecialChars: boolean;
  preventReuse: number;
  maxAge: number;
  complexity: 'low' | 'medium' | 'high';
};

export type SecurityPolicy = {
  id: string;
  name: string;
  description: string;
  version: string;
  rules: SecurityRule[];
  conditions: PolicyCondition[];
  isActive: boolean;
  organizationId?: string;
  createdBy: string;
  approvedBy?: string;
  effectiveDate: Date;
  expirationDate?: Date;
  createdAt: Date;
  updatedAt: Date;
  tags: string[];
  priority: number;
};

export type PolicyCondition = {
  type: 'user_role' | 'organization' | 'environment' | 'time' | 'location';
  operator: 'equals' | 'not_equals' | 'in' | 'not_in';
  value: any;
};

export type SecurityRule = {
  id: string;
  type: SecurityRuleType;
  name: string;
  description: string;
  condition: string;
  action: SecurityAction;
  parameters: Record<string, any>;
  priority: number;
  enabled: boolean;
  createdAt: Date;
  updatedAt: Date;
};

export type SecurityRuleType = 
  | 'access_control'
  | 'rate_limiting'
  | 'input_validation'
  | 'output_encoding'
  | 'encryption_required'
  | 'authentication_required'
  | 'authorization_required'
  | 'audit_required'
  | 'ip_whitelist'
  | 'ip_blacklist'
  | 'time_restriction'
  | 'data_classification'
  | 'vulnerability_scan';

export type SecurityAction = 
  | 'allow'
  | 'deny'
  | 'log'
  | 'alert'
  | 'block'
  | 'quarantine'
  | 'encrypt'
  | 'redact'
  | 'sanitize'
  | 'monitor'
  | 'escalate';

@Injectable()
export class SecurityService {
  private readonly logger = new Logger(SecurityService.name);
  private readonly securityEvents = new Map<string, SecurityEvent>();
  private readonly accessControlCache = new Map<string, AccessControlMatrix>();
  private readonly threatIntelligence = new Map<string, ThreatIntelligence>();
  private readonly securityConfig: SecurityConfiguration;

  constructor(
    private readonly configService: ConfigService,
    private readonly encryptionService: EncryptionService,
    private readonly sessionService: SessionService,
    private readonly rbacService: RbacService,
    private readonly userService: UserService,
    private readonly auditService: AuditService,
    private readonly securityEventService: SecurityEventService,
    private readonly validationService: ValidationService,
  ) {
    this.securityConfig = this.loadSecurityConfiguration();
    this.initializeThreatIntelligence();
  }

  /**
   * Create a security context for a user
   */
  async createSecurityContext(
    userId: string,
    organizationId?: string,
    sessionId?: string,
    ipAddress?: string,
    userAgent?: string,
  ): Promise<SecurityContext> {
    try {
      // Get user roles and permissions
      const roles = await this.rbacService.getUserRoles(userId, organizationId);
      const permissions = await this.rbacService.getUserPermissionObjects(userId, organizationId);

      // Calculate risk score
      const riskScore = await this.calculateRiskScore(userId, ipAddress, userAgent);
      
      // Generate device fingerprint
      const deviceFingerprint = userAgent && ipAddress 
        ? this.encryptionService.generateDeviceFingerprint(userAgent, ipAddress)
        : undefined;

      // Determine security level
      const securityLevel = this.determineSecurityLevel(riskScore);

      // Get geolocation if available
      const geoLocation = ipAddress ? await this.getGeoLocation(ipAddress) : undefined;

      const context: SecurityContext = {
        userId,
        ...(organizationId && { organizationId }),
        roles: roles.map((role: any) => role.name),
        permissions: permissions.map((permission: any) => permission.name),
        ...(sessionId && { sessionId }),
        ...(ipAddress && { ipAddress }),
        ...(userAgent && { userAgent }),
        timestamp: new Date(),
        securityLevel,
        deviceFingerprint,
        geoLocation,
        riskScore,
      };

      // Log context creation
      await this.auditService.logEvent(
        'security_context',
        'create',
        'security_context',
        { securityLevel, riskScore },
        { userId, organizationId, sessionId, ipAddress, userAgent },
      );

      return context;
    } catch (error) {
      this.logger.error(`Failed to create security context for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Validate security context
   */
  async validateSecurityContext(context: SecurityContext): Promise<boolean> {
    try {
      // Check if session is valid;
      if (context.sessionId) {const isValidSession = await this.userService.validateSession(context.sessionId);
        if (!isValidSession) {
          return false;
    } catch (error) {
      console.error(error);
    }

      }

      // Check if user still has required roles/permissions
      if (context.userId && context.organizationId) {;
        const currentRoles = await this.rbacService.getUserRoles(context.userId, context.organizationId);
        const currentRoleNames = currentRoles.map((role: any) => role.name)
        
        // Check if user still has at least one of the original roles;
        const hasValidRole = context.roles.some(role => currentRoleNames.includes(role));
        if (!hasValidRole) {
          return false}
      }

      return true} catch (error) {;
      this.logger.error('Failed to validate security context:', error);
      return false}
  }

  /**
   * Check if user has required permission
   */
  async hasPermission(
    userId: string,
    permission: string,
    organizationId?: string,
    resourceId?: string)
  ): Promise<boolean> {
    try {
      return await this.rbacService.hasPermission(userId, permission, organizationId, resourceId);
    } catch (error) {
      console.error(error);
    }
 catch (error) {;
      this.logger.error(`Failed to check permission ${permission} for user ${userId}:`, error);
      return false}
  }

  /**
   * Check if user has required role
   */
  async hasRole(
    userId: string,
    role: string,
    organizationId?: string)
  ): Promise<boolean> {
    try {
      return await this.rbacService.hasRole(userId, role, organizationId);
    } catch (error) {
      console.error(error);
    }
 catch (error) {;
      this.logger.error(`Failed to check role ${role} for user ${userId}:`, error);
      return false}
  }

  /**
   * Encrypt sensitive data
   */
  async encryptSensitiveData(data: string, context?: string): Promise<string> {
    try {
      return await this.encryptionService.encrypt(data, context);
    } catch (error) {
      console.error(error);
    }
 catch (error) {;
      this.logger.error('Failed to encrypt sensitive data:', error);
      throw error}
  }

  /**
   * Decrypt sensitive data
   */
  async decryptSensitiveData(encryptedData: string, context?: string): Promise<string> {
    try {;
      return await this.encryptionService.decrypt(encryptedData, context);
    } catch (error) {
      console.error(error);
    }
 catch (error) {;
      this.logger.error('Failed to decrypt sensitive data:', error);
      throw error}
  }

  /**
   * Generate secure token
   */
  generateSecureToken(length: number = 32): string {;
    try {return this.encryptionService.generateSecureToken(length);
    } catch (error) {
      console.error(error);
    }
 catch (error) {;
      this.logger.error('Failed to generate secure token:', error);
      throw error}
  }

  /**
   * Hash password
   */
  async hashPassword(password: string): Promise<string> {;
    try {return await this.encryptionService.hashPassword(password);
    } catch (error) {
      console.error(error);
    }
 catch (error) {;
      this.logger.error('Failed to hash password:', error);
      throw error}
  }

  /**
   * Verify password
   */
  async verifyPassword(password: string, hash: string): Promise<boolean> {;
    try {return await this.encryptionService.verifyPassword(password, hash);
    } catch (error) {
      console.error(error);
    }
 catch (error) {;
      this.logger.error('Failed to verify password:', error);
      return false}
  }

  /**
   * Sanitize input data
   */
  sanitizeInput(input: any): any {
    if (typeof input === 'string') {
      // Remove potentially dangerous characters;
      return input;
replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
replace(/[^a-z0-9]+/g, '-').replace(/[^a-z0-9]+/g, '-');
trim()}

    if (Array.isArray(input)) {
      return input.map((item: any) => this.sanitizeInput(item))}

    if (typeof input === 'object' && input !== null) {;
      const sanitized: any = {};
      for (const [key, value] of Object.entries(input)) {
        sanitized[this.sanitizeInput(key)] = this.sanitizeInput(value)}
      return sanitized}

    return input}

  /**
   * Validate input against security rules
   */
  validateInput(input: any, rules: SecurityRule[]): boolean {
    try {
      for (const rule of rules) {
        if (rule.type === 'validation') {;
          // Apply validation rule;
          const isValid = this.applyValidationRule(input, rule);
          if (!isValid && rule.action === 'deny') {
            return false;
    } catch (error) {
      console.error(error);
    }

        }
      }
      return true} catch (error) {;
      this.logger.error('Failed to validate input:', error);
      return false}
  }

  /**
   * Apply validation rule to input
   */;
  private applyValidationRule(input: any, rule: SecurityRule): boolean {const { condition, parameters } = rule;

    switch (condition) {
      case 'max_length':
        return typeof input === 'string' && input.length <= (parameters.maxLength || 1000);
      case 'min_length':
        return typeof input === 'string' && input.length >= (parameters.minLength || 0);
      case 'pattern':
        return typeof input === 'string' && new RegExp(parameters.pattern).test(input);
      case 'no_script_tags':
        return typeof input === 'string' && !/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi.test(input);
      case 'no_sql_injection':
        return typeof input === 'string' && !/(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/gi.test(input);
      default: return true}
  }

  /**
   * Get security configuration
   */
  getSecurityConfig(): Record<string, any> {
    return {
      jwtSecret: this.configService.get<string>('JWT_SECRET'),
    jwtExpiresIn: this.configService.get<string>('JWT_EXPIRES_IN', '24h'),
      sessionTimeout: this.configService.get<number>('SESSION_TIMEOUT', 86400000),
      rateLimitTtl: this.configService.get<number>('RATE_LIMIT_TTL', 60),
      rateLimitMax: this.configService.get<number>('RATE_LIMIT_MAX', 100),
      encryptionEnabled: this.configService.get<boolean>('ENCRYPTION_ENABLED', true),
      auditLoggingEnabled: this.configService.get<boolean>('AUDIT_LOGGING_ENABLED', true),
    }}
}
;