import { Module } from '@nestjs/common';
import { EncryptionService } from './encryption.service';
import { SessionService } from './session.service';
import { UserService } from './user.service';
import { DatabaseModule } from '@/core/database';

@Module({
  imports: [DatabaseModule],
    providers: [EncryptionService, SessionService, UserService],
  exports: [EncryptionService, SessionService, UserService])
});
export class SecurityModule {}