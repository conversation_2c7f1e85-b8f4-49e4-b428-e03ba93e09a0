import { Request, Response } from 'express';
import { ExecutionContext } from '@nestjs/common';
import { Observable } from 'rxjs';

// ========================
// Base Types
// ========================

export interface SecurityContext {
  user?: {
    id: string;
    username?: string;
    email?: string;
    roles?: string[];
    permissions?: string[];
    organizationId?: string;
  };
  apiKey?: {
    id: string;
    name: string;
    organizationId?: string;
    permissions: string[];
    rateLimit?: number;
  };
  session?: {
    sessionId: string;
    userId: string;
    expiresAt: Date;
  };
  device?: {
    fingerprint: string;
    userAgent: string;
    ip: string;
  };
}

export interface SecurityHeaders {
  'Content-Security-Policy'?: string;
  'Strict-Transport-Security'?: string;
  'X-Frame-Options'?: string;
  'X-Content-Type-Options'?: string;
  'X-XSS-Protection'?: string;
  'Referrer-Policy'?: string;
  'Permissions-Policy'?: string;
  'Cross-Origin-Embedder-Policy'?: string;
  'Cross-Origin-Opener-Policy'?: string;
  'Cross-Origin-Resource-Policy'?: string;
  'X-Request-ID'?: string;
  'X-Response-Time'?: string;
}

export interface RateLimitInfo {
  limit: number;
  remaining: number;
  resetTime: Date;
  totalHits?: number;
  windowMs?: number;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings?: ValidationWarning[];
}

export interface ValidationError {
  field: string;
  message: string;
  code: string;
  value?: any;
}

export interface ValidationWarning {
  field: string;
  message: string;
  code: string;
  value?: any;
}

// ========================
// Request Types
// ========================

export interface BaseSecurityRequest extends Request {
  securityContext?: SecurityContext;
  requestId?: string;
  startTime?: number;
}

export interface AuthenticatedRequest extends BaseSecurityRequest {
  user: {
    sub: string; // User ID
    email?: string;
    username?: string;
    organizationId?: string;
    roles?: string[];
    permissions?: string[];
    iat?: number;
    exp?: number;
    iss?: string;
    aud?: string;
  };
}

export interface ApiKeyRequest extends BaseSecurityRequest {
  apiKey: {
    id: string;
    name: string;
    organizationId?: string;
    permissions: string[];
    rateLimit?: number;
    scopes?: string[];
    isActive: boolean;
    createdAt: Date;
    lastUsedAt?: Date;
  };
}

export interface RateLimitRequest extends BaseSecurityRequest {
  user?: AuthenticatedRequest['user'];
  apiKey?: ApiKeyRequest['apiKey'];
  rateLimitInfo?: RateLimitInfo;
}

export interface EnhancedRequest extends AuthenticatedRequest, ApiKeyRequest, RateLimitRequest {
  deviceFingerprint?: string;
  csrfToken?: string;
  validationResult?: ValidationResult;
}

// ========================
// Guard Types
// ========================

export interface GuardResult {
  canActivate: boolean;
  reason?: string;
  context?: Record<string, any>;
}

export interface JwtGuardConfig {
  secret: string;
  issuer?: string;
  audience?: string;
  expiresIn?: string;
  ignoreExpiration?: boolean;
  publicPaths?: string[];
}

export interface ApiKeyGuardConfig {
  required: boolean;
  permissions?: string[];
  rateLimiting?: {
    enabled: boolean;
    windowMs: number;
    maxRequests: number;
  };
}

export interface RateLimitGuardConfig {
  windowMs: number;
  maxRequests: number;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  keyGenerator: 'ip' | 'user' | 'api_key' | 'endpoint' | 'custom';
  customKeyGenerator?: (request: Request) => string;
  message?: string;
  statusCode?: number;
}

export interface RbacGuardConfig {
  roles?: string[];
  permissions?: string[];
  organizationRequired?: boolean;
  resourceId?: string;
  strictMode?: boolean;
}

export interface SecurityHeadersConfig {
  contentSecurityPolicy?: string | boolean;
  strictTransportSecurity?: string | boolean;
  xFrameOptions?: string | boolean;
  xContentTypeOptions?: boolean;
  xXssProtection?: string | boolean;
  referrerPolicy?: string | boolean;
  permissionsPolicy?: string | boolean;
  crossOriginEmbedderPolicy?: string | boolean;
  crossOriginOpenerPolicy?: string | boolean;
  crossOriginResourcePolicy?: string | boolean;
}

// ========================
// Interceptor Types
// ========================

export interface InterceptorContext {
  executionContext: ExecutionContext;
  request: EnhancedRequest;
  response: Response;
  startTime: number;
  requestId: string;
}

export interface SecurityInterceptorConfig {
  enableRequestValidation: boolean;
  enableResponseValidation: boolean;
  enableRateLimiting: boolean;
  enableCSRFProtection: boolean;
  enableInputSanitization: boolean;
  enableSecurityHeaders: boolean;
  logSecurityEvents: boolean;
}

export interface ValidationInterceptorConfig {
  validateRequest: boolean;
  validateResponse: boolean;
  sanitizeRequest: boolean;
  sanitizeResponse: boolean;
  requestRules?: ValidationRule[];
  responseRules?: ValidationRule[];
  strictMode: boolean;
  logValidationErrors: boolean;
}

export interface AuditInterceptorConfig {
  logRequests: boolean;
  logResponses: boolean;
  logHeaders: boolean;
  logBody: boolean;
  sensitiveFields: string[];
  maxBodySize: number;
}

// ========================
// Validation Types
// ========================

export interface ValidationRule {
  field: string;
  type: 'string' | 'number' | 'boolean' | 'email' | 'url' | 'date' | 'object' | 'array';
  required: boolean;
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
  pattern?: string;
  allowedValues?: any[];
  customValidator?: (value: any) => boolean | string;
  sanitize?: boolean;
  nested?: ValidationRule[];
}

export interface SanitizationOptions {
  trimStrings: boolean;
  removeHtml: boolean;
  removeScripts: boolean;
  normalizeEmail: boolean;
  maxStringLength: number;
  allowedTags?: string[];
  allowedAttributes?: Record<string, string[]>;
}

// ========================
// Security Event Types
// ========================

export interface SecurityEvent {
  id: string;
  type: SecurityEventType;
  severity: SecurityEventSeverity;
  message: string;
  details: Record<string, any>;
  context: SecurityEventContext;
  timestamp: Date;
  resolved?: boolean;
  resolvedAt?: Date;
  resolvedBy?: string;
}

export enum SecurityEventType {
  AUTHENTICATION_FAILURE = 'authentication_failure',
  AUTHORIZATION_FAILURE = 'authorization_failure',
  RATE_LIMIT_EXCEEDED = 'rate_limit_exceeded',
  SUSPICIOUS_ACTIVITY = 'suspicious_activity',
  SECURITY_VIOLATION = 'security_violation',
  INPUT_VALIDATION_FAILURE = 'input_validation_failure',
  CSRF_VIOLATION = 'csrf_violation',
  XSS_ATTEMPT = 'xss_attempt',
  SQL_INJECTION_ATTEMPT = 'sql_injection_attempt',
  BRUTE_FORCE_ATTEMPT = 'brute_force_attempt'
}

export enum SecurityEventSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export interface SecurityEventContext {
  userId?: string;
  organizationId?: string;
  ipAddress: string;
  userAgent: string;
  endpoint: string;
  method: string;
  headers?: Record<string, string>;
  body?: any;
  timestamp: Date;
}

// ========================
// Error Types
// ========================

export interface SecurityError extends Error {
  type: SecurityEventType;
  severity: SecurityEventSeverity;
  context: SecurityEventContext;
  code: string;
  details?: Record<string, any>;
}

export interface AuthenticationError extends SecurityError {
  type: SecurityEventType.AUTHENTICATION_FAILURE;
  reason: 'invalid_credentials' | 'expired_token' | 'malformed_token' | 'missing_token';
}

export interface AuthorizationError extends SecurityError {
  type: SecurityEventType.AUTHORIZATION_FAILURE;
  reason: 'insufficient_permissions' | 'invalid_role' | 'resource_forbidden';
  requiredPermissions?: string[];
  userPermissions?: string[];
}

export interface RateLimitError extends SecurityError {
  type: SecurityEventType.RATE_LIMIT_EXCEEDED;
  limit: number;
  windowMs: number;
  retryAfter: number;
}

export interface ValidationError extends SecurityError {
  type: SecurityEventType.INPUT_VALIDATION_FAILURE;
  field: string;
  value: any;
  rule: ValidationRule;
}

// ========================
// Utility Types
// ========================

export type GuardHandler<T = any> = (context: ExecutionContext) => Promise<T>;
export type InterceptorHandler<T = any> = (context: InterceptorContext) => Promise<T> | Observable<T>;
export type ValidationHandler = (data: any, rules: ValidationRule[]) => ValidationResult;
export type SanitizationHandler = (data: any, options: SanitizationOptions) => any;

export interface MiddlewareMetadata {
  type: 'guard' | 'interceptor';
  priority: number;
  config: Record<string, any>;
  dependencies?: string[];
}

export interface SecurityMetrics {
  authenticationAttempts: number;
  authenticationFailures: number;
  authorizationFailures: number;
  rateLimitViolations: number;
  validationFailures: number;
  securityEvents: number;
  blockedRequests: number;
  averageResponseTime: number;
}

// ========================
// Constants
// ========================

export const SECURITY_CONSTANTS = {
  HEADERS: {
    REQUEST_ID: 'X-Request-ID',
    RESPONSE_TIME: 'X-Response-Time',
    RATE_LIMIT: {
      LIMIT: 'X-RateLimit-Limit',
      REMAINING: 'X-RateLimit-Remaining',
      RESET: 'X-RateLimit-Reset',
      RETRY_AFTER: 'Retry-After'
    },
    SECURITY: {
      CSP: 'Content-Security-Policy',
      HSTS: 'Strict-Transport-Security',
      FRAME_OPTIONS: 'X-Frame-Options',
      CONTENT_TYPE: 'X-Content-Type-Options',
      XSS_PROTECTION: 'X-XSS-Protection',
      REFERRER_POLICY: 'Referrer-Policy'
    }
  },
  TIMEOUTS: {
    JWT_EXPIRATION: '1h',
    REFRESH_TOKEN_EXPIRATION: '7d',
    SESSION_TIMEOUT: 30 * 60 * 1000, // 30 minutes
    RATE_LIMIT_WINDOW: 60 * 1000 // 1 minute
  },
  LIMITS: {
    MAX_REQUEST_SIZE: 10 * 1024 * 1024, // 10MB
    MAX_REQUESTS_PER_MINUTE: 100,
    MAX_AUTH_ATTEMPTS: 5,
    MAX_STRING_LENGTH: 10000
  },
  PATTERNS: {
    EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    USERNAME: /^[a-zA-Z0-9_-]{3,50}$/,
    PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/,
    UUID: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  }
} as const;

export type SecurityConstant = typeof SECURITY_CONSTANTS;

// ========================
// Type Guards
// ========================

export function isAuthenticatedRequest(request: any): request is AuthenticatedRequest {
  return request && typeof request.user === 'object' && typeof request.user.sub === 'string';
}

export function isApiKeyRequest(request: any): request is ApiKeyRequest {
  return request && typeof request.apiKey === 'object' && typeof request.apiKey.id === 'string';
}

export function isRateLimitRequest(request: any): request is RateLimitRequest {
  return request && typeof request.rateLimitInfo === 'object';
}

export function isSecurityError(error: any): error is SecurityError {
  return error && error.type && error.severity && error.context;
}

export function isValidationError(error: any): error is ValidationError {
  return isSecurityError(error) && error.type === SecurityEventType.INPUT_VALIDATION_FAILURE;
}

export function isAuthenticationError(error: any): error is AuthenticationError {
  return isSecurityError(error) && error.type === SecurityEventType.AUTHENTICATION_FAILURE;
}

export function isAuthorizationError(error: any): error is AuthorizationError {
  return isSecurityError(error) && error.type === SecurityEventType.AUTHORIZATION_FAILURE;
}

export function isRateLimitError(error: any): error is RateLimitError {
  return isSecurityError(error) && error.type === SecurityEventType.RATE_LIMIT_EXCEEDED;
}
