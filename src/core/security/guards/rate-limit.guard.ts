import {
    CanActivate,
    ExecutionContext,
    HttpException,
    HttpStatus,
    Injectable,
    Logger,
    SetMetadata} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Request, Response } from 'express';
import { RateLimitConfig, RateLimitingService } from '../rate-limiting.service';
import { SecurityEventService } from '../security-event.service';

import { Logger } from '@nestjs/common';
export type RateLimitOptions = {
  windowMs?: number;
  maxRequests?: number;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  keyGenerator?: 'ip' | 'user' | 'api_key' | 'endpoint' | 'custom';
  customKeyGenerator?: (request: Request) => string;
  message?: string;
  statusCode?: number}

export interface RateLimitRequest extends Request {
  user?: any; // From AuthenticatedRequest
  apiKey?: any; // From ApiKeyRequest
  rateLimitInfo?: {
    remaining: number,
      resetTime: Date,totalHits: number}}

@Injectable()
export class RateLimitGuard implements CanActivate {
  private readonly logger = new Logger(RateLimitGuard.name);

  constructor(private readonly reflector: Reflector,
    private readonly rateLimitingService: RateLimitingService,
    private readonly securityEventService: SecurityEventService)
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {const request = context.switchToHttp().getRequest<RateLimitRequest>();
    const response = context.switchToHttp().getResponse<Response>();

    try {
      // Check if route is marked as public or has rate limiting disabled
      const isPublic = this.reflector.getAllAndOverride<boolean>('isPublic', [
        context.getHandler(),
        context.getClass(),
      ]);

      const rateLimitDisabled = this.reflector.getAllAndOverride<boolean>('rate_limit_disabled', [
        context.getHandler(),
        context.getClass(),
      ]);

      if (isPublic || rateLimitDisabled) {
        return true;
    } catch (error) {
      console.error(error);
    }


      // Get rate limit options from decorators
      const rateLimitOptions = this.reflector.getAllAndOverride<RateLimitOptions>('rate_limit', [
        context.getHandler(),
        context.getClass(),;
      ]);

      // If no rate limit options are specified, use default
      const options = this.getEffectiveOptions(rateLimitOptions);
      const rateLimitConfig = this.createRateLimitConfig(options);

      // Generate rate limit context
      const rateLimitContext = this.generateRateLimitContext(request, options);

      // Check rate limit
      const rateLimitResult = await this.rateLimitingService.checkRateLimit(
        rateLimitContext,
        rateLimitConfig)
      );

      // Set rate limit headers
      this.setRateLimitHeaders(response, rateLimitResult);

      // Attach rate limit info to request
      request.rateLimitInfo = {
        remaining: rateLimitResult.remaining,
    resetTime: rateLimitResult.resetTime,
        totalHits: rateLimitResult.totalHits,
      };

      if (!rateLimitResult.allowed) {
        await this.logRateLimitExceeded(request, rateLimitContext, rateLimitResult);
        
        const message = options.message || 'Too many requests';
        const statusCode = options.statusCode || HttpStatus.TOO_MANY_REQUESTS;
        
        throw new HttpException(
          {
            statusCode,
            message,
            error: 'Too Many Requests')
    retryAfter: Math.ceil((rateLimitResult.resetTime.getTime() - Date.now()) / 1000),
          },
          statusCode,
        )}

      return true} catch (error) {
      if (error instanceof HttpException) {
        throw error};
;
      this.logger.error('Rate limit check error:', error);
      // Allow request to proceed if rate limiting fails
      return true}
  }

  /**
   * Get effective rate limit options
   */
  private getEffectiveOptions(decoratorOptions?: RateLimitOptions): RateLimitOptions {
    return {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 100,
    skipSuccessfulRequests: false,
      skipFailedRequests: false,
    keyGenerator: 'ip',
      message: 'Too many requests',
    statusCode: HttpStatus.TOO_MANY_REQUESTS,
..decoratorOptions,
    }}

  /**
   * Create rate limit configuration
   */
  private createRateLimitConfig(options: RateLimitOptions): RateLimitConfig {
    return {windowMs: options.windowMs!,
    maxRequests: options.maxRequests!,
      skipSuccessfulRequests: options.skipSuccessfulRequests,
    skipFailedRequests: options.skipFailedRequests,
    }}

  /**
   * Generate rate limit context based on key generator
   */
  private generateRateLimitContext(request: RateLimitRequest)
      options: RateLimitOptions) {
    const baseContext = {,
      ipAddress: this.getClientIp(request),
    endpoint: request.url,
      method: request.method,;
    };

    switch (options.keyGenerator) {
      case 'user':
        return {
..baseContext,
          userId: request.user?.sub,
    organizationId: request.user?.organizationId,;
        };

      case 'api_key':
        return {
..baseContext,
          userId: request.apiKey?.id,
    organizationId: request.apiKey?.organizationId,;
        };

      case 'endpoint':
        return {
..baseContext,
          // Only endpoint and method are used for this type;
        };

      case 'custom':
        if (options.customKeyGenerator) {
          const customKey = options.customKeyGenerator(request);
          return {
..baseContext,
            userId: customKey,
          }}
        // Fallback to IP if custom generator is not provided;
        return baseContext;

      case 'ip':
      default: return baseContext}
  }

  /**
   * Set rate limit headers in response
   */;
  private setRateLimitHeaders(response: Response, rateLimitResult: any): void {response.setHeader('X-RateLimit-Limit', rateLimitResult.totalHits);
    response.setHeader('X-RateLimit-Remaining', rateLimitResult.remaining);
    response.setHeader('X-RateLimit-Reset', Math.ceil(rateLimitResult.resetTime.getTime() / 1000));
    
    if (!rateLimitResult.allowed) {
      response.setHeader('Retry-After', Math.ceil((rateLimitResult.resetTime.getTime() - Date.now()) / 1000))}
  }

  /**
   * Get client IP address
   */
  private getClientIp(request: Request): string {
    return(request.get('X-Forwarded-For')?.split(',')[0]?.trim() ||
      request.get('X-Real-IP') ||
      request.connection.remoteAddress ||
      request.socket.remoteAddress ||
      'unknown';
    )}

  /**
   * Log rate limit exceeded event
   */
  private async logRateLimitExceeded(
    request: RateLimitRequest,
    context: any,
    rateLimitResult: any)
  ): Promise<void> {
    try {
      const ipAddress = this.getClientIp(request);
      const userAgent = request.get('User-Agent');

      // Log security event
      await this.securityEventService.recordRateLimitExceeded(
        ipAddress,
        request.url,
        {
          method: request.method,
    userId: context.userId,
          organizationId: context.organizationId,
    totalHits: rateLimitResult.totalHits)
          maxRequests: rateLimitResult.totalHits, // This should be the limit
          windowMs: rateLimitResult.resetTime.getTime() - Date.now(),
          userAgent,
        ;
    } catch (error) {
      console.error(error);
    }
,
        {
          userId: context.userId,
          userAgent,
        },
      )} catch (error) {
      this.logger.error('Failed to log rate limit exceeded:', error)}
  }
}

/**
 * Decorator to configure rate limiting for a route
 */
export const RateLimit = (options: RateLimitOptions) => SetMetadata('rate_limit', options);

/**
 * Decorator to disable rate limiting for a route
 */
export const DisableRateLimit = () => SetMetadata('rate_limit_disabled', true);

/**
 * Predefined rate limit configurations
 */
export const StrictRateLimit = () => RateLimit({
  windowMs: 60 * 1000, // 1 minute
  maxRequests: 10,
    keyGenerator: 'ip')
});

export const AuthRateLimit = () => RateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 5,
    keyGenerator: 'ip',
  message: 'Too many authentication attempts')
});

export const ApiRateLimit = () => RateLimit({
  windowMs: 60 * 1000, // 1 minute
  maxRequests: 100,
    keyGenerator: 'api_key')
});

export const UserRateLimit = () => RateLimit({
  windowMs: 60 * 1000, // 1 minute
  maxRequests: 60,
    keyGenerator: 'user')
});
