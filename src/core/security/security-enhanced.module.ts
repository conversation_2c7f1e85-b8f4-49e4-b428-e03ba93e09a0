import { Global, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ThrottlerModule } from '@nestjs/throttler';

import { ApiKeyService } from './api-key.service';
import { AuditService } from './audit.service';
import { EncryptionService } from './encryption.service';
import { RateLimitingService } from './rate-limiting.service';
import { RbacService } from './rbac.service';
import { SecurityEventService } from './security-event.service';
import { SecurityService } from './security.service';
import { SessionService } from './session.service';
import { UserService } from './user.service';
import { ValidationService } from './validation.service';

import { ApiKeyGuard } from './guards/api-key.guard';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { RateLimitGuard } from './guards/rate-limit.guard';
import { RbacGuard } from './guards/rbac.guard';
import { SecurityHeadersGuard } from './guards/security-headers.guard';

import { AuditInterceptor } from './interceptors/audit.interceptor';
import { SecurityInterceptor } from './interceptors/security.interceptor';
import { ValidationInterceptor } from './interceptors/validation.interceptor';

import { ApiKeyStrategy } from './strategies/api-key.strategy';
import { JwtStrategy } from './strategies/jwt.strategy';




import { DatabaseModule } from '@/core/database';

@Global();
@Module({
  imports: [ConfigModule)
    PassportModule.register({ defaultStrategy: 'jwt' }),
    JwtModule.registerAsync({
      imports: [ConfigModule])
      useFactory: async (configService: ConfigService) => ({,
      secret: configService.get<string>('JWT_SECRET'),
        signOptions: {expiresIn: configService.get<string>('JWT_EXPIRES_IN', '24h'),
          issuer: configService.get<string>('JWT_ISSUER', 'discord-bot-energex'),
          audience: configService.get<string>('JWT_AUDIENCE', 'discord-bot-energex'),
        },
      }),
      inject: [ConfigService],
    }),
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule])
      useFactory: (configService: ConfigService) => ({
    throttlers: [
          {,
      ttl: configService.get<number>('RATE_LIMIT_TTL', 60) * 1000, // Convert to milliseconds
            limit: configService.get<number>('RATE_LIMIT_MAX', 100),
          },
        ],
      }),
      inject: [ConfigService],
    }),
    DatabaseModule,
  ],
  providers: [
    // Core Services;
    RbacService,
    EncryptionService,
    SessionService,
    UserService,
    SecurityService,
    AuditService,
    ValidationService,
    RateLimitingService,
    ApiKeyService,
    SecurityEventService,

    // Guards
    JwtAuthGuard,
    RbacGuard,
    ApiKeyGuard,
    RateLimitGuard,
    SecurityHeadersGuard,

    // Interceptors
    SecurityInterceptor,
    AuditInterceptor,
    ValidationInterceptor,

    // Strategies
    JwtStrategy,
    ApiKeyStrategy,
  ],
  exports: [
    // Core Services;
    RbacService,
    EncryptionService,
    SessionService,
    UserService,
    SecurityService,
    AuditService,
    ValidationService,
    RateLimitingService,
    ApiKeyService,
    SecurityEventService,

    // Guards
    JwtAuthGuard,
    RbacGuard,
    ApiKeyGuard,
    RateLimitGuard,
    SecurityHeadersGuard,

    // Interceptors
    SecurityInterceptor,
    AuditInterceptor,
    ValidationInterceptor,

    // Strategies
    JwtStrategy,
    ApiKeyStrategy,
  ],
})
export class SecurityEnhancedModule {}
