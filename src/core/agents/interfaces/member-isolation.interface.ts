import { MembershipTier } from '../../services/interfaces';
import { AgentType, MemberContext, PersonalizedResponse, MemberFeedback, CustomContent } from './personal-agent.interface';
import { PrivateMemoryStore } from './private-memory.interface';

export type PersonalAIAgent = {
  memberId: string,
      agentType: AgentType,personalMemory: PrivateMemoryStore,
    learningProfile: any;
  conversationHistory: any,
      createdAt: Date
lastInteraction: Date
  ;
  processMessage(message: string, context: MemberContext): Promise<PersonalizedResponse>
  updatePersonalMemory(interaction: any): Promise<void>
  adaptToMemberStyle(feedback: MemberFeedback): Promise<void>;
  generatePersonalizedContent(): Promise<CustomContent>;
  getPersonalizationLevel(): Promise<number>; // 0-100
  exportPersonalData(): Promise<any>}

export type PersonalAgentSet = {
  memberId: string,
      tier: MembershipTier,createdAt: Date,
    lastSync: Date
  ;
  aiMasteryAgent: PersonalAIAgent,
      wealthCreationAgent: PersonalAIAgent,personalGrowthAgent: PersonalAIAgent,
    intakeSpecialist: PersonalAIAgent;
  devSupportAgent?: PersonalAIAgent; // Enterprise only
  
  getAgent(agentType: AgentType): Promise<PersonalAIAgent | null>;
  getAllAgents(): Promise<PersonalAIAgent[]>;
  syncAgentData(): Promise<void>;
  verifyIsolation(): Promise<boolean>}

export type AgentCluster = {
  clusterId: string,
      memberId: string,tier: MembershipTier,
    agents: PersonalAgentSet;
  coordinator: PersonalAgentCoordinator,
      isolation: ClusterIsolation,metrics: ClusterMetrics}

export type PersonalAgentCoordinator = {
  memberId: string,
    agentCluster: string
  ;
  coordinateAgents(message: string, context: MemberContext): Promise<CoordinatedResponse>
  shareContextBetweenAgents(context: any): Promise<void>;
  maintainConsistentPersonality(): Promise<void>;
  orchestrateMultiAgentResponse(agentTypes: AgentType[]): Promise<string>;
  trackCrossAgentLearning(): Promise<void>}

export type CoordinatedResponse = {
  primaryAgent: AgentType,
      response: string,supportingAgents: AgentType[],
    sharedContext: any;
  crossAgentInsights: string[]}

export type ClusterIsolation = {
  isolationLevel: 'complete' | 'partial',
      encryptionEnabled: boolean,memoryFirewall: boolean,
    crossMemberAccess: boolean // Should always be false;
  lastVerification: Date,
    isolationScore: number // 0-100}

export type ClusterMetrics = {
  totalInteractions: number,
      averageResponseTime: number,personalizationAccuracy: number // 0-100,
    memberSatisfaction: number // 0-100;
  learningProgress: number // 0-100,
    retentionImpact: number // 0-100}

export type MemberLearningProfile = {
  memberId: string,
      learningStyle: LearningStyle,adaptationSpeed: number // 0-100,
    retentionRate: number // 0-100;
  engagementPatterns: EngagementPattern[],
      preferredContentTypes: ContentPreference[],skillProgression: SkillProgression[]}

export type LearningStyle = {
  visual: number // 0-100,
      auditory: number // 0-100,kinesthetic: number // 0-100,
    analytical: number // 0-100;
  creative: number // 0-100}

export type EngagementPattern = {
  timeOfDay: 'morning' | 'afternoon' | 'evening' | 'late_night',
      channelTypes: string[],responseRate: number // 0-100,
    averageSessionLength: number // minutes;
  preferredInteractionStyle: 'quick_tips' | 'deep_dive' | 'step_by_step'}

export type ContentPreference = {
  type: 'tutorial' | 'example' | 'explanation' | 'exercise',
      preference: number // 0-100,effectiveness: number // 0-100}

export type SkillProgression = {
  skill: string,
      currentLevel: number // 0-100,progressRate: number // points per week,
    plateauAreas: string[];
  accelerators: string[]}

export type AgentEvolutionStage = {
  stage: 'basic' | 'learning' | 'adapting' | 'personalizing' | 'mastery' | 'wealth_building' | 'developing' | 'technical_lead',
      startDate: Date,milestones: EvolutionMilestone[],
    nextStageRequirements: string[]}

export type EvolutionMilestone = {
  milestone: string,
      achieved: boolean;
  achievedAt?: Date,impact: number // 0-100}

export type IMemberAgentIsolation = {
  createMemberAgentCluster(memberId: string, tier: MembershipTier): Promise<AgentCluster>
  getMemberAgents(memberId: string): Promise<PersonalAgentSet>;
  isolateAgentMemory(memberId: string, agentType: AgentType): Promise<PrivateMemoryStore>
  destroyMemberAgents(memberId: string): Promise<void>;
  upgradeMemberTier(memberId: string, newTier: MembershipTier): Promise<void>;
  downgradeMemberTier(memberId: string, newTier: MembershipTier): Promise<void>
  verifyCompleteIsolation(memberId: string): Promise<boolean>
  auditMemberIsolation(memberId: string): Promise<IsolationAuditReport>
  exportMemberData(memberId: string): Promise<MemberDataExport>;
  importMemberData(memberId: string, data: MemberDataExport): Promise<void>}

export type IsolationAuditReport = {
  memberId: string,
      auditDate: Date,isolationScore: number // 0-100,
    violations: IsolationViolation[];
  recommendations: string[],
    riskLevel: 'low' | 'medium' | 'high' | 'critical'}

export type IsolationViolation = {
  type: 'memory_leak' | 'cross_contamination' | 'access_breach',
      severity: 'low' | 'medium' | 'high' | 'critical',description: string,
    affectedData: string[];
  discoveredAt: Date,
    resolved: boolean}

export type MemberDataExport = {
  memberId: string,
      exportDate: Date,agentData: AgentDataExport[],
    memoryData: MemoryDataExport;
  learningProfile: MemberLearningProfile,
    metrics: ClusterMetrics}

export type AgentDataExport = {
  agentType: AgentType,
      conversations: any[],learningData: any,
    personalizationSettings: any;
  evolutionStage: AgentEvolutionStage}

export type MemoryDataExport = {
  encryptedMemory: string,
      memorySize: number,isolationMetadata: any,
    accessLogs: any[]}