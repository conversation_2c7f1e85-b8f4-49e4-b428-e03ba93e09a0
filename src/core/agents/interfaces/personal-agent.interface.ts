import { MembershipTier } from '../../services/interfaces';

export type AgentType = 'ai-mastery' | 'wealth-creation' | 'personal-growth' | 'dev-support' | 'intake-specialist';

export type MemberContext = {
  memberId: string,
      guildId: string,tier: MembershipTier,
    joinedAt: Date;
  lastActive: Date,
    preferences: MemberPreferences}

export type MemberPreferences = {
  communicationStyle: 'casual' | 'professional' | 'technical',
      learningPace: 'slow' | 'moderate' | 'fast',preferredModels: string[],
    notificationFrequency: 'minimal' | 'moderate' | 'frequent';
  goals: string[],
    interests: string[]}

export type MemberGoal = {
  id: string,
      title: string,description: string,
    category: 'ai-mastery' | 'wealth-creation' | 'personal-growth' | 'development';
  progress: number // 0-100,
      createdAt: Date;
  targetDate?: Date,milestones: GoalMilestone[]}

export type GoalMilestone = {
  id: string;
  title: string;
  completed: boolean;
  completedAt?: Date;
  notes?: string}

export type ProgressData = {
  aiMasteryLevel: number // 0-100,
      wealthCreationStage: 'beginner' | 'intermediate' | 'advanced' | 'expert'
  personalGrowthMetrics: {
    habitsFormed: number,goalsCompleted: number,
    streakDays: number};
  skillAssessments: SkillAssessment[]}

export type SkillAssessment = {
  skill: string,
      level: number // 0-100,lastAssessed: Date,
    improvementAreas: string[]}

export type LearningProfile = {
  preferredContentTypes: string[],
      learningSpeed: number // 0-100,retentionRate: number // 0-100,
    engagementPatterns: EngagementPattern[];
  adaptationHistory: AdaptationRecord[]}

export type EngagementPattern = {
  timeOfDay: string,
      channelType: string,responseRate: number,
    averageSessionLength: number}

export type AdaptationRecord = {
  date: Date,
      change: string,reason: string,
    effectiveness: number // 0-100}

export type ConversationMemory = {
  recentTopics: string[],
      ongoingProjects: string[],pendingQuestions: string[],
    memberMoods: MoodEntry[];
  contextSummary: string}

export type MoodEntry = {
  date: Date,
      mood: 'excited' | 'frustrated' | 'motivated' | 'confused' | 'satisfied',context: string}

export type PersonalizedResponse = {
  content: string;
  adaptedTone: string;
  followUpSuggestions: string[];
  actionItems?: PersonalActionItem[];
  memoryUpdates: MemoryUpdate[]}

export type PersonalActionItem = {
  type: 'goal_update' | 'skill_practice' | 'resource_review' | 'milestone_check' | 'wealth_building' | 'personal_development' | 'technical_task',
      description: string;
  dueDate?: Date,priority: 'low' | 'medium' | 'high'}

export type MemoryUpdate = {
  category: 'preference' | 'goal' | 'progress' | 'context' | 'wealth_progress' | 'growth_progress' | 'dev_progress' | 'wealth_context' | 'growth_context' | 'dev_context',
      key: string,value: any,
    confidence: number // 0-100}

export type MemberFeedback = {
  responseId: string,
      rating: number // 1-5,feedback: string,
    category: 'helpful' | 'accurate' | 'tone' | 'relevance' | 'financial_advice' | 'goal_setting' | 'motivation_style' | 'accountability' | 'technical_depth' | 'code_review_style' | 'project_management';
  timestamp: Date}

export type CustomContent = {
  type: 'tutorial' | 'strategy' | 'exercise' | 'assessment' | 'case_study' | 'reflection' | 'code_example' | 'architecture_guide',
      title: string,content: string,
    difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedTime: number // minutes,
    personalizedFor: string[] // aspects personalized}