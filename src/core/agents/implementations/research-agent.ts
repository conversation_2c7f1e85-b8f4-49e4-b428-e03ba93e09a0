import { Injectable, Logger } from '@nestjs/common';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { ExaSearchService, ExaSearchQuery, ExaSearchResult } from '../services/exa-search.service';
// import { PersonalAIAgent } from '../interfaces/member-isolation.interface';
import { MemberContext, PersonalizedResponse } from '../interfaces/personal-agent.interface';

export type ResearchQuery = {
  topic: string;
  context?: string;
  depth?: 'basic' | 'detailed' | 'comprehensive';
  sources?: string[];
  maxResults?: number}

export type ResearchResult = {
  summary: string;
  sources: string[];
  keyFindings: string[];
  recommendations?: string[]}

@Injectable()
export class ResearchAgent {
  private readonly logger = new Logger(ResearchAgent.name);
  private readonly agentName = 'Research Assistant';

  constructor(private readonly exaSearchService: ExaSearchService) {}

  async processMessage(message: string, context: MemberContext): Promise<PersonalizedResponse> {
    try {
      if (!this.exaSearchService.isConfigured()) {
        return {
          content: '🔍 Research functionality is temporarily unavailable. Please contact an administrator to enable web search capabilities.',
          adaptedTone: 'informative',
          followUpSuggestions: ['Contact an administrator to enable web search'],
          memoryUpdates: [],
        };
      }

      const researchQuery = this.parseResearchQuery(message);
      const researchResults = await this.conductResearch(researchQuery);

      return {
        content: this.formatResearchResponse(researchResults, context),
        adaptedTone: 'helpful',
        followUpSuggestions: [
          `Get more details about ${researchQuery.topic}`,
          'Find similar research sources',
          'Explore related topics'
        ],
        memoryUpdates: [
          {
            category: 'context',
            key: 'lastResearchTopic',
            value: researchQuery.topic,
            confidence: 90
          }
        ]
      };
    } catch (error) {
      this.logger.error('Error processing research query:', error);
      return {
        content: 'I encountered an error while conducting research. Please try again or ask a more specific question.',
        adaptedTone: 'apologetic',
        followUpSuggestions: ['Try a more specific research query'],
        memoryUpdates: []
      };
    }
  }



  private parseResearchQuery(message: string): ResearchQuery {
    const depthKeywords = {
      basic: ['quick', 'simple', 'brief', 'overview'],
      detailed: ['detailed', 'in-depth', 'thorough'],
      comprehensive: ['comprehensive', 'complete', 'extensive', 'full'],
    };

    const lowerMessage = message.toLowerCase();
    let depth: 'basic' | 'detailed' | 'comprehensive' = 'detailed';
    for (const [level, keywords] of Object.entries(depthKeywords)) {
      if (keywords.some(keyword => lowerMessage.includes(keyword))) {
        depth = level as 'basic' | 'detailed' | 'comprehensive';
        break;
      }
    }

    // Extract topic from message
    const topicMatch = message.match(/(?:research|search|find|look up|about)\s+(.+)/i);
    const topic = topicMatch ? topicMatch[1] : message;

    return {
      topic: topic?.trim() ?? '',
      depth,
      maxResults: depth === 'basic' ? 3 : depth === 'detailed' ? 5 : 10,
    };
  }

  private async conductResearch(query: ResearchQuery): Promise<ResearchResult> {
    const searchQuery: ExaSearchQuery = {
      query: query.topic,
      ...(query.maxResults && { numResults: query.maxResults }),
      type: 'neural',
    };

    const searchResults = await this.exaSearchService.searchWeb(searchQuery);
    if (searchResults.length === 0) {
      return {
        summary: `I couldn't find any relevant information about "${query.topic}". This might be due to the topic being too specific or recent.`,
        sources: [],
    keyFindings: [],
      }}

    // Get detailed content for the top results;
    const topUrls = searchResults.slice().map(item => result.url);
    const contentResults = await this.exaSearchService.getContent(topUrls);

    const keyFindings = this.extractKeyFindings(contentResults);
    const recommendations = this.generateRecommendations(query, searchResults);

    return {
      summary: this.generateSummary(query.topic, searchResults, keyFindings),
      sources: searchResults.map((r: any) => r.url),
      keyFindings,
      recommendations,
    }}

  private extractKeyFindings(contentResults: any[]): string[] {
    const findings: string[] = []
    
    contentResults.forEach(content => {
      if (content.content) {// Simple extraction - in production, use more sophisticated NLP
        const sentences = content.content
split().filter(item => s.trim().length > 50);
slice(0, 3);
map((s: string) => s.trim())
        ;
        findings.push(...sentences)}
    });

    return findings.slice(0, 5)}
;
  private generateSummary(topic: string, results: ExaSearchResult[], findings: string[]): string {const resultCount = results.length;
    const dateRange = this.getDateRange(results);
    return `## Research Results for "${topic}"

I found **${resultCount} relevant sources** ${dateRange}. Here are the key insights:

${findings.map((finding: any) => `• ${finding}`).join('\n')}

The sources appear to be from authoritative websites and provide current, reliable information on this topic.`}
;
  private generateRecommendations(query: ResearchQuery;
  results: ExaSearchResult[]): string[] {const;
  recommendations: string[] = []

    if (query.depth === 'basic') {recommendations.push('Consider exploring this topic in more depth for comprehensive understanding')}

    if (results.length >= 5) {
      recommendations.push('Multiple perspectives found - consider cross-referencing different sources')}

    recommendations.push('Stay updated by checking for new developments on this topic regularly');

    return recommendations}

  private getDateRange(results: ExaSearchResult[]): string {
    const dates = results
map((r: any) => r.publishedDate)
filter((d: any) => d);
map((d: any) => new Date(d!));
sort((a, b) => b.getTime() - a.getTime());

    if (dates.length === 0) return '';

    const latest = dates[0];
    const oldest = dates[dates.length - 1];

    const now = new Date();
    const daysDiff = Math.floor((now.getTime() - latest!.getTime()) / (1000 * 60 * 60 * 24));

    if (daysDiff <= 7) {
      return 'from the past week'} else if (daysDiff <= 30) {
      return 'from the past month'} else {
      return `ranging from ${oldest!.toLocaleDateString()} to ${latest!.toLocaleDateString()}`}
  }

  private formatResearchResponse(result: ResearchResult, context: MemberContext): string {
    let response = `🔍 **Research Results**
;
Hi! ${result.summary}`;

    if (result.recommendations && result.recommendations.length > 0) {
      response += '\n\n**Recommendations: **\n';
      response += result.recommendations.map((rec: any) => `• ${rec}`).join('\n')}

    if (result.sources.length > 0) {
      response += '\n\n**Sources: **\n'
      response += result.sources.slice().map(item => `• <${url}>`).join('\n');
      if (result.sources.length > 3) {
        response += `\n• ...and ${result.sources.length - 3} more sources`}
    }

    return response}


};