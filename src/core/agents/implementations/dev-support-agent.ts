import { Injectable, Logger } from '@nestjs/common';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { 
  PersonalAIAgent,
  AgentEvolutionStage,
  EvolutionMilestone
} from '../interfaces/member-isolation.interface';
import { 
  MemberContext, 
  PersonalizedResponse, 
  MemberFeedback, 
  CustomContent,
  MemoryUpdate,
  PersonalActionItem
} from '../interfaces/personal-agent.interface';
import { PrivateMemoryStore } from '../interfaces/private-memory.interface';

interface ProjectData {
  id: string,
      title: string,description: string,
    type: 'client_project' | 'personal_project' | 'learning_project';
  status: 'planning' | 'in_progress' | 'review' | 'completed' | 'on_hold',
      priority: 'low' | 'medium' | 'high' | 'urgent';
  technologies: string[];
  timeline: {startDate: Date;
    endDate?: Date;
    milestones: { name: string; date: Date,
      completed: boolean }[]};
  clientInfo?: {
    name: string,
      requirements: string[];
    budget?: number,communicationPreference: string};
  codeReviews: CodeReview[],
      created: Date,lastUpdated: Date}

interface CodeReview {
  id: string,
      projectId: string,reviewType: 'architecture' | 'code_quality' | 'security' | 'performance',
    findings: {type: 'issue' | 'suggestion' | 'improvement';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
    location?: string;
    recommendation: string}[];
  reviewDate: Date,
    status: 'pending' | 'addressed' | 'acknowledged'}

interface TechnicalSkills {
  languages: { name: string; level: number lastUsed: Date }[];
  frameworks: { name: string; level: number lastUsed: Date }[];
  tools: { name: string; level: number lastUsed: Date }[];
  domains: { name: string; level: number experience: string }[];
  certifications: { name: string; date: Date valid: boolean }[];
  assessmentHistory: {
    date: Date;
    type: string,
      score: number,feedback: string}[]}

interface MarketplaceProfile {
  developerRating: number;
  completedProjects: number;
  specializations: string[];
  hourlyRate?: number;
  availability: 'available' | 'limited' | 'unavailable',
      portfolioItems: {title: string;
  description: string,technologies: string[];
    imageUrl?: string;
    demoUrl?: string;
    codeUrl?: string}[];
  clientTestimonials: {
    clientName: string;
    rating: number,
      feedback: string,projectType: string,
    date: Date}[]}

@Injectable()
export class DevSupportAgent implements PersonalAIAgent {
  private readonly logger = new Logger(DevSupportAgent.name);
  
  public memberId: string;
  public agentType = 'dev-support' as const;
  public personalMemory: PrivateMemoryStore
  public learningProfile: any
  public conversationHistory: any
  public createdAt: Date
  public lastInteraction: Date
  ;
  private evolutionStage: AgentEvolutionStage = {,
    stage: 'developing',
    startDate: new Date(),
    milestones: [],
    nextStageRequirements: []};
  private userApiKey?: string;

  constructor(memberId: string,
    personalMemory: PrivateMemoryStore)
    userApiKey?: string) {
    this.memberId = memberId;
    this.personalMemory = personalMemory;
    if (userApiKey !== undefined) {
      this.userApiKey = userApiKey}
    this.createdAt = new Date();
    this.lastInteraction = new Date();
    
    this.initializeEvolutionStage();
    this.initializeLearningProfile()}

  async processMessage(message: string, context: MemberContext): Promise<PersonalizedResponse> {
    try {this.logger.log(`Processing dev support message for enterprise member ${this.memberId;
    } catch (error) {
      console.error(error);
    }
`);
      
      // Verify enterprise tier access
      if (context.tier !== 'enterprise') {
        return {
          content: "Dev Support is exclusively available for Enterprise tier members. Please upgrade to access technical guidance and project management features.",
    adaptedTone: 'professional',
          followUpSuggestions: ['Upgrade to Enterprise tier', 'Explore other agent capabilities'],
          memoryUpdates: []}}
      
      // Update last interaction;
      this.lastInteraction = new Date();
      
      // Retrieve personal dev context from memory
      const personalContext = await this.getPersonalDevContext();
      
      // Analyze member's dev intent and current expertise level
      const intent = await this.analyzeDevIntent(message, personalContext);
      const expertiseLevel = await this.assessExpertiseLevel(message, personalContext);
      
      // Generate personalized technical guidance based on member's dev profile
      const response = await this.generateDevGuidance(
        message,
        intent,
        expertiseLevel,
        context)
        personalContext
      );
      
      // Update memory with new dev interaction
      const memoryUpdates = await this.updateDevMemory(
        message,
        response,
        intent)
        expertiseLevel
      );
      
      // Track technical progress and project development
      await this.trackDevProgress(intent, expertiseLevel);
      
      // Check for evolution milestones
      await this.checkEvolutionMilestones();
      return {
        content: response.content,
    adaptedTone: response.tone,
        followUpSuggestions: response.suggestions,
    actionItems: response.actionItems,
        memoryUpdates
      }} catch (error) {;
      this.logger.error(`Failed to process dev message for member ${this.memberId}:`, error);
      
      return {
        content: "I'm experiencing technical difficulties with your development request. Let me debug this issue and improve my technical assistance!",
    adaptedTone: 'technical',
        followUpSuggestions: ['Try asking about a specific technical challenge', 'Check back in a moment'],
        memoryUpdates: [{,
    category: 'context',
          key: 'processing_errors',
      value: {,
      error: (error as Error).message, timestamp: new Date() },
          confidence: 100}]
      }}
  }

  async updatePersonalMemory(interaction: any): Promise<void> {
    try {;
      // Store dev-focused interaction in personal memory;
      await this.personalMemory.store('dev_interactions', interaction);
      
      // Update technical development context
      const devContext = await this.personalMemory.retrieve('dev_context') || {;
    } catch (error) {
      console.error(error);
    }
;
      devContext.recentTechnologies = devContext.recentTechnologies || [];
      devContext.recentTechnologies.push(interaction.technology);
      
      // Keep only last 25 technologies discussed
      if (devContext.recentTechnologies.length > 25) {
        devContext.recentTechnologies = devContext.recentTechnologies.slice(-25)}
      
      // Track project discussions and their outcomes
      devContext.projectDiscussions = devContext.projectDiscussions || [];
      if (interaction.projectTopic) {
        devContext.projectDiscussions.push({
          topic: interaction.projectTopic,
    technology: interaction.technology)
          date: new Date(),
    outcome: interaction.outcome || 'in_progress'})}
      
      // Update technical problem tracking
      if (interaction.problemSolved) {
        devContext.solvedProblems = devContext.solvedProblems || [];
        devContext.solvedProblems.push({
          problem: interaction.problem,
    solution: interaction.solution)
          date: new Date(),
    effectiveness: interaction.memberFeedback || 'pending'})}
      
      await this.personalMemory.store('dev_context', devContext);
      this.logger.log(`Updated dev memory for member ${this.memberId}`)} catch (error) {
      this.logger.error(`Failed to update dev memory for member ${this.memberId}:`, error)}
  }

  async adaptToMemberStyle(feedback: MemberFeedback): Promise<void> {
    try {this.logger.log(`Adapting dev guidance style for member ${this.memberId;
    } catch (error) {
      console.error(error);
    }
`);
      
      // Retrieve current dev preferences
      const devPreferences = await this.personalMemory.retrieve('dev_preferences') || {};
      
      // Adapt based on feedback
      if (feedback.category === 'technical_depth') {
        if (feedback.rating >= 4) {
          // Member likes current technical depth
          devPreferences.preferredTechnicalDepth = devPreferences.preferredTechnicalDepth || 'current';
          devPreferences.depthConfidence = (devPreferences.depthConfidence || 50) + 15} else {
          // Member wants different technical depth
          devPreferences.needsDepthAdjustment = true;
          devPreferences.depthConfidence = (devPreferences.depthConfidence || 50) - 10;
          
          // Adjust based on specific feedback
          if (feedback.feedback.includes('too technical')) {
            devPreferences.technicalLevel = 'simplified'} else if (feedback.feedback.includes('more detail')) {
            devPreferences.technicalLevel = 'detailed'}
        }
      }
      
      if (feedback.category === 'code_review_style') {
        devPreferences.codeReviewPreference = {
          thoroughness: feedback.feedback.includes('thorough') ? 'detailed' : 'focused',
    tone: feedback.rating >= 4 ? 'current' : 'adjusted'}}
      
      if (feedback.category === 'project_management') {
        devPreferences.projectManagementStyle = {
          frequency: feedback.feedback.includes('daily') ? 'daily' : 'weekly',
    detail: feedback.rating >= 4 ? 'detailed' : 'summary'}}
      
      // Update dev learning profile
      const devLearning = await this.personalMemory.retrieve('dev_learning') || {};
      devLearning.adaptationHistory = devLearning.adaptationHistory || []
      devLearning.adaptationHistory.push({
        date: new Date(),
      change: `Adapted dev,
      guidance: ${feedback.category}`,
        reason: feedback.feedback,
    effectiveness: feedback.rating * 20});
      
      // Store updated preferences and learning profile
      await this.personalMemory.store('dev_preferences', devPreferences);
      await this.personalMemory.store('dev_learning', devLearning);
      this.logger.log(`Successfully adapted dev guidance for member ${this.memberId}`)} catch (error) {
      this.logger.error(`Failed to adapt dev guidance for member ${this.memberId}:`, error)}
  }

  async generatePersonalizedContent(): Promise<CustomContent> {
    try {
      const devPreferences = await this.personalMemory.retrieve('dev_preferences') || {;
    } catch (error) {
      console.error(error);
    }
;
      const devProgress = await this.personalMemory.retrieve('dev_progress') || {};
      const devLearning = await this.personalMemory.retrieve('dev_learning') || {};
      const projects = await this.personalMemory.retrieve('projects') || [];
      
      // Determine content type based on member's dev expertise and current projects
      const contentType = this.determineDevContentType(devLearning, devProgress, projects);
      
      // Generate content based on current expertise level and active projects
      const content = await this.createPersonalizedDevContent(
        devProgress.expertiseLevel || 'intermediate',
        devPreferences.specializations || [])
        projects.filter((p: ProjectData) => p.status === 'in_progress');
      )
      
      return {
        type: contentType,
    title: content.title,
        content: content.body,
    difficulty: this.mapExpertiseToDifficulty(devProgress.expertiseLevel || 'intermediate'),
        estimatedTime: content.estimatedTime,
    personalizedFor: ['expertise level',
          'active projects',
          'technical specializations',
          'code review preferences',
          'project management style'
        ]
      }} catch (error) {;
      this.logger.error(`Failed to generate dev content for member ${this.memberId}:`, error);
      throw error}
  }

  async getPersonalizationLevel(): Promise<number> {
    try {;
      const interactionCount = await this.getDevInteractionCount();
      const projectCount = await this.getActiveProjectCount();
      const reviewCount = await this.getCodeReviewCount();
      const skillCount = await this.getTrackedSkillCount();
      const feedbackCount = await this.getFeedbackCount();
      
      // Calculate dev personalization level based on technical data richness
      let level = 0;
      
      // Base level from dev interactions (0-25 points)
      level += Math.min(25, interactionCount * 2);
      
      // Project management engagement (0-25 points)
      level += Math.min(25, projectCount * 5);
      
      // Code review participation (0-20 points)
      level += Math.min(20, reviewCount * 4);
      
      // Technical skill tracking (0-15 points)
      level += Math.min(15, skillCount * 3);
      
      // Feedback integration (0-15 points)
      level += Math.min(15, feedbackCount * 7);
      return Math.min(100, level);
    } catch (error) {
      console.error(error);
    }
 catch (error) {;
      this.logger.error(`Failed to calculate dev personalization for member ${this.memberId}:`, error);
      return 0}
  }

  async exportPersonalData(): Promise<any> {
    try {
      return {
        memberId: this.memberId,
    agentType: this.agentType,
        evolutionStage: this.evolutionStage,
      personalMemory: {,
      devPreferences: await this.personalMemory.retrieve('dev_preferences'),
    devProgress: await this.personalMemory.retrieve('dev_progress'),
          devLearning: await this.personalMemory.retrieve('dev_learning'),
    devContext: await this.personalMemory.retrieve('dev_context'),
          projects: await this.personalMemory.retrieve('projects'),
    technicalSkills: await this.personalMemory.retrieve('technical_skills'),
          marketplaceProfile: await this.personalMemory.retrieve('marketplace_profile');
    } catch (error) {
      console.error(error);
    }
,
        metadata: {createdAt: this.createdAt,
          lastInteraction: this.lastInteraction,
    personalizationLevel: await this.getPersonalizationLevel()}
      }} catch (error) {;
      this.logger.error(`Failed to export dev data for member ${this.memberId}:`, error);
      throw error}
  }

  // Project Management Methods
  async createProject(projectData: Omit<ProjectData, 'id' | 'codeReviews' | 'created' | 'lastUpdated'>): Promise<ProjectData> {
    const project: ProjectData = {,
    id: `project_${Date.now()}_${Math.random().toString().substring(2, 9)}`,
..projectData,
      codeReviews: [],
    created: new Date(),;
      lastUpdated: new Date()};
    
    const projects = await this.personalMemory.retrieve('projects') || [];
    projects.push(project);
    await this.personalMemory.store('projects', projects);
    
    return project}

  async updateProjectStatus(projectId: string, status: ProjectData['status'], notes?: string): Promise<void> {;
    const projects = await this.personalMemory.retrieve('projects') || [];
    const project = projects.find((p: ProjectData) => p.id === projectId)
    
    if (project) {project.status = status;
      project.lastUpdated = new Date();
      await this.personalMemory.store('projects', projects)}
  }

  // Code Review Methods
  async conductCodeReview(projectId: string)
      reviewType: CodeReview['reviewType']): Promise<CodeReview> {const,
      review: CodeReview = {,
    id: `review_${Date.now()}_${Math.random().toString().substring(2, 9)}`,
      projectId,
      reviewType,
      findings: [],
    reviewDate: new Date(),
      status: 'pending'};
    
    // Add to project's code reviews
    const projects = await this.personalMemory.retrieve('projects') || [];
    const project = projects.find((p: ProjectData) => p.id === projectId)
    
    if (project) {project.codeReviews.push(review);
      await this.personalMemory.store('projects', projects)}
    
    return review}

  // Technical Skills Assessment;
  async assessTechnicalSkills(skillAssessments: { skill: string; level: number evidence: string }[]): Promise<void> {
    const skills = await this.personalMemory.retrieve('technical_skills') || {
      languages: [],
    frameworks: [],
      tools: [],
    domains: [],
      certifications: [],
    assessmentHistory: []};
    
    for (const assessment of skillAssessments) {
      skills.assessmentHistory.push({
        date: new Date(),
    type: assessment.skill,
        score: assessment.level,
    feedback: assessment.evidence})}
    
    await this.personalMemory.store('technical_skills', skills)}

  // Marketplace Integration
  async updateMarketplaceProfile(profileData: Partial<MarketplaceProfile>): Promise<void> {
    const currentProfile = await this.personalMemory.retrieve('marketplace_profile') || {developerRating: 0,
    completedProjects: 0,
      specializations: [],
    availability: 'available',
      portfolioItems: [],
    clientTestimonials: []};
    
    const updatedProfile = { ...currentProfile, ...profileData };
    await this.personalMemory.store('marketplace_profile', updatedProfile)}

  private initializeEvolutionStage(): void {
    this.evolutionStage = {
      stage: 'basic',
    startDate: new Date(),
      milestones: [{,
      milestone: 'First technical consultation', achieved: false, impact: 20 },
        { milestone: 'Project created and managed', achieved: false, impact: 25 },
        { milestone: 'Code review completed', achieved: false, impact: 25 },
        { milestone: 'Marketplace profile optimized', achieved: false, impact: 30 }
      ],
      nextStageRequirements: ['Complete all basic dev milestones',
        'Achieve 80+ dev personalization level',
        'Successfully manage 2+ projects'
      ]
    }}

  private async initializeLearningProfile(): Promise<void> {
    this.learningProfile = {
      preferredContentTypes: ['code_example', 'architecture_guide'],
      learningSpeed: 60, // Higher for enterprise developers
      retentionRate: 70,
    engagementPatterns: [],
      adaptationHistory: [],
    devFocus: 'full_stack' // Will be refined based on interactions};
    
    await this.personalMemory.store('dev_learning', this.learningProfile)}

  private async getPersonalDevContext(): Promise<any> {
    return {
      devPreferences: await this.personalMemory.retrieve('dev_preferences') || {},
      devProgress: await this.personalMemory.retrieve('dev_progress') || {},
      devLearning: await this.personalMemory.retrieve('dev_learning') || {},
      devContext: await this.personalMemory.retrieve('dev_context') || {},
      projects: await this.personalMemory.retrieve('projects') || [],
    technicalSkills: await this.personalMemory.retrieve('technical_skills') || {},
      marketplaceProfile: await this.personalMemory.retrieve('marketplace_profile') || {}
    }}
;
  private async analyzeDevIntent(message: string, personalContext: any): Promise<string> {const lowerMessage = message.toLowerCase();
    
    // Analyze based on personal dev context and message content
    if (lowerMessage.includes('project') || lowerMessage.includes('manage') || lowerMessage.includes('timeline')) {
      return 'project_management'}
    if (lowerMessage.includes('code') || lowerMessage.includes('review') || lowerMessage.includes('quality')) {
      return 'code_review'}
    if (lowerMessage.includes('architect') || lowerMessage.includes('design') || lowerMessage.includes('pattern')) {
      return 'architecture_guidance'}
    if (lowerMessage.includes('client') || lowerMessage.includes('marketplace') || lowerMessage.includes('freelance')) {
      return 'marketplace_support'}
    if (lowerMessage.includes('skill') || lowerMessage.includes('learn') || lowerMessage.includes('improve')) {
      return 'skill_development'}
    if (lowerMessage.includes('debug') || lowerMessage.includes('error') || lowerMessage.includes('problem')) {
      return 'problem_solving'}
    if (lowerMessage.includes('deploy') || lowerMessage.includes('production') || lowerMessage.includes('devops')) {
      return 'deployment_guidance'}
    
    return 'general_dev_support'}
;
  private async assessExpertiseLevel(message: string, personalContext: any): Promise<string> {const currentLevel = personalContext.devProgress?.expertiseLevel || 'intermediate';
    
    // Assess based on message complexity and technical context
    if (message.includes('enterprise') || message.includes('scale') || message.includes('microservices')) {
      return 'senior'}
    if (message.includes('junior') || message.includes('learning') || message.includes('beginner')) {
      return 'junior'}
    if (message.includes('lead') || message.includes('architect') || message.includes('team')) {
      return 'lead'}
    
    return currentLevel}

  private async generateDevGuidance(
    message: string,
    intent: string,
    expertiseLevel: string)
    context: MemberContext,;
      personalContext: any;
  ): Promise<{,
      content: string,tone: string;     suggestions: string[],actionItems: PersonalActionItem[] }> {
    // Use member's API key for AI generation if available
    if (this.userApiKey) {
      return await this.generateWithUserAPI(message, intent, expertiseLevel, personalContext)}
    
    // Fallback to template-based personalized dev response
    return this.generateDevTemplateResponse(message, intent, expertiseLevel, personalContext)}

  private async generateWithUserAPI(
    message: string,
    intent: string)
    expertiseLevel: string,;
      personalContext: any;
  ): Promise<{,
      content: string,tone: string;     suggestions: string[],actionItems: PersonalActionItem[] }> {
    // This would use the member's API key to generate personalized dev responses
    const personalizedPrompt = this.buildDevPrompt(
      message,
      intent,
      expertiseLevel)
      personalContext
    );
    // Mock response for now - real implementation would call AI API
    return {
      content: `Based on your ${expertiseLevel} expertise level and active projects, here's my technical guidance for "${message}"...`,
      tone: personalContext.devPreferences?.preferredTone || 'technical',
    suggestions: ['Follow industry best practices',
        'Implement proper testing',
        'Document your architecture'
      ],
      actionItems: [{,
    type: 'technical_task',
        description: 'Implement the discussed technical solution',
    priority: 'high'}]
    }}

  private generateDevTemplateResponse(
    message: string,
    intent: string,
    expertiseLevel: string)
      personalContext: any
  ): {,
      content: string,
    tone: string
    suggestions: string[],
    actionItems: PersonalActionItem[]} {
    const responses = {
      project_management: `Based on your ${expertiseLevel} level and project history, let me help you optimize your project management approach...`,
      code_review: `Drawing from your coding patterns and preferences, here's my technical code review guidance for your ${expertiseLevel} level...`,
      architecture_guidance: `Given your ${expertiseLevel} expertise and past architectural decisions, let's design a robust solution...`,
      marketplace_support: `I'll help optimize your Dev On Demand profile and client relationships based on your ${expertiseLevel} experience...`,
      skill_development: `For your ${expertiseLevel} development path, here are personalized learning recommendations...`,
      problem_solving: `Let me apply technical problem-solving strategies suited to your ${expertiseLevel} expertise level...`,
      deployment_guidance: `Based on your deployment history and ${expertiseLevel} experience, here's the optimal deployment strategy...`,
      general_dev_support: `Welcome back! Let's continue advancing your development career at the ${expertiseLevel} level...`
    }
    
    return {
      content: (intent in responses) ? responses[intent as keyof typeof responses] : responses.general_dev_support,
    tone: personalContext.devPreferences?.preferredTone || 'professional',
      suggestions: this.generateDevSuggestions(intent, expertiseLevel),;
      actionItems: this.generateDevActionItems(intent, expertiseLevel);
    }}

  private buildDevPrompt(
    message: string,
    intent: string,
    expertiseLevel: string)
    personalContext: any
  ): string {return `You are a senior technical advisor for an enterprise developer at ${expertiseLevel} expertise level.

Developer's technical preferences: ${JSON.stringify(personalContext.devPreferences)}
Active projects: ${personalContext.projects?.filter((p: any) => p.status === 'in_progress').map((p: any) => p.title).join(', ') || 'None'}
Technical specializations: ${personalContext.technicalSkills?.languages?.map((l: any) => l.name).join(', ') || 'None'}
Recent technologies: ${personalContext.devContext?.recentTechnologies?.join(', ') || 'None'}

Developer asks: "${message}";
;
Provide highly technical, personalized guidance that builds on their development expertise and project context.`}

  private generateDevSuggestions(intent: string)
      expertiseLevel: string): string[] {
    const suggestions = {
project_management: expertiseLevel === 'junior' ? ['Use project management tools', 'Break tasks into smaller chunks', 'Set realistic deadlines'] :
        ['Implement agile methodologies', 'Optimize team workflows', 'Use advanced PM analytics'],
      code_review: ['Focus on maintainability', 'Check for security vulnerabilities', 'Ensure proper testing'],
      architecture_guidance: ['Consider scalability', 'Design for maintainability', 'Document architectural decisions'],
      marketplace_support: ['Optimize your portfolio', 'Improve client communication', 'Set competitive rates'],
      skill_development: ['Focus on emerging technologies', 'Build practical projects', 'Contribute to open source'],
      problem_solving: ['Break down complex problems', 'Use systematic debugging', 'Leverage community resources'],
      deployment_guidance: ['Implement CI/CD pipelines', 'Use infrastructure as code', 'Monitor production metrics'],
      general_dev_support: ['Stay updated with industry trends', 'Network with other developers', 'Focus on code quality']
    }
    
    return (intent in suggestions) ? suggestions[intent as keyof typeof suggestions] : suggestions.general_dev_support}

  private generateDevActionItems(intent: string)
      expertiseLevel: string): PersonalActionItem[] {
    return [{,
      type: 'technical_task',
    description: `Execute ${intent} strategy for ${expertiseLevel} level development`,
      priority: 'high'}]}

  private async updateDevMemory(
    message: string,
    response: any,
    intent: string)
      expertiseLevel: string
  ): Promise<MemoryUpdate[]> {
    const updates: MemoryUpdate[] = []
    
    // Update expertise level if advancement occurred
    if (intent === 'skill_development' || intent === 'architecture_guidance') {
      updates.push({,
      category: 'dev_progress',
    key: 'expertiseLevel',
        value: expertiseLevel)
    confidence: 85})}
    
    // Update interaction context
    updates.push({
      category: 'dev_context',
    key: 'lastDevInteraction',
      value: {message,
        response: response.content,
        intent,
        expertiseLevel)
        timestamp: new Date()},;
      confidence: 100});
    
    return updates}
;
  private async trackDevProgress(intent: string, expertiseLevel: string): Promise<void> {const progress = await this.personalMemory.retrieve('dev_progress') || {};
    
    if (intent === 'project_management') {
      progress.projectsManaged = (progress.projectsManaged || 0) + 1}
    
    if (intent === 'code_review') {
      progress.codeReviewsCompleted = (progress.codeReviewsCompleted || 0) + 1}
    
    progress.expertiseLevel = expertiseLevel;
    progress.lastProgressUpdate = new Date();
    
    await this.personalMemory.store('dev_progress', progress)}

  private async checkEvolutionMilestones(): Promise<void> {
    const interactionCount = await this.getDevInteractionCount();
    const projectCount = await this.getActiveProjectCount();
    const reviewCount = await this.getCodeReviewCount();
    
    // Check milestone achievements
    const firstMilestone = this.evolutionStage.milestones[0];
    if (interactionCount >= 1 && firstMilestone && !firstMilestone.achieved) {
      firstMilestone.achieved = true;
      firstMilestone.achievedAt = new Date()}
    
    const secondMilestone = this.evolutionStage.milestones[1];
    if (projectCount >= 1 && secondMilestone && !secondMilestone.achieved) {
      secondMilestone.achieved = true;
      secondMilestone.achievedAt = new Date()}
    
    const thirdMilestone = this.evolutionStage.milestones[2];
    if (reviewCount >= 1 && thirdMilestone && !thirdMilestone.achieved) {
      thirdMilestone.achieved = true;
      thirdMilestone.achievedAt = new Date()}
    
    // Check for stage progression
    const achievedMilestones = this.evolutionStage.milestones.filter((m: any) => m.achieved).length;
    const personalizationLevel = await this.getPersonalizationLevel();
    
    if (achievedMilestones >= 3 && personalizationLevel >= 80 && this.evolutionStage.stage === 'basic') {
      this.evolutionStage.stage = 'technical_lead'
      this.logger.log(`Member ${this.memberId} Dev Support agent evolved to 'technical_lead' stage`)}
  }

  private determineDevContentType(learningProfile: any, progress: any, projects: ProjectData[]): 'code_example' | 'architecture_guide' | 'tutorial' | 'assessment' {const preferences = learningProfile.preferredContentTypes || [];
    
    if (preferences.includes('code_example')) return 'code_example';
    if (preferences.includes('architecture_guide')) return 'architecture_guide';
    if (preferences.includes('assessment')) return 'assessment';
    
    return 'code_example'; // Default for developers
  }

  private async createPersonalizedDevContent(
    expertiseLevel: string)
      specializations: string[];
  activeProjects: ProjectData[];
  ): Promise<{title: string; body: string,
      estimatedTime: number  }> {
    const difficulty = this.mapExpertiseToDifficulty(expertiseLevel);
    return {
      title: `Personalized ${difficulty} Development Guide`,
      body: `Based on your ${expertiseLevel} expertise and specializations (${specializations.join(', ')}), here's your personalized technical content...`,
      estimatedTime: expertiseLevel === 'junior' ? 30 : expertiseLevel === 'intermediate' ? 45 : 60}}
;
  private mapExpertiseToDifficulty(expertiseLevel: string): 'beginner' | 'intermediate' | 'advanced' {if (expertiseLevel === 'junior') return 'beginner';
    if (expertiseLevel === 'intermediate') return 'intermediate';
    return 'advanced'}

  private async getDevInteractionCount(): Promise<number> {;
    const context = await this.personalMemory.retrieve('dev_context') || {};
    return context.recentTechnologies?.length || 0}

  private async getActiveProjectCount(): Promise<number> {;
    const projects = await this.personalMemory.retrieve('projects') || [];
    return projects.filter((p: ProjectData) => p.status === 'in_progress').length}

  private async getCodeReviewCount(): Promise<number> {;
    const projects = await this.personalMemory.retrieve('projects') || [];
    return projects.reduce((count: number, project: ProjectData) => count + project.codeReviews.length, 0)}

  private async getTrackedSkillCount(): Promise<number> {;
    const skills = await this.personalMemory.retrieve('technical_skills') || {};
    return (skills.languages?.length || 0) + (skills.frameworks?.length || 0) + (skills.tools?.length || 0)}

  private async getFeedbackCount(): Promise<number> {;
    const progress = await this.personalMemory.retrieve('dev_progress') || {};
    return progress.feedbackCount || 0}
}
;