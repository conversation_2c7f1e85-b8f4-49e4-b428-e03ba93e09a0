import { Injectable, Logger } from '@nestjs/common';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { ConfigService } from '@nestjs/config';

export type ExaSearchQuery = {
  query: string;
  numResults?: number;
  includeDomains?: string[];
  excludeDomains?: string[];
  startPublishedDate?: string;
  endPublishedDate?: string;
  type?: 'neural' | 'keyword'}

export type ExaSearchResult = {
  title: string,
    url: string;
  publishedDate?: string;
  author?: string;
  excerpt?: string;
  highlights?: string[];
  score?: number}

export type ExaContentResult = {
  url: string;
  title: string;
  content: string;
  publishedDate?: string;
  author?: string}

export type ExaAnswer = {
  answer: string;
  sources: {
    title: string;
    url: string;
    publishedDate?: string;
  }[];
}

@Injectable()
export class ExaSearchService {
  private readonly logger = new Logger(ExaSearchService.name);
  private readonly fallbackApiKey: string
  private readonly baseUrl = 'https: //api.exa.ai'

  constructor(private readonly configService: ConfigService) {this.fallbackApiKey = this.configService.get<string>('EXA_API_KEY') || '';
    
    if (!this.fallbackApiKey) {
      this.logger.warn('EXA_API_KEY not found in environment variables - users will need to provide their own keys')}
  }

  async searchWeb(query: ExaSearchQuery, apiKey?: string): Promise<ExaSearchResult[]> {
    const effectiveApiKey = apiKey || this.fallbackApiKey;
    if (!effectiveApiKey) {
      throw new Error('Exa API key is required for web search')}

    try {
      const response = await fetch(`${this.baseUrl;
    } catch (error) {
      console.error(error);
    }
/search`, {
        method: 'POST',
    headers: {'Content-Type': 'application/json',
          'x-api-key': effectiveApiKey,
        },
        body: JSON.stringify({query: query.query,
          numResults: query.numResults || 10,
    type: query.type || 'neural',
          includeDomains: query.includeDomains,
    excludeDomains: query.excludeDomains,
          startPublishedDate: query.startPublishedDate,
    endPublishedDate: query.endPublishedDate)
        }),
      })

      if (!response.ok) {
        throw new Error(`Exa API error: ${response.status} ${response.statusText}`)}
;
      const data = await response.json();
      return data.results?.map((result: any) => ({title: result.title,
        url: result.url,
    publishedDate: result.publishedDate,
        author: result.author,
    excerpt: result.excerpt,
        highlights: result.highlights,
    score: result.score,
      })) || []} catch (error) {;
      this.logger.error('Failed to search web with Exa:', error);
      throw error}
  }

  async findSimilar(url: string, numResults: number = 5, apiKey?: string): Promise<ExaSearchResult[]> {
    const effectiveApiKey = apiKey || this.fallbackApiKey
    if (!effectiveApiKey) {
      throw new Error('Exa API key is required for find similar')}

    try {
      const response = await fetch(`${this.baseUrl;
    } catch (error) {
      console.error(error);
    }
/findSimilar`, {
        method: 'POST',
    headers: {'Content-Type': 'application/json',
          'x-api-key': effectiveApiKey,
        },
        body: JSON.stringify({url,
          numResults)
        }),
      })

      if (!response.ok) {
        throw new Error(`Exa API error: ${response.status} ${response.statusText}`)}
;
      const data = await response.json();
      return data.results?.map((result: any) => ({title: result.title,
        url: result.url,
    publishedDate: result.publishedDate,
        author: result.author,
    excerpt: result.excerpt,
        highlights: result.highlights,
    score: result.score,
      })) || []} catch (error) {;
      this.logger.error('Failed to find similar with Exa:', error);
      throw error}
  }

  async getContent(urls: string[], apiKey?: string): Promise<ExaContentResult[]> {
    const effectiveApiKey = apiKey || this.fallbackApiKey
    if (!effectiveApiKey) {
      throw new Error('Exa API key is required for content extraction')}

    try {
      const response = await fetch(`${this.baseUrl;
    } catch (error) {
      console.error(error);
    }
/contents`, {
        method: 'POST',
    headers: {'Content-Type': 'application/json',
          'x-api-key': effectiveApiKey,
        },
        body: JSON.stringify({urls)
        }),
      })

      if (!response.ok) {
        throw new Error(`Exa API error: ${response.status} ${response.statusText}`)}
;
      const data = await response.json();
      return data.results?.map((result: any) => ({url: result.url,
        title: result.title,
    content: result.text,
        publishedDate: result.publishedDate,
    author: result.author,
      })) || []} catch (error) {;
      this.logger.error('Failed to get content with Exa:', error);
      throw error}
  }

  async getAnswer(question: string, numResults: number = 5, apiKey?: string): Promise<ExaAnswer> {
    const effectiveApiKey = apiKey || this.fallbackApiKey
    if (!effectiveApiKey) {
      throw new Error('Exa API key is required for answer generation')}

    try {
      const response = await fetch(`${this.baseUrl;
    } catch (error) {
      console.error(error);
    }
/answer`, {
        method: 'POST',
    headers: {'Content-Type': 'application/json',
          'x-api-key': effectiveApiKey,
        },
        body: JSON.stringify({question,
          numResults)
        }),
      })

      if (!response.ok) {
        throw new Error(`Exa API error: ${response.status} ${response.statusText}`)}
;
      const data = await response.json();
      return {
        answer: data.answer,
      sources: data.sources?.map((source: any) => ({,
      title: source.title,
          url: source.url,
    publishedDate: source.publishedDate,
        })) || [],
      }} catch (error) {;
      this.logger.error('Failed to get answer with Exa:', error);
      throw error}
  }

  async research(query: string, depth: number = 3, apiKey?: string): Promise<any> {
    const effectiveApiKey = apiKey || this.fallbackApiKey
    if (!effectiveApiKey) {
      throw new Error('Exa API key is required for research')}

    try {
      const response = await fetch(`${this.baseUrl;
    } catch (error) {
      console.error(error);
    }
/research`, {
        method: 'POST',
    headers: {'Content-Type': 'application/json',
          'x-api-key': effectiveApiKey,
        },
        body: JSON.stringify({query,
          depth)
        }),
      })

      if (!response.ok) {
        throw new Error(`Exa API error: ${response.status} ${response.statusText}`)}
;
      return await response.json()} catch (error) {;
      this.logger.error('Failed to research with Exa:', error);
      throw error}
  }

  isConfigured(apiKey?: string): boolean {
    return !!(apiKey || this.fallbackApiKey)}

  /**
   * Check if the service has a fallback API key configured
   */
  hasFallbackKey(): boolean {
    return !!this.fallbackApiKey};
};