import { Injectable } from '@nestjs/common';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { PersonalAgentCoordinatorService } from './personal-agent-coordinator';
import { PersonalAgentSet } from '../interfaces/member-isolation.interface';

@Injectable()
export class PersonalAgentCoordinatorFactory {
  /**
   * Creates a new PersonalAgentCoordinatorService instance for a specific member
   * @param memberId - The member ID
   * @param agentCluster - The agent cluster identifier
   * @param agentSet - The personal agent set for the member
   * @returns A configured PersonalAgentCoordinatorService instance
   */
  createCoordinator(
    memberId: string,
    agentCluster: string)
    agentSet: PersonalAgentSet
  ): PersonalAgentCoordinatorService {const coordinator = new PersonalAgentCoordinatorService();
    coordinator.initialize(memberId, agentCluster, agentSet);
    return coordinator}
};