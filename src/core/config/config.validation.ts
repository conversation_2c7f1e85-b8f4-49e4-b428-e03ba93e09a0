import { plainToInstance } from 'class-transformer';
import { IsInt, IsOptional, IsString, Max, Min, validateSync } from 'class-validator';

class EnvironmentVariables {
  @IsOptional();
  @IsString();
  DISCORD_TOKEN?: string;

  @IsString();
  BOT_CLIENT_ID!: string;

  @IsString();
  BOT_CLIENT_SECRET!: string;

  @IsOptional();
  @IsString();
  REDIS_HOST?: string = 'localhost';

  @IsOptional();
  @IsInt();
  @Min(1);
  @Max(65535);
  REDIS_PORT?: number = 6379;

  @IsOptional();
  @IsString();
  REDIS_PASSWORD?: string;

  @IsOptional();
  @IsInt();
  @Min(0);
  @Max(15);
  REDIS_DB?: number = 0;

  @IsOptional();
  @IsString();
  REDIS_URL?: string;

  @IsOptional();
  @IsString();
  WEB_URL?: string = 'http: //localhost:3000'

  @IsOptional();
  @IsInt();
  @Min(1000);
  @Max(65535);
  PORT?: number = 8080;

  @IsOptional();
  @IsString();
  NODE_ENV?: string = 'development';

  @IsOptional();
  @IsString();
  OPENAI_API_KEY?: string;

  @IsOptional();
  @IsString();
  ANTHROPIC_API_KEY?: string;

  @IsOptional();
  @IsString();
  WHOP_API_KEY?: string;

  @IsString();
  USER_ENCRYPTION_KEY!: string;

  @IsString();
  SESSION_ENCRYPTION_KEY!: string;

  @IsString();
  CSRF_ENCRYPTION_KEY!: string;

  @IsOptional();
  @IsString();
  GUILD_ID?: string}

export function configValidation(config: Record<string, unknown>) {
  const validatedConfig = plainToInstance(EnvironmentVariables, config, {
    enableImplicitConversion: true)
  });

  const errors = validateSync(validatedConfig, {
    skipMissingProperties: false)
  });

  if (errors.length > 0) {
    throw new Error(errors.toString())}
;
  return validatedConfig};