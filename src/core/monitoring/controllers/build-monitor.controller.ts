import { <PERSON>, Get, Post, Query, <PERSON>s, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { Response } from 'express';
import { BuildMonitorService } from '../build-monitor.service';
import { DashboardService } from '../dashboard.service';
import { BuildAnalyzerService } from '../build-analyzer.service';

@Controller('api/build-monitor')
export class BuildMonitorController {
  private readonly logger = new Logger(BuildMonitorController.name);

  constructor(
    private buildMonitorService: BuildMonitorService,
    private dashboardService: DashboardService,
    private buildAnalyzerService: BuildAnalyzerService
  ) {}

  @Get('status')
  async getBuildStatus() {
    try {
      const currentMetrics = await this.buildMonitorService.getCurrentMetrics();
      const agentStatus = await this.buildMonitorService.getAgentCoordinationStatus();
      const dashboardStatus = await this.dashboardService.getStatus();

      return {
        timestamp: new Date(),
        buildMetrics: currentMetrics,
        agentCoordination: agentStatus,
        dashboard: dashboardStatus
      };
    } catch (error) {
      this.logger.error(`Failed to get build status: ${error.message}`);
      throw new HttpException('Failed to retrieve build status', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('progress')
  async getBuildProgress() {
    try {
      return await this.buildMonitorService.generateProgressReport();
    } catch (error) {
      this.logger.error(`Failed to get progress report: ${error.message}`);
      throw new HttpException('Failed to retrieve progress report', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('history')
  async getBuildHistory(@Query('limit') limit?: string) {
    try {
      const historyLimit = limit ? parseInt(limit, 10) : 100;
      const history = this.buildMonitorService.getHistoricalData(historyLimit);
      
      return {
        total: history.length,
        limit: historyLimit,
        history
      };
    } catch (error) {
      this.logger.error(`Failed to get build history: ${error.message}`);
      throw new HttpException('Failed to retrieve build history', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('analysis')
  async getComprehensiveAnalysis() {
    try {
      const analysis = await this.buildAnalyzerService.performComprehensiveAnalysis();
      const fileRankings = await this.buildAnalyzerService.getFileRankings();

      return {
        timestamp: new Date(),
        analysis,
        fileRankings: fileRankings.slice(0, 20) // Top 20 files
      };
    } catch (error) {
      this.logger.error(`Failed to get comprehensive analysis: ${error.message}`);
      throw new HttpException('Failed to retrieve analysis', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('dashboard')
  async getDashboardHTML(@Res() res: Response) {
    try {
      const html = await this.dashboardService.generateHTMLDashboard();
      res.setHeader('Content-Type', 'text/html');
      res.setHeader('Cache-Control', 'no-cache');
      res.send(html);
    } catch (error) {
      this.logger.error(`Failed to generate dashboard: ${error.message}`);
      res.status(500).send('<h1>Dashboard temporarily unavailable</h1>');
    }
  }

  @Post('dashboard/save')
  async saveDashboard(@Query('path') outputPath?: string) {
    try {
      const savedPath = await this.dashboardService.saveDashboardHTML(outputPath);
      return {
        success: true,
        message: 'Dashboard saved successfully',
        path: savedPath
      };
    } catch (error) {
      this.logger.error(`Failed to save dashboard: ${error.message}`);
      throw new HttpException('Failed to save dashboard', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('metrics/export')
  async exportMetrics(@Query('path') outputPath?: string) {
    try {
      const exportPath = await this.buildMonitorService.exportMetricsData(outputPath);
      return {
        success: true,
        message: 'Metrics exported successfully',
        path: exportPath
      };
    } catch (error) {
      this.logger.error(`Failed to export metrics: ${error.message}`);
      throw new HttpException('Failed to export metrics', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('analysis/export')
  async exportAnalysis(@Query('path') outputPath?: string) {
    try {
      const exportPath = await this.buildAnalyzerService.exportAnalysis(outputPath);
      return {
        success: true,
        message: 'Analysis exported successfully',
        path: exportPath
      };
    } catch (error) {
      this.logger.error(`Failed to export analysis: ${error.message}`);
      throw new HttpException('Failed to export analysis', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('agent/progress')
  async reportAgentProgress(
    @Query('agent') agentType: string,
    @Query('status') taskStatus: 'active' | 'completed' | 'failed',
    @Query('progress') progress: string
  ) {
    try {
      if (!agentType || !taskStatus || !progress) {
        throw new HttpException('Missing required parameters', HttpStatus.BAD_REQUEST);
      }

      const progressValue = parseInt(progress, 10);
      if (isNaN(progressValue) || progressValue < 0 || progressValue > 100) {
        throw new HttpException('Invalid progress value', HttpStatus.BAD_REQUEST);
      }

      this.buildMonitorService.reportAgentProgress(agentType, taskStatus, progressValue);
      
      // Log activity to dashboard
      this.dashboardService.logActivity(
        'agent_progress',
        `Agent ${agentType} reported ${taskStatus} status with ${progressValue}% progress`,
        taskStatus === 'failed' ? 'error' : taskStatus === 'completed' ? 'success' : 'info'
      );

      return {
        success: true,
        message: 'Agent progress reported successfully'
      };
    } catch (error) {
      this.logger.error(`Failed to report agent progress: ${error.message}`);
      throw error;
    }
  }

  @Get('realtime/clients')
  async getConnectedClients() {
    return {
      connectedClients: this.dashboardService.getConnectedClientsCount(),
      timestamp: new Date()
    };
  }

  @Post('activity/log')
  async logActivity(
    @Query('type') type: string,
    @Query('message') message: string,
    @Query('severity') severity?: 'info' | 'warning' | 'success' | 'error'
  ) {
    try {
      if (!type || !message) {
        throw new HttpException('Missing required parameters', HttpStatus.BAD_REQUEST);
      }

      const validTypes = ['error_fixed', 'milestone', 'agent_progress', 'build_success', 'build_failure'];
      if (!validTypes.includes(type)) {
        throw new HttpException('Invalid activity type', HttpStatus.BAD_REQUEST);
      }

      this.dashboardService.logActivity(type as any, message, severity || 'info');

      return {
        success: true,
        message: 'Activity logged successfully'
      };
    } catch (error) {
      this.logger.error(`Failed to log activity: ${error.message}`);
      throw error;
    }
  }

  @Get('health')
  async getHealthCheck() {
    try {
      const currentMetrics = await this.buildMonitorService.getCurrentMetrics();
      const isHealthy = currentMetrics.buildTime < 120000; // Less than 2 minutes
      
      return {
        status: isHealthy ? 'healthy' : 'degraded',
        timestamp: new Date(),
        metrics: {
          totalErrors: currentMetrics.totalErrors,
          buildTime: currentMetrics.buildTime,
          modifiedFiles: currentMetrics.modifiedFiles
        },
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage()
      };
    } catch (error) {
      this.logger.error(`Health check failed: ${error.message}`);
      return {
        status: 'unhealthy',
        timestamp: new Date(),
        error: error.message,
        uptime: process.uptime()
      };
    }
  }

  @Post('clear-cache')
  async clearAnalysisCache() {
    try {
      this.buildAnalyzerService.clearCache();
      return {
        success: true,
        message: 'Analysis cache cleared successfully'
      };
    } catch (error) {
      this.logger.error(`Failed to clear cache: ${error.message}`);
      throw new HttpException('Failed to clear cache', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('summary')
  async getBuildSummary() {
    try {
      const currentMetrics = await this.buildMonitorService.getCurrentMetrics();
      const history = this.buildMonitorService.getHistoricalData(10);
      const analysis = await this.buildAnalyzerService.performComprehensiveAnalysis();

      // Calculate summary statistics
      const baseline = history.length > 0 ? history[0] : currentMetrics;
      const totalReduction = baseline.totalErrors - currentMetrics.totalErrors;
      const reductionPercent = baseline.totalErrors > 0 
        ? (totalReduction / baseline.totalErrors) * 100 
        : 100;

      const avgBuildTime = history.length > 0 
        ? history.reduce((sum, h) => sum + h.buildTime, 0) / history.length 
        : currentMetrics.buildTime;

      return {
        timestamp: new Date(),
        summary: {
          currentErrors: currentMetrics.totalErrors,
          startingErrors: baseline.totalErrors,
          errorsReduced: totalReduction,
          reductionPercent: Math.round(reductionPercent * 100) / 100,
          progressPercent: currentMetrics.modifiedFiles > 0 
            ? Math.round((currentMetrics.completedFiles / currentMetrics.modifiedFiles) * 100)
            : 100,
          avgBuildTime: Math.round(avgBuildTime / 1000), // in seconds
          estimatedCompletion: analysis.predictions.estimatedCompletion,
          riskLevel: analysis.predictions.riskAssessment
        },
        topErrorTypes: Object.entries(currentMetrics.errorsByType)
          .sort(([, a], [, b]) => (b as number) - (a as number))
          .slice(0, 5),
        topRecommendations: analysis.recommendations.priority.slice(0, 3)
      };
    } catch (error) {
      this.logger.error(`Failed to get build summary: ${error.message}`);
      throw new HttpException('Failed to retrieve build summary', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}