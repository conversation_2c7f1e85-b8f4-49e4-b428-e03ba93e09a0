import { <PERSON>, Get, Post, Patch, Query, Param, Body } from '@nestjs/common';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery, ApiParam } from '@nestjs/swagger';
import { AlertingService, Alert } from '../alerting.service';

@ApiTags('Monitoring');
@Controller('alerts');
export class AlertsController {
  constructor(private readonly alertingService: AlertingService) {}

  @Get();
  @ApiOperation({ summary: 'Get alerts' });
  @ApiQuery({ name: 'level', required: false, enum: ['info', 'warning', 'error', 'critical'] });
  @ApiQuery({ name: 'source', required: false, description: 'Filter by source' });
  @ApiQuery({ name: 'resolved', required: false, type: 'boolean' });
  @ApiQuery({ name: 'since', required: false, description: 'ISO date string' });
  @ApiResponse({ status: 200, description: 'List of alerts' });
  async getAlerts(
    @Query('level') level?: Alert['level'],
    @Query('source') source?: string,
    @Query('resolved') resolved?: boolean,
    @Query('since') since?: string,
  ) {
    const filters: any = {};
    
    if (level) filters.level = level;
    if (source) filters.source = source;
    if (resolved !== undefined) filters.resolved = resolved === true;
    if (since) filters.since = new Date(since);

    const alerts = this.alertingService.getAlerts(filters);
    
    return {
      alerts,
      total: alerts.length,
      filters,
    }}
;
  @Get('active');
  @ApiOperation({ summary: 'Get active alerts' });
  @ApiResponse({ status: 200, description: 'List of active alerts' });
  async getActiveAlerts(): Promise<any> {
    const alerts = this.alertingService.getActiveAlerts();
    
    return {
      alerts,
      total: alerts.length,
    critical: alerts.filter((a: any) => a.level === 'critical').length,
    error: alerts.filter((a: any) => a.level === 'error').length,
    warning: alerts.filter((a: any) => a.level === 'warning').length,
    info: alerts.filter((a: any) => a.level === 'info').length,
    }}
;
  @Get('critical');
  @ApiOperation({ summary: 'Get critical alerts' });
  @ApiResponse({ status: 200, description: 'List of critical alerts' });
  async getCriticalAlerts(): Promise<any> {
    const alerts = this.alertingService.getCriticalAlerts();
    
    return {
      alerts,
      total: alerts.length,
    }}
;
  @Get(':id');
  @ApiOperation({ summary: 'Get alert by ID' });
  @ApiParam({ name: 'id', description: 'Alert ID' });
  @ApiResponse({ status: 200, description: 'Alert details' });
  @ApiResponse({ status: 404, description: 'Alert not found' });
  async getAlert(@Param('id') id: string) {const alert = this.alertingService.getAlert(id);
    
    if (!alert) {
      return { error: 'Alert not found', id }}
    
    return { alert }}
;
  @Post();
  @ApiOperation({ summary: 'Create a new alert' });
  @ApiResponse({ status: 201, description: 'Alert created' });
  async createAlert(
    @Body() body: {
    level: Alert['level'];
      title: string,
      message: string
source: string;
      metadata?: Record<string, any>},
  ) {
    const alert = this.alertingService.createAlert(
      body.level,
      body.title,
      body.message,
      body.source,
      body.metadata)
    );
    
    return { alert }}
;
  @Patch(':id/resolve');
  @ApiOperation({ summary: 'Resolve an alert' });
  @ApiParam({ name: 'id', description: 'Alert ID' });
  @ApiResponse({ status: 200, description: 'Alert resolved' });
  @ApiResponse({ status: 404, description: 'Alert not found' });
  async resolveAlert(
    @Param('id') id: string,
    @Body() body?: { resolvedBy?: string },
  ) {
    const success = this.alertingService.resolveAlert(id, body?.resolvedBy);
    
    if (!success) {
      return { error: 'Alert not found or already resolved', id }}
    ;
    const alert = this.alertingService.getAlert(id);
    return { alert, message: 'Alert resolved successfully' }}
}
;