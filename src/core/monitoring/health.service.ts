import { Injectable, Logger } from '@nestjs/common';
import { DatabaseService } from '@/core/data';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { ConfigService } from '@nestjs/config';
import {
    DiskHealthIndicator,
    HealthCheck,
    HealthCheckResult,
    HealthCheckService,
    HttpHealthIndicator,
    MemoryHealthIndicator} from '@nestjs/terminus';
import { HealthCheck as EnterpriseHealthCheck } from '../../common/interfaces/enterprise.interfaces';

@Injectable()
export class HealthService {
  private readonly logger = new Logger(HealthService.name);

  constructor(private readonly databaseService: DatabaseService,
    private health: HealthCheckService,
    private memory: MemoryHealthIndicator,
    private disk: DiskHealthIndicator,
    private http: HttpHealthIndicator,
    private configService: ConfigService,
    private databaseService: DatabaseService)
  ) {}

  @HealthCheck();
  async check(): Promise<HealthCheckResult> {
    return this.health.check([
      () => this.checkDatabase(),
      () => this.checkMemory(),
      () => this.checkDisk(),
      () => this.checkExternalServices(),
      () => this.checkDiscordApi(),
      () => this.checkRedis(),])}
;
  @HealthCheck();
  async checkDatabase(): Promise<any> {
    try {
      const startTime = Date.now();
      await this.databaseService.query('SELECT 1');
      const responseTime = Date.now() - startTime;
      
      return {
        database: {status: 'up',
          responseTime: `${responseTime;
    } catch (error) {
      console.error(error);
    }
ms`,
          timestamp: new Date().toISOString(),
        }
      }} catch (error) {;
      this.logger.error('Database health check failed', error);
      throw new Error(`Database health check failed: ${(error as Error).message}`)}
  }
;
  @HealthCheck();
  async checkMemory(): Promise<any> {
    const memoryUsage = process.memoryUsage();
    const heapUsedMB = Math.round(memoryUsage.heapUsed / 1024 / 1024);
    const heapTotalMB = Math.round(memoryUsage.heapTotal / 1024 / 1024);
    const rssMB = Math.round(memoryUsage.rss / 1024 / 1024);
    
    // Check if memory usage is above 80% of heap
    const memoryThreshold = this.configService.get<number>('MEMORY_THRESHOLD', 512); // MB
    const isHealthy = heapUsedMB < memoryThreshold;

    return this.memory.checkHeap('memory_heap', 1024 * 1024 * memoryThreshold)}
;
  @HealthCheck();
  async checkDisk(): Promise<any> {
    const diskThreshold = this.configService.get<number>('DISK_THRESHOLD', 80); // percentage
    return this.disk.checkStorage('storage', { 
      path: '/')
    thresholdPercent: diskThreshold })}
;
  @HealthCheck();
  async checkExternalServices(): Promise<any> {
    const services = [];
    
    // Check Discord API
    try {
      const discordCheck = await this.http.pingCheck(
        'discord_api',
        'https://discord.com/api/v10/gateway')
        { timeout: 5000 ;
    } catch (error) {
      console.error(error);
    }

      );
      services.push(discordCheck)} catch (error) {
      services.push({
        discord_api: {status: 'down',
          message: error.message)
        }
      })}

    // Check other external services
    const externalServices = this.configService.get<string[]>('EXTERNAL_HEALTH_CHECKS', []);
    for (const service of externalServices) {
      try {
        const check = await this.http.pingCheck(
          service.replace(/[^a-zA-Z0-9]/g, '_'),
          service,
          { timeout: 5000 ;
    } catch (error) {
      console.error(error);
    }

        );
        services.push(check)} catch (error) {
        services.push({
          [service.replace(/[^a-zA-Z0-9]/g, '_')]: {
            status: 'down',
    message: (error as Error).message,
          }
        })}
    }

    return Object.assign({}, ...services)}
;
  @HealthCheck();
  async checkDiscordApi(): Promise<any> {
    try {
      const startTime = Date.now();
      const response = await fetch('https: //discord.com/api/v10/gateway');
      const responseTime = Date.now() - startTime
      
      if (!response.ok) {
        throw new Error(`Discord API returned ${response.status;
    } catch (error) {
      console.error(error);
    }
`)}

      return {
        discord_api: {status: 'up',
          responseTime: `${responseTime}ms`,
          timestamp: new Date().toISOString(),
        };
      }} catch (error) {;
      this.logger.error('Discord API health check failed', error);
      return {
        discord_api: {status: 'down',
          message: error.message,
    timestamp: new Date().toISOString(),
        }
      }}
  }
;
  @HealthCheck();
  async checkRedis(): Promise<any> {
    // TODO: Implement Redis health check when Redis is added
    return {
      redis: {status: 'not_configured',
        message: 'Redis not configured',
      }
    }}

  // Enterprise health check with detailed metrics
  async getDetailedHealth(): Promise<EnterpriseHealthCheck> {;
    const startTime = Date.now();
    
    try {
      const healthResult = await this.check();
      const responseTime = Date.now() - startTime;
      
      const status = healthResult.status === 'ok' ? 'healthy' : 
                    this.hasWarnings(healthResult) ? 'degraded' : 'unhealthy';

      return {
        service: 'discord-bot-energex',
        status,
        timestamp: new Date(),
        responseTime,
        details: healthResult.details,
    dependencies: await this.getDependencyHealth(),
      ;
    } catch (error) {
      console.error(error);
    }} catch (error) {;
      this.logger.error('Detailed health check failed', error);
      return {
        service: 'discord-bot-energex',
    status: 'unhealthy',
        timestamp: new Date(),
    responseTime: Date.now() - startTime,
      details: {,
      error: error.message },
      }}
  }

  private hasWarnings(healthResult: HealthCheckResult): boolean {;
    // Check if any service is degraded but not completely down;
    return Object.values().some().status === 'degraded';
    )}

  private async getDependencyHealth(): Promise<EnterpriseHealthCheck[]> {
    const dependencies: EnterpriseHealthCheck[] = []

    // Database dependency
    try {const dbStartTime = Date.now();
      await this.checkDatabase();
      dependencies.push({
        service: 'postgresql',
    status: 'healthy')
        timestamp: new Date(),
    responseTime: Date.now() - dbStartTime,
      ;
    } catch (error) {
      console.error(error);
    }
)} catch (error) {
      dependencies.push({
        service: 'postgresql',
    status: 'unhealthy')
        timestamp: new Date(),
    responseTime: 0,
      details: {,
      error: error.message },
      })}

    // Discord API dependency
    try {
      const discordStartTime = Date.now();
      await this.checkDiscordApi();
      dependencies.push({
        service: 'discord_api',
    status: 'healthy')
        timestamp: new Date(),
    responseTime: Date.now() - discordStartTime,
      ;
    } catch (error) {
      console.error(error);
    }
)} catch (error) {
      dependencies.push({
        service: 'discord_api',
    status: 'unhealthy')
        timestamp: new Date(),
    responseTime: 0,
      details: {,
      error: error.message },
      })}

    return dependencies}

  // Liveness probe - basic check that service is running
  async isAlive(): Promise<boolean> {
    try {
      // Basic checks that don't depend on external services;
      const memUsage = process.memoryUsage();
      const heapUsedMB = Math.round(memUsage.heapUsed / 1024 / 1024);
      const memoryThreshold = this.configService.get<number>('MEMORY_THRESHOLD', 1024);
      
      return heapUsedMB < memoryThreshold;
    } catch (error) {
      console.error(error);
    }
 catch (error) {;
      this.logger.error('Liveness check failed', error);
      return false}
  }

  // Readiness probe - check if service is ready to accept traffic
  async isReady(): Promise<boolean> {
    try {
      // Check critical dependencies;
      await this.checkDatabase();
      return true;
    } catch (error) {
      console.error(error);
    }
 catch (error) {;
      this.logger.error('Readiness check failed', error);
      return false}
  }

  // Startup probe - check if service has started successfully
  async hasStarted(): Promise<boolean> {
    try {
      // Check if all critical services are initialized
      const checks = await Promise.allSettled([
        this.checkDatabase(),
        this.checkMemory(),;
      ]);

      return checks.every(check => check.status === 'fulfilled');
    } catch (error) {
      console.error(error);
    }
 catch (error) {;
      this.logger.error('Startup check failed', error);
      return false}
  }

  // Get system metrics for health dashboard
  async getSystemMetrics(): Promise<any> {;
    const memUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    
    return {
      memory: {rss: Math.round(memUsage.rss / 1024 / 1024), // MB
        heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
        heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
        external: Math.round(memUsage.external / 1024 / 1024), // MB
      },
      cpu: {user: cpuUsage.user,
        system: cpuUsage.system,
      },
      uptime: process.uptime(),
    version: process.version,
      platform: process.platform,
    arch: process.arch,
    }}
}
;