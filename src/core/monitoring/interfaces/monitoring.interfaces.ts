import {
  BaseMetric,
  Alert,
  AlertRule,
  AlertFilters,
  PerformanceMetric,
  PerformanceProfile,
  PerformanceThreshold,
  TraceContext,
  DistributedTrace,
  StructuredLog,
  LogSearchQuery,
  LogExportFilters,
  SystemHealth,
  ServiceHealth,
  MetricAggregation,
  TimeRange,
  DashboardConfig,
  MonitoringConfig,
  LogLevel,
  AlertLevel,
  HealthStatus
} from '../types/monitoring.types';

// Core monitoring service interfaces
export interface IMonitoringService {
  initialize(config: MonitoringConfig): Promise<void>;
  shutdown(): Promise<void>;
  getHealth(): Promise<SystemHealth>;
  exportMetrics(): Promise<string>;
}

export interface IMetricsCollector {
  collect(): Promise<BaseMetric[]>;
  collectSystemMetrics(): Promise<BaseMetric[]>;
  collectBusinessMetrics(): Promise<BaseMetric[]>;
  collectCustomMetrics(): Promise<BaseMetric[]>;
}

export interface IMetricsStorage {
  store(metrics: BaseMetric[]): Promise<void>;
  query(query: MetricsQuery): Promise<BaseMetric[]>;
  aggregate(query: MetricsAggregationQuery): Promise<MetricAggregation>;
  cleanup(olderThan: Date): Promise<number>;
}

export interface MetricsQuery {
  metricName?: string;
  labels?: Record<string, string>;
  timeRange?: TimeRange;
  limit?: number;
  offset?: number;
}

export interface MetricsAggregationQuery extends MetricsQuery {
  aggregationFunction: 'sum' | 'avg' | 'min' | 'max' | 'count';
  groupBy?: string[];
  interval?: string;
}

// Enhanced alerting interfaces
export interface IAlertManager {
  initialize(rules: AlertRule[]): Promise<void>;
  addRule(rule: AlertRule): Promise<void>;
  removeRule(ruleId: string): Promise<boolean>;
  updateRule(ruleId: string, rule: Partial<AlertRule>): Promise<boolean>;
  evaluateRules(metrics: BaseMetric[]): Promise<Alert[]>;
  getActiveAlerts(): Promise<Alert[]>;
  acknowledgeAlert(alertId: string, acknowledgedBy: string): Promise<boolean>;
  resolveAlert(alertId: string, resolvedBy: string): Promise<boolean>;
}

export interface INotificationManager {
  sendAlert(alert: Alert): Promise<void>;
  sendTestNotification(target: string, type: string): Promise<boolean>;
  getNotificationHistory(alertId?: string): Promise<NotificationHistory[]>;
}

export interface NotificationHistory {
  id: string;
  alertId: string;
  target: string;
  type: string;
  status: 'sent' | 'failed' | 'pending';
  sentAt: Date;
  error?: string;
}

// Performance monitoring interfaces
export interface IPerformanceProfiler {
  startProfile(operation: string, metadata?: Record<string, any>): PerformanceProfile;
  endProfile(profile: PerformanceProfile): PerformanceProfile;
  getProfiles(operation?: string, timeRange?: TimeRange): Promise<PerformanceProfile[]>;
  getBottlenecks(timeRange?: TimeRange): Promise<BottleneckAnalysis[]>;
}

export interface BottleneckAnalysis {
  operation: string;
  averageDuration: number;
  maxDuration: number;
  frequency: number;
  impact: 'low' | 'medium' | 'high' | 'critical';
  recommendations: string[];
}

export interface IThresholdManager {
  setThreshold(threshold: PerformanceThreshold): void;
  getThreshold(metric: string): PerformanceThreshold | undefined;
  removeThreshold(metric: string): boolean;
  checkThresholds(metrics: PerformanceMetric[]): Alert[];
  getRecommendedThresholds(metric: string): PerformanceThreshold[];
}

// Enhanced health monitoring interfaces
export interface IHealthMonitor {
  registerHealthCheck(name: string, check: HealthCheckFunction): void;
  unregisterHealthCheck(name: string): boolean;
  runHealthChecks(): Promise<SystemHealth>;
  getHealthHistory(timeRange?: TimeRange): Promise<HealthSnapshot[]>;
  scheduleHealthChecks(interval: number): void;
  stopHealthChecks(): void;
}

export interface HealthCheckFunction {
  (): Promise<ServiceHealth>;
}

export interface HealthSnapshot {
  timestamp: Date;
  overall: HealthStatus;
  services: Record<string, HealthStatus>;
  metrics: {
    memory: number;
    cpu: number;
    disk?: number;
  };
}

// Enhanced tracing interfaces
export interface IDistributedTracer {
  startTrace(operation: string, metadata?: Record<string, any>): TraceContext;
  continueTrace(traceId: string, operation: string, metadata?: Record<string, any>): TraceContext;
  finishTrace(context: TraceContext, result?: any, error?: Error): Promise<void>;
  getTrace(traceId: string): Promise<DistributedTrace | null>;
  searchTraces(query: TraceSearchQuery): Promise<DistributedTrace[]>;
  getTraceStatistics(timeRange?: TimeRange): Promise<TraceStatistics>;
}

export interface TraceSearchQuery {
  operation?: string;
  service?: string;
  status?: 'success' | 'error' | 'timeout';
  minDuration?: number;
  maxDuration?: number;
  timeRange?: TimeRange;
  tags?: Record<string, string>;
  limit?: number;
}

export interface TraceStatistics {
  totalTraces: number;
  averageDuration: number;
  errorRate: number;
  servicesInvolved: string[];
  topOperations: OperationStats[];
}

export interface OperationStats {
  operation: string;
  count: number;
  averageDuration: number;
  errorRate: number;
}

// Enhanced logging interfaces
export interface ILogManager {
  log(log: Omit<StructuredLog, 'timestamp'>): void;
  batchLog(logs: Array<Omit<StructuredLog, 'timestamp'>>): void;
  searchLogs(query: LogSearchQuery): Promise<LogSearchResult>;
  exportLogs(filters: LogExportFilters): Promise<string>;
  getLogStatistics(timeRange?: TimeRange): Promise<LogStatistics>;
  rotateLogs(): Promise<void>;
  archiveLogs(olderThan: Date): Promise<number>;
}

export interface LogSearchResult {
  logs: StructuredLog[];
  total: number;
  hasMore: boolean;
}

export interface LogStatistics {
  totalLogs: number;
  logsByLevel: Record<LogLevel, number>;
  topContexts: Array<{ context: string; count: number }>;
  errorRate: number;
  avgResponseTime: number;
}

// Dashboard and visualization interfaces
export interface IDashboardManager {
  createDashboard(config: DashboardConfig): Promise<string>;
  updateDashboard(id: string, config: Partial<DashboardConfig>): Promise<boolean>;
  deleteDashboard(id: string): Promise<boolean>;
  getDashboard(id: string): Promise<DashboardConfig | null>;
  listDashboards(): Promise<DashboardSummary[]>;
  renderDashboard(id: string, timeRange?: TimeRange): Promise<DashboardData>;
}

export interface DashboardSummary {
  id: string;
  name: string;
  description?: string;
  tags?: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface DashboardData {
  config: DashboardConfig;
  data: Record<string, PanelData>;
  timestamp: Date;
}

export interface PanelData {
  id: string;
  type: string;
  data: any[];
  metadata?: Record<string, any>;
}

// Data retention and cleanup interfaces
export interface IDataRetentionManager {
  setRetentionPolicy(dataType: string, days: number): void;
  getRetentionPolicy(dataType: string): number;
  cleanupExpiredData(): Promise<CleanupResult>;
  archiveData(dataType: string, olderThan: Date): Promise<number>;
  getStorageStatistics(): Promise<StorageStatistics>;
}

export interface CleanupResult {
  deletedRecords: number;
  archivedRecords: number;
  freedBytes: number;
  errors: string[];
}

export interface StorageStatistics {
  totalSize: number;
  dataTypes: Record<string, {
    count: number;
    size: number;
    oldestRecord: Date;
    newestRecord: Date;
  }>;
}

// Security monitoring interfaces
export interface ISecurityMonitor {
  logSecurityEvent(event: SecurityEvent): void;
  detectAnomalies(timeRange?: TimeRange): Promise<SecurityAnomaly[]>;
  getSecurityMetrics(timeRange?: TimeRange): Promise<SecurityMetrics>;
  createSecurityAlert(event: SecurityEvent): Promise<Alert | null>;
}

export interface SecurityEvent {
  type: 'authentication' | 'authorization' | 'data_access' | 'configuration_change' | 'suspicious_activity';
  severity: 'low' | 'medium' | 'high' | 'critical';
  source: string;
  userId?: string;
  organizationId?: string;
  ipAddress?: string;
  userAgent?: string;
  details: Record<string, any>;
  timestamp: Date;
}

export interface SecurityAnomaly {
  id: string;
  type: 'unusual_access_pattern' | 'multiple_failed_logins' | 'suspicious_ip' | 'data_exfiltration';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  affectedUsers: string[];
  timeRange: TimeRange;
  indicators: Record<string, any>;
  recommendations: string[];
}

export interface SecurityMetrics {
  authenticationAttempts: {
    total: number;
    successful: number;
    failed: number;
    failureRate: number;
  };
  accessPatterns: {
    uniqueUsers: number;
    uniqueIPs: number;
    topCountries: Array<{ country: string; count: number }>;
  };
  threats: {
    detected: number;
    blocked: number;
    resolved: number;
  };
}

// Business intelligence interfaces
export interface IBusinessIntelligence {
  generateReport(type: ReportType, timeRange: TimeRange): Promise<BusinessReport>;
  getKPIs(timeRange?: TimeRange): Promise<KPIMetrics>;
  detectTrends(metric: string, timeRange: TimeRange): Promise<TrendAnalysis>;
  getPredictions(metric: string, horizon: number): Promise<Prediction[]>;
}

export interface BusinessReport {
  type: ReportType;
  timeRange: TimeRange;
  summary: Record<string, any>;
  metrics: Record<string, number>;
  charts: ChartData[];
  insights: string[];
  recommendations: string[];
  generatedAt: Date;
}

export type ReportType = 'performance' | 'usage' | 'cost' | 'security' | 'custom';

export interface ChartData {
  title: string;
  type: 'line' | 'bar' | 'pie' | 'area';
  data: Array<{ x: any; y: any; label?: string }>;
  metadata?: Record<string, any>;
}

export interface KPIMetrics {
  uptime: number;
  averageResponseTime: number;
  errorRate: number;
  throughput: number;
  userSatisfaction: number;
  costPerRequest: number;
  availability: number;
}

export interface TrendAnalysis {
  metric: string;
  trend: 'increasing' | 'decreasing' | 'stable' | 'volatile';
  changeRate: number; // percentage
  confidence: number; // 0-1
  anomalies: Array<{ timestamp: Date; value: number; deviation: number }>;
  projectedValue: number;
}

export interface Prediction {
  timestamp: Date;
  value: number;
  confidence: number;
  upperBound: number;
  lowerBound: number;
}

// Integration interfaces
export interface IMonitoringIntegration {
  name: string;
  version: string;
  initialize(config: Record<string, any>): Promise<void>;
  shutdown(): Promise<void>;
  isHealthy(): Promise<boolean>;
  exportData(query: any): Promise<any>;
}

export interface IPrometheusIntegration extends IMonitoringIntegration {
  scrapeMetrics(): Promise<string>;
  registerMetric(metric: BaseMetric): void;
  createCustomCollector(name: string, collector: () => BaseMetric[]): void;
}

export interface IGrafanaIntegration extends IMonitoringIntegration {
  createDashboard(config: any): Promise<string>;
  updateDashboard(id: string, config: any): Promise<boolean>;
  createAlert(rule: any): Promise<string>;
}

export interface IElasticsearchIntegration extends IMonitoringIntegration {
  indexLogs(logs: StructuredLog[]): Promise<void>;
  searchLogs(query: any): Promise<any>;
  createIndex(name: string, mapping: any): Promise<boolean>;
}

// Event-driven monitoring interfaces
export interface IMonitoringEventBus {
  subscribe(eventType: string, handler: MonitoringEventHandler): string;
  unsubscribe(subscriptionId: string): boolean;
  publish(event: MonitoringEvent): Promise<void>;
  getSubscriptions(): MonitoringSubscription[];
}

export interface MonitoringEventHandler {
  (event: MonitoringEvent): Promise<void> | void;
}

export interface MonitoringSubscription {
  id: string;
  eventType: string;
  handler: MonitoringEventHandler;
  createdAt: Date;
}

export interface MonitoringEvent {
  id: string;
  type: string;
  source: string;
  timestamp: Date;
  data: any;
  metadata?: Record<string, any>;
}

// Configuration validation interfaces
export interface IConfigValidator {
  validateConfig(config: MonitoringConfig): ValidationResult;
  validateAlertRule(rule: AlertRule): ValidationResult;
  validateThreshold(threshold: PerformanceThreshold): ValidationResult;
  getConfigSchema(): any;
}

export interface ValidationResult {
  valid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

export interface ValidationWarning {
  field: string;
  message: string;
  suggestion?: string;
}
