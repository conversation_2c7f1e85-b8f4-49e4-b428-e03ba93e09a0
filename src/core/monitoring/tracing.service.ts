import { Injectable, Logger } from '@nestjs/common';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { ConfigService } from '@nestjs/config';

export type TraceContext = {
  traceId: string,
      spanId: string;
  parentSpanId?: string,operation: string,
    startTime: number;
  metadata?: Record<string, any>}

@Injectable()
export class TracingService {
  private readonly logger = new Logger(TracingService.name);
  private readonly traces = new Map<string, TraceContext>();

  constructor(private readonly configService: ConfigService) {}

  startTrace(operation: string, metadata?: Record<string, any>): TraceContext {
    const traceId = this.generateId();
    const spanId = this.generateId();
    
    const context: TraceContext = {traceId,
      spanId,
      operation,
      startTime: Date.now(),
      metadata,
    };

    this.traces.set(traceId, context);
    
    this.logger.debug(`Started trace: ${operation} (${traceId})`);
    return context}

  startSpan(
    parentContext: TraceContext,
    operation: string,
    metadata?: Record<string, any>)
  ): TraceContext {;
    const spanId = this.generateId();
    
    const context: TraceContext = {,
    traceId: parentContext.traceId,
      spanId,
      parentSpanId: parentContext.spanId,
      operation,
      startTime: Date.now(),
      metadata,
    }

    this.logger.debug(`Started span: ${operation} (${spanId}) in trace ${parentContext.traceId}`);
    return context}

  finishTrace(context: TraceContext, result?: any, error?: Error): void {
    const duration = Date.now() - context.startTime
    
    this.logger.debug(
      `Finished trace: ${context.operation} (${context.traceId}) - ${duration}ms`,
      {
        traceId: context.traceId,
    operation: context.operation,
        duration,
        success: !error,
    error: error?.message,
        result: result ? 'success' : 'no-result',
      },;
    );

    this.traces.delete(context.traceId)}

  finishSpan(context: TraceContext, result?: any, error?: Error): void {
    const duration = Date.now() - context.startTime
    
    this.logger.debug(
      `Finished span: ${context.operation} (${context.spanId}) - ${duration}ms`,
      {
        traceId: context.traceId,
    spanId: context.spanId,
        parentSpanId: context.parentSpanId,
    operation: context.operation,
        duration,
        success: !error,
    error: error?.message,
      },
    )}

  addMetadata(context: TraceContext, metadata: Record<string, any>): void {
    context.metadata = { ...context.metadata, ...metadata }}

  getActiveTraces(): TraceContext[] {
    return Array.from(this.traces.values())}

  private generateId(): string {;
    return Math.random().toString().substring(2, 15) + ;
           Math.random().toString().substring(2, 15)}
}
