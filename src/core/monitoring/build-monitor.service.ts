import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Gauge, Counter, Histogram } from 'prom-client';
import { execSync } from 'child_process';
import * as fs from 'fs/promises';
import * as path from 'path';

interface BuildMetrics {
  totalErrors: number;
  errorsByType: Record<string, number>;
  buildTime: number;
  modifiedFiles: number;
  completedFiles: number;
  timestamp: Date;
  buildStatus: 'success' | 'failed' | 'in_progress';
}

interface ErrorPattern {
  pattern: RegExp;
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
}

@Injectable()
export class BuildMonitorService {
  private readonly logger = new Logger(BuildMonitorService.name);
  
  // Build Monitoring Metrics
  public readonly buildErrorsTotal = new Gauge({
    name: 'discord_bot_build_errors_total',
    help: 'Total number of build errors',
    labelNames: ['error_type', 'severity', 'file']
  });

  public readonly buildProgress = new Gauge({
    name: 'discord_bot_build_progress_percent',
    help: 'Build progress percentage'
  });

  public readonly buildDuration = new Histogram({
    name: 'discord_bot_build_duration_seconds',
    help: 'Build duration in seconds',
    buckets: [10, 30, 60, 120, 300, 600, 1800]
  });

  public readonly filesModified = new Gauge({
    name: 'discord_bot_files_modified',
    help: 'Number of modified files being tracked'
  });

  public readonly errorReductionRate = new Gauge({
    name: 'discord_bot_error_reduction_rate',
    help: 'Rate of error reduction per minute'
  });

  public readonly agentProgress = new Gauge({
    name: 'discord_bot_agent_progress',
    help: 'Progress of different fixing agents',
    labelNames: ['agent_type', 'task_status']
  });

  private buildHistory: BuildMetrics[] = [];
  private readonly maxHistorySize = 1000;
  private monitoringInterval: NodeJS.Timeout | null = null;
  private readonly projectRoot: string;
  
  // Error patterns for categorization
  private readonly errorPatterns: ErrorPattern[] = [
    {
      pattern: /TS1005.*',' expected/,
      type: 'syntax_comma',
      severity: 'medium',
      description: 'Missing comma in TypeScript syntax'
    },
    {
      pattern: /TS1109.*expression expected/,
      type: 'syntax_expression',
      severity: 'medium',
      description: 'Invalid expression syntax'
    },
    {
      pattern: /TS2304.*Cannot find name/,
      type: 'undefined_variable',
      severity: 'high',
      description: 'Undefined variable or import'
    },
    {
      pattern: /TS2307.*Cannot find module/,
      type: 'missing_import',
      severity: 'high',
      description: 'Missing module import'
    },
    {
      pattern: /TS2322.*Type.*is not assignable/,
      type: 'type_mismatch',
      severity: 'medium',
      description: 'Type assignment error'
    },
    {
      pattern: /TS2345.*Argument of type.*is not assignable/,
      type: 'argument_type',
      severity: 'medium',
      description: 'Invalid argument type'
    },
    {
      pattern: /TS2571.*Object is of type 'unknown'/,
      type: 'unknown_type',
      severity: 'low',
      description: 'Unknown type usage'
    },
    {
      pattern: /TS2339.*Property.*does not exist/,
      type: 'missing_property',
      severity: 'high',
      description: 'Missing object property'
    }
  ];

  constructor(private configService: ConfigService) {
    this.projectRoot = process.cwd();
    this.initializeMonitoring();
  }

  private async initializeMonitoring() {
    this.logger.log('Initializing build monitoring system');
    
    // Start continuous monitoring
    await this.startContinuousMonitoring();
    
    // Initial baseline measurement
    const baseline = await this.measureCurrentState();
    this.buildHistory.push(baseline);
    
    this.logger.log(`Build monitoring started. Baseline: ${baseline.totalErrors} errors across ${baseline.modifiedFiles} files`);
  }

  async startContinuousMonitoring() {
    // Monitor every 30 seconds
    this.monitoringInterval = setInterval(async () => {
      try {
        await this.performMonitoringCycle();
      } catch (error) {
        this.logger.error(`Monitoring cycle failed: ${error.message}`);
      }
    }, 30000);

    this.logger.log('Continuous monitoring started (30s intervals)');
  }

  private async performMonitoringCycle() {
    const startTime = Date.now();
    
    try {
      // Measure current state
      const currentMetrics = await this.measureCurrentState();
      
      // Update Prometheus metrics
      this.updateMetrics(currentMetrics);
      
      // Store history
      this.addToHistory(currentMetrics);
      
      // Calculate progress and trends
      const progressAnalysis = this.analyzeProgress();
      
      // Log progress update
      this.logProgressUpdate(currentMetrics, progressAnalysis);
      
      // Record monitoring cycle duration
      const duration = (Date.now() - startTime) / 1000;
      this.buildDuration.observe(duration);
      
    } catch (error) {
      this.logger.error(`Monitoring cycle error: ${error.message}`, error.stack);
    }
  }

  private async measureCurrentState(): Promise<BuildMetrics> {
    const startTime = Date.now();
    
    try {
      // Count modified files
      const modifiedFiles = await this.countModifiedFiles();
      
      // Run build and capture output
      const buildResult = await this.runBuildAnalysis();
      
      const buildTime = Date.now() - startTime;
      
      return {
        totalErrors: buildResult.totalErrors,
        errorsByType: buildResult.errorsByType,
        buildTime,
        modifiedFiles,
        completedFiles: modifiedFiles - buildResult.filesWithErrors.size,
        timestamp: new Date(),
        buildStatus: buildResult.totalErrors === 0 ? 'success' : 'failed'
      };
      
    } catch (error) {
      this.logger.error(`Failed to measure current state: ${error.message}`);
      throw error;
    }
  }

  private async countModifiedFiles(): Promise<number> {
    try {
      const gitStatus = execSync('git status --porcelain', { 
        cwd: this.projectRoot,
        encoding: 'utf8'
      });
      
      return gitStatus.trim().split('\n').filter(line => line.trim()).length;
    } catch (error) {
      this.logger.warn(`Could not get git status: ${error.message}`);
      return 0;
    }
  }

  private async runBuildAnalysis(): Promise<{
    totalErrors: number;
    errorsByType: Record<string, number>;
    filesWithErrors: Set<string>;
  }> {
    const errorsByType: Record<string, number> = {};
    const filesWithErrors = new Set<string>();
    let totalErrors = 0;

    try {
      // Run TypeScript compilation
      const buildOutput = execSync('npx tsc --noEmit --skipLibCheck', {
        cwd: this.projectRoot,
        encoding: 'utf8',
        stdio: 'pipe'
      });
    } catch (error) {
      // Parse build errors from stderr
      const buildErrors = error.stdout?.toString() || error.stderr?.toString() || '';
      
      // Split into individual error messages
      const errorLines = buildErrors.split('\n').filter(line => 
        line.includes('error TS') || line.includes('ERROR in')
      );

      for (const errorLine of errorLines) {
        totalErrors++;
        
        // Extract file path
        const fileMatch = errorLine.match(/([^(]+\.ts)/);
        if (fileMatch) {
          filesWithErrors.add(fileMatch[1]);
        }
        
        // Categorize error
        const errorType = this.categorizeError(errorLine);
        errorsByType[errorType] = (errorsByType[errorType] || 0) + 1;
      }
    }

    return { totalErrors, errorsByType, filesWithErrors };
  }

  private categorizeError(errorLine: string): string {
    for (const pattern of this.errorPatterns) {
      if (pattern.pattern.test(errorLine)) {
        return pattern.type;
      }
    }
    
    // Default categorization
    if (errorLine.includes('TS1005')) return 'syntax_error';
    if (errorLine.includes('TS2304')) return 'undefined_reference';
    if (errorLine.includes('TS2307')) return 'import_error';
    if (errorLine.includes('TS2322')) return 'type_error';
    
    return 'other';
  }

  private updateMetrics(metrics: BuildMetrics) {
    // Update total errors
    this.buildErrorsTotal.reset();
    for (const [errorType, count] of Object.entries(metrics.errorsByType)) {
      const severity = this.getErrorSeverity(errorType);
      this.buildErrorsTotal.set({ error_type: errorType, severity, file: 'overall' }, count);
    }

    // Update progress (completion percentage)
    const progressPercent = metrics.modifiedFiles > 0 
      ? (metrics.completedFiles / metrics.modifiedFiles) * 100 
      : 100;
    this.buildProgress.set(progressPercent);

    // Update file counts
    this.filesModified.set(metrics.modifiedFiles);

    // Calculate error reduction rate
    if (this.buildHistory.length > 1) {
      const previousMetrics = this.buildHistory[this.buildHistory.length - 2];
      const timeDiff = (metrics.timestamp.getTime() - previousMetrics.timestamp.getTime()) / (1000 * 60); // minutes
      const errorDiff = previousMetrics.totalErrors - metrics.totalErrors;
      const reductionRate = timeDiff > 0 ? errorDiff / timeDiff : 0;
      this.errorReductionRate.set(reductionRate);
    }
  }

  private getErrorSeverity(errorType: string): string {
    const pattern = this.errorPatterns.find(p => p.type === errorType);
    return pattern?.severity || 'medium';
  }

  private addToHistory(metrics: BuildMetrics) {
    this.buildHistory.push(metrics);
    
    // Keep history size manageable
    if (this.buildHistory.length > this.maxHistorySize) {
      this.buildHistory = this.buildHistory.slice(-this.maxHistorySize);
    }
  }

  private analyzeProgress() {
    if (this.buildHistory.length < 2) {
      return {
        errorReduction: 0,
        avgBuildTime: 0,
        trend: 'unknown' as const
      };
    }

    const recent = this.buildHistory.slice(-10);
    const latest = recent[recent.length - 1];
    const previous = recent[0];

    const errorReduction = previous.totalErrors - latest.totalErrors;
    const avgBuildTime = recent.reduce((sum, m) => sum + m.buildTime, 0) / recent.length;
    
    // Determine trend
    const recentErrorCounts = recent.map(m => m.totalErrors);
    const isImproving = recentErrorCounts[recentErrorCounts.length - 1] < recentErrorCounts[0];
    const isStable = Math.abs(recentErrorCounts[recentErrorCounts.length - 1] - recentErrorCounts[0]) < 5;
    
    let trend: 'improving' | 'stable' | 'degrading';
    if (isImproving) trend = 'improving';
    else if (isStable) trend = 'stable';
    else trend = 'degrading';

    return { errorReduction, avgBuildTime, trend };
  }

  private logProgressUpdate(metrics: BuildMetrics, analysis: any) {
    const progressPercent = metrics.modifiedFiles > 0 
      ? ((metrics.completedFiles / metrics.modifiedFiles) * 100).toFixed(1)
      : '100.0';

    this.logger.log(`
╭─── BUILD MONITOR UPDATE ─────────────────────────────────────
│ 📊 Current Status: ${metrics.totalErrors} errors (${analysis.trend})
│ 📈 Progress: ${progressPercent}% complete (${metrics.completedFiles}/${metrics.modifiedFiles} files)
│ ⏱️  Build Time: ${(metrics.buildTime / 1000).toFixed(2)}s
│ 📉 Error Reduction: ${analysis.errorReduction} errors since last period
│ 🎯 Error Breakdown: ${Object.entries(metrics.errorsByType)
  .map(([type, count]) => `${type}:${count}`)
  .join(', ') || 'none'}
╰─────────────────────────────────────────────────────────────
    `.trim());

    // Log milestone achievements
    if (metrics.totalErrors === 0) {
      this.logger.log('🎉 MILESTONE: Zero build errors achieved!');
    } else if (metrics.totalErrors < 100 && this.buildHistory.length > 1) {
      const previous = this.buildHistory[this.buildHistory.length - 2];
      if (previous.totalErrors >= 100) {
        this.logger.log('🎯 MILESTONE: Build errors reduced below 100!');
      }
    }
  }

  // Public methods for external monitoring
  async getCurrentMetrics(): Promise<BuildMetrics> {
    return this.measureCurrentState();
  }

  getHistoricalData(limit: number = 100): BuildMetrics[] {
    return this.buildHistory.slice(-limit);
  }

  async generateProgressReport(): Promise<string> {
    const current = await this.getCurrentMetrics();
    const analysis = this.analyzeProgress();
    
    if (this.buildHistory.length === 0) {
      return 'No historical data available yet.';
    }

    const baseline = this.buildHistory[0];
    const totalReduction = baseline.totalErrors - current.totalErrors;
    const reductionPercent = baseline.totalErrors > 0 
      ? ((totalReduction / baseline.totalErrors) * 100).toFixed(1)
      : '0';

    return `
🔍 BUILD MONITORING REPORT
═══════════════════════════
📊 Current Status:
  • Total Errors: ${current.totalErrors}
  • Files Modified: ${current.modifiedFiles}
  • Files Completed: ${current.completedFiles}
  • Progress: ${((current.completedFiles / Math.max(current.modifiedFiles, 1)) * 100).toFixed(1)}%

📈 Progress Since Start:
  • Starting Errors: ${baseline.totalErrors}
  • Errors Reduced: ${totalReduction}
  • Reduction Rate: ${reductionPercent}%
  • Current Trend: ${analysis.trend}

⏱️  Performance:
  • Avg Build Time: ${(analysis.avgBuildTime / 1000).toFixed(2)}s
  • Monitoring Duration: ${Math.round((Date.now() - baseline.timestamp.getTime()) / (1000 * 60))} minutes

🎯 Error Categories:
${Object.entries(current.errorsByType)
  .map(([type, count]) => `  • ${type}: ${count}`)
  .join('\n') || '  • No errors remaining!'}

${current.totalErrors === 0 ? '🎉 BUILD SUCCESS: All errors resolved!' : 
  `🎯 Next Target: Reduce ${Object.entries(current.errorsByType)[0]?.[0] || 'remaining'} errors`}
    `.trim();
  }

  async exportMetricsData(filePath?: string): Promise<string> {
    const exportPath = filePath || path.join(this.projectRoot, 'build-metrics-export.json');
    
    const exportData = {
      timestamp: new Date(),
      buildHistory: this.buildHistory,
      currentMetrics: await this.getCurrentMetrics(),
      progressAnalysis: this.analyzeProgress(),
      errorPatterns: this.errorPatterns
    };

    await fs.writeFile(exportPath, JSON.stringify(exportData, null, 2));
    this.logger.log(`Metrics data exported to: ${exportPath}`);
    
    return exportPath;
  }

  stopMonitoring() {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
      this.logger.log('Build monitoring stopped');
    }
  }

  // Agent coordination methods
  reportAgentProgress(agentType: string, taskStatus: 'active' | 'completed' | 'failed', progress: number) {
    this.agentProgress.set({ agent_type: agentType, task_status: taskStatus }, progress);
    this.logger.log(`Agent ${agentType} progress: ${taskStatus} (${progress}%)`);
  }

  async getAgentCoordinationStatus(): Promise<{
    totalAgents: number;
    activeAgents: number;
    completedTasks: number;
    failedTasks: number;
    overallProgress: number;
  }> {
    // This would integrate with the actual agent system
    // For now, return estimated values based on error reduction
    const current = await this.getCurrentMetrics();
    const baseline = this.buildHistory[0] || current;
    
    const overallProgress = baseline.totalErrors > 0 
      ? Math.min(100, ((baseline.totalErrors - current.totalErrors) / baseline.totalErrors) * 100)
      : 100;

    return {
      totalAgents: 7, // Based on the agent types mentioned
      activeAgents: current.totalErrors > 0 ? 3 : 0,
      completedTasks: Math.floor(overallProgress / 10),
      failedTasks: 0,
      overallProgress
    };
  }
}