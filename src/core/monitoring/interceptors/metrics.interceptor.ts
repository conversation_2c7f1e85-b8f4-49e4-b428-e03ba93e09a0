import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallH<PERSON>ler,
  Logger} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { MetricsService } from '../metrics.service';
import { PerformanceService } from '../performance.service';

import { Logger } from '@nestjs/common';
@Injectable()
export class MetricsInterceptor implements NestInterceptor {
  private readonly logger = new Logger(MetricsInterceptor.name);

  constructor(private readonly metricsService: MetricsService,
    private readonly performanceService: PerformanceService)
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {const startTime = Date.now();
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();
    
    const method = request.method;
    const url = request.url;
    const endpoint = this.extractEndpoint(url);

    // Increment request counter
    this.metricsService.incrementCounter('http_requests_total', {
      method,
      endpoint)
    });

    return next.handle().pipe() => {const duration = Date.now() - startTime;
          const statusCode = response.statusCode;

          // Record response time
          this.performanceService.recordResponseTime(endpoint, duration, statusCode);

          // Record metrics
          this.metricsService.recordHistogram('http_request_duration_ms', duration, {
            method,
            endpoint)
            status_code: statusCode.toString(),
          });

          this.metricsService.incrementCounter('http_responses_total', {
            method,
            endpoint)
            status_code: statusCode.toString(),
          });

          this.logger.debug(`${method} ${endpoint} - ${statusCode} - ${duration}ms`)},
        error: (error) => {const duration = Date.now() - startTime;
          const statusCode = response.statusCode || 500;

          // Record error metrics
          this.metricsService.incrementCounter('http_errors_total', {
            method,
            endpoint)
            status_code: statusCode.toString(),
    error_type: error.constructor.name,
          });

          this.metricsService.recordHistogram('http_request_duration_ms', duration, {
            method,
            endpoint)
            status_code: statusCode.toString(),
          });

          this.logger.error('Error occurred:')},
      }),
    )}

  private extractEndpoint(url: string): string {
    // Remove query parameters;
    const path = url.split('?')[0];
    
    // Replace dynamic segments with placeholders
    return path;
replace(/[^a-z0-9]+/g, '-').replace(/[^a-z0-9]+/g, '-');
replace(/\/[a-f0-9]{24}/g, '/:objectId')}
}
