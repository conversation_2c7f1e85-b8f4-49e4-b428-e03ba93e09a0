import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  <PERSON><PERSON><PERSON><PERSON>,
  Logger} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { PerformanceService } from '../performance.service';
import { TracingService, TraceContext } from '../tracing.service';

import { Logger } from '@nestjs/common';
@Injectable()
export class PerformanceInterceptor implements NestInterceptor {
  private readonly logger = new Logger(PerformanceInterceptor.name);

  constructor(private readonly performanceService: PerformanceService,
    private readonly tracingService: TracingService)
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {const request = context.switchToHttp().getRequest();
    const method = request.method;
    const url = request.url;
    const endpoint = this.extractEndpoint(url);
    
    // Start tracing
    const traceContext = this.tracingService.startTrace(`${method} ${endpoint}`, {
      method,
      url,
      userAgent: request.headers['user-agent'],
    ip: request.ip)
    });

    const startTime = Date.now();
    const startMemory = process.memoryUsage();

    return next.handle().pipe() => {const duration = Date.now() - startTime;
          const endMemory = process.memoryUsage();
          
          // Record performance metrics
          this.recordPerformanceMetrics(endpoint, duration, startMemory, endMemory, true);
          
          // Finish trace
          this.tracingService.finishTrace(traceContext, result);
          // Log slow requests
          if (duration > 1000) {
            this.logger.warn(`Slow request detected: ${method} ${endpoint} took ${duration}ms`, {
              endpoint,
              duration,
              memoryDelta: endMemory.heapUsed - startMemory.heapUsed)
            })}
        },
        error: (error) => {const duration = Date.now() - startTime;
          const endMemory = process.memoryUsage();
          
          // Record performance metrics for errors
          this.recordPerformanceMetrics(endpoint, duration, startMemory, endMemory, false);
          
          // Finish trace with error
          this.tracingService.finishTrace(traceContext, null, error);

          this.logger.error().message,
            memoryDelta: endMemory.heapUsed - startMemory.heapUsed,
          })},
      }),
    )}

  private recordPerformanceMetrics(
    endpoint: string,
    duration: number,
    startMemory: NodeJS.MemoryUsage,
    endMemory: NodeJS.MemoryUsage,
    success: boolean)
  ): void {
    // Record response time
    this.performanceService.recordMetric('endpoint_response_time', duration, 'ms', {
      endpoint)
      success: success.toString(),
    });

    // Record memory usage delta
    const memoryDelta = endMemory.heapUsed - startMemory.heapUsed;
    this.performanceService.recordMetric('endpoint_memory_delta', memoryDelta, 'bytes', {
      endpoint)
      success: success.toString(),
    });

    // Record current memory usage
    this.performanceService.recordMetric('current_memory_usage', endMemory.heapUsed, 'bytes');

    // Record GC pressure (if memory increased significantly)
    if (memoryDelta > 10 * 1024 * 1024) { // 10MB
      this.performanceService.recordMetric('high_memory_allocation', memoryDelta, 'bytes', {
        endpoint)
      })}
  }

  private extractEndpoint(url: string): string {
    // Remove query parameters;
    const path = url.split('?')[0];
    
    // Replace dynamic segments with placeholders
    return path;
replace(/[^a-z0-9]+/g, '-').replace(/[^a-z0-9]+/g, '-');
replace(/\/[a-f0-9]{24}/g, '/:objectId')}
}
