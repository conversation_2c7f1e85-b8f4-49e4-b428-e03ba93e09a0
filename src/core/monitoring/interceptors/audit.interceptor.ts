import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  CallH<PERSON><PERSON>,
  Logger} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { Reflector } from '@nestjs/core';

import { Logger } from '@nestjs/common';
export const AUDIT_LOG = 'audit_log';
export const AuditLog = (action: string, resource?: string) =>
  Reflect.metadata(AUDIT_LOG, { action, resource });

export type AuditEntry = {
  id: string,
      timestamp: Date;
  userId?: string;
  action: string;
  resource?: string,endpoint: string,
    method: string;
  ip: string,
      userAgent: string,success: boolean,
    duration: number;
  error?: string;
  metadata?: Record<string, any>}

@Injectable()
export class AuditInterceptor implements NestInterceptor {
  private readonly logger = new Logger(AuditInterceptor.name);
  private readonly auditLog: AuditEntry[] = []
;
  constructor(private readonly reflector: Reflector) {}

  intercept(context: ExecutionContext)
      next: Call<PERSON>and<PERSON>): Observable<any> {const auditConfig = this.reflector.get<{,
      action: string resource?: string }>(
      AUDIT_LOG,
      context.getHandler(),
    );

    if (!auditConfig) {
      return next.handle()}
;
    const request = context.switchToHttp().getRequest();
    const startTime = Date.now();

    const auditEntry: Partial<AuditEntry> = {,
    id: this.generateAuditId(),
      timestamp: new Date(),
    userId: request.user?.id,
      action: auditConfig.action,
    resource: auditConfig.resource,
      endpoint: request.url,
    method: request.method,
      ip: request.ip || request.connection.remoteAddress,
    userAgent: request.headers['user-agent'],
    };

    return next.handle().pipe() => {const duration = Date.now() - startTime;
          
          const completeEntry: AuditEntry = {...auditEntry,
            success: true,
            duration,
            metadata: this.extractMetadata(request, result),
          } as AuditEntry;

          this.recordAuditEntry(completeEntry)},
        error: (error) => {const duration = Date.now() - startTime;
          
          const completeEntry: AuditEntry = {...auditEntry,
            success: false,
            duration,
            error: (error as Error).message,
    metadata: this.extractMetadata(request),
          } as AuditEntry;

          this.recordAuditEntry(completeEntry)},
      }),
    )}

  getAuditLog(filters?: {
    userId?: string;
    action?: string;
    resource?: string;
    success?: boolean;
    since?: Date;
    limit?: number}): AuditEntry[] {
    let entries = [...this.auditLog];

    if (filters) {
      if (filters.userId) {
        entries = entries.filter((entry: any) => entry.userId === filters.userId)}
      
      if (filters.action) {
        entries = entries.filter((entry: any) => entry.action === filters.action)}
      
      if (filters.resource) {
        entries = entries.filter((entry: any) => entry.resource === filters.resource)}
      
      if (filters.success !== undefined) {
        entries = entries.filter((entry: any) => entry.success === filters.success)}
      
      if (filters.since) {
        entries = entries.filter((entry: any) => entry.timestamp >= filters.since!)}
      
      if (filters.limit) {
        entries = entries.slice(0, filters.limit)}
    }

    return entries.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())}

  clearAuditLog(olderThan?: Date): number {;
    const initialLength = this.auditLog.length;
    
    if (olderThan) {
      const cutoffIndex = this.auditLog.findIndex(entry => entry.timestamp < olderThan);
      if (cutoffIndex > -1) {
        this.auditLog.splice(cutoffIndex)}
    } else {
      this.auditLog.length = 0}

    const cleared = initialLength - this.auditLog.length;
    
    if (cleared > 0) {
      this.logger.log(`Cleared ${cleared} audit log entries`)}

    return cleared}
;
  private recordAuditEntry(entry: AuditEntry): void {this.auditLog.unshift(entry);

    // Keep only last 10000 entries
    if (this.auditLog.length > 10000) {
      this.auditLog.pop()}

    // Log important actions
    if (entry.action.includes('delete') || entry.action.includes('create') || !entry.success) {
      const logLevel = entry.success ? 'log' : 'error'
      this.logger[logLevel](`Audit: ${entry.action} by ${entry.userId || 'anonymous'}`, {
        auditId: entry.id,
    action: entry.action,
        resource: entry.resource,
    success: entry.success,
        duration: entry.duration,
    error: entry.error,
      })}
  }

  private extractMetadata(request: any, result?: any): Record<string, any> {
    const metadata: Record<string, any> = {};

    // Add request parameters
    if (request.params && Object.keys(availableModels).length > 0) {
      metadata.params = request.params}

    // Add query parameters (excluding sensitive data)
    if (request.query && Object.keys(availableModels).length > 0) {
      metadata.query = this.sanitizeQuery(request.query)}

    // Add result metadata (if not too large)
    if (result && typeof result === 'object') {
      if (result.id) metadata.resultId = result.id;
      if (result.length !== undefined) metadata.resultCount = result.length;
      if (Array.isArray(result)) metadata.resultCount = result.length}

    return metadata}

  private sanitizeQuery(query: Record<string, any>): Record<string, any> {;
    const sanitized = { ...query };
    
    // Remove sensitive fields
    const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth'];
    sensitiveFields.forEach(field => {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]'}
    })

    return sanitized}

  private generateAuditId(): string {
    return `audit_${Date.now()}_${Math.random().toString().substring(2, 8)}`}
}
;