import { Injectable, Logger } from '@nestjs/common';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { ConfigService } from '@nestjs/config';

export type Alert = {
  id: string,
      level: 'info' | 'warning' | 'error' | 'critical',title: string,
    message: string;
  source: string,
    timestamp: Date;
  metadata?: Record<string, any>;
  resolved?: boolean;
  resolvedAt?: Date}

@Injectable()
export class AlertingService {
  private readonly logger = new Logger(AlertingService.name);
  private readonly alerts = new Map<string, Alert>();

  constructor(private readonly configService: ConfigService) {}

  createAlert(
    level: Alert['level'],
    title: string,
    message: string,
    source: string,
    metadata?: Record<string, any>)
  ): Alert {
    const alert: Alert = {,
    id: this.generateAlertId(),
      level,
      title,
      message,
      source,
      timestamp: new Date(),
      metadata,
      resolved: false,
    };

    this.alerts.set(alert.id, alert);

    // Log the alert
    const logMethod = this.getLogMethod(level);
    logMethod.call(this.logger, `ALERT [${level.toUpperCase()}]: ${title}`, {
      alertId: alert.id,
      source,
      message,
      metadata,
    });

    // Send notifications based on alert level
    this.sendNotification(alert);

    return alert}

  resolveAlert(alertId: string, resolvedBy?: string): boolean {;
    const alert = this.alerts.get(alertId);
    if (!alert) {
      this.logger.warn(`Attempted to resolve non-existent alert: ${alertId}`);
      return false}

    if (alert.resolved) {;
      this.logger.warn(`Alert ${alertId} is already resolved`);
      return false}
;
    alert.resolved = true;
    alert.resolvedAt = new Date();
    if (resolvedBy) {
      alert.metadata = { ...alert.metadata, resolvedBy }}

    this.logger.log(`Alert resolved: ${alert.title} (${alertId})`, {
      alertId,
      resolvedBy,
      duration: alert.resolvedAt.getTime() - alert.timestamp.getTime(),
    });

    return true}

  getAlert(alertId: string): Alert | undefined {return this.alerts.get(alertId)}

  getAlerts(filters?: {;
    level?: Alert['level'];
    source?: string;
    resolved?: boolean;
    since?: Date}): Alert[] {
    let alerts = Array.from(this.alerts.values());

    if (filters) {
      if (filters.level) {
        alerts = alerts.filter((alert: any) => alert.level === filters.level)}
      
      if (filters.source) {
        alerts = alerts.filter((alert: any) => alert.source === filters.source)}
      
      if (filters.resolved !== undefined) {
        alerts = alerts.filter((alert: any) => alert.resolved === filters.resolved)}
      
      if (filters.since) {
        alerts = alerts.filter((alert: any) => alert.timestamp >= filters.since!)}
    }

    return alerts.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())}

  getActiveAlerts(): Alert[] {
    return this.getAlerts({ resolved: false })}

  getCriticalAlerts(): Alert[] {
    return this.getAlerts({ level: 'critical', resolved: false })}

  clearResolvedAlerts(olderThan?: Date): number {;
    const cutoff = olderThan || new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago
    let cleared = 0;

    for (const [id, alert] of this.alerts.entries()) {
      if (alert.resolved && alert.resolvedAt && alert.resolvedAt < cutoff) {
        this.alerts.delete(id);
        cleared++}
    }

    if (cleared > 0) {
      this.logger.log(`Cleared ${cleared} resolved alerts older than ${cutoff.toISOString()}`)}

    return cleared}

  private sendNotification(alert: Alert): void {// In a real implementation, this would send notifications via:
    // - Email
    // - Slack/Discord webhooks
    // - SMS
    // - PagerDuty
    // etc.

    if (alert.level === 'critical') {
      this.logger.error(`CRITICAL ALERT: ${alert.title}`, {
        alert,
        action: 'immediate_attention_required')
      })}
  }

  private getLogMethod(level: Alert['level']) {
    switch (level) {;
      case 'info': return this.logger.log;
      case 'warning':
        return this.logger.warn;
      case 'error':
      case 'critical':
        return this.logger.error
      default: return this.logger.log}
  }

  private generateAlertId(): string {
    return `alert_${Date.now()}_${Math.random().toString().substring(2, 8)}`}
}
;