import { Injectable, Logger } from '@nestjs/common';
import { BuildMonitorService } from './build-monitor.service';
import { MetricsService } from './metrics.service';
import * as WebSocket from 'ws';
import { EventEmitter } from 'events';

interface DashboardData {
  timestamp: Date;
  buildMetrics: {
    totalErrors: number;
    errorsByType: Record<string, number>;
    progress: number;
    buildTime: number;
    trend: 'improving' | 'stable' | 'degrading';
  };
  systemMetrics: {
    memoryUsage: number;
    cpuUsage: number;
    activeConnections: number;
  };
  agentStatus: {
    totalAgents: number;
    activeAgents: number;
    completedTasks: number;
    overallProgress: number;
  };
  recentActivity: ActivityItem[];
}

interface ActivityItem {
  timestamp: Date;
  type: 'error_fixed' | 'milestone' | 'agent_progress' | 'build_success' | 'build_failure';
  message: string;
  severity: 'info' | 'warning' | 'success' | 'error';
}

@Injectable()
export class DashboardService extends EventEmitter {
  private readonly logger = new Logger(DashboardService.name);
  private wsServer: WebSocket.Server | null = null;
  private connectedClients: Set<WebSocket> = new Set();
  private recentActivity: ActivityItem[] = [];
  private updateInterval: NodeJS.Timeout | null = null;
  
  constructor(
    private buildMonitorService: BuildMonitorService,
    private metricsService: MetricsService
  ) {
    super();
    this.initializeDashboard();
  }

  private initializeDashboard() {
    this.logger.log('Initializing real-time dashboard service');
    
    // Start WebSocket server for real-time updates
    this.startWebSocketServer();
    
    // Start periodic dashboard updates
    this.startPeriodicUpdates();
    
    this.logger.log('Dashboard service initialized');
  }

  private startWebSocketServer() {
    try {
      this.wsServer = new WebSocket.Server({ 
        port: 3001,
        path: '/dashboard-ws'
      });

      this.wsServer.on('connection', (ws) => {
        this.connectedClients.add(ws);
        this.logger.log(`Dashboard client connected. Total clients: ${this.connectedClients.size}`);
        
        // Send initial data to new client
        this.sendDashboardUpdate(ws);
        
        ws.on('close', () => {
          this.connectedClients.delete(ws);
          this.logger.log(`Dashboard client disconnected. Total clients: ${this.connectedClients.size}`);
        });
        
        ws.on('error', (error) => {
          this.logger.error(`WebSocket client error: ${error.message}`);
          this.connectedClients.delete(ws);
        });
      });

      this.logger.log('WebSocket server started on port 3001');
    } catch (error) {
      this.logger.error(`Failed to start WebSocket server: ${error.message}`);
    }
  }

  private startPeriodicUpdates() {
    // Update dashboard every 5 seconds
    this.updateInterval = setInterval(async () => {
      try {
        await this.broadcastDashboardUpdate();
      } catch (error) {
        this.logger.error(`Dashboard update failed: ${error.message}`);
      }
    }, 5000);
  }

  private async broadcastDashboardUpdate() {
    if (this.connectedClients.size === 0) return;
    
    const dashboardData = await this.collectDashboardData();
    const message = JSON.stringify({
      type: 'dashboard_update',
      data: dashboardData
    });

    // Broadcast to all connected clients
    const deadClients: WebSocket[] = [];
    
    this.connectedClients.forEach((client) => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(message);
      } else {
        deadClients.push(client);
      }
    });

    // Clean up dead connections
    deadClients.forEach(client => {
      this.connectedClients.delete(client);
    });
  }

  private async sendDashboardUpdate(client: WebSocket) {
    try {
      const dashboardData = await this.collectDashboardData();
      const message = JSON.stringify({
        type: 'dashboard_update',
        data: dashboardData
      });

      if (client.readyState === WebSocket.OPEN) {
        client.send(message);
      }
    } catch (error) {
      this.logger.error(`Failed to send dashboard update: ${error.message}`);
    }
  }

  private async collectDashboardData(): Promise<DashboardData> {
    try {
      // Get build metrics
      const buildMetrics = await this.buildMonitorService.getCurrentMetrics();
      const agentStatus = await this.buildMonitorService.getAgentCoordinationStatus();
      
      // Get system metrics
      const memUsage = process.memoryUsage();
      const systemMetrics = {
        memoryUsage: Math.round((memUsage.heapUsed / memUsage.heapTotal) * 100),
        cpuUsage: Math.round(Math.random() * 20 + 10), // Placeholder - would get real CPU usage
        activeConnections: this.connectedClients.size
      };

      // Determine trend
      const history = this.buildMonitorService.getHistoricalData(5);
      let trend: 'improving' | 'stable' | 'degrading' = 'stable';
      if (history.length >= 2) {
        const recent = history[history.length - 1].totalErrors;
        const previous = history[0].totalErrors;
        if (recent < previous) trend = 'improving';
        else if (recent > previous) trend = 'degrading';
      }

      return {
        timestamp: new Date(),
        buildMetrics: {
          totalErrors: buildMetrics.totalErrors,
          errorsByType: buildMetrics.errorsByType,
          progress: buildMetrics.modifiedFiles > 0 
            ? Math.round((buildMetrics.completedFiles / buildMetrics.modifiedFiles) * 100)
            : 100,
          buildTime: buildMetrics.buildTime,
          trend
        },
        systemMetrics,
        agentStatus,
        recentActivity: this.recentActivity.slice(-10) // Last 10 activities
      };
    } catch (error) {
      this.logger.error(`Error collecting dashboard data: ${error.message}`);
      throw error;
    }
  }

  // Public methods for logging activities
  logActivity(type: ActivityItem['type'], message: string, severity: ActivityItem['severity'] = 'info') {
    const activity: ActivityItem = {
      timestamp: new Date(),
      type,
      message,
      severity
    };

    this.recentActivity.push(activity);
    
    // Keep only last 100 activities
    if (this.recentActivity.length > 100) {
      this.recentActivity = this.recentActivity.slice(-100);
    }

    // Broadcast activity update immediately for important events
    if (severity === 'success' || severity === 'error') {
      this.broadcastActivityUpdate(activity);
    }

    this.logger.log(`Activity logged: [${severity.toUpperCase()}] ${type} - ${message}`);
  }

  private async broadcastActivityUpdate(activity: ActivityItem) {
    if (this.connectedClients.size === 0) return;
    
    const message = JSON.stringify({
      type: 'activity_update',
      data: activity
    });

    this.connectedClients.forEach((client) => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(message);
      }
    });
  }

  // Generate HTML dashboard (static)
  async generateHTMLDashboard(): Promise<string> {
    const dashboardData = await this.collectDashboardData();
    const progressReport = await this.buildMonitorService.generateProgressReport();
    
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EnergeX Build Monitor Dashboard</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            min-height: 100vh;
        }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; }
        .header .subtitle { opacity: 0.8; font-size: 1.2em; }
        .dashboard-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .card { 
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .card h3 { margin-bottom: 15px; color: #64b5f6; }
        .metric { 
            display: flex; 
            justify-content: space-between; 
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        .metric:last-child { border-bottom: none; }
        .metric-value { font-weight: bold; }
        .progress-bar { 
            width: 100%; 
            height: 20px; 
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            overflow: hidden;
            margin-top: 10px;
        }
        .progress-fill { 
            height: 100%; 
            background: linear-gradient(90deg, #4caf50, #8bc34a);
            transition: width 0.3s ease;
        }
        .trend-indicator {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }
        .trend-improving { background: #4caf50; }
        .trend-stable { background: #ff9800; }
        .trend-degrading { background: #f44336; }
        .activity-log { 
            max-height: 300px; 
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.2);
            padding: 15px;
            border-radius: 8px;
        }
        .activity-item { 
            margin-bottom: 10px; 
            padding: 8px;
            border-left: 3px solid #64b5f6;
            background: rgba(255, 255, 255, 0.05);
        }
        .activity-time { font-size: 0.8em; opacity: 0.7; }
        .report-section { 
            background: rgba(0, 0, 0, 0.3);
            padding: 20px;
            border-radius: 12px;
            margin-top: 20px;
        }
        .report-section pre { 
            white-space: pre-wrap; 
            font-size: 0.9em;
            line-height: 1.4;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        .live-indicator {
            animation: pulse 2s infinite;
            color: #4caf50;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 EnergeX Build Monitor</h1>
            <div class="subtitle">
                Real-time Build Progress & Error Reduction Tracking
                <span class="live-indicator">● LIVE</span>
            </div>
        </div>

        <div class="dashboard-grid">
            <!-- Build Status Card -->
            <div class="card">
                <h3>📊 Build Status</h3>
                <div class="metric">
                    <span>Total Errors:</span>
                    <span class="metric-value">${dashboardData.buildMetrics.totalErrors}</span>
                </div>
                <div class="metric">
                    <span>Build Progress:</span>
                    <span class="metric-value">${dashboardData.buildMetrics.progress}%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${dashboardData.buildMetrics.progress}%"></div>
                </div>
                <div class="metric" style="margin-top: 15px;">
                    <span>Trend:</span>
                    <span class="trend-indicator trend-${dashboardData.buildMetrics.trend}">
                        ${dashboardData.buildMetrics.trend.toUpperCase()}
                    </span>
                </div>
            </div>

            <!-- Error Breakdown Card -->
            <div class="card">
                <h3>🎯 Error Categories</h3>
                ${Object.entries(dashboardData.buildMetrics.errorsByType).length > 0 
                  ? Object.entries(dashboardData.buildMetrics.errorsByType)
                      .map(([type, count]) => `
                        <div class="metric">
                            <span>${type.replace(/_/g, ' ')}:</span>
                            <span class="metric-value">${count}</span>
                        </div>
                      `).join('')
                  : '<div class="metric"><span>🎉 No errors remaining!</span></div>'
                }
            </div>

            <!-- Agent Coordination Card -->
            <div class="card">
                <h3>🤖 Agent Status</h3>
                <div class="metric">
                    <span>Total Agents:</span>
                    <span class="metric-value">${dashboardData.agentStatus.totalAgents}</span>
                </div>
                <div class="metric">
                    <span>Active Agents:</span>
                    <span class="metric-value">${dashboardData.agentStatus.activeAgents}</span>
                </div>
                <div class="metric">
                    <span>Completed Tasks:</span>
                    <span class="metric-value">${dashboardData.agentStatus.completedTasks}</span>
                </div>
                <div class="metric">
                    <span>Overall Progress:</span>
                    <span class="metric-value">${dashboardData.agentStatus.overallProgress.toFixed(1)}%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${dashboardData.agentStatus.overallProgress}%"></div>
                </div>
            </div>

            <!-- System Metrics Card -->
            <div class="card">
                <h3>💻 System Health</h3>
                <div class="metric">
                    <span>Memory Usage:</span>
                    <span class="metric-value">${dashboardData.systemMetrics.memoryUsage}%</span>
                </div>
                <div class="metric">
                    <span>CPU Usage:</span>
                    <span class="metric-value">${dashboardData.systemMetrics.cpuUsage}%</span>
                </div>
                <div class="metric">
                    <span>Active Connections:</span>
                    <span class="metric-value">${dashboardData.systemMetrics.activeConnections}</span>
                </div>
                <div class="metric">
                    <span>Build Time:</span>
                    <span class="metric-value">${(dashboardData.buildMetrics.buildTime / 1000).toFixed(2)}s</span>
                </div>
            </div>
        </div>

        <!-- Activity Log -->
        <div class="card">
            <h3>📝 Recent Activity</h3>
            <div class="activity-log">
                ${dashboardData.recentActivity.length > 0 
                  ? dashboardData.recentActivity.reverse().map(activity => `
                    <div class="activity-item">
                        <div class="activity-time">${activity.timestamp.toLocaleString()}</div>
                        <div>[${activity.severity.toUpperCase()}] ${activity.type}: ${activity.message}</div>
                    </div>
                  `).join('')
                  : '<div class="activity-item">No recent activity</div>'
                }
            </div>
        </div>

        <!-- Detailed Report -->
        <div class="report-section">
            <h3>📈 Detailed Progress Report</h3>
            <pre>${progressReport}</pre>
        </div>

        <!-- Auto-refresh notice -->
        <div style="text-align: center; margin-top: 20px; opacity: 0.7;">
            Dashboard auto-refreshes every 30 seconds • Last updated: ${new Date().toLocaleString()}
        </div>
    </div>

    <script>
        // Auto-refresh the page every 30 seconds
        setTimeout(() => {
            window.location.reload();
        }, 30000);
    </script>
</body>
</html>
    `.trim();
  }

  async saveDashboardHTML(filePath?: string): Promise<string> {
    const html = await this.generateHTMLDashboard();
    const outputPath = filePath || '/tmp/energex-dashboard.html';
    
    require('fs').writeFileSync(outputPath, html);
    this.logger.log(`Dashboard HTML saved to: ${outputPath}`);
    
    return outputPath;
  }

  getConnectedClientsCount(): number {
    return this.connectedClients.size;
  }

  async getStatus(): Promise<{
    dashboardActive: boolean;
    connectedClients: number;
    recentActivities: number;
    lastUpdate: Date;
  }> {
    return {
      dashboardActive: this.wsServer !== null,
      connectedClients: this.connectedClients.size,
      recentActivities: this.recentActivity.length,
      lastUpdate: new Date()
    };
  }

  shutdown() {
    this.logger.log('Shutting down dashboard service...');
    
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }

    if (this.wsServer) {
      this.wsServer.close();
      this.wsServer = null;
    }

    this.connectedClients.clear();
    this.logger.log('Dashboard service shutdown complete');
  }
}