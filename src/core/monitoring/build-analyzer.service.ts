import { Injectable, Logger } from '@nestjs/common';
import { BuildMonitorService } from './build-monitor.service';
import { execSync, spawn } from 'child_process';
import * as fs from 'fs/promises';
import * as path from 'path';

interface BuildAnalysis {
  errorReduction: {
    hourly: number;
    daily: number;
    total: number;
    percentage: number;
  };
  performance: {
    avgBuildTime: number;
    buildTimeImprovement: number;
    successRate: number;
  };
  bottlenecks: {
    slowestFiles: Array<{ file: string; buildTime: number; errors: number }>;
    errorHotspots: Array<{ errorType: string; count: number; files: string[] }>;
    criticalPath: string[];
  };
  predictions: {
    estimatedCompletion: Date;
    remainingEffort: string;
    riskAssessment: 'low' | 'medium' | 'high';
  };
  recommendations: {
    priority: string[];
    optimization: string[];
    warnings: string[];
  };
}

interface FileAnalysis {
  filePath: string;
  errorCount: number;
  errorTypes: string[];
  buildTime: number;
  complexity: number;
  dependencies: string[];
  lastModified: Date;
  priority: 'low' | 'medium' | 'high' | 'critical';
}

@Injectable()
export class BuildAnalyzerService {
  private readonly logger = new Logger(BuildAnalyzerService.name);
  private analysisCache: Map<string, any> = new Map();
  private readonly cacheTimeout = 300000; // 5 minutes

  constructor(private buildMonitorService: BuildMonitorService) {}

  async performComprehensiveAnalysis(): Promise<BuildAnalysis> {
    this.logger.log('Starting comprehensive build analysis...');
    
    const startTime = Date.now();
    
    try {
      const [
        errorReduction,
        performance,
        bottlenecks,
        predictions,
        recommendations
      ] = await Promise.all([
        this.analyzeErrorReduction(),
        this.analyzePerformance(),
        this.identifyBottlenecks(),
        this.generatePredictions(),
        this.generateRecommendations()
      ]);

      const analysis: BuildAnalysis = {
        errorReduction,
        performance,
        bottlenecks,
        predictions,
        recommendations
      };

      const analysisTime = Date.now() - startTime;
      this.logger.log(`Comprehensive analysis completed in ${analysisTime}ms`);

      return analysis;
    } catch (error) {
      this.logger.error(`Build analysis failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  private async analyzeErrorReduction() {
    const history = this.buildMonitorService.getHistoricalData();
    
    if (history.length < 2) {
      return {
        hourly: 0,
        daily: 0,
        total: 0,
        percentage: 0
      };
    }

    const latest = history[history.length - 1];
    const baseline = history[0];
    
    // Calculate time-based reductions
    const totalTime = latest.timestamp.getTime() - baseline.timestamp.getTime();
    const hours = totalTime / (1000 * 60 * 60);
    
    const totalReduction = baseline.totalErrors - latest.totalErrors;
    const hourlyRate = hours > 0 ? totalReduction / hours : 0;
    const dailyRate = hourlyRate * 24;
    
    const percentage = baseline.totalErrors > 0 
      ? (totalReduction / baseline.totalErrors) * 100 
      : 100;

    return {
      hourly: Math.round(hourlyRate * 100) / 100,
      daily: Math.round(dailyRate * 100) / 100,
      total: totalReduction,
      percentage: Math.round(percentage * 100) / 100
    };
  }

  private async analyzePerformance() {
    const history = this.buildMonitorService.getHistoricalData();
    
    if (history.length === 0) {
      return {
        avgBuildTime: 0,
        buildTimeImprovement: 0,
        successRate: 0
      };
    }

    const avgBuildTime = history.reduce((sum, h) => sum + h.buildTime, 0) / history.length;
    
    // Calculate improvement over time
    const recent = history.slice(-5);
    const older = history.slice(0, 5);
    
    const recentAvg = recent.reduce((sum, h) => sum + h.buildTime, 0) / recent.length;
    const olderAvg = older.reduce((sum, h) => sum + h.buildTime, 0) / older.length;
    
    const buildTimeImprovement = older.length > 0 
      ? ((olderAvg - recentAvg) / olderAvg) * 100 
      : 0;

    const successfulBuilds = history.filter(h => h.buildStatus === 'success').length;
    const successRate = (successfulBuilds / history.length) * 100;

    return {
      avgBuildTime: Math.round(avgBuildTime),
      buildTimeImprovement: Math.round(buildTimeImprovement * 100) / 100,
      successRate: Math.round(successRate * 100) / 100
    };
  }

  private async identifyBottlenecks() {
    const fileAnalyses = await this.analyzeIndividualFiles();
    
    // Identify slowest files
    const slowestFiles = fileAnalyses
      .sort((a, b) => b.buildTime - a.buildTime)
      .slice(0, 10)
      .map(f => ({
        file: f.filePath,
        buildTime: f.buildTime,
        errors: f.errorCount
      }));

    // Identify error hotspots
    const errorCounts: Record<string, { count: number; files: Set<string> }> = {};
    
    fileAnalyses.forEach(file => {
      file.errorTypes.forEach(errorType => {
        if (!errorCounts[errorType]) {
          errorCounts[errorType] = { count: 0, files: new Set() };
        }
        errorCounts[errorType].count += file.errorCount;
        errorCounts[errorType].files.add(file.filePath);
      });
    });

    const errorHotspots = Object.entries(errorCounts)
      .sort(([, a], [, b]) => b.count - a.count)
      .slice(0, 5)
      .map(([errorType, data]) => ({
        errorType,
        count: data.count,
        files: Array.from(data.files)
      }));

    // Identify critical path
    const criticalPath = await this.identifyCriticalPath(fileAnalyses);

    return {
      slowestFiles,
      errorHotspots,
      criticalPath
    };
  }

  private async analyzeIndividualFiles(): Promise<FileAnalysis[]> {
    const cacheKey = 'file_analyses';
    const cached = this.getCachedResult(cacheKey);
    if (cached) return cached;

    try {
      // Get list of TypeScript files with errors
      const buildOutput = await this.runBuildAnalysis();
      const fileAnalyses: FileAnalysis[] = [];

      for (const file of buildOutput.filesWithErrors) {
        const analysis = await this.analyzeFile(file);
        fileAnalyses.push(analysis);
      }

      this.setCachedResult(cacheKey, fileAnalyses);
      return fileAnalyses;
    } catch (error) {
      this.logger.error(`Failed to analyze individual files: ${error.message}`);
      return [];
    }
  }

  private async analyzeFile(filePath: string): Promise<FileAnalysis> {
    try {
      const absolutePath = path.resolve(filePath);
      const fileContent = await fs.readFile(absolutePath, 'utf8');
      
      // Analyze file complexity (rough estimate based on lines and constructs)
      const lines = fileContent.split('\n').length;
      const classCount = (fileContent.match(/class\s+\w+/g) || []).length;
      const interfaceCount = (fileContent.match(/interface\s+\w+/g) || []).length;
      const functionCount = (fileContent.match(/function\s+\w+/g) || []).length;
      
      const complexity = lines + (classCount * 10) + (interfaceCount * 5) + (functionCount * 3);
      
      // Extract dependencies
      const importMatches = fileContent.match(/import.*from\s+['"]([^'"]+)['"]/g) || [];
      const dependencies = importMatches.map(imp => {
        const match = imp.match(/from\s+['"]([^'"]+)['"]/);
        return match ? match[1] : '';
      }).filter(dep => dep);

      // Get file stats
      const stats = await fs.stat(absolutePath);
      
      // Determine priority based on error count and dependencies
      const errorCount = await this.getFileErrorCount(filePath);
      let priority: FileAnalysis['priority'] = 'low';
      
      if (errorCount > 20 || dependencies.length > 15) priority = 'critical';
      else if (errorCount > 10 || dependencies.length > 10) priority = 'high';
      else if (errorCount > 5 || dependencies.length > 5) priority = 'medium';

      return {
        filePath,
        errorCount,
        errorTypes: await this.getFileErrorTypes(filePath),
        buildTime: complexity * 0.1, // Rough estimate
        complexity,
        dependencies,
        lastModified: stats.mtime,
        priority
      };
    } catch (error) {
      this.logger.warn(`Failed to analyze file ${filePath}: ${error.message}`);
      return {
        filePath,
        errorCount: 0,
        errorTypes: [],
        buildTime: 0,
        complexity: 0,
        dependencies: [],
        lastModified: new Date(),
        priority: 'low'
      };
    }
  }

  private async runBuildAnalysis(): Promise<{ filesWithErrors: Set<string> }> {
    try {
      execSync('npx tsc --noEmit --skipLibCheck', { stdio: 'pipe' });
      return { filesWithErrors: new Set() };
    } catch (error) {
      const output = error.stdout?.toString() || error.stderr?.toString() || '';
      const filesWithErrors = new Set<string>();
      
      // Extract file paths from error output
      const fileMatches = output.match(/([^(]+\.ts)\(/g) || [];
      fileMatches.forEach(match => {
        const file = match.replace('(', '').trim();
        filesWithErrors.add(file);
      });
      
      return { filesWithErrors };
    }
  }

  private async getFileErrorCount(filePath: string): Promise<number> {
    try {
      const output = execSync(`npx tsc --noEmit --skipLibCheck ${filePath}`, { 
        stdio: 'pipe',
        encoding: 'utf8'
      });
      return 0;
    } catch (error) {
      const errorOutput = error.stderr?.toString() || error.stdout?.toString() || '';
      const errorLines = errorOutput.split('\n').filter(line => line.includes('error TS'));
      return errorLines.length;
    }
  }

  private async getFileErrorTypes(filePath: string): Promise<string[]> {
    try {
      execSync(`npx tsc --noEmit --skipLibCheck ${filePath}`, { stdio: 'pipe' });
      return [];
    } catch (error) {
      const errorOutput = error.stderr?.toString() || error.stdout?.toString() || '';
      const errorTypes = new Set<string>();
      
      if (errorOutput.includes('TS1005')) errorTypes.add('syntax_error');
      if (errorOutput.includes('TS2304')) errorTypes.add('undefined_reference');
      if (errorOutput.includes('TS2307')) errorTypes.add('missing_import');
      if (errorOutput.includes('TS2322')) errorTypes.add('type_mismatch');
      if (errorOutput.includes('TS2339')) errorTypes.add('missing_property');
      
      return Array.from(errorTypes);
    }
  }

  private async identifyCriticalPath(fileAnalyses: FileAnalysis[]): Promise<string[]> {
    // Sort by priority and error count to identify critical path
    const criticalFiles = fileAnalyses
      .filter(f => f.priority === 'critical' || f.priority === 'high')
      .sort((a, b) => {
        // First sort by priority, then by error count
        const priorityWeight = { critical: 4, high: 3, medium: 2, low: 1 };
        const aPriority = priorityWeight[a.priority];
        const bPriority = priorityWeight[b.priority];
        
        if (aPriority !== bPriority) return bPriority - aPriority;
        return b.errorCount - a.errorCount;
      })
      .slice(0, 5)
      .map(f => f.filePath);

    return criticalFiles;
  }

  private async generatePredictions() {
    const history = this.buildMonitorService.getHistoricalData();
    
    if (history.length < 3) {
      return {
        estimatedCompletion: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
        remainingEffort: 'Unknown',
        riskAssessment: 'medium' as const
      };
    }

    const latest = history[history.length - 1];
    const errorReduction = await this.analyzeErrorReduction();
    
    // Predict completion time based on current rate
    const remainingErrors = latest.totalErrors;
    const hoursRemaining = errorReduction.hourly > 0 
      ? remainingErrors / errorReduction.hourly 
      : 24;
    
    const estimatedCompletion = new Date(Date.now() + hoursRemaining * 60 * 60 * 1000);
    
    // Calculate remaining effort
    const remainingEffort = hoursRemaining < 1 
      ? `${Math.round(hoursRemaining * 60)} minutes`
      : hoursRemaining < 24 
        ? `${Math.round(hoursRemaining)} hours`
        : `${Math.round(hoursRemaining / 24)} days`;

    // Assess risk based on trend and error types
    let riskAssessment: 'low' | 'medium' | 'high' = 'medium';
    
    if (errorReduction.hourly > 5 && latest.totalErrors < 50) {
      riskAssessment = 'low';
    } else if (errorReduction.hourly < 1 || latest.totalErrors > 200) {
      riskAssessment = 'high';
    }

    return {
      estimatedCompletion,
      remainingEffort,
      riskAssessment
    };
  }

  private async generateRecommendations() {
    const bottlenecks = await this.identifyBottlenecks();
    const performance = await this.analyzePerformance();
    const errorReduction = await this.analyzeErrorReduction();
    
    const priority: string[] = [];
    const optimization: string[] = [];
    const warnings: string[] = [];

    // Priority recommendations
    if (bottlenecks.errorHotspots.length > 0) {
      const topHotspot = bottlenecks.errorHotspots[0];
      priority.push(`Focus on ${topHotspot.errorType} errors (${topHotspot.count} occurrences)`);
    }

    if (bottlenecks.criticalPath.length > 0) {
      priority.push(`Address critical path files: ${bottlenecks.criticalPath[0]}`);
    }

    if (errorReduction.hourly < 2) {
      priority.push('Error reduction rate is slow - consider parallel processing');
    }

    // Optimization recommendations
    if (performance.avgBuildTime > 60000) {
      optimization.push('Build time is high - consider incremental compilation');
    }

    if (bottlenecks.slowestFiles.length > 0) {
      optimization.push(`Optimize slow files: ${bottlenecks.slowestFiles.slice(0, 3).map(f => path.basename(f.file)).join(', ')}`);
    }

    optimization.push('Use TypeScript project references for better build performance');
    optimization.push('Enable strict mode incrementally to catch errors early');

    // Warnings
    if (performance.successRate < 50) {
      warnings.push('Low build success rate - focus on stability');
    }

    if (bottlenecks.errorHotspots.some(h => h.count > 50)) {
      warnings.push('High error concentration detected - may indicate systemic issues');
    }

    const latest = this.buildMonitorService.getHistoricalData(-1)[0];
    if (latest && latest.totalErrors > 1000) {
      warnings.push('Very high error count - consider breaking down into smaller chunks');
    }

    return { priority, optimization, warnings };
  }

  // Cache management
  private getCachedResult(key: string): any {
    const cached = this.analysisCache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    return null;
  }

  private setCachedResult(key: string, data: any): void {
    this.analysisCache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  // Public analysis methods
  async getFileRankings(): Promise<Array<{ file: string; score: number; reason: string }>> {
    const fileAnalyses = await this.analyzeIndividualFiles();
    
    return fileAnalyses
      .map(file => {
        let score = 0;
        let reasons: string[] = [];
        
        // Score based on error count
        score += file.errorCount * 10;
        if (file.errorCount > 0) reasons.push(`${file.errorCount} errors`);
        
        // Score based on dependencies
        score += file.dependencies.length * 2;
        if (file.dependencies.length > 10) reasons.push('high dependencies');
        
        // Score based on complexity
        score += file.complexity / 10;
        if (file.complexity > 500) reasons.push('high complexity');
        
        // Priority bonus
        const priorityBonus = { critical: 100, high: 50, medium: 20, low: 0 };
        score += priorityBonus[file.priority];
        reasons.push(`${file.priority} priority`);
        
        return {
          file: file.filePath,
          score: Math.round(score),
          reason: reasons.join(', ')
        };
      })
      .sort((a, b) => b.score - a.score);
  }

  async exportAnalysis(outputPath?: string): Promise<string> {
    const analysis = await this.performComprehensiveAnalysis();
    const filePath = outputPath || path.join(process.cwd(), 'build-analysis-report.json');
    
    const reportData = {
      timestamp: new Date(),
      analysis,
      fileRankings: await this.getFileRankings(),
      buildHistory: this.buildMonitorService.getHistoricalData()
    };

    await fs.writeFile(filePath, JSON.stringify(reportData, null, 2));
    this.logger.log(`Analysis report exported to: ${filePath}`);
    
    return filePath;
  }

  clearCache(): void {
    this.analysisCache.clear();
    this.logger.log('Analysis cache cleared');
  }
}