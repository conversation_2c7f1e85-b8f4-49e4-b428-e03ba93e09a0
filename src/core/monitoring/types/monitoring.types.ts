// Core monitoring types and interfaces
export type LogLevel = 'error' | 'warn' | 'log' | 'debug' | 'verbose';
export type AlertLevel = 'info' | 'warning' | 'error' | 'critical';
export type MetricType = 'counter' | 'gauge' | 'histogram' | 'summary';
export type HealthStatus = 'healthy' | 'degraded' | 'unhealthy' | 'unknown';
export type PerformanceStatus = 'optimal' | 'degraded' | 'critical';

// Base metric interface
export interface BaseMetric {
  name: string;
  type: MetricType;
  value: number;
  timestamp: Date;
  labels?: Record<string, string>;
  tags?: Record<string, string>;
}

// Extended metrics for different use cases
export interface CounterMetric extends BaseMetric {
  type: 'counter';
  increment?: number;
}

export interface GaugeMetric extends BaseMetric {
  type: 'gauge';
  previousValue?: number;
}

export interface HistogramMetric extends BaseMetric {
  type: 'histogram';
  buckets: number[];
  count: number;
  sum: number;
  percentiles?: Record<string, number>;
}

export interface SummaryMetric extends BaseMetric {
  type: 'summary';
  quantiles: Record<string, number>;
  count: number;
  sum: number;
}

// Metric aggregation types
export interface MetricAggregation {
  metric: string;
  average: number;
  min: number;
  max: number;
  count: number;
  sum: number;
  percentiles: {
    p50: number;
    p90: number;
    p95: number;
    p99: number;
  };
}

// Performance monitoring types
export interface PerformanceMetric {
  name: string;
  value: number;
  unit: 'ms' | 'seconds' | 'bytes' | 'count' | 'percent' | 'rate';
  timestamp: Date;
  tags?: Record<string, string>;
  threshold?: PerformanceThreshold;
}

export interface PerformanceThreshold {
  metric: string;
  warning: number;
  critical: number;
  unit: string;
  operator?: 'gt' | 'lt' | 'eq' | 'gte' | 'lte';
}

export interface PerformanceProfile {
  operation: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  memoryBefore: NodeJS.MemoryUsage;
  memoryAfter?: NodeJS.MemoryUsage;
  cpuBefore: NodeJS.CpuUsage;
  cpuAfter?: NodeJS.CpuUsage;
  success: boolean;
  error?: Error;
  metadata?: Record<string, any>;
}

// Alert system types
export interface Alert {
  id: string;
  level: AlertLevel;
  title: string;
  message: string;
  source: string;
  timestamp: Date;
  metadata?: Record<string, any>;
  resolved?: boolean;
  resolvedAt?: Date;
  resolvedBy?: string;
  correlationId?: string;
  fingerprint?: string;
  tags?: string[];
}

export interface AlertRule {
  id: string;
  name: string;
  description?: string;
  condition: AlertCondition;
  level: AlertLevel;
  enabled: boolean;
  cooldown?: number; // seconds
  notifications: NotificationTarget[];
  tags?: string[];
}

export interface AlertCondition {
  metric: string;
  operator: 'gt' | 'lt' | 'eq' | 'gte' | 'lte';
  threshold: number;
  duration?: number; // seconds - how long condition must persist
  aggregation?: 'avg' | 'min' | 'max' | 'sum' | 'count';
  timeWindow?: number; // seconds - time window for aggregation
}

export interface NotificationTarget {
  type: 'email' | 'slack' | 'discord' | 'webhook' | 'pagerduty' | 'sms';
  target: string; // email address, webhook URL, etc.
  settings?: Record<string, any>;
}

// Health check types
export interface HealthCheck {
  name: string;
  status: HealthStatus;
  responseTime: number;
  timestamp: Date;
  details?: Record<string, any>;
  error?: string;
}

export interface ServiceHealth {
  service: string;
  status: HealthStatus;
  timestamp: Date;
  responseTime: number;
  details?: Record<string, any>;
  dependencies?: ServiceHealth[];
  version?: string;
}

export interface SystemHealth {
  overall: HealthStatus;
  services: ServiceHealth[];
  metrics: {
    memory: MemoryMetrics;
    cpu: CpuMetrics;
    disk?: DiskMetrics;
    network?: NetworkMetrics;
  };
  uptime: number;
  timestamp: Date;
}

export interface MemoryMetrics {
  rss: number;
  heapTotal: number;
  heapUsed: number;
  external: number;
  arrayBuffers: number;
  heapUsedPercent: number;
}

export interface CpuMetrics {
  user: number;
  system: number;
  percent?: number;
  loadAverage?: number[];
}

export interface DiskMetrics {
  total: number;
  free: number;
  used: number;
  usedPercent: number;
  inode?: {
    total: number;
    free: number;
    used: number;
    usedPercent: number;
  };
}

export interface NetworkMetrics {
  bytesIn: number;
  bytesOut: number;
  packetsIn: number;
  packetsOut: number;
  errorsIn: number;
  errorsOut: number;
}

// Logging types
export interface StructuredLog {
  timestamp: string;
  level: LogLevel;
  message: string;
  context?: string;
  organizationId?: string;
  userId?: string;
  guildId?: string;
  correlationId?: string;
  traceId?: string;
  spanId?: string;
  metadata?: Record<string, any>;
  error?: ErrorInfo;
  performance?: PerformanceInfo;
  security?: SecurityInfo;
}

export interface ErrorInfo {
  name: string;
  message: string;
  stack?: string;
  code?: string | number;
  cause?: any;
}

export interface PerformanceInfo {
  duration: number;
  operation: string;
  memory?: {
    before: number;
    after: number;
    delta: number;
  };
}

export interface SecurityInfo {
  eventType: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  ipAddress?: string;
  userAgent?: string;
  fingerprint?: string;
}

// Tracing types
export interface TraceContext {
  traceId: string;
  spanId: string;
  parentSpanId?: string;
  operation: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  status: 'pending' | 'success' | 'error';
  metadata?: Record<string, any>;
  tags?: Record<string, string>;
  logs?: TraceLog[];
}

export interface TraceLog {
  timestamp: number;
  level: LogLevel;
  message: string;
  fields?: Record<string, any>;
}

export interface DistributedTrace {
  traceId: string;
  spans: TraceSpan[];
  duration: number;
  startTime: number;
  endTime: number;
  services: string[];
  status: 'success' | 'error' | 'timeout';
}

export interface TraceSpan {
  spanId: string;
  parentSpanId?: string;
  service: string;
  operation: string;
  startTime: number;
  endTime: number;
  duration: number;
  status: 'success' | 'error';
  tags: Record<string, string>;
  logs: TraceLog[];
}

// Dashboard and visualization types
export interface DashboardConfig {
  id: string;
  name: string;
  description?: string;
  panels: DashboardPanel[];
  variables?: DashboardVariable[];
  refresh?: string;
  timeRange?: TimeRange;
  tags?: string[];
}

export interface DashboardPanel {
  id: string;
  title: string;
  type: 'graph' | 'table' | 'stat' | 'gauge' | 'heatmap' | 'logs';
  queries: MetricQuery[];
  visualization?: VisualizationConfig;
  position: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

export interface DashboardVariable {
  name: string;
  type: 'query' | 'const' | 'interval' | 'datasource';
  label?: string;
  query?: string;
  values?: string[];
  defaultValue?: string;
  multi?: boolean;
}

export interface MetricQuery {
  id: string;
  metric: string;
  filters?: Record<string, string>;
  aggregation?: string;
  timeRange?: TimeRange;
  interval?: string;
}

export interface VisualizationConfig {
  legend?: boolean;
  tooltip?: boolean;
  yAxis?: AxisConfig;
  xAxis?: AxisConfig;
  colors?: string[];
  thresholds?: ThresholdConfig[];
}

export interface AxisConfig {
  label?: string;
  min?: number;
  max?: number;
  unit?: string;
}

export interface ThresholdConfig {
  value: number;
  color: string;
  op: 'gt' | 'lt';
}

export interface TimeRange {
  from: string | Date;
  to: string | Date;
}

// Monitoring service interfaces
export interface IMetricsService {
  recordMetric(metric: BaseMetric): void;
  incrementCounter(name: string, labels?: Record<string, string>, value?: number): void;
  setGauge(name: string, value: number, labels?: Record<string, string>): void;
  recordHistogram(name: string, value: number, labels?: Record<string, string>): void;
  getMetrics(name?: string): Promise<BaseMetric[]>;
  getMetricAggregation(name: string, timeRange?: TimeRange): Promise<MetricAggregation>;
}

export interface IAlertingService {
  createAlert(alert: Omit<Alert, 'id' | 'timestamp'>): Alert;
  resolveAlert(alertId: string, resolvedBy?: string): boolean;
  getAlerts(filters?: AlertFilters): Alert[];
  addRule(rule: AlertRule): void;
  removeRule(ruleId: string): boolean;
  evaluateRules(): Promise<void>;
}

export interface AlertFilters {
  level?: AlertLevel;
  source?: string;
  resolved?: boolean;
  since?: Date;
  tags?: string[];
}

export interface IHealthService {
  check(): Promise<SystemHealth>;
  checkService(serviceName: string): Promise<ServiceHealth>;
  isHealthy(): Promise<boolean>;
  isReady(): Promise<boolean>;
  getLiveness(): Promise<boolean>;
}

export interface IPerformanceService {
  startProfile(operation: string): PerformanceProfile;
  endProfile(profile: PerformanceProfile): PerformanceProfile;
  recordMetric(metric: PerformanceMetric): void;
  getMetrics(name: string, timeRange?: TimeRange): PerformanceMetric[];
  getAggregation(name: string, timeRange?: TimeRange): MetricAggregation;
  setThreshold(threshold: PerformanceThreshold): void;
}

export interface ITracingService {
  startTrace(operation: string, metadata?: Record<string, any>): TraceContext;
  startSpan(parentContext: TraceContext, operation: string, metadata?: Record<string, any>): TraceContext;
  finishTrace(context: TraceContext, result?: any, error?: Error): void;
  finishSpan(context: TraceContext, result?: any, error?: Error): void;
  addMetadata(context: TraceContext, metadata: Record<string, any>): void;
  getTrace(traceId: string): Promise<DistributedTrace | null>;
}

export interface ILoggingService {
  log(level: LogLevel, message: string, context?: string, metadata?: Record<string, any>): void;
  structuredLog(log: Omit<StructuredLog, 'timestamp'>): void;
  searchLogs(query: LogSearchQuery): Promise<StructuredLog[]>;
  exportLogs(filters: LogExportFilters): Promise<string>;
}

export interface LogSearchQuery {
  level?: LogLevel;
  context?: string;
  organizationId?: string;
  userId?: string;
  startTime?: Date;
  endTime?: Date;
  limit?: number;
  offset?: number;
  query?: string;
}

export interface LogExportFilters {
  organizationId: string;
  startTime: Date;
  endTime: Date;
  format: 'json' | 'csv' | 'txt';
  level?: LogLevel;
}

// Business-specific monitoring types
export interface DiscordMetrics {
  guildsConnected: number;
  usersActive: number;
  commandsExecuted: number;
  eventsProcessed: number;
  messagesSent: number;
  messagesReceived: number;
  rateLimitHits: number;
}

export interface DatabaseMetrics {
  connectionsActive: number;
  connectionsIdle: number;
  queriesTotal: number;
  queriesPerSecond: number;
  averageQueryTime: number;
  slowQueries: number;
  errors: number;
}

export interface CacheMetrics {
  hitRate: number;
  missRate: number;
  evictions: number;
  size: number;
  memory: number;
}

export interface BusinessMetrics {
  organizationsTotal: number;
  organizationsActive: number;
  sessionsActive: number;
  apiKeysActive: number;
  revenue: number;
  costs: number;
}

// Configuration types
export interface MonitoringConfig {
  enabled: boolean;
  metrics: {
    enabled: boolean;
    retention: number; // days
    exportInterval: number; // seconds
    defaultLabels: Record<string, string>;
  };
  alerts: {
    enabled: boolean;
    rules: AlertRule[];
    notifications: NotificationTarget[];
  };
  logging: {
    level: LogLevel;
    structured: boolean;
    audit: boolean;
    retention: number; // days
  };
  health: {
    enabled: boolean;
    checkInterval: number; // seconds
    timeout: number; // seconds
  };
  tracing: {
    enabled: boolean;
    sampleRate: number; // 0-1
    retention: number; // days
  };
  performance: {
    enabled: boolean;
    thresholds: PerformanceThreshold[];
  };
}

// Event types for monitoring system
export interface MonitoringEvent {
  type: 'metric' | 'alert' | 'health' | 'trace' | 'log';
  timestamp: Date;
  data: any;
  metadata?: Record<string, any>;
}

// Export utility types
export type MetricValue = string | number | boolean;
export type MetricLabels = Record<string, MetricValue>;
export type TimeWindow = '1m' | '5m' | '15m' | '30m' | '1h' | '6h' | '12h' | '24h' | '7d' | '30d';
export type AggregationFunction = 'sum' | 'avg' | 'min' | 'max' | 'count' | 'rate' | 'increase';
