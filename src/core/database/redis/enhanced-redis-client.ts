import { Injectable, Logger, OnModule<PERSON><PERSON>roy, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Redis from 'ioredis';
import {
  RedisConfig,
  RedisClient,
  RedisHealthCheck,
  RedisPerformanceMetrics,
  RedisOperationResult,
  RedisCacheOptions,
  RedisLock,
  RedisLockManager,
  RedisLockRetryOptions,
  RedisPubSubManager,
  RedisEventListener,
  RedisPubSubMessage,
  RedisSessionStorage,
  RedisError,
  RedisConnectionError,
  RedisTimeoutError,
  RedisSerializationError
} from '../types/redis.types';

@Injectable()
export class EnhancedRedisClient implements RedisClient, OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(EnhancedRedisClient.name);
  private redis!: Redis;
  private subscriber!: Redis;
  private publisher!: Redis;
  private _isConnected = false;
  private eventListeners = new Map<string, Set<RedisEventListener>>();
  private patternListeners = new Map<string, Set<RedisEventListener>>();

  constructor(private readonly configService: ConfigService) {}

  get isConnected(): boolean {
    return this._isConnected;
  }

  get config(): RedisConfig {
    return {
      host: this.configService.get<string>('REDIS_HOST', 'localhost'),
      port: this.configService.get<number>('REDIS_PORT', 6379),
      password: this.configService.get<string>('REDIS_PASSWORD'),
      db: this.configService.get<number>('REDIS_DB', 0),
      url: this.configService.get<string>('REDIS_URL'),
      keyPrefix: this.configService.get<string>('REDIS_KEY_PREFIX', ''),
      maxRetriesPerRequest: 3,
      retryDelayOnFailover: 100,
      enableReadyCheck: true,
      lazyConnect: true,
      family: 4 as const,
      keepAlive: 30000,
      reconnectOnError: (err) => {
        const targetError = 'READONLY';
        return err.message.includes(targetError);
      }
    };
  }

  async onModuleInit(): Promise<void> {
    try {
      await this.connect();
      this.logger.log('Enhanced Redis client initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Redis client:', error);
      throw error;
    }
  }

  async onModuleDestroy(): Promise<void> {
    await this.disconnect();
  }

  private async connect(): Promise<void> {
    const config = this.config;
    
    // Create main Redis instance
    if (config.url) {
      this.redis = new Redis(config.url, config);
    } else {
      this.redis = new Redis({
        ...config,
        host: config.host,
        port: config.port,
        password: config.password,
        db: config.db
      });
    }

    // Create separate instances for pub/sub
    this.subscriber = this.redis.duplicate();
    this.publisher = this.redis.duplicate();

    // Setup event handlers
    this.setupEventHandlers(this.redis, 'main');
    this.setupEventHandlers(this.subscriber, 'subscriber');
    this.setupEventHandlers(this.publisher, 'publisher');

    // Setup subscriber message handlers
    this.subscriber.on('message', (channel: string, message: string) => {
      this.handleMessage(channel, message);
    });

    this.subscriber.on('pmessage', (pattern: string, channel: string, message: string) => {
      this.handlePatternMessage(pattern, channel, message);
    });

    // Wait for connection
    await Promise.all([
      this.redis.connect(),
      this.subscriber.connect(),
      this.publisher.connect()
    ]);

    this._isConnected = true;
  }

  private setupEventHandlers(redis: Redis, instanceName: string): void {
    redis.on('connect', () => {
      this.logger.log(`Redis ${instanceName} instance connected`);
    });

    redis.on('ready', () => {
      this.logger.log(`Redis ${instanceName} instance ready`);
    });

    redis.on('error', (error) => {
      this.logger.error(`Redis ${instanceName} error:`, error);
      if (instanceName === 'main') {
        this._isConnected = false;
      }
    });

    redis.on('close', () => {
      this.logger.warn(`Redis ${instanceName} connection closed`);
      if (instanceName === 'main') {
        this._isConnected = false;
      }
    });

    redis.on('reconnecting', (ms: number) => {
      this.logger.warn(`Redis ${instanceName} reconnecting in ${ms}ms`);
    });
  }

  private async disconnect(): Promise<void> {
    try {
      await Promise.all([
        this.redis?.quit(),
        this.subscriber?.quit(),
        this.publisher?.quit()
      ]);
      this._isConnected = false;
      this.logger.log('Redis client disconnected');
    } catch (error) {
      this.logger.error('Error during Redis disconnect:', error);
    }
  }

  // Proxy all Redis methods to the main instance
  [key: string]: any;

  // Health check implementation
  async healthCheck(): Promise<RedisHealthCheck> {
    const startTime = Date.now();
    
    try {
      // Test connectivity
      await this.redis.ping();
      const latency = Date.now() - startTime;

      // Get Redis info
      const [info, memoryInfo, stats, keyspace] = await Promise.all([
        this.redis.info(),
        this.redis.info('memory'),
        this.redis.info('stats'),
        this.redis.info('keyspace')
      ]);

      // Parse info sections
      const parsedInfo = this.parseInfo(info);
      const parsedMemory = this.parseInfo(memoryInfo);
      const parsedStats = this.parseInfo(stats);
      const parsedKeyspace = this.parseInfo(keyspace);

      // Extract metrics
      const memory = {
        used: parsedMemory.used_memory_human || '0',
        peak: parsedMemory.used_memory_peak_human || '0',
        fragmentation: parseFloat(parsedMemory.mem_fragmentation_ratio || '1')
      };

      const connections = {
        active: parseInt(parsedInfo.connected_clients || '0'),
        total: parseInt(parsedInfo.total_connections_received || '0')
      };

      const uptime = parseInt(parsedInfo.uptime_in_seconds || '0');
      const version = parsedInfo.redis_version || 'unknown';

      // Determine status
      const checks = {
        connectivity: latency < 1000,
        memory: memory.fragmentation < 1.5,
        performance: latency < 100,
        replication: true // Could add more sophisticated replication checks
      };

      let status: 'healthy' | 'unhealthy' | 'degraded' = 'healthy';
      if (!checks.connectivity) {
        status = 'unhealthy';
      } else if (!checks.memory || !checks.performance) {
        status = 'degraded';
      }

      return {
        status,
        latency,
        memory,
        connections,
        uptime,
        version,
        checks,
        timestamp: new Date()
      };

    } catch (error) {
      this.logger.error('Health check failed:', error);
      return {
        status: 'unhealthy',
        latency: Date.now() - startTime,
        memory: { used: '0', peak: '0', fragmentation: 0 },
        connections: { active: 0, total: 0 },
        uptime: 0,
        version: 'unknown',
        checks: {
          connectivity: false,
          memory: false,
          performance: false,
          replication: false
        },
        timestamp: new Date()
      };
    }
  }

  // Performance metrics implementation
  async getMetrics(): Promise<RedisPerformanceMetrics> {
    try {
      const [info, slowlog, memInfo, keyspaceInfo] = await Promise.all([
        this.redis.info(),
        this.redis.slowlog('GET', 10),
        this.redis.info('memory'),
        this.redis.info('keyspace')
      ]);

      const parsedInfo = this.parseInfo(info);
      const parsedMemory = this.parseInfo(memInfo);
      const parsedKeyspace = this.parseInfo(keyspaceInfo);
      const parsedStats = this.parseInfo(info);

      // Calculate operations
      const operations = {
        reads: parseInt(parsedStats.total_reads_processed || '0'),
        writes: parseInt(parsedStats.total_writes_processed || '0'),
        deletes: parseInt(parsedStats.expired_keys || '0'),
        total: parseInt(parsedStats.total_commands_processed || '0')
      };

      // Calculate performance metrics
      const keyspaceHits = parseInt(parsedStats.keyspace_hits || '0');
      const keyspaceMisses = parseInt(parsedStats.keyspace_misses || '0');
      const hitRate = keyspaceHits + keyspaceMisses > 0 
        ? keyspaceHits / (keyspaceHits + keyspaceMisses) 
        : 1;

      const performance = {
        averageLatency: 0, // Would need to track this separately
        slowQueries: Array.isArray(slowlog) ? slowlog.length : 0,
        errorRate: 0, // Would need to track this separately
        throughput: parseInt(parsedStats.instantaneous_ops_per_sec || '0')
      };

      // Memory metrics
      const memory = {
        used: parsedMemory.used_memory_human || '0',
        peak: parsedMemory.used_memory_peak_human || '0',
        fragmentation: parseFloat(parsedMemory.mem_fragmentation_ratio || '1'),
        efficiency: hitRate
      };

      // Connection metrics
      const connections = {
        active: parseInt(parsedInfo.connected_clients || '0'),
        peak: parseInt(parsedInfo.connected_clients || '0'), // Redis doesn't track peak directly
        refused: parseInt(parsedInfo.rejected_connections || '0'),
        total: parseInt(parsedInfo.total_connections_received || '0')
      };

      // Keyspace metrics
      const keyspace = {
        totalKeys: parseInt(parsedKeyspace.keys || '0'),
        expires: parseInt(parsedKeyspace.expires || '0'),
        avgTtl: parseInt(parsedKeyspace.avg_ttl || '0'),
        hitRate
      };

      // Replication metrics
      const replication = {
        role: parsedInfo.role as 'master' | 'slave' || 'master',
        connectedSlaves: parseInt(parsedInfo.connected_slaves || '0'),
        replBacklogSize: parseInt(parsedInfo.repl_backlog_size || '0'),
        replLag: 0 // Would need more sophisticated calculation
      };

      return {
        operations,
        performance,
        memory,
        connections,
        keyspace,
        replication
      };

    } catch (error) {
      this.logger.error('Failed to get metrics:', error);
      throw new RedisError('Failed to get performance metrics', 'METRICS_ERROR', 'getMetrics');
    }
  }

  private parseInfo(info: string): Record<string, string> {
    const parsed: Record<string, string> = {};
    const lines = info.split('\\r\\n');
    
    for (const line of lines) {
      if (line.includes(':') && !line.startsWith('#')) {
        const [key, value] = line.split(':');
        if (key && value !== undefined) {
          parsed[key] = value;
        }
      }
    }
    
    return parsed;
  }

  // Cache strategy implementation
  async cacheGet<T>(key: string): Promise<T | null> {
    try {
      const value = await this.redis.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      throw new RedisSerializationError(`Failed to get cache key ${key}`, key);
    }
  }

  async cacheSet<T>(key: string, value: T, options?: RedisCacheOptions): Promise<boolean> {
    try {
      const serialized = JSON.stringify(value);
      if (options?.ttl) {
        await this.redis.setex(key, options.ttl, serialized);
      } else {
        await this.redis.set(key, serialized);
      }
      return true;
    } catch (error) {
      throw new RedisSerializationError(`Failed to set cache key ${key}`, key);
    }
  }

  async cacheDelete(key: string): Promise<boolean> {
    try {
      const result = await this.redis.del(key);
      return result === 1;
    } catch (error) {
      throw new RedisError(`Failed to delete cache key ${key}`, 'DELETE_ERROR', 'cacheDelete', key);
    }
  }

  // Lock manager implementation
  createLockManager(): RedisLockManager {
    return new RedisLockManagerImpl(this.redis, this.logger);
  }

  // Pub/Sub manager implementation  
  createPubSubManager(): RedisPubSubManager {
    return new RedisPubSubManagerImpl(this.subscriber, this.publisher, this.logger);
  }

  // Session storage implementation
  createSessionStorage(keyPrefix = 'session:'): RedisSessionStorage {
    return new RedisSessionStorageImpl(this.redis, keyPrefix, this.logger);
  }

  private handleMessage(channel: string, message: string): void {
    const listeners = this.eventListeners.get(channel);
    if (listeners) {
      const parsedMessage: RedisPubSubMessage = {
        channel,
        data: this.tryParseJson(message),
        timestamp: new Date()
      };

      listeners.forEach(listener => {
        try {
          listener(parsedMessage);
        } catch (error) {
          this.logger.error(`Error in message listener for channel ${channel}:`, error);
        }
      });
    }
  }

  private handlePatternMessage(pattern: string, channel: string, message: string): void {
    const listeners = this.patternListeners.get(pattern);
    if (listeners) {
      const parsedMessage: RedisPubSubMessage = {
        channel,
        pattern,
        data: this.tryParseJson(message),
        timestamp: new Date()
      };

      listeners.forEach(listener => {
        try {
          listener(parsedMessage);
        } catch (error) {
          this.logger.error(`Error in pattern listener for pattern ${pattern}:`, error);
        }
      });
    }
  }

  private tryParseJson(value: string): any {
    try {
      return JSON.parse(value);
    } catch {
      return value;
    }
  }

  // Proxy all Redis methods
  private setupProxy(): void {
    return new Proxy(this, {
      get: (target, prop) => {
        if (prop in target) {
          return target[prop];
        }
        
        if (target.redis && prop in target.redis) {
          const value = target.redis[prop];
          return typeof value === 'function' ? value.bind(target.redis) : value;
        }
        
        return undefined;
      }
    });
  }
}

// Lock Manager Implementation
class RedisLockManagerImpl implements RedisLockManager {
  constructor(
    private readonly redis: Redis,
    private readonly logger: Logger
  ) {}

  async acquire(
    key: string, 
    ttl: number = 30, 
    retryOptions?: RedisLockRetryOptions
  ): Promise<RedisLock | null> {
    const lockKey = `lock:${key}`;
    const lockValue = `${Date.now()}_${Math.random().toString(36).substring(2)}`;
    
    const options = {
      retryAttempts: 5,
      retryDelay: 100,
      exponentialBackoff: true,
      maxRetryDelay: 1000,
      ...retryOptions
    };

    for (let attempt = 0; attempt < options.retryAttempts; attempt++) {
      try {
        const result = await this.redis.set(lockKey, lockValue, 'EX', ttl, 'NX');
        if (result === 'OK') {
          return new RedisLockImpl(lockKey, lockValue, ttl, this.redis, this.logger);
        }

        if (attempt < options.retryAttempts - 1) {
          const delay = options.exponentialBackoff 
            ? Math.min(options.retryDelay * Math.pow(2, attempt), options.maxRetryDelay)
            : options.retryDelay;
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      } catch (error) {
        this.logger.error(`Lock acquisition attempt ${attempt + 1} failed:`, error);
      }
    }

    return null;
  }

  async release(lock: RedisLock): Promise<boolean> {
    return lock.release();
  }

  async extend(lock: RedisLock, ttl: number): Promise<boolean> {
    return lock.extend(ttl);
  }

  async isLocked(key: string): Promise<boolean> {
    return await this.redis.exists(`lock:${key}`) === 1;
  }

  async forceClear(key: string): Promise<boolean> {
    const result = await this.redis.del(`lock:${key}`);
    return result === 1;
  }
}

// Lock Implementation
class RedisLockImpl implements RedisLock {
  constructor(
    public readonly key: string,
    public readonly value: string,
    public readonly ttl: number,
    private readonly redis: Redis,
    private readonly logger: Logger,
    private _acquired: boolean = true
  ) {}

  get acquired(): boolean {
    return this._acquired;
  }

  async extend(ttl: number): Promise<boolean> {
    if (!this._acquired) return false;

    const script = `
      if redis.call('get', KEYS[1]) == ARGV[1] then
        return redis.call('expire', KEYS[1], ARGV[2])
      else
        return 0
      end
    `;

    try {
      const result = await this.redis.eval(script, 1, this.key, this.value, ttl.toString()) as number;
      return result === 1;
    } catch (error) {
      this.logger.error(`Failed to extend lock ${this.key}:`, error);
      return false;
    }
  }

  async release(): Promise<boolean> {
    if (!this._acquired) return false;

    const script = `
      if redis.call('get', KEYS[1]) == ARGV[1] then
        return redis.call('del', KEYS[1])
      else
        return 0
      end
    `;

    try {
      const result = await this.redis.eval(script, 1, this.key, this.value) as number;
      this._acquired = false;
      return result === 1;
    } catch (error) {
      this.logger.error(`Failed to release lock ${this.key}:`, error);
      return false;
    }
  }
}

// Pub/Sub Manager Implementation
class RedisPubSubManagerImpl implements RedisPubSubManager {
  private subscriptions = new Map<string, Set<RedisEventListener>>();
  private patternSubscriptions = new Map<string, Set<RedisEventListener>>();

  constructor(
    private readonly subscriber: Redis,
    private readonly publisher: Redis,
    private readonly logger: Logger
  ) {
    this.setupMessageHandlers();
  }

  private setupMessageHandlers(): void {
    this.subscriber.on('message', (channel: string, message: string) => {
      this.handleMessage(channel, message);
    });

    this.subscriber.on('pmessage', (pattern: string, channel: string, message: string) => {
      this.handlePatternMessage(pattern, channel, message);
    });
  }

  async subscribe<T>(channel: string, listener: RedisEventListener<T>): Promise<void> {
    if (!this.subscriptions.has(channel)) {
      this.subscriptions.set(channel, new Set());
      await this.subscriber.subscribe(channel);
    }
    
    this.subscriptions.get(channel)!.add(listener as RedisEventListener);
  }

  async unsubscribe(channel: string, listener?: RedisEventListener): Promise<void> {
    const listeners = this.subscriptions.get(channel);
    if (!listeners) return;

    if (listener) {
      listeners.delete(listener);
      if (listeners.size === 0) {
        await this.subscriber.unsubscribe(channel);
        this.subscriptions.delete(channel);
      }
    } else {
      await this.subscriber.unsubscribe(channel);
      this.subscriptions.delete(channel);
    }
  }

  async publish<T>(channel: string, data: T): Promise<number> {
    const message = typeof data === 'string' ? data : JSON.stringify(data);
    return await this.publisher.publish(channel, message);
  }

  async psubscribe<T>(pattern: string, listener: RedisEventListener<T>): Promise<void> {
    if (!this.patternSubscriptions.has(pattern)) {
      this.patternSubscriptions.set(pattern, new Set());
      await this.subscriber.psubscribe(pattern);
    }
    
    this.patternSubscriptions.get(pattern)!.add(listener as RedisEventListener);
  }

  async punsubscribe(pattern: string, listener?: RedisEventListener): Promise<void> {
    const listeners = this.patternSubscriptions.get(pattern);
    if (!listeners) return;

    if (listener) {
      listeners.delete(listener);
      if (listeners.size === 0) {
        await this.subscriber.punsubscribe(pattern);
        this.patternSubscriptions.delete(pattern);
      }
    } else {
      await this.subscriber.punsubscribe(pattern);
      this.patternSubscriptions.delete(pattern);
    }
  }

  getSubscriptions(): readonly string[] {
    return Array.from(this.subscriptions.keys());
  }

  getPatternSubscriptions(): readonly string[] {
    return Array.from(this.patternSubscriptions.keys());
  }

  private handleMessage(channel: string, message: string): void {
    const listeners = this.subscriptions.get(channel);
    if (listeners) {
      const parsedMessage: RedisPubSubMessage = {
        channel,
        data: this.tryParseJson(message),
        timestamp: new Date()
      };

      listeners.forEach(listener => {
        try {
          const result = listener(parsedMessage);
          if (result instanceof Promise) {
            result.catch(error => {
              this.logger.error(`Error in async message listener for channel ${channel}:`, error);
            });
          }
        } catch (error) {
          this.logger.error(`Error in message listener for channel ${channel}:`, error);
        }
      });
    }
  }

  private handlePatternMessage(pattern: string, channel: string, message: string): void {
    const listeners = this.patternSubscriptions.get(pattern);
    if (listeners) {
      const parsedMessage: RedisPubSubMessage = {
        channel,
        pattern,
        data: this.tryParseJson(message),
        timestamp: new Date()
      };

      listeners.forEach(listener => {
        try {
          const result = listener(parsedMessage);
          if (result instanceof Promise) {
            result.catch(error => {
              this.logger.error(`Error in async pattern listener for pattern ${pattern}:`, error);
            });
          }
        } catch (error) {
          this.logger.error(`Error in pattern listener for pattern ${pattern}:`, error);
        }
      });
    }
  }

  private tryParseJson(value: string): any {
    try {
      return JSON.parse(value);
    } catch {
      return value;
    }
  }
}

// Session Storage Implementation
class RedisSessionStorageImpl implements RedisSessionStorage {
  constructor(
    private readonly redis: Redis,
    private readonly keyPrefix: string,
    private readonly logger: Logger
  ) {}

  private getKey(sessionId: string): string {
    return `${this.keyPrefix}${sessionId}`;
  }

  async get<T>(sessionId: string): Promise<T | null> {
    try {
      const value = await this.redis.get(this.getKey(sessionId));
      return value ? JSON.parse(value) : null;
    } catch (error) {
      this.logger.error(`Failed to get session ${sessionId}:`, error);
      return null;
    }
  }

  async set<T>(sessionId: string, data: T, ttl?: number): Promise<boolean> {
    try {
      const key = this.getKey(sessionId);
      const serialized = JSON.stringify(data);
      
      if (ttl) {
        await this.redis.setex(key, ttl, serialized);
      } else {
        await this.redis.set(key, serialized);
      }
      
      return true;
    } catch (error) {
      this.logger.error(`Failed to set session ${sessionId}:`, error);
      return false;
    }
  }

  async destroy(sessionId: string): Promise<boolean> {
    try {
      const result = await this.redis.del(this.getKey(sessionId));
      return result === 1;
    } catch (error) {
      this.logger.error(`Failed to destroy session ${sessionId}:`, error);
      return false;
    }
  }

  async touch(sessionId: string, ttl?: number): Promise<boolean> {
    try {
      const key = this.getKey(sessionId);
      if (ttl) {
        const result = await this.redis.expire(key, ttl);
        return result === 1;
      }
      return true;
    } catch (error) {
      this.logger.error(`Failed to touch session ${sessionId}:`, error);
      return false;
    }
  }

  async exists(sessionId: string): Promise<boolean> {
    try {
      return await this.redis.exists(this.getKey(sessionId)) === 1;
    } catch (error) {
      this.logger.error(`Failed to check session existence ${sessionId}:`, error);
      return false;
    }
  }

  async clear(): Promise<number> {
    try {
      const keys = await this.redis.keys(`${this.keyPrefix}*`);
      if (keys.length === 0) return 0;
      
      return await this.redis.del(...keys);
    } catch (error) {
      this.logger.error('Failed to clear sessions:', error);
      return 0;
    }
  }

  async length(): Promise<number> {
    try {
      const keys = await this.redis.keys(`${this.keyPrefix}*`);
      return keys.length;
    } catch (error) {
      this.logger.error('Failed to get session count:', error);
      return 0;
    }
  }

  async getAllSessions<T>(): Promise<Record<string, T>> {
    try {
      const keys = await this.redis.keys(`${this.keyPrefix}*`);
      if (keys.length === 0) return {};

      const pipeline = this.redis.pipeline();
      keys.forEach(key => pipeline.get(key));
      
      const results = await pipeline.exec();
      const sessions: Record<string, T> = {};
      
      if (results) {
        keys.forEach((key, index) => {
          const [err, value] = results[index];
          if (!err && value) {
            try {
              const sessionId = key.replace(this.keyPrefix, '');
              sessions[sessionId] = JSON.parse(value as string);
            } catch (parseError) {
              this.logger.warn(`Failed to parse session data for key ${key}:`, parseError);
            }
          }
        });
      }

      return sessions;
    } catch (error) {
      this.logger.error('Failed to get all sessions:', error);
      return {};
    }
  }

  async regenerateId(oldSessionId: string, newSessionId: string): Promise<boolean> {
    try {
      const oldKey = this.getKey(oldSessionId);
      const newKey = this.getKey(newSessionId);
      
      // Use RENAME to atomically move the session
      const result = await this.redis.rename(oldKey, newKey);
      return result === 'OK';
    } catch (error) {
      this.logger.error(`Failed to regenerate session ID from ${oldSessionId} to ${newSessionId}:`, error);
      return false;
    }
  }
}