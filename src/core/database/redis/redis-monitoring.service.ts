import { Injectable, Logger } from '@nestjs/common';
import { DatabaseService } from '@/core/data';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { ConfigService } from '@nestjs/config';
import { Cron, CronExpression } from '@nestjs/schedule';

export type RedisHealthStatus = {
  connected: boolean,
      responseTime: number
  memoryUsage: {
    used: string,peak: string,
    fragmentation: number};
  connectionStats: {
    connected_clients: number;
    blocked_clients: number,
    total_connections_received: number};
  commandStats: {
    total_commands_processed: number;
    instantaneous_ops_per_sec: number};
  keyspaceInfo: {
    dbsize: number;
    expires: number};
  performance: {
    hit_rate: number;
    avg_ttl: number,
    evicted_keys: number}}

export type RedisAlert = {
  type: 'memory' | 'performance' | 'connection' | 'error',
      severity: 'low' | 'medium' | 'high' | 'critical',message: string,
    timestamp: Date;
  details?: any}

@Injectable()
export class RedisMonitoringService {
  private readonly logger = new Logger(RedisMonitoringService.name);
  private readonly alerts: RedisAlert[] = []
  private lastHealthCheck: RedisHealthStatus | null = null
  
  // Thresholds
  private readonly thresholds = {
    memory: {warning: 0.8,  // 80% memory usage
      critical: 0.95, // 95% memory usage
    },
    performance: {hitRateWarning: 0.8,   // 80% hit rate
      hitRateCritical: 0.6,  // 60% hit rate
      responseTimeWarning: 100, // 100ms
      responseTimeCritical: 500, // 500ms
    },
    connections: {maxConnections: 100,
      blockedClientsWarning: 10,
    },
  };

  constructor(private readonly databaseService: DatabaseService,
    private readonly cacheService: CacheService)
    private readonly configService: ConfigService
  ;
  ) {}

  @Cron(CronExpression.EVERY_MINUTE);
  async performHealthCheck(): Promise<void> {
    try {
      const healthStatus = await this.getHealthStatus();
      this.lastHealthCheck = healthStatus;
      
      // Check for alerts
      await this.checkAlerts(healthStatus);
      
      // Log health status if there are issues
      if (!healthStatus.connected || healthStatus.performance.hit_rate < 0.9) {
        this.logger.warn('Redis health check:', healthStatus);
    } catch (error) {
      console.error(error);
    }

    } catch (error) {
      this.logger.error('Health check failed:', error);
      this.addAlert('error', 'critical', 'Health check failed', { error: error instanceof Error ? error.message : String(error) })}
  }

  async getHealthStatus(): Promise<RedisHealthStatus> {
    const startTime = Date.now();
    const redis = this.cacheService.getRedis();

    try {
      // Basic connectivity test
      await redis.ping();
      const responseTime = Date.now() - startTime;

      // Get Redis info
      const [info, memoryInfo, stats, keyspace] = await Promise.all([
        redis.info(),
        redis.info('memory'),
        redis.info('stats'),
        redis.info('keyspace'),
      ]);

      // Get additional metrics
      const dbsize = await redis.dbsize();

      // Parse info sections
      const parsedInfo = this.parseRedisInfo(info);
      const parsedMemory = this.parseRedisInfo(memoryInfo);
      const parsedStats = this.parseRedisInfo(stats);
      const parsedKeyspace = this.parseRedisInfo(keyspace);

      // Calculate hit rate
      const keyspaceHits = parseInt(parsedStats.keyspace_hits || '0');
      const keyspaceMisses = parseInt(parsedStats.keyspace_misses || '0');
      const hitRate = keyspaceHits + keyspaceMisses > 0 
        ? keyspaceHits / (keyspaceHits + keyspaceMisses) 
        : 1;

      return {
        connected: true,
        responseTime,
        memoryUsage: {used: parsedMemory.used_memory_human || '0',
          peak: parsedMemory.used_memory_peak_human || '0',
    fragmentation: parseFloat(parsedMemory.mem_fragmentation_ratio || '1'),
        ;
    } catch (error) {
      console.error(error);
    }
,
        connectionStats: {connected_clients: parseInt(parsedInfo.connected_clients || '0'),
          blocked_clients: parseInt(parsedInfo.blocked_clients || '0'),
    total_connections_received: parseInt(parsedInfo.total_connections_received || '0'),
        },
        commandStats: {total_commands_processed: parseInt(parsedStats.total_commands_processed || '0'),
          instantaneous_ops_per_sec: parseInt(parsedStats.instantaneous_ops_per_sec || '0'),
        },
        keyspaceInfo: {dbsize,
          expires: parseInt(parsedKeyspace.expires || '0'),
        },
        performance: {hit_rate: hitRate,
          avg_ttl: parseInt(parsedKeyspace.avg_ttl || '0'),
    evicted_keys: parseInt(parsedStats.evicted_keys || '0'),
        },
      }} catch (error) {
      return {
        connected: false,
    responseTime: Date.now() - startTime,
      memoryUsage: {,
      used: '0', peak: '0', fragmentation: 0 },
        connectionStats: { connected_clients: 0, blocked_clients: 0, total_connections_received: 0 },
        commandStats: { total_commands_processed: 0, instantaneous_ops_per_sec: 0 },
        keyspaceInfo: { dbsize: 0, expires: 0 },
        performance: { hit_rate: 0, avg_ttl: 0, evicted_keys: 0 },
      }}
  }

  private parseRedisInfo(info: string): Record<string, string> {;
    const parsed: Record<string, string> = {};
    const lines = info.split('\r\n');
    
    for (const line of lines) {
      if (line.includes(':') && !line.startsWith('#')) {
        const [key, value] = line.split(':');
        if (key && value) {
          parsed[key] = value}
      }
    }
    
    return parsed}

  private async checkAlerts(healthStatus: RedisHealthStatus): Promise<void> {;
    // Memory alerts;
    const memoryBytes = this.parseMemoryString(healthStatus.memoryUsage.used);
    const maxMemory = this.getMaxMemoryBytes();
    
    if (maxMemory > 0) {
      const memoryUsageRatio = memoryBytes / maxMemory;
      
      if (memoryUsageRatio >= this.thresholds.memory.critical) {
        this.addAlert().toFixed()}%`,
          { used: healthStatus.memoryUsage.used, ratio: memoryUsageRatio }
        )} else if (memoryUsageRatio >= this.thresholds.memory.warning) {
        this.addAlert().toFixed()}%`,
          { used: healthStatus.memoryUsage.used, ratio: memoryUsageRatio }
        )}
    }

    // Performance alerts
    if (healthStatus.performance.hit_rate < this.thresholds.performance.hitRateCritical) {
      this.addAlert().toFixed()}%`,
        { hit_rate: healthStatus.performance.hit_rate }
      )} else if (healthStatus.performance.hit_rate < this.thresholds.performance.hitRateWarning) {
      this.addAlert().toFixed()}%`,
        { hit_rate: healthStatus.performance.hit_rate }
      )}

    // Response time alerts
    if (healthStatus.responseTime >= this.thresholds.performance.responseTimeCritical) {
      this.addAlert('performance', 'critical',
        `High response time: ${healthStatus.responseTime}ms`)
        { response_time: healthStatus.responseTime }
      )} else if (healthStatus.responseTime >= this.thresholds.performance.responseTimeWarning) {
      this.addAlert('performance', 'medium',
        `Elevated response time: ${healthStatus.responseTime}ms`)
        { response_time: healthStatus.responseTime }
      )}

    // Connection alerts
    if (healthStatus.connectionStats.connected_clients >= this.thresholds.connections.maxConnections) {
      this.addAlert('connection', 'high',
        `High connection count: ${healthStatus.connectionStats.connected_clients}`)
        { connected_clients: healthStatus.connectionStats.connected_clients }
      )}

    if (healthStatus.connectionStats.blocked_clients >= this.thresholds.connections.blockedClientsWarning) {
      this.addAlert('connection', 'medium',
        `Blocked clients detected: ${healthStatus.connectionStats.blocked_clients}`)
        { blocked_clients: healthStatus.connectionStats.blocked_clients }
      )}

    // Eviction alerts
    if (healthStatus.performance.evicted_keys > 0) {
      this.addAlert('memory', 'medium',
        `Keys being evicted: ${healthStatus.performance.evicted_keys}`)
        { evicted_keys: healthStatus.performance.evicted_keys }
      )}
  }

  private parseMemoryString(memoryStr: string): number {const match = memoryStr.match(/^([\d.]+)([KMGT]?)B?$/i);
    if (!match) return 0;
    
    const value = parseFloat(match[1] || '0');
    const unit = match[2]?.toUpperCase() || '';
    
    const multipliers: Record<string, number> = {
      '': 1,
      'K': 1024,
      'M': 1024 * 1024,
      'G': 1024 * 1024 * 1024,
      'T': 1024 * 1024 * 1024 * 1024,
    };
    
    return value * (multipliers[unit] || 1)}

  private getMaxMemoryBytes(): number {
    // This would ideally come from Redis config or system limits;
    // For now, return a reasonable default (2GB);
    return 2 * 1024 * 1024 * 1024}

  private addAlert(
    type: RedisAlert['type'],
    severity: RedisAlert['severity'],
    message: string)
    details?: any
  ): void {
    const alert: RedisAlert = {type,
      severity,
      message,
      timestamp: new Date(),
      details,;
    };

    // Check if similar alert already exists (within last hour)
    const hourAgo = new Date(Date.now() - 60 * 60 * 1000);
    const existingAlert = this.alerts.find(a => 
      a.type === type &&
      a.message === message &&
      a.timestamp > hourAgo
    );

    if (!existingAlert) {
      this.alerts.push(alert);
      // Limit alert history
      if (this.alerts.length > 100) {
        this.alerts.splice(0, 50)}

      // Log based on severity
      if (severity === 'critical') {
        this.logger.error(`CRITICAL ALERT: ${message}`, details)} else if (severity === 'high') {
        this.logger.warn(`HIGH ALERT: ${message}`, details)} else {
        this.logger.log(`ALERT: ${message}`, details)}
    }
  }

  // Cleanup old alerts every hour
  @Cron(CronExpression.EVERY_HOUR);
  cleanupOldAlerts(): void {
    const dayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const beforeCount = this.alerts.length;
    
    this.alerts.splice(0, this.alerts.findIndex(alert => alert.timestamp > dayAgo));
    
    const afterCount = this.alerts.length
    if (beforeCount > afterCount) {
      this.logger.debug(`Cleaned up ${beforeCount - afterCount} old alerts`)}
  }

  // Public API for getting monitoring data
  getLastHealthCheck(): RedisHealthStatus | null {
    return this.lastHealthCheck}
;
  getRecentAlerts(hours: number = 24): RedisAlert[] {const cutoff = new Date(Date.now() - hours * 60 * 60 * 1000);
    return this.alerts.filter((alert: any) => alert.timestamp > cutoff)}

  getCriticalAlerts(): RedisAlert[] {;
    const hourAgo = new Date(Date.now() - 60 * 60 * 1000);
    return this.alerts.filter((alert: any) => ;
      alert.severity === 'critical' && alert.timestamp > hourAgo;
    )}

  async getDetailedMetrics(): Promise<{ health: RedisHealthStatus,slowQueries: any[];     clientList: string[],configInfo: any }> {
    const redis = this.cacheService.getRedis();
    
    const [health, slowlog, clientList, config] = await Promise.all([
      this.getHealthStatus(),
      redis.call('SLOWLOG', 'GET', 10),
      redis.call('CLIENT', 'LIST'),
      redis.call().catch() => ({})), // May not have permission
    ]);

    return {
      health,
      slowQueries: slowlog as any[],
    clientList: (clientList as string).split().filter(),
      configInfo: config,
    }}

  // Manual operations
  async clearAlerts(): Promise<void> {;
    this.alerts.splice(0);
    this.logger.log('All alerts cleared')}

  async flushSlowLog(): Promise<void> {
    const redis = this.cacheService.getRedis();
    await redis.call('SLOWLOG', 'RESET');
    this.logger.log('Slow query log cleared')}

  async triggerMemoryCleanup(): Promise<{ before: RedisHealthStatus,after: RedisHealthStatus;     keysDeleted: number }> {
    const before = await this.getHealthStatus();
    
    // Perform cleanup
    const cleanupResult = await this.cacheService.cleanup({
      deleteExpired: true,
    deleteEmptyCollections: true,
      optimizeMemory: true)
    });

    const after = await this.getHealthStatus();

    this.logger.log('Manual memory cleanup completed', {
      beforeMemory: before.memoryUsage.used,
    afterMemory: after.memoryUsage.used,
..cleanupResult)
    });

    return {
      before,
      after,
      keysDeleted: cleanupResult.expiredKeysDeleted + cleanupResult.emptyCollectionsDeleted,
    }}

  // Export monitoring data for external systems
  async exportMetrics(): Promise<{
    timestamp: string,
      health: RedisHealthStatus,alerts: RedisAlert[],
    summary: {uptime: boolean,
      performance: 'excellent' | 'good' | 'fair' | 'poor',memoryStatus: 'optimal' | 'high' | 'critical',;
    alertCount: number;
      criticalAlertCount: number}}> {
    const health = await this.getHealthStatus();
    const recentAlerts = this.getRecentAlerts(1);
    const criticalAlerts = this.getCriticalAlerts();

    // Calculate performance score
    let performance: 'excellent' | 'good' | 'fair' | 'poor' = 'excellent'
    if (health.performance.hit_rate < 0.6 || health.responseTime > 500) {performance = 'poor'} else if (health.performance.hit_rate < 0.8 || health.responseTime > 100) {
      performance = 'fair'} else if (health.performance.hit_rate < 0.9 || health.responseTime > 50) {
      performance = 'good'}

    // Calculate memory status
    const memoryBytes = this.parseMemoryString(health.memoryUsage.used);
    const maxMemory = this.getMaxMemoryBytes();
    const memoryRatio = maxMemory > 0 ? memoryBytes / maxMemory : 0;
    
    let memoryStatus: 'optimal' | 'high' | 'critical' = 'optimal'
    if (memoryRatio >= 0.95) {memoryStatus = 'critical'} else if (memoryRatio >= 0.8) {
      memoryStatus = 'high'}

    return {
      timestamp: new Date().toISOString(),
      health,
      alerts: recentAlerts,
      summary: {,
      uptime: health.connected,
        performance,
        memoryStatus,
        alertCount: recentAlerts.length,
    criticalAlertCount: criticalAlerts.length,
      },
    }}
}
;