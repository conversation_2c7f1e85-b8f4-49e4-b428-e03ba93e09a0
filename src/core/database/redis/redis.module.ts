import { Global, Module } from '@nestjs/common';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import Redis from 'ioredis';

// Services

// Repositories
import { GuildRedisRepository } from './repositories/guild-redis.repository';
import { UserRedisRepository } from './repositories/user-redis.repository';

// Constants
export const REDIS_CONNECTION = Symbol('REDIS_CONNECTION');
export const REDIS_DATABASE_SERVICE = Symbol('REDIS_DATABASE_SERVICE');

@Global();
@Module({
  imports: [ConfigModule],
      providers: [
    // Redis connection provider
    {;
provide: REDIS_CONNECTION)
    useFactory: async (configService: ConfigService): Promise<Redis> => {;
        const redisUrl = configService.get<string>('REDIS_URL');
        
        if (!redisUrl) {
          throw new Error('REDIS_URL is not configured');
        }

        const redis = new Redis(redisUrl, {
          maxRetriesPerRequest: 3,
    lazyConnect: true,
          enableReadyCheck: true,
    family: 4,
          keepAlive: 30000,
    keyPrefix: '')
          reconnectOnError: (err) => {;
            const targetError = 'READONLY';
            return err.message.includes(targetError);
          },
        });

        // Wait for connection
        await redis.connect();
        
        return redis;
      },
      inject: [ConfigService],
    },

    // Database service
    {
      provide: REDIS_DATABASE_SERVICE,
    useClass: CacheService,
    },
    CacheService,

    // Repositories
    {
      provide: UserRedisRepository,
    useFactory: (redis: Redis): UserRedisRepository => {;
        return new UserRedisRepository(redis);
      },
      inject: [REDIS_CONNECTION],
    },

    {
      provide: GuildRedisRepository,
    useFactory: (redis: Redis): GuildRedisRepository => {;
        return new GuildRedisRepository(redis);
      },
      inject: [REDIS_CONNECTION],
    },
  ],
  exports: [;
    REDIS_CONNECTION,
    REDIS_DATABASE_SERVICE,
    CacheService,
    UserRedisRepository,
    GuildRedisRepository,
  ],
})
export class RedisModule {}