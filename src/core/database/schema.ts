/**
 * Redis-compatible entity exports
 * All entities are now TypeScript interfaces instead of Drizzle ORM schemas
 */

// Core entities
export * from './entities/base.entity';
export * from './entities/user.entity';
export * from './entities/session.entity';
export * from './entities/guild.entity';
export * from './entities/organization.entity';

// AI-related entities
export * from './entities/ai-agent-config.entity';
export * from './entities/ai-channel-config.entity';
export * from './entities/ai-chat-session.entity';
export * from './entities/agent-interaction.entity';
export * from './entities/agent-memory.entity';

// User management entities
export * from './entities/user-relationship.entity';
export * from './entities/user-api-keys.entity';
export * from './entities/user-panel-state.entity';
export * from './entities/personal-growth-plan.entity';

// Panel system entities
export * from './entities/panel-analytics.entity';
export * from './entities/panel-cleanup.entity';
export * from './entities/panel-deployment.entity';
export * from './entities/dynamic-content.entity';

// Community entities
export * from './entities/community-events.entity';
export * from './entities/support-tickets.entity';

// Content category entities
export * from './entities/ai-mastery.entity';
export * from './entities/announcements.entity';
export * from './entities/creative-showcase.entity';
export * from './entities/educational-resources.entity';
export * from './entities/gaming-entertainment.entity';
export * from './entities/networking-business.entity';
export * from './entities/trading-markets.entity';
export * from './entities/wealth-creation.entity';


// Export repositories
export * from './repositories/base-redis.repository';
export * from './repositories/user.repository';

