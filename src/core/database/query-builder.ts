import { RedisQuery, CacheService } from './redis.service';
import { BaseEntity } from './types/base.interface';

export interface WhereCondition<T = any> {
  field: keyof T,
    operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'nin' | 'like' | 'exists' | 'nexists';
  value?: any;
  values?: any[]}

export type JoinCondition = {
  entityType: string;
  localField: string;
  foreignField: string;
  alias?: string}

export type AggregateFunction = {
  type: 'count' | 'sum' | 'avg' | 'min' | 'max';
  field?: string;
  alias?: string}

export class RedisQueryBuilder<T extends BaseEntity = BaseEntity> {
  private entityType: string
  private selectFields: (keyof T)[] = []
  private whereConditions: WhereCondition<T>[] = [];
  private orderByFields: { field: keyof T direction: 'ASC' | 'DESC' }[] = [];
  private limitValue: number | undefined
  private offsetValue: number | undefined
  private joinConditions: JoinCondition[] = []
  private groupByFields: (keyof T)[] = []
  private havingConditions: WhereCondition<T>[] = []
  private aggregates: AggregateFunction[] = []
;
  constructor(private readonly cacheService: CacheService)
    entityType: string
  
  ) {this.entityType = entityType}

  /**
   * Select specific fields
   */
  select(...fields: (keyof T)[]): this {this.selectFields = fields;
    return this}

  /**
   * Add WHERE condition
   */
  where(field: keyof T, operator: WhereCondition<T>['operator'], value?: any, values?: any[]): this {;
    const condition: WhereCondition<T> = { field, operator };
    if (value !== undefined) condition.value = value;
    if (values !== undefined) condition.values = values;
    this.whereConditions.push(condition);
    return this}

  /**
   * Add WHERE condition with equals operator
   */
  whereEq(field: keyof T, value: any): this {return this.where(field, 'eq', value)}

  /**
   * Add WHERE condition with not equals operator
   */
  whereNe(field: keyof T, value: any): this {return this.where(field, 'ne', value)}

  /**
   * Add WHERE condition with greater than operator
   */
  whereGt(field: keyof T, value: any): this {return this.where(field, 'gt', value)}

  /**
   * Add WHERE condition with greater than or equal operator
   */
  whereGte(field: keyof T, value: any): this {return this.where(field, 'gte', value)}

  /**
   * Add WHERE condition with less than operator
   */
  whereLt(field: keyof T, value: any): this {return this.where(field, 'lt', value)}

  /**
   * Add WHERE condition with less than or equal operator
   */
  whereLte(field: keyof T, value: any): this {return this.where(field, 'lte', value)}

  /**
   * Add WHERE condition with IN operator
   */
  whereIn(field: keyof T, values: any[]): this {return this.where(field, 'in', undefined, values)}

  /**
   * Add WHERE condition with NOT IN operator
   */
  whereNotIn(field: keyof T, values: any[]): this {return this.where(field, 'nin', undefined, values)}

  /**
   * Add WHERE condition with LIKE operator (contains)
   */
  whereLike(field: keyof T, value: string): this {return this.where(field, 'like', value)}

  /**
   * Add WHERE condition to check if field exists
   */
  whereExists(field: keyof T): this {return this.where(field, 'exists')}

  /**
   * Add WHERE condition to check if field does not exist
   */
  whereNotExists(field: keyof T): this {return this.where(field, 'nexists')}

  /**
   * Add multiple WHERE conditions with AND logic
   */
  whereAnd(conditions: Array<[keyof T, WhereCondition<T>['operator'], any?, any[]?]>): this {
    conditions.forEach(([field, operator, value, values]) => {;
      this.where(field, operator, value, values)});
    return this}

  /**
   * Add ORDER BY clause
   */;
  orderBy(field: keyof T, direction: 'ASC' | 'DESC' = 'ASC'): this {this.orderByFields.push({ field, direction });
    return this}

  /**
   * Add ascending order
   */
  asc(field: keyof T): this {return this.orderBy(field, 'ASC')}

  /**
   * Add descending order
   */
  desc(field: keyof T): this {return this.orderBy(field, 'DESC')}

  /**
   * Set limit
   */;
  limit(limit: number): this {this.limitValue = limit;
    return this}

  /**
   * Set offset
   */;
  offset(offset: number): this {this.offsetValue = offset;
    return this}

  /**
   * Set pagination
   */;
  paginate(page: number, perPage: number = 20): this {this.limitValue = perPage;
    this.offsetValue = (page - 1) * perPage;
    return this}

  /**
   * Add JOIN condition (for related entities)
   */
  join(entityType: string, localField: string, foreignField: string, alias?: string): this {;
    const join: JoinCondition = { entityType, localField, foreignField };
    if (alias !== undefined) join.alias = alias;
    this.joinConditions.push(join);
    return this}

  /**
   * Add LEFT JOIN condition
   */
  leftJoin(entityType: string, localField: string, foreignField: string, alias?: string): this {
    return this.join(entityType, localField, foreignField, alias)}

  /**
   * Add GROUP BY clause
   */;
  groupBy(...fields: (keyof T)[]): this {this.groupByFields = fields;
    return this}

  /**
   * Add HAVING condition (for grouped results)
   */
  having(field: keyof T, operator: WhereCondition<T>['operator'], value?: any, values?: any[]): this {;
    const condition: WhereCondition<T> = { field, operator };
    if (value !== undefined) condition.value = value;
    if (values !== undefined) condition.values = values;
    this.havingConditions.push(condition);
    return this}

  /**
   * Add aggregate function
   */
  aggregate(type: AggregateFunction['type'], field?: string, alias?: string): this {;
    const agg: AggregateFunction = { type };
    if (field !== undefined) agg.field = field;
    if (alias !== undefined) agg.alias = alias;
    this.aggregates.push(agg);
    return this}

  /**
   * Count records (builder method)
   */
  countRecords(field?: string, alias?: string): this {
    return this.aggregate('count', field, alias)}

  /**
   * Sum values
   */
  sum(field: string, alias?: string): this {
    return this.aggregate('sum', field, alias)}

  /**
   * Average values
   */
  avg(field: string, alias?: string): this {
    return this.aggregate('avg', field, alias)}

  /**
   * Minimum value
   */
  min(field: string, alias?: string): this {
    return this.aggregate('min', field, alias)}

  /**
   * Maximum value
   */
  max(field: string, alias?: string): this {
    return this.aggregate('max', field, alias)}

  /**
   * Execute the query and return results
   */;
  async get(): Promise<T[]> {const query = await this.buildQuery();
    
    if (this.joinConditions.length > 0) {
      return this.executeJoinQuery(query)}
    
    if (this.aggregates.length > 0) {
      return this.executeAggregateQuery()}
    ;
    const results = await this.cacheService.find<any>(this.entityType, query as any);
    return results as T[]}

  /**
   * Execute the query and return first result
   */;
  async first(): Promise<T | null> {const originalLimit = this.limitValue;
    this.limit(1);
    const results = await this.get();
    if (originalLimit !== undefined) {
      this.limitValue = originalLimit} else {
      this.limitValue = undefined}
    return results[0] || null}

  /**
   * Execute the query and return count
   */;
  async count(): Promise<number> {const query = await this.buildQuery();
    return this.cacheService.exists(this.entityType, query as any)}

  /**
   * Execute the query and check if any records exist
   */
  async exists(): Promise<boolean> {;
    const count = await this.count();
    return count > 0}

  /**
   * Execute a raw Redis command
   */;
  async raw(command: string, ...args: any[]): Promise<any> {const client = this.cacheService;
    return client.call(command, ...args)}

  /**
   * Build Redis query from builder conditions
   */
  private async buildQuery(): Promise<RedisQuery<T>> {;
    const query: any = {};

    // Select fields
    if (this.selectFields.length > 0) {
      query.select = [...this.selectFields]}

    // Where conditions
    if (this.whereConditions.length > 0) {
      query.where = await this.buildWhereClause()}

    // Order by
    if (this.orderByFields.length > 0) {
      // For now, only support single order by (first one)
      const orderBy = this.orderByFields[0];
      if (orderBy) {
        query.orderBy = {
          field: String(orderBy.field),
    direction: orderBy.direction}}
    }

    // Limit and offset
    if (this.limitValue !== undefined) {
      query.limit = this.limitValue}
    
    if (this.offsetValue !== undefined) {
      query.offset = this.offsetValue}

    return query}

  /**
   * Build WHERE clause from conditions
   */
  private async buildWhereClause(): Promise<Partial<T>> {;
    const where: any = {};

    for (const condition of this.whereConditions) {
      const { field, operator, value, values } = condition;

      switch (operator) {
        case 'eq':
          where[field] = value;
          break;
        
        case 'ne':
          // For complex conditions, we'll need custom filtering
          // For now, store as is and handle in filtering logic
          where[`${String(field)}_ne`] = value;
          break;
          
        case 'in':
          where[`${String(field)}_in`] = values;
          break;
          
        case 'like':
          where[`${String(field)}_like`] = value;
          break;
          
        // Add more operators as needed
        default:
          where[`${String(field)}_${operator}`] = value || values}
    }

    return where}

  /**
   * Execute query with JOIN conditions
   */
  private async executeJoinQuery(query: RedisQuery<T>): Promise<T[]> {// For JOIN operations, we need to:
    // 1. Get the main entities
    // 2. For each JOIN, fetch related entities
    // 3. Merge the results
    ;
    const mainEntities = await JSON.parse(this.cacheService.get(this.entityType, query) || 'null');
    
    for (const join of this.joinConditions) {
      for (const entity of mainEntities) {
        const relatedEntities = await this.cacheService.find(
          join.entityType)
          {
            where: { [join.foreignField]: entity[join.localField as keyof T] }
          }
        );
        
        // Attach related entities to main entity
        const alias = join.alias || join.entityType;
        (entity as any)[alias] = relatedEntities}
    }
    
    return [...mainEntities]}

  /**
   * Execute aggregate query
   */
  private async executeAggregateQuery(): Promise<any[]> {;
    const query = await this.buildQuery();
    const entities = await JSON.parse(this.cacheService.get(this.entityType, query) || 'null');
    
    const results: any[] = []
    
    for (const aggregate of this.aggregates) {
      let result: any
      
      switch (aggregate.type) {
        case 'count': result = entities.length;
          break;
          
        case 'sum':
          result = entities.reduce((sum, entity) => {
            const value = Number((entity as any)[aggregate.field!]) || 0;
            return sum + value}, 0);
          break;
          
        case 'avg':
          const sum = entities.reduce((sum, entity) => {
            const value = Number((entity as any)[aggregate.field!]) || 0;
            return sum + value}, 0);
          result = entities.length > 0 ? sum / entities.length : 0;
          break;
          
        case 'min':
          result = Math.min(...entities.map((entity: any) => Number((entity as any)[aggregate.field!]) || 0));
          break;
          
        case 'max':
          result = Math.max(...entities.map((entity: any) => Number((entity as any)[aggregate.field!]) || 0));
          break}
      
      const resultObj: any = {}
      const key = aggregate.alias || `${aggregate.type}_${aggregate.field || 'all'}`;
      resultObj[key] = result;
      results.push(resultObj)}
    
    return results}

  /**
   * Clone the query builder
   */
  clone(): RedisQueryBuilder<T> {;
    const cloned = new RedisQueryBuilder<T>(this.cacheService, this.entityType);
    (cloned as any).selectFields = [...this.selectFields];
    (cloned as any).whereConditions = [...this.whereConditions];
    (cloned as any).orderByFields = [...this.orderByFields];
    if (this.limitValue !== undefined) (cloned as any).limitValue = this.limitValue;
    if (this.offsetValue !== undefined) (cloned as any).offsetValue = this.offsetValue;
    (cloned as any).joinConditions = [...this.joinConditions];
    (cloned as any).groupByFields = [...this.groupByFields];
    (cloned as any).havingConditions = [...this.havingConditions];
    (cloned as any).aggregates = [...this.aggregates];
    return cloned}

  /**
   * Reset the query builder
   */
  reset(): this {;
    this.selectFields = [];
    this.whereConditions = [];
    this.orderByFields = [];
    this.limitValue = undefined;
    this.offsetValue = undefined;
    this.joinConditions = [];
    this.groupByFields = [];
    this.havingConditions = [];
    this.aggregates = [];
    return this}
}

/**
 * Create a new query builder instance
 */
export function createQueryBuilder<T extends BaseEntity = BaseEntity>(
  redisService: CacheService,
    entityType: string
): RedisQueryBuilder<T> {return new RedisQueryBuilder<T>(redisService, entityType)};