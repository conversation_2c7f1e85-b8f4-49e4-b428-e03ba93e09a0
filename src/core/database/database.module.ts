import { Global, Module } from '@nestjs/common';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { ConfigService } from '@nestjs/config';
import { Redis, RedisOptions } from 'ioredis';
import { REDIS_CONNECTION, DatabaseService } from './database.service';
import { RedisConfig } from './types/redis.types';

@Global();
@Module({
  providers: [
    {provide: REDIS_CONNECTION,
    inject: [ConfigService])
      useFactory: async (configService: ConfigService): Promise<Redis> => {const redisUrl = configService.get<string>('REDIS_URL');
        const redisConfig = {
host: configService.get<string>('REDIS_HOST', 'localhost'),
          port: configService.get<number>('REDIS_PORT', 6379),
          password: configService.get<string>('REDIS_PASSWORD'),
    db: configService.get<number>('REDIS_DB', 0),
          url: redisUrl,
    maxRetriesPerRequest: 3,
          connectTimeout: 10000,
    commandTimeout: 5000,
          retryStrategy: (times: number) => Math.min(times * 50, 2000),
          lazyConnect: true,
    keepAlive: 30000,
          family: 4,
    enableAutoPipelining: false,
          enableOfflineQueue: true,
    readOnly: false,
        };

        let redis: Redis

        // Create Redis connection options (only include valid ioredis options);
        const connectionOptions: RedisOptions = {};
        if (redisConfig.retryStrategy) connectionOptions.retryStrategy = redisConfig.retryStrategy;
        if (redisConfig.maxRetriesPerRequest !== undefined) connectionOptions.maxRetriesPerRequest = redisConfig.maxRetriesPerRequest;
        if (redisConfig.connectTimeout !== undefined) connectionOptions.connectTimeout = redisConfig.connectTimeout;
        if (redisConfig.commandTimeout !== undefined) connectionOptions.commandTimeout = redisConfig.commandTimeout;
        if (redisConfig.lazyConnect !== undefined) connectionOptions.lazyConnect = redisConfig.lazyConnect;
        if (redisConfig.keepAlive !== undefined) connectionOptions.keepAlive = redisConfig.keepAlive;
        if (redisConfig.family !== undefined) connectionOptions.family = redisConfig.family;
        if (redisConfig.enableAutoPipelining !== undefined) connectionOptions.enableAutoPipelining = redisConfig.enableAutoPipelining;
        if (redisConfig.enableOfflineQueue !== undefined) connectionOptions.enableOfflineQueue = redisConfig.enableOfflineQueue;
        if (redisConfig.readOnly !== undefined) connectionOptions.readOnly = redisConfig.readOnly;

        if (redisUrl) {
          // Use Redis URL if provided
          redis = new Redis(redisUrl, connectionOptions)} else {
          // Use individual Redis configuration
          const options: RedisOptions = {,
    host: redisConfig.host ?? 'localhost',
            port: redisConfig.port ?? 6379,
    db: redisConfig.db ?? 0,
..connectionOptions,
          };
          if (redisConfig.password) {
            options.password = redisConfig.password}
          redis = new Redis(options)}

        // Add event listeners for Redis connection
        redis.on('connect', () => {
          console.log('✅ Redis connection established')});

        redis.on('ready', () => {
          console.log('✅ Redis ready to accept commands')});

        redis.on('error', (error) => {
          console.error('❌ Redis connection error:', error)});

        redis.on('close', () => {
          console.log('⚠️ Redis connection closed')});

        redis.on('reconnecting', (delay: number) => {console.log(`🔄 Redis reconnecting in ${delay}ms...`)});

        // Test connection
        try {
          await redis.connect();
          const result = await redis.ping();
          if (result === 'PONG') {
            console.log('✅ Redis connection test successful');
    } catch (error) {
      console.error(error);
    }

        } catch (error) {
          console.error('❌ Redis connection test failed:', error);
          throw error}

        return redis},
    },
    DatabaseService,
  ],
  exports: [DatabaseService, REDIS_CONNECTION],
});
export class DatabaseModule {};