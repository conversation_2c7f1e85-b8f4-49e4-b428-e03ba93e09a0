import { CacheService } from '@/core/cache/cache.service';
import { Injectable, Logger, OnApplicationShutdown, OnModuleInit } from '@nestjs/common';
import { DatabaseService } from '@/core/data';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { ConfigService } from '@nestjs/config';
import { GuildRepository } from './repositories/guild.repository';
import { RepositoryFactory } from './repositories/repository.factory';
import { SessionRepository } from './repositories/session.repository';
import { UserRepository } from './repositories/user.repository';
import { RedisHealthCheck, RedisMetrics } from './types/redis-base.types';
import { RedisDataUtil, RedisMigrationUtil } from './utils/redis-migration.util';

/**
 * Main Redis Database Service
 * Provides centralized access to all Redis operations and repositories
 */
@Injectable()
export class RedisDatabaseService implements OnModuleInit, OnApplicationShutdown {
  private readonly logger = new Logger(RedisDatabaseService.name);
  private isInitialized = false;

  constructor(
    private readonly databaseService: DatabaseService,
    private readonly configService: ConfigService,
    private readonly cacheService: CacheService,
    private readonly repositoryFactory: RepositoryFactory,
    private readonly userRepository: UserRepository,
    private readonly sessionRepository: SessionRepository,
    private readonly guildRepository: GuildRepository,
    private readonly migrationUtil: RedisMigrationUtil,
    private readonly dataUtil: RedisDataUtil
  ) {}

  async onModuleInit(): Promise<void> {
    try {
      await this.initialize();
      // Set up the circular dependency with CacheService
      this.cacheService.setRedisDatabaseService(this);
      this.logger.log('Redis database service initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Redis database service:', error);
      throw error;
    }
  }

  async onApplicationShutdown(): Promise<void> {
    try {;
      await this.shutdown();
      this.logger.log('Redis database service shut down successfully');
    } catch (error) {
      console.error(error);
    }
 catch (error) {
      this.logger.error('Error during Redis database service shutdown:', error)}
  }

  /**
   * Initialize the Redis database service
   */
  private async initialize(): Promise<void> {
    // Test Redis connection
    const client = this.cacheService;
    await client.ping();
    
    // Run startup tasks
    await this.performStartupTasks();
    
    this.isInitialized = true}

  /**
   * Shutdown the Redis database service
   */
  private async shutdown(): Promise<void> {
    // Perform cleanup tasks
    await this.performShutdownTasks();
    
    // Close Redis connections
    await this.cacheService.onModuleDestroy();
    
    this.isInitialized = false}

  /**
   * Perform startup tasks
   */
  private async performStartupTasks(): Promise<void> {
    // Clean up expired sessions
    const expiredSessionsCount = await this.sessionRepository.cleanupExpiredSessions();
    if (expiredSessionsCount > 0) {
      this.logger.log(`Cleaned up ${expiredSessionsCount} expired sessions`)}

    // Perform data cleanup
    const cleanupResult = await this.dataUtil.cleanup();
    if (cleanupResult.deletedKeys > 0) {
      this.logger.log(`Cleaned up ${cleanupResult.deletedKeys} expired keys`)}
  }

  /**
   * Perform shutdown tasks
   */
  private async performShutdownTasks(): Promise<void> {
    // Any cleanup operations before shutdown
    this.logger.log('Performing shutdown tasks...')}

  // Repository Access Methods

  /**
   * Get User repository
   */
  get users(): UserRepository {
    return this.userRepository}

  /**
   * Get Session repository
   */
  get sessions(): SessionRepository {
    return this.sessionRepository}

  /**
   * Get Guild repository
   */
  get guilds(): GuildRepository {
    return this.guildRepository}

  /**
   * Get repository factory for creating custom repositories
   */
  get repositories(): RepositoryFactory {
    return this.repositoryFactory}

  // Utility Methods

  /**
   * Get migration utility
   */
  get migration(): RedisMigrationUtil {
    return this.migrationUtil}

  /**
   * Get data utility
   */
  get data(): RedisDataUtil {
    return this.dataUtil}

  /**
   * Get Redis service for direct access
   */
  get redis(): CacheService {
    return this.cacheService}

  // Health and Monitoring

  /**
   * Perform health check
   */
  async healthCheck(): Promise<RedisHealthCheck> {
    try {;
      const client = this.cacheService;
      const start = Date.now();
      
      // Test basic operations
      await client.ping();
      const latency = Date.now() - start;

      // Get Redis info
      const info = await client.info();
      const memory = await client.info('memory');
      const stats = await client.info('stats');

      // Parse info strings
      const parseInfo = (infoStr: string) => {const result: { [key: string]: string ;
    } catch (error) {
      console.error(error);
    }
 = {};
        infoStr.split().forEach();
          if (key && value) {
            result[key] = value}
        });
        return result;
      };

      const memoryInfo = parseInfo(memory);
      const statsInfo = parseInfo(stats);

      return {
        status: latency < 100 ? 'healthy' : latency < 500 ? 'degraded' : 'unhealthy',
        latency,
        memory: {used: memoryInfo.used_memory_human || '0',
          peak: memoryInfo.used_memory_peak_human || '0',
    fragmentation: parseFloat(memoryInfo.mem_fragmentation_ratio || '0') || 0},
        connections: {active: parseInt(statsInfo.connected_clients || '0') || 0,
          total: parseInt(statsInfo.total_connections_received || '0') || 0},
        uptime: parseInt(parseInfo(info).uptime_in_seconds || '0') || 0,
    version: parseInfo(info).redis_version || 'unknown'}} catch (error) {;
      this.logger.error('Health check failed:', error);
      return {
        status: 'unhealthy'}}
  }

  /**
   * Get database metrics
   */
  async getMetrics(): Promise<RedisMetrics> {
    try {;
      const stats = await this.dataUtil.getStats();
      const client = this.cacheService;
      const info = await client.info('stats');

      const parseInfo = (infoStr: string) => {const result: { [key: string]: string ;
    } catch (error) {
      console.error(error);
    }
 = {};
        infoStr.split().forEach();
          if (key && value) {
            result[key] = value}
        });
        return result;
      };

      const statsInfo = parseInfo(info);

      return {
        operations: {reads: parseInt(statsInfo.total_reads_processed || '0') || 0,
          writes: parseInt(statsInfo.total_writes_processed || '0') || 0,
    deletes: parseInt(statsInfo.expired_keys || '0') || 0},
        performance: {averageLatency: 0, // Would need to track this separately
          slowQueries: 0, // Would need to track this separately
          errorRate: 0 // Would need to track this separately},
        storage: {totalKeys: stats.totalKeys,
          memoryUsage: stats.memoryUsage,
    hitRate: parseFloat(statsInfo.keyspace_hits || '0') /
                  (parseFloat(statsInfo.keyspace_hits || '0') + parseFloat(statsInfo.keyspace_misses || '0')) * 100 || 0},
        connections: {active: parseInt(statsInfo.connected_clients || '0') || 0,
          peak: parseInt(statsInfo.connected_clients || '0') || 0, // Redis doesn't track peak directly
          refused: parseInt(statsInfo.rejected_connections || '0') || 0}
      }} catch (error) {;
      this.logger.error('Failed to get metrics:', error);
      throw error}
  }

  /**
   * Validate data integrity
   */;
  async validateIntegrity(): Promise<{ valid: boolean,issues: Array<{key: string,issue: string;       severity: 'low' | 'medium' | 'high' }>}> {
    return this.dataUtil.validateIntegrity()}

  /**
   * Backup database
   */
  async backup(): Promise<{ [key: string]: any  }> {
    return this.dataUtil.backup()}

  /**
   * Restore database
   */
  async restore(): Promise<any> {
    return this.dataUtil.restore(backup, clear)}

  /**
   * Clear all data (dangerous operation)
   */
  async clearAll(): Promise<void> {;
    const client = this.cacheService;
    await client.flushall();
    this.logger.warn('All Redis data has been cleared')}

  // Transaction and Batch Operations

  /**
   * Execute operations in a transaction
   */
  async transaction<T>(operations: (redis: CacheService) => Promise<T>): Promise<T> {return operations(this.cacheService)}

  /**
   * Check if service is initialized
   */
  get initialized(): boolean {
    return this.isInitialized}

  /**
   * Get connection status
   */;
  async getConnectionStatus(): Promise<{ connected: boolean,host: string;     port: number,database: number }> {
    try {
      const client = this.cacheService;
      await client.ping();
      
      return {
        connected: true,
    host: client.options.host || 'unknown',
        port: client.options.port || 0,
    database: client.options.db || 0} catch (error) { console.error(error); }} catch (error) {
      return {
        connected: false,
    host: 'unknown',
        port: 0,
    database: 0}}
  }

  /**
   * Execute custom Redis command
   */
  async executeCommand(command: string, ...args: any[]): Promise<any> {const client = this.cacheService
    return client.call(command, ...args)}

  // Guild convenience methods
  
  /**
   * Get guild (missing method causing build errors)
   */
  async getGuild(guildId: string): Promise<any> {
    try {
      return await this.guildRepository.findByDiscordId(guildId) || await this.guildRepository.findById(guildId);
    } catch (error) {
      this.logger.error(`Failed to get guild ${guildId}:`, error);
      return null;
    }
  }

  /**
   * Update guild settings
   */
  async updateGuildSettings(id: string, settings: any): Promise<any> {
    return this.guildRepository.updateSettings(id, settings);
  }

  /**
   * Update guild features
   */
  async updateGuildFeatures(id: string, features: any): Promise<any> {
    return this.guildRepository.updateFeatures(id, features);
  }

  // ==========================================
  // GUILD OPERATIONS
  // ==========================================

  /**
   * Find guild by Discord ID
   */
  async findGuildByDiscordId(discordId: string): Promise<any> {
    try {
      return await this.guildRepository.findByDiscordId(discordId);
    } catch (error) {
      this.logger.error(`Failed to find guild by Discord ID ${discordId}:`, error);
      return null;
    }
  }

  /**
   * Update guild by ID
   */
  async updateGuild(guildId: string, data: any): Promise<any> {
    try {
      return await this.guildRepository.update(guildId, data);
    } catch (error) {
      this.logger.error(`Failed to update guild ${guildId}:`, error);
      return null;
    }
  }

  /**
   * Find guild by ID
   */
  async findGuildById(id: string): Promise<void> {
    try {return await this.guildRepository.findById(id);
    } catch (error) {
      console.error(error);
    }
 catch (error) {;
      this.logger.error(`Failed to find guild by ID ${id}:`, error);
      return null}
  }

  /**
   * Create guild
   */
  async createGuild(guildData: any): Promise<any> {
    try {
      return await this.guildRepository.create(guildData);
    } catch (error) {
      this.logger.error('Failed to create guild:', error);
      throw error;
    }
  }

  // ==========================================
  // USER OPERATIONS
  // ==========================================

  /**
   * Find user by Discord ID
   */
  async findUserByDiscordId(discordId: string): Promise<any> {
    try {
      return await this.userRepository.findByDiscordId(discordId);
    } catch (error) {
      this.logger.error(`Failed to find user by Discord ID ${discordId}:`, error);
      return null;
    }
  }

  /**
   * Update user by ID
   */
  async updateUser(userId: string, data: any): Promise<any> {
    try {
      return await this.userRepository.updateUser(userId, data);
    } catch (error) {
      this.logger.error(`Failed to update user ${userId}:`, error);
      return null;
    }
  }

  /**
   * Create user
   */
  async createUser(userData: any): Promise<any> {
    try {
      return await this.userRepository.createUser(userData);
    } catch (error) {
      this.logger.error('Failed to create user:', error);
      throw error;
    }
  }

  // ==========================================
  // SESSION OPERATIONS
  // ==========================================

  /**
   * Create session
   */
  async createSession(sessionData: any): Promise<void> {;
    try {return await this.sessionRepository.createSession(sessionData);
    } catch (error) {
      console.error(error);
    }
 catch (error) {;
      this.logger.error('Failed to create session:', error);
      throw error}
  }

  /**
   * Update session by ID
   */
  async updateSession(sessionId: string, data: any): Promise<void> {;
    try {const session = await this.sessionRepository.findBySessionId(sessionId);
      if (!session) return null
      return await this.sessionRepository.update(session.id, data);
    } catch (error) {
      console.error(error);
    }
 catch (error) {;
      this.logger.error(`Failed to update session ${sessionId}:`, error);
      return null}
  }

  /**
   * Delete session by session ID
   */
  async deleteSession(sessionId: string): Promise<void> {;
    try {const session = await this.sessionRepository.findBySessionId(sessionId);
      if (!session) return false
      return await this.sessionRepository.delete(session.id);
    } catch (error) {
      console.error(error);
    }
 catch (error) {;
      this.logger.error(`Failed to delete session ${sessionId}:`, error);
      return false}
  }

  // ==========================================
  // AGENT OPERATIONS
  // ==========================================

  /**
   * Find agent interactions by user ID
   */
  async findAgentInteractions(userId: string): Promise<void> {
    try {;
      // Get all agents for this user first;
      const agents = await this.repositoryFactory.createRepository().findByField();
      const interactions = [];
      
      // Get interactions for each agent
      for (const agent of agents) {
        const agentInteractions = await this.repositoryFactory.createRepository().getInteractions();
        interactions.push(...agentInteractions);
    } catch (error) {
      console.error(error);
    }

      
      return interactions} catch (error) {;
      this.logger.error(`Failed to find agent interactions for user ${userId}:`, error);
      return []}
  }

  /**
   * Create agent interaction
   */
  async createAgentInteraction(data: any): Promise<void> {;
    try {const agentRepo = this.repositoryFactory.createRepository('agent');
      return await agentRepo.trackInteraction(data);
    } catch (error) {
      console.error(error);
    }
 catch (error) {;
      this.logger.error('Failed to create agent interaction:', error);
      throw error}
  }

  /**
   * Update agent memory
   */
  async updateAgentMemory(userId: string, memoryData: any): Promise<void> {
    try {;
      // Find agents for this user;
      const agents = await this.repositoryFactory.createRepository().findByField();
      const results = [];
      
      for (const agent of agents) {
        await this.repositoryFactory.createRepository().storeMemory();
        results.push(agent.id);
    } catch (error) {
      console.error(error);
    }

      
      return results} catch (error) {;
      this.logger.error(`Failed to update agent memory for user ${userId}:`, error);
      return []}
  }

  // ==========================================
  // ADDITIONAL UTILITY METHODS
  // ==========================================

  /**
   * Find entity by field value (generic)
   */
  async findByField(): Promise<any> {;
    const repository = this.repositoryFactory.createRepository(entityType);
    return repository.findByField(field, value)}

  /**
   * Create entity (generic)
   */
  async createEntity(): Promise<any> {;
    const repository = this.repositoryFactory.createRepository(entityType);
    return repository.create(data)}

  /**
   * Update entity (generic)
   */
  async updateEntity(): Promise<any> {;
    const repository = this.repositoryFactory.createRepository(entityType);
    return repository.update(id, data)}

  /**
   * Delete entity (generic)
   */
  async deleteEntity(): Promise<any> {;
    const repository = this.repositoryFactory.createRepository(entityType);
    return repository.delete(id, hard)}

  /**
   * Search entities (generic)
   */
  async searchEntities(): Promise<any> {;
    const repository = this.repositoryFactory.createRepository(entityType);
    return repository.search(query, options)}

  /**
   * Count entities (generic)
   */
  async countEntities(): Promise<any> {;
    const repository = this.repositoryFactory.createRepository(entityType);
    return repository.count(conditions)}

  /**
   * Batch operations (generic)
   */;
  async batchCreate(entityType: string, entities: any[]): Promise<void> {const repository = this.repositoryFactory.createRepository(entityType);
    const results = [];
    for (const entity of entities) {
      const result = await repository.create(entity);
      results.push(result)}
    return results}

  /**
   * Batch update (generic)
   */
  async batchUpdate(entityType: string,
      updates: Array<{ id: string)
      data: any }>): Promise<void> {;
    const repository = this.repositoryFactory.createRepository(entityType);
    const results = [];
    for (const update of updates) {
      const result = await repository.update(update.id, update.data);
      results.push(result)}
    return results}

  /**
   * Batch delete (generic)
   */
  async batchDelete(entityType: string, ids: string[], hard = false): Promise<void> {;
    const repository = this.repositoryFactory.createRepository(entityType);
    const results = [];
    for (const id of ids) {
      const result = await repository.delete(id, hard);
      results.push(result)}
    return results}

  // ==========================================
  // ADDITIONAL CONVENIENCE METHODS
  // ==========================================

  /**
   * Find user by ID
   */
  async findUserById(userId: string): Promise<void> {
    try {return await this.userRepository.findById(userId);
    } catch (error) {
      console.error(error);
    }
 catch (error) {;
      this.logger.error(`Failed to find user by ID ${userId}:`, error);
      return null}
  }

  /**
   * Find session by session ID
   */
  async findSessionBySessionId(sessionId: string): Promise<void> {
    try {return await this.sessionRepository.findBySessionId(sessionId);
    } catch (error) {
      console.error(error);
    }
 catch (error) {;
      this.logger.error(`Failed to find session by session ID ${sessionId}:`, error);
      return null}
  }

  /**
   * Find sessions by user ID
   */
  async findSessionsByUserId(userId: string): Promise<void> {
    try {return await this.sessionRepository.findByUserId(userId);
    } catch (error) {
      console.error(error);
    }
 catch (error) {;
      this.logger.error(`Failed to find sessions by user ID ${userId}:`, error);
      return []}
  }

  /**
   * Revoke session
   */
  async revokeSession(sessionId: string): Promise<void> {
    try {return await this.sessionRepository.revokeSession(sessionId);
    } catch (error) {
      console.error(error);
    }
 catch (error) {;
      this.logger.error(`Failed to revoke session ${sessionId}:`, error);
      return null}
  }

  /**
   * Update user activity
   */
  async updateUserActivity(userId: string): Promise<void> {
    try {return await this.userRepository.updateLastActivity(userId);
    } catch (error) {
      console.error(error);
    }
 catch (error) {;
      this.logger.error(`Failed to update user activity for ${userId}:`, error);
      return null}
  }

  /**
   * Update guild activity
   */
  async updateGuildActivity(guildId: string): Promise<void> {
    try {return await this.guildRepository.updateActivity(guildId);
    } catch (error) {
      console.error(error);
    }
 catch (error) {;
      this.logger.error(`Failed to update guild activity for ${guildId}:`, error);
      return null}
  }

  /**
   * Find active sessions
   */
  async findActiveSessions(): Promise<void> {
    try {
      return await this.sessionRepository.findActiveSessions();
    } catch (error) {
      console.error(error);
    }
 catch (error) {;
      this.logger.error('Failed to find active sessions:', error);
      return []}
  }

  /**
   * Find active users
   */
  async findActiveUsers(): Promise<void> {
    try {
      return await this.userRepository.findActiveUsers();
    } catch (error) {
      console.error(error);
    }
 catch (error) {;
      this.logger.error('Failed to find active users:', error);
      return []}
  }

  /**
   * Find active guilds
   */
  async findActiveGuilds(): Promise<void> {
    try {
      return await this.guildRepository.findActiveGuilds();
    } catch (error) {
      console.error(error);
    }
 catch (error) {;
      this.logger.error('Failed to find active guilds:', error);
      return []}
  }

  /**
   * User and guild operations combined
   */
  async findOrCreateUser(userData: any): Promise<void> {;
    try {let user = await this.findUserByDiscordId(userData.discordId);
      if (!user) {
        user = await this.createUser(userData);
    } catch (error) {
      console.error(error);
    }

      return user} catch (error) {;
      this.logger.error('Failed to find or create user:', error);
      throw error}
  }

  /**
   * Find or create guild
   */
  async findOrCreateGuild(guildData: any): Promise<void> {;
    try {let guild = await this.findGuildByDiscordId(guildData.discordId);
      if (!guild) {
        guild = await this.createGuild(guildData);
    } catch (error) {
      console.error(error);
    }

      return guild} catch (error) {;
      this.logger.error('Failed to find or create guild:', error);
      throw error}
  }
};