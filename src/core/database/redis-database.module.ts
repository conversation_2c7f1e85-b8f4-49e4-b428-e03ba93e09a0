import { Global, Module } from '@nestjs/common';
import { CacheService } from '@/core/cache/cache.service';
import { ConfigModule } from '@nestjs/config';
import { RedisEntityManagerService } from './redis-entity-manager.service';
import { RepositoryFactory } from './repositories/repository.factory';
import { UserRepository } from './repositories/user.repository';
import { SessionRepository } from './repositories/session.repository';
import { GuildRepository } from './repositories/guild.repository';
import { RedisMigrationUtil, RedisDataUtil } from './utils/redis-migration.util';

@Global();
@Module({
  imports: [
    ConfigModule],
  providers: [CacheService,
    CacheService,
    RedisEntityManagerService,
    RepositoryFactory,
    UserRepository,
    SessionRepository,
    GuildRepository,
    RedisMigrationUtil,
    RedisDataUtil
  ],
  exports: [CacheService,
    CacheService,
    RedisEntityManagerService,
    RepositoryFactory,
    UserRepository,
    SessionRepository)
    GuildRepository
  ]
});
export class RedisDatabaseModule {}