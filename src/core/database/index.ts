// Core database services
export { DatabaseModule } from './database.module';
export { DatabaseService, REDIS_CONNECTION } from './database.service';
export { RedisQueryBuilder } from './query-builder';
export { RedisDatabaseModule } from './redis-database.module';
export { RedisEntityManagerService } from './redis-entity-manager.service';
export { CacheService } from './redis.service';

// Constants
export * from './database.constants';

// Repositories - comprehensive exports
export {
    AgentRepository, BaseRedisRepositoryImpl, GuildRepository, RepositoryFactory, SessionRepository, UserRepository
} from './repositories';

// Repository index
export * from './repositories/index';

// Types and interfaces - comprehensive exports
export type {
    ActivityTracked, Auditable, BaseEntity, ChannelSpecific, ConfigurationStorage, CreateEntity, EntityKeyGenerator, GuildSpecific, MetadataStorage, PreferenceStorage,
    ProfileStorage, Searchable, StatisticsStorage, Taggable, UpdateEntity,
    UserOwned, Versioned
} from './types/base.interface';

export type {
    RedisRepository as BaseRedisRepository,
    RedisRepository as ExtendedRedisRepository, RedisConfig, RedisError, RedisOperationResult, RedisSearchOptions, RedisTransaction
} from './types/redis.types';

// Export RedisSerializer from types/index.ts
export { RedisSerializer } from './types/index';

export type {
    RepositoryOptions
} from './types/repository.types';

// All types index (Redis-compatible entity interfaces and related types)
export * from './types';

// Utilities
export * from './utils';
export { RedisDataUtil, RedisMigrationUtil } from './utils';

// Schema exports - All entity types and enums
export * from './schema';
export * from './schema.ts';
export * from './schema-types';

// Specific entity type exports for common imports
export type {
    // AI Agent types
    AIAgentConfig, AgentConfiguration,
    AgentPermissions, AgentType, CreateAIAgentConfig, NewAIAgentConfig, UpdateAIAgentConfig
} from './entities/ai-agent-config.entity';

export type {
    // Agent Memory types
    AgentMemory,
    CreateAgentMemory, GoalStatus,
    MemoryGoal, MemorySentiment, MemoryType,
    MemoryValue, NewAgentMemory, UpdateAgentMemory
} from './entities/agent-memory.entity';

export type {
    // Agent Interaction types
    AgentInteraction,
    CreateAgentInteraction, InteractionContext,
    InteractionMetadata, InteractionStatus, InteractionType, UpdateAgentInteraction, UserMood
} from './entities/agent-interaction.entity';

export type {
    CreateUser, NewUser, UpdateUser,
    // User types
    User, UserPreferences, UserProfile
} from './entities/user.entity';

export type {
    CreateGuild,
    // Guild types
    Guild, GuildFeatures, GuildSettings, NewGuild, UpdateGuild, WelcomeRoles
} from './entities/guild.entity';

export type {
    CreateSession,
    // Session types
    Session, SessionMetadata, UpdateSession
} from './entities/session.entity';

export type {
    CreateOrganization,
    // Organization types
    Organization, UpdateOrganization
} from './entities/organization.entity';

// Table exports for Drizzle-style compatibility
export { guilds } from './entities/guild.entity';
export { organizations } from './entities/organization.entity';
export { users } from './entities/user.entity';

