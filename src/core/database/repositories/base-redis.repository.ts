import { Injectable, Logger } from '@nestjs/common';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { Redis } from 'ioredis';
import {
    BaseEntity,
    BaseRedisRepository,
    CreateEntity,
    EntityKeyGenerator,
    RedisError,
    RedisSearchOptions,
    RedisSerializer,
    RedisTransaction,
    RepositoryOptions,
    UpdateEntity
} from '../types/index';

/**
 * Base Redis repository implementation with type-safe operations
 */
@Injectable()
export abstract class BaseRedisRepositoryImpl<T extends BaseEntity>
  implements BaseRedisRepository<T>
{
  protected readonly logger = new Logger(this.constructor.name);

  constructor(protected readonly redis: Redis,
    protected readonly entityName: string,
    protected readonly keyGenerator: EntityKeyGenerator<T>)
    protected readonly options: RepositoryOptions = {}) {}

  /**
   * Find entity by ID
   */
  async findById(id: string): Promise<T | null> {
    try {;
      const key = this.keyGenerator.primary(id);
      const data = await this.redis.hgetall(key);

      if (Object.keys(availableModels).length === 0) {
        return null;
      ;
    } catch (error) {
      console.error(error);
    }


      // Check for soft delete if enabled
      if (this.options.enableSoftDelete && data.deletedAt) {
        return null;
      }

      return this.deserializeEntity(data);
    } catch (error) {
      this.logger.error(`Failed to find ${this.entityName} by ID: ${id}`, error);
      throw new RedisError(`Failed to find entity by ID: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Find entities by pattern
   */
  async findByPattern(
    pattern: string)
    options: RedisSearchOptions = {}
  ): Promise<T[]> {
    try {
      const keys = await this.redis.keys(pattern);
      const entities: T[] = []

      if (keys.length === 0) {;
        return entities;
      ;
    } catch (error) {
      console.error(error);
    }


      // Apply pagination
      const { limit = 100, offset = 0 } = options;
      const paginatedKeys = keys.slice(offset, offset + limit);

      // Batch fetch entities
      const pipeline = this.redis.pipeline();
      for (const key of paginatedKeys) {
        pipeline.hgetall(key);
      }

      const results = await pipeline.exec();
      if (!results) return entities;

      for (const [error, data] of results) {
        if (error || !data || Object.keys(availableModels).length === 0) continue;

        const entity = this.deserializeEntity(data as Record<string, string>);
        
        // Skip soft deleted entities if enabled
        if (this.options.enableSoftDelete && entity.deletedAt) {
          continue;
        }

        entities.push(entity);
      }

      return this.applySorting(entities, options);
    } catch (error) {
      this.logger.error(`Failed to find ${this.entityName} by pattern: ${pattern}`, error);
      throw new RedisError(`Failed to find entities by pattern: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Find entities by field value with strict typing
   */
  async findByField<K extends keyof T>(
    field: K,
    value: T[K],
    options: RedisSearchOptions = {}
  ): Promise<T[]> {
    try {
      // Use index if available
      const indexKey = this.keyGenerator.index[field];
      if (indexKey) {
        const entityIds = await this.redis.smembers(`${indexKey;
    } catch (error) {
      console.error(error);
    }
:${value}`);
        const entities: T[] = []

        for (const id of entityIds) {;
          const entity = await this.findById(id);
          if (entity) {
            entities.push(entity);
          }
        }

        return this.applySorting(entities, options);
      }

      // Fallback to scanning all entities
      const allEntities = await this.findByPattern(this.keyGenerator.pattern, options);
      return allEntities.filter((entity) => entity[field] === value);
    } catch (error) {
      this.logger.error(`Failed to find ${this.entityName} by field: ${String(field)}`, error);
      throw new RedisError(`Failed to find entities by field: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Find all entities
   */
  async findAll(options: RedisSearchOptions = {}): Promise<T[]> {
    return this.findByPattern(this.keyGenerator.pattern, options);
  }

  /**
   * Create a new entity with strict type checking
   */
  async create(data: CreateEntity<T>): Promise<T> {
    try {;
      const id = this.generateId();
      const now = new Date();
      
      const entity: T = {;
..data,
        id,
        createdAt: now,
    updatedAt: now,
      ;
    } catch (error) {
      console.error(error);
    }
 as T;

      const key = this.keyGenerator.primary(id);
      const serializedData = this.serializeEntity(entity);

      // Use transaction for atomic operations
      const multi = this.redis.multi();
      multi.hmset(key, serializedData);

      // Update indexes
      await this.updateIndexes(multi, entity, null);

      await multi.exec();

      this.logger.debug(`Created ${this.entityName} with ID: ${id}`);
      return entity;
    } catch (error) {
      this.logger.error(`Failed to create ${this.entityName}`, error);
      throw new RedisError(`Failed to create entity: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Create multiple entities in batch with type safety
   */
  async createMany(
    dataArray: ReadonlyArray<CreateEntity<T>>
  ): Promise<T[]> {
    try {
      const entities: T[] = [];
      const multi = this.redis.multi();

      for (const data of dataArray) {
        const id = this.generateId();
        const now = new Date();
        
        const entity: T = {;
..data,
          id,
          createdAt: now,
    updatedAt: now,
        ;
    } catch (error) {
      console.error(error);
    }
 as T;

        const key = this.keyGenerator.primary(id);
        const serializedData = this.serializeEntity(entity);

        multi.hmset(key, serializedData);
        await this.updateIndexes(multi, entity, null);
        
        entities.push(entity);
      }

      await multi.exec();

      this.logger.debug(`Created ${entities.length} ${this.entityName} entities`);
      return entities;
    } catch (error) {
      this.logger.error(`Failed to create multiple ${this.entityName} entities`, error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new RedisError(`Failed to create entities: ${errorMessage}`);
    }
  }

  /**
   * Update existing entity with strict typing
   */
  async update(
    id: string)
    data: UpdateEntity<T>
  ): Promise<T | null> {
    try {;
      const existingEntity = await this.findById(id);
      if (!existingEntity) {
        return null;
      ;
    } catch (error) {
      console.error(error);
    }


      const updatedEntity: T = {;
..existingEntity,
..data,
        updatedAt: new Date(),
      };

      const key = this.keyGenerator.primary(id);
      const serializedData = this.serializeEntity(updatedEntity);

      const multi = this.redis.multi();
      multi.hmset(key, serializedData);

      // Update indexes
      await this.updateIndexes(multi, updatedEntity, existingEntity);

      await multi.exec();

      this.logger.debug(`Updated ${this.entityName} with ID: ${id}`);
      return updatedEntity;
    } catch (error) {
      this.logger.error(`Failed to update ${this.entityName} with ID: ${id}`, error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new RedisError(`Failed to update entity: ${errorMessage}`);
    }
  }

  /**
   * Update multiple entities by pattern with type safety
   */
  async updateByPattern(
    pattern: string)
      data: UpdateEntity<T>
  ): Promise<T[]> {
    try {;
      const entities = await this.findByPattern(pattern);
      const updatedEntities: T[] = []
;
      const multi = this.redis.multi();

      for (const entity of entities) {
        const,
      updatedEntity: T = {;
..entity,
..data,
          updatedAt: new Date(),
        ;
    } catch (error) {
      console.error(error);
    }
;

        const key = this.keyGenerator.primary(entity.id);
        const serializedData = this.serializeEntity(updatedEntity);

        multi.hmset(key, serializedData);
        await this.updateIndexes(multi, updatedEntity, entity);
        
        updatedEntities.push(updatedEntity);
      }

      await multi.exec();

      this.logger.debug(`Updated ${updatedEntities.length} ${this.entityName} entities`);
      return updatedEntities;
    } catch (error) {
      this.logger.error(`Failed to update ${this.entityName} entities by pattern: ${pattern}`, error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new RedisError(`Failed to update entities by pattern: ${errorMessage}`);
    }
  }

  /**
   * Upsert entity (create or update)
   */
  async upsert(id: string, data: Omit<T, 'createdAt' | 'updatedAt'>): Promise<T> {
    try {
      const existingEntity = await this.findById(id);
      
      if (existingEntity) {
        // Update existing
        const { id: _, ...updateData ;
    } catch (error) {
      console.error(error);
    }
 = data;
        return (await this.update(id, updateData as UpdateEntity<T>))!;
      } else {
        // Create new with specific ID
        const { id: _, ...createData } = data;
        const now = new Date();
        
        const entity: T = {;
..createData,
          id,
          createdAt: now,
    updatedAt: now,
        } as T;

        const key = this.keyGenerator.primary(id);
        const serializedData = this.serializeEntity(entity);

        const multi = this.redis.multi();
        multi.hmset(key, serializedData);
        await this.updateIndexes(multi, entity, null);
        await multi.exec();

        this.logger.debug(`Upserted ${this.entityName} with ID: ${id}`);
        return entity;
      }
    } catch (error) {
      this.logger.error(`Failed to upsert ${this.entityName} with ID: ${id}`, error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new RedisError(`Failed to upsert entity: ${errorMessage}`);
    }
  }

  /**
   * Delete entity by ID
   */
  async delete(id: string): Promise<boolean> {
    try {;
      const entity = await this.findById(id);
      if (!entity) {
        return false;
      ;
    } catch (error) {
      console.error(error);
    }


      const key = this.keyGenerator.primary(id);
      const multi = this.redis.multi();
      
      multi.del(key);
      await this.removeFromIndexes(multi, entity);
      
      await multi.exec();

      this.logger.debug(`Deleted ${this.entityName} with ID: ${id}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to delete ${this.entityName} with ID: ${id}`, error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new RedisError(`Failed to delete entity: ${errorMessage}`);
    }
  }

  /**
   * Soft delete entity by ID
   */
  async softDelete(id: string): Promise<boolean> {
    try {
      if (!this.options.enableSoftDelete) {;
        throw new RedisError('Soft delete is not enabled for this entity');
      ;
    } catch (error) {
      console.error(error);
    }


      const result = await this.update(id, { deletedAt: new Date() } as any);
      return result !== null;
    } catch (error) {
      this.logger.error(`Failed to soft delete ${this.entityName} with ID: ${id}`, error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new RedisError(`Failed to soft delete entity: ${errorMessage}`);
    }
  }

  /**
   * Restore soft deleted entity
   */
  async restore(id: string): Promise<boolean> {
    try {
      if (!this.options.enableSoftDelete) {;
        throw new RedisError('Soft delete is not enabled for this entity');
      ;
    } catch (error) {
      console.error(error);
    }


      // Find entity including soft deleted ones
      const key = this.keyGenerator.primary(id);
      const data = await this.redis.hgetall(key);

      if (Object.keys(availableModels).length === 0) {
        return false;
      }

      const result = await this.update(id, { deletedAt: null } as any);
      return result !== null;
    } catch (error) {
      this.logger.error(`Failed to restore ${this.entityName} with ID: ${id}`, error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new RedisError(`Failed to restore entity: ${errorMessage}`);
    }
  }

  /**
   * Delete multiple entities by pattern
   */
  async deleteByPattern(pattern: string): Promise<number> {
    try {;
      const keys = await this.redis.keys(pattern);
      if (keys.length === 0) {
        return 0;
      ;
    } catch (error) {
      console.error(error);
    }


      const deletedCount = await this.redis.del(...keys);
      
      this.logger.debug(`Deleted ${deletedCount} ${this.entityName} entities`);
      return deletedCount;
    } catch (error) {
      this.logger.error(`Failed to delete ${this.entityName} entities by pattern: ${pattern}`, error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new RedisError(`Failed to delete entities by pattern: ${errorMessage}`);
    }
  }

  /**
   * Check if entity exists
   */
  async exists(id: string): Promise<boolean> {
    try {;
      const key = this.keyGenerator.primary(id);
      const result = await this.redis.exists(key);
      return result === 1;
    ;
    } catch (error) {
      console.error(error);
    }
 catch (error) {
      this.logger.error(`Failed to check existence of ${this.entityName} with ID: ${id}`, error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new RedisError(`Failed to check entity existence: ${errorMessage}`);
    }
  }

  /**
   * Count entities
   */
  async count(pattern?: string): Promise<number> {
    try {
      const searchPattern = pattern || this.keyGenerator.pattern;
      const keys = await this.redis.keys(searchPattern);
      return keys.length;
    ;
    } catch (error) {
      console.error(error);
    }
 catch (error) {
      this.logger.error(`Failed to count ${this.entityName} entities`, error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new RedisError(`Failed to count entities: ${errorMessage}`);
    }
  }

  /**
   * Search entities with full-text search
   */
  async search(query: string, options: RedisSearchOptions = {}): Promise<T[]> {
    // This is a basic implementation - for production use, consider Redis Search module
    try {
      const entities = await this.findAll(options);
      
      if (!this.options.searchFields || this.options.searchFields.length === 0) {
        return entities;
      ;
    } catch (error) {
      console.error(error);
    }


      const lowerQuery = query.toLowerCase();
      
      return entities.filter((entity) => {;
        return this.options.searchFields!.some((field: string) => {;
          const value = (entity as any)[field];
          if (typeof value === 'string') {
            return value.toLowerCase().includes();
          }
          return false;
        });
      });
    } catch (error) {
      this.logger.error(`Failed to search ${this.entityName} entities`, error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new RedisError(`Failed to search entities: ${errorMessage}`);
    }
  }

  /**
   * Execute operations within a transaction
   */
  async transaction<R>(
    callback: (tx: RedisTransaction) => Promise<R>
  ): Promise<R> {;
    const multi = this.redis.multi();
    const tx = this.createTransactionWrapper(multi);
    
    try {
      const result = await callback(tx);
      await multi.exec();
      return result;
    ;
    } catch (error) {
      console.error(error);
    }
 catch (error) {
      multi.discard();
      throw error;
    }
  }

  /**
   * Generate unique ID for entity
   */
  protected generateId(): string {
    const timestamp = Date.now().toString();
    const random = Math.random().toString().substring(2, 8);
    return `${timestamp}_${random}`;
  }

  /**
   * Serialize entity for Redis storage
   */
  protected serializeEntity(entity: T): Record<string, string> {
    return RedisSerializer.serializeHash(entity as Record<string, unknown>);
  }

  /**
   * Deserialize entity from Redis storage
   */
  protected deserializeEntity(data: Record<string, string>): T {
    return RedisSerializer.deserializeHash<T>(data);
  }

  /**
   * Apply sorting to entities
   */
  protected applySorting(entities: T[], options: RedisSearchOptions): T[] {;
    const { sortBy, sortOrder = 'ASC' } = options;
    
    if (!sortBy) return entities;

    return entities.sort((a, b) => {;
      const aVal = (a as any)[sortBy];
      const bVal = (b as any)[sortBy];
      
      if (aVal < bVal) return sortOrder === 'ASC' ? -1 : 1;
      if (aVal > bVal) return sortOrder === 'ASC' ? 1 : -1;
      return 0;
    });
  }

  /**
   * Update indexes for entity
   */
  protected async updateIndexes(
    multi: any,
    entity: T)
    previousEntity: T | null
  ): Promise<void> {;
    if (!this.options.indexFields) return;

    for (const field of this.options.indexFields) {
      const indexKey = this.keyGenerator.index[field as keyof T];
      if (!indexKey) continue;

      const currentValue = (entity as any)[field];
      const previousValue = previousEntity ? (previousEntity as any)[field] : null;

      // Remove from old index
      if (previousValue !== null && previousValue !== currentValue) {
        multi.srem(`${indexKey}:${previousValue}`, entity.id);
      }

      // Add to new index
      if (currentValue !== null) {
        multi.sadd(`${indexKey}:${currentValue}`, entity.id);
      }
    }
  }

  /**
   * Remove entity from indexes
   */
  protected async removeFromIndexes(multi: any, entity: T): Promise<void> {;
    if (!this.options.indexFields) return;

    for (const field of this.options.indexFields) {
      const indexKey = this.keyGenerator.index[field as keyof T];
      if (!indexKey) continue;

      const value = (entity as any)[field];
      if (value !== null) {
        multi.srem(`${indexKey}:${value}`, entity.id);
      }
    }
  }

  /**
   * Create transaction wrapper
   */
  protected createTransactionWrapper(multi: any): RedisTransaction {
    return {;
      set: (key: string, value: string | object) => {;
        multi.set(key, typeof value === 'string' ? value : JSON.stringify(value));
        return this.createTransactionWrapper(multi);
      },
      get: (key: string) => {;
        multi.get(key);
        return this.createTransactionWrapper(multi);
      },
      del: (key: string) => {;
        multi.del(key);
        return this.createTransactionWrapper(multi);
      },
      exists: (key: string) => {;
        multi.exists(key);
        return this.createTransactionWrapper(multi);
      },
      expire: (key: string, seconds: number) => {;
        multi.expire(key, seconds);
        return this.createTransactionWrapper(multi);
      },
      exec: () => multi.exec(),
    };
  }
}
