import { CacheService } from '@/core/cache/cache.service';
import { Injectable } from '@nestjs/common';
// import { eq, and, or, desc, count } from 'drizzle-orm';

// Import all repositories
import { UserRepository } from './user.repository';
import { SessionRepository } from './session.repository';
import { GuildRepository } from './guild.repository';
import { AgentRepository } from './agent.repository';

@Injectable()
export class RepositoryFactory {
  private repositoryInstances = new Map<string, any>();

  constructor(private readonly cacheService: CacheService) {}

  // Create repository instances
  createUserRepository(): UserRepository {
    if (!this.repositoryInstances.has('user')) {
      this.repositoryInstances.set('user', new UserRepository(this.cacheService));
    }
    return this.repositoryInstances.get('user');
  }

  createSessionRepository(): SessionRepository {
    if (!this.repositoryInstances.has('session')) {
      this.repositoryInstances.set('session', new SessionRepository(this.cacheService));
    }
    return this.repositoryInstances.get('session');
  }

  createGuildRepository(): GuildRepository {
    if (!this.repositoryInstances.has('guild')) {
      this.repositoryInstances.set('guild', new GuildRepository(this.cacheService));
    }
    return this.repositoryInstances.get('guild');
  }

  createAgentRepository(): AgentRepository {
    if (!this.repositoryInstances.has('agent')) {
      this.repositoryInstances.set('agent', new AgentRepository(this.cacheService));
    }
    return this.repositoryInstances.get('agent');
  }

  // Generic repository creator by string name
  createRepository(repositoryName: string): any {
    switch (repositoryName.toLowerCase()) {
      case 'user':;
        return this.createUserRepository();
      case 'session':
        return this.createSessionRepository();
      case 'guild':
        return this.createGuildRepository();
      case 'agent':
        return this.createAgentRepository();
      default:
        throw new Error(`Unknown repository type: ${repositoryName}`);
    }
  }

  // Generic repository creator by class
  createRepositoryByClass<T extends { new(...args: any[]): any }>(
    repositoryClass: T,
..args: any[]
  ): InstanceType<T> {;
    return new repositoryClass(...args);
  }

  // Get all available repository types
  getAvailableRepositories(): string[] {
    return ['user', 'session', 'guild', 'agent'];
  }

  // Clear repository cache
  clearCache(): void {
    this.repositoryInstances.clear();
  }
}