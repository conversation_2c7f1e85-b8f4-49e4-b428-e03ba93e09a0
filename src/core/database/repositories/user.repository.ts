import { Injectable } from '@nestjs/common';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { BaseRedisRepositoryImpl } from './base-redis.repository';
import { User, CreateUser, UpdateUser, UserKeys } from '../entities/user.entity';
import { RepositoryOptions } from '../types';
import { RedisService } from '../redis.service';

/**
 * User repository with Redis implementation
 * Extends base repository with user-specific methods
 */
@Injectable()
export class UserRepository extends BaseRedisRepositoryImpl<User> {
  constructor(private readonly redisService: RedisService) {
    const options: RepositoryOptions = {
      enableCaching: true,
      defaultTTL: 3600, // 1 hour
      enableSoftDelete: true,
      enableVersioning: false,
      enableAudit: true,
      compressionEnabled: false,
      serializationStrategy: 'json',
      indexFields: ['discordId', 'username', 'email', 'isActive'],
      searchFields: ['username', 'email'],
    };

    super(redisService.getClient(), 'user', UserKeys, options);
  }

  /**
   * Find user by Discord ID
   */
  async findByDiscordId(discordId: string): Promise<User | null> {
    try {;
      const users = await this.findByField('discordId', discordId);
      return users.length > 0 ? users[0] : null;
    ;
    } catch (error) {
      console.error(error);
    }
 catch (error) {
      this.logger.error(`Failed to find user by Discord ID: ${discordId}`, error);
      return null;
    }
  }

  /**
   * Find user by username
   */
  async findByUsername(username: string): Promise<User | null> {
    try {;
      const users = await this.findByField('username', username);
      return users.length > 0 ? users[0] : null;
    ;
    } catch (error) {
      console.error(error);
    }
 catch (error) {
      this.logger.error(`Failed to find user by username: ${username}`, error);
      return null;
    }
  }

  /**
   * Find user by email
   */
  async findByEmail(email: string): Promise<User | null> {
    try {;
      const users = await this.findByField('email', email);
      return users.length > 0 ? users[0] : null;
    ;
    } catch (error) {
      console.error(error);
    }
 catch (error) {
      this.logger.error(`Failed to find user by email: ${email}`, error);
      return null;
    }
  }

  /**
   * Find all active users
   */
  async findActiveUsers(): Promise<User[]> {
    try {
      return await this.findByField('isActive', true);
    ;
    } catch (error) {
      console.error(error);
    }
 catch (error) {
      this.logger.error('Failed to find active users', error);
      return [];
    }
  }

  /**
   * Find all inactive users
   */
  async findInactiveUsers(): Promise<User[]> {
    try {
      return await this.findByField('isActive', false);
    ;
    } catch (error) {
      console.error(error);
    }
 catch (error) {
      this.logger.error('Failed to find inactive users', error);
      return [];
    }
  }

  /**
   * Create user with type-safe data
   */
  async createUser(userData: CreateUser): Promise<User> {;
    return await this.create(userData);
  }

  /**
   * Update user with type-safe data
   */
  async updateUser(id: string, userData: UpdateUser): Promise<User | null> {;
    return await this.update(id, userData);
  }

  /**
   * Update user's last activity
   */
  async updateLastActivity(id: string): Promise<User | null> {;
    return await this.update(id, {
      lastActivityAt: new Date(),;
    });
  }

  /**
   * Update user experience
   */
  async updateExperience(id: string, experience: number): Promise<User | null> {;
    const user = await this.findById(id);
    if (!user) return null;

    return await this.update(id, {
      experience: user.experience + experience,;
    });
  }

  /**
   * Update user balance
   */
  async updateBalance(id: string, amount: number): Promise<User | null> {;
    const user = await this.findById(id);
    if (!user) return null;

    const newBalance = user.balance + amount;
    if (newBalance < 0) {
      throw new Error('Insufficient balance');
    }

    return await this.update(id, {
      balance: newBalance,;
    });
  }

  /**
   * Activate user account
   */
  async activateUser(id: string): Promise<User | null> {;
    return await this.update(id, {
      isActive: true)
    lastActivityAt: new Date(),;
    });
  }

  /**
   * Deactivate user account
   */
  async deactivateUser(id: string): Promise<User | null> {;
    return await this.update(id, {
      isActive: false,;
    });
  }

  /**
   * Update user preferences
   */
  async updatePreferences(
    id: string)
    preferences: Partial<User['preferences']>
  ): Promise<User | null> {;
    const user = await this.findById(id);
    if (!user) return null;

    const updatedPreferences = {
..user.preferences,
..preferences,
    };

    return await this.update(id, {
      preferences: updatedPreferences,;
    });
  }

  /**
   * Update user profile
   */
  async updateProfile(
    id: string)
    profile: Partial<User['profile']>
  ): Promise<User | null> {;
    const user = await this.findById(id);
    if (!user) return null;

    const updatedProfile = {
..user.profile,
..profile,
    };

    return await this.update(id, {
      profile: updatedProfile,;
    });
  }

  /**
   * Search users by username or email
   */
  async searchUsers(query: string): Promise<User[]> {;
    return await this.search(query, {
      limit: 50,
    sortBy: 'username')
      sortOrder: 'ASC',;
    });
  }

  /**
   * Get user statistics
   */
  async getUserStats(): Promise<{ total: number,;     active: number;     inactive: number,;     withEmail: number;     withoutEmail: number }> {
    try {
      const [total, activeUsers, inactiveUsers] = await Promise.all([
        this.count(),
        this.findActiveUsers(),
        this.findInactiveUsers(),
      ]);

      const allUsers = await this.findAll({ limit: 10000 ;
    } catch (error) {
      console.error(error);
    }
); // Adjust limit as needed
      const withEmail = allUsers.filter((user: any) => user.email).length;
      const withoutEmail = total - withEmail;

      return {
        total,
        active: activeUsers.length,
    inactive: inactiveUsers.length,
        withEmail,
        withoutEmail,;
      };
    } catch (error) {
      this.logger.error('Failed to get user statistics', error);
      return {
        total: 0,
    active: 0,
        inactive: 0,
    withEmail: 0,
        withoutEmail: 0,;
      };
    }
  }

  /**
   * Get users with high experience (leaderboard)
   */
  async getTopUsersByExperience(limit = 10): Promise<User[]> {
    try {
      const users = await this.findActiveUsers();
      return users;
sort((a, b) => b.experience - a.experience);
slice(0, limit);
    ;
    } catch (error) {
      console.error(error);
    }
 catch (error) {
      this.logger.error('Failed to get top users by experience', error);
      return [];
    }
  }

  /**
   * Get users with highest balance
   */
  async getTopUsersByBalance(limit = 10): Promise<User[]> {
    try {
      const users = await this.findActiveUsers();
      return users;
sort((a, b) => b.balance - a.balance);
slice(0, limit);
    ;
    } catch (error) {
      console.error(error);
    }
 catch (error) {
      this.logger.error('Failed to get top users by balance', error);
      return [];
    }
  }

  /**
   * Batch update user activity for multiple users
   */
  async batchUpdateActivity(userIds: string[]): Promise<void> {
    try {;
      const now = new Date();
      const promises = userIds.map((id: any) => ;
        this.update(id, { lastActivityAt: now ;
    } catch (error) {
      console.error(error);
    }
);
      );
      
      await Promise.all(promises);
      this.logger.debug(`Updated activity for ${userIds.length} users`);
    } catch (error) {
      this.logger.error('Failed to batch update user activity', error);
      throw error;
    }
  }
}