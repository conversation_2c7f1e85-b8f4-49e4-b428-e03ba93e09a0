/**
 * Comprehensive type definitions for database schema
 * This file provides all the missing types that are causing import errors
 */

// Agent-related types
export type AgentType = 'personal_growth_coach' | 'intake_specialist' | 'progress_tracker' | 'general' | 'support' | 'moderation' | 'entertainment';

export type InteractionType = 'text' | 'voice' | 'slash_command' | 'button' | 'select_menu' | 'modal' | 'context_menu';

export type MemoryType = 'assessment' | 'profile' | 'preference' | 'goal' | 'achievement' | 'conversation' | 'insight' | 'pattern' | 'interaction' | 'metrics' | 'routing';

export type MemorySentiment = 'positive' | 'neutral' | 'negative';

export type GoalStatus = 'active' | 'completed' | 'paused';

// AI Agent Configuration
export interface AIAgentConfig {
  id: string;
  guildId: string;
  name: string;
  type: AgentType;
  description?: string;
  personality?: {
    tone: string;
    style: string;
    expertise: string[];
    responseLength: 'short' | 'medium' | 'long';
  };
  triggers?: {
    keywords?: string[];
    reactions?: string[];
    conditions?: Record<string, any>;
    autoRespond?: boolean;
  };
  limits?: {
    maxInteractionsPerHour?: number;
    maxMessageLength?: number;
    cooldownSeconds?: number;
  };
  permissions?: {
    canDirectMessage?: boolean;
    canMentionUsers?: boolean;
    canUseExternalEmojis?: boolean;
    canAccessPrivateChannels?: boolean;
  };
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Agent Memory
export interface AgentMemory {
  id: string;
  agentId: string;
  userId: string;
  type: MemoryType;
  content: Record<string, any>;
  sentiment?: MemorySentiment;
  importance: number;
  context?: Record<string, any>;
  tags?: string[];
  expiresAt?: Date;
  isEncrypted: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Agent Interaction
export interface AgentInteraction {
  id: string;
  agentId: string;
  userId: string;
  guildId?: string;
  channelId?: string;
  type: InteractionType;
  input: string;
  response: string;
  context?: Record<string, any>;
  metadata?: Record<string, any>;
  sentiment?: MemorySentiment;
  processingTimeMs: number;
  wasSuccessful: boolean;
  createdAt: Date;
}

// User types
export interface User {
  id: string;
  discordId: string;
  username: string;
  discriminator?: string;
  avatar?: string;
  preferences?: Record<string, any>;
  profile?: Record<string, any>;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Guild types
export interface Guild {
  id: string;
  discordId: string;
  name: string;
  icon?: string;
  ownerId: string;
  settings?: Record<string, any>;
  features?: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Session types
export interface Session {
  id: string;
  userId: string;
  token: string;
  expiresAt: Date;
  metadata?: Record<string, any>;
  createdAt: Date;
}

// Organization types
export interface Organization {
  id: string;
  name: string;
  slug: string;
  settings?: Record<string, any>;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Create/Update types
export type CreateAIAgentConfig = Omit<AIAgentConfig, 'id' | 'createdAt' | 'updatedAt'>;
export type UpdateAIAgentConfig = Partial<CreateAIAgentConfig>;
export type NewAIAgentConfig = CreateAIAgentConfig;

export type CreateAgentMemory = Omit<AgentMemory, 'id' | 'createdAt' | 'updatedAt'>;
export type UpdateAgentMemory = Partial<CreateAgentMemory>;
export type NewAgentMemory = CreateAgentMemory;

export type CreateAgentInteraction = Omit<AgentInteraction, 'id' | 'createdAt'>;
export type UpdateAgentInteraction = Partial<CreateAgentInteraction>;

export type CreateUser = Omit<User, 'id' | 'createdAt' | 'updatedAt'>;
export type UpdateUser = Partial<CreateUser>;
export type NewUser = CreateUser;

export type CreateGuild = Omit<Guild, 'id' | 'createdAt' | 'updatedAt'>;
export type UpdateGuild = Partial<CreateGuild>;
export type NewGuild = CreateGuild;

export type CreateSession = Omit<Session, 'id' | 'createdAt'>;
export type UpdateSession = Partial<CreateSession>;

export type CreateOrganization = Omit<Organization, 'id' | 'createdAt' | 'updatedAt'>;
export type UpdateOrganization = Partial<CreateOrganization>;

// Additional types for compatibility
export interface AgentConfiguration {
  personality: AIAgentConfig['personality'];
  triggers: AIAgentConfig['triggers'];
  limits: AIAgentConfig['limits'];
  permissions: AIAgentConfig['permissions'];
}

export interface AgentPermissions {
  canDirectMessage: boolean;
  canMentionUsers: boolean;
  canUseExternalEmojis: boolean;
  canAccessPrivateChannels: boolean;
}

export interface InteractionContext {
  guildId?: string;
  channelId?: string;
  messageId?: string;
  threadId?: string;
  timestamp: Date;
}

export interface InteractionMetadata {
  responseTime: number;
  confidence: number;
  fallbackUsed: boolean;
  [key: string]: any;
}

export type InteractionStatus = 'pending' | 'processing' | 'completed' | 'failed';

export type UserMood = 'happy' | 'sad' | 'excited' | 'frustrated' | 'calm' | 'anxious' | 'neutral';

export interface MemoryGoal {
  id: string;
  title: string;
  description: string;
  status: GoalStatus;
  progress: number;
  dueDate?: string;
}

export interface MemoryValue {
  value: any;
  confidence: number;
  lastUpdated: Date;
  source: string;
}

export interface UserPreferences {
  theme?: 'light' | 'dark' | 'auto';
  notifications?: boolean;
  privacy?: 'public' | 'friends' | 'private';
  language?: string;
  [key: string]: any;
}

export interface UserProfile {
  displayName?: string;
  bio?: string;
  location?: string;
  interests?: string[];
  goals?: string[];
  achievements?: string[];
  [key: string]: any;
}

export interface GuildSettings {
  prefix?: string;
  language?: string;
  timezone?: string;
  autoModeration?: boolean;
  welcomeMessage?: string;
  [key: string]: any;
}

export interface GuildFeatures {
  aiAgents: boolean;
  moderation: boolean;
  music: boolean;
  games: boolean;
  economy: boolean;
  [key: string]: boolean;
}

export type WelcomeRoles = string[];

export interface SessionMetadata {
  userAgent?: string;
  ipAddress?: string;
  location?: string;
  [key: string]: any;
}