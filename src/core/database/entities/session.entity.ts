import { BaseEntity, CreateEntity, UpdateEntity, UserOwned, ActivityTracked, MetadataStorage } from '../types/base.interface';

/**
 * Session metadata interface
 */
export type SessionMetadata = {
  loginMethod?: 'discord' | 'email' | 'token';
  deviceType?: 'mobile' | 'desktop' | 'tablet';
  location?: {
    country?: string;
    region?: string;
    city?: string;
  };
  security?: {
    riskScore?: number;
    flags?: string[];
  };
  permissions?: string[];
  preferences?: Record<string, any>;
}

/**
 * Session entity interface - Redis compatible
 */
export interface Session extends BaseEntity, UserOwned, ActivityTracked, MetadataStorage<SessionMetadata> {
  sessionId: string;
  encryptedData?: string;
  expiresAt: Date;
  ipAddress?: string;
  userAgent?: string;
  deviceFingerprint?: string;
  isRevoked: boolean;
  lastAccessedAt?: Date;
}

/**
 * Type for creating a new session
 */
export type CreateSession = CreateEntity<Session>;

/**
 * Type for updating a session
 */
export type UpdateSession = UpdateEntity<Session>;

/**
 * Legacy compatibility types
 */
export type NewSession = CreateSession;

/**
 * Session with relations type for expanded queries
 */
export interface SessionWithUser extends Session {
  user?: any; // Will be properly typed when user relations are implemented
}

/**
 * Redis key patterns for session entity
 */
export const SessionKeys = {
  primary: (id: string) => `session:${id}`,
  bySessionId: (sessionId: string) => `session:sid:${sessionId}`,
  byUser: (userId: string) => `sessions:user:${userId}`,
  byDevice: (deviceFingerprint: string) => `sessions:device:${deviceFingerprint}`,
  active: () => 'sessions:active',
    expired: () => 'sessions: expired',
    revoked: () => 'sessions: revoked',
    pattern: 'session: *',
      index: {;
sessionId: 'idx:session:session_id',
    userId: 'idx: session:user_id',
    expiresAt: 'idx: session:expires_at',
    isRevoked: 'idx: session:is_revoked',
    lastAccessedAt: 'idx:session:last_accessed_at',
  }
} as const;

/**
 * Compatibility export for Drizzle-style references
 */
export const sessions = {
  $inferSelect: {} as Session,
  $inferInsert: {} as CreateSession
} as const;

export type SessionsTable = typeof sessions;

