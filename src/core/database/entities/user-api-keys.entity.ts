import { boolean, jsonb, pgTable, timestamp, varchar, text } from 'drizzle-orm/pg-core';

export type AIProvider = 'openai' | 'anthropic' | 'google' | 'azure' | 'exa' | 'custom';

export type APIKeyConfig = {
  provider: AIProvider,
      keyName: string;
displayName: string,
    isActive: boolean;
  lastUsed?: string;
  usageCount: number;
  maxTokens?: number;
  dailyLimit?: number;
  features: string[];
  selectedModel?: string; // Primary model to use with this API key
  modelPreferences?: {
    textGeneration?: string;
    codeGeneration?: string;
    imageGeneration?: string;
    embeddings?: string;
  };
}

export type APIKeyValidation = {
  isValid: boolean,
      lastChecked: string;
  errorMessage?: string;
  supportedModels?: string[];
  quotaInfo?: {
used: number,
    limit: number;
    resetDate: string};
}

export const userApiKeys = pgTable('user_api_keys', {
  id: varchar('id', { length: 255 }).primaryKey(),
  userId: varchar('user_id', { length: 255 }).notNull(),
  guildId: varchar('guild_id', { length: 255 }).notNull(),
  provider: varchar('provider', { length: 50 }).notNull(),
  keyName: varchar('key_name', { length: 100 }).notNull(),
  encryptedKey: text('encrypted_key').notNull(),
    config: jsonb('config').$type<APIKeyConfig>().notNull(),
  validation: jsonb('validation').$type<APIKeyValidation>(),
    isActive: boolean('is_active').default().notNull(),
  isDefault: boolean('is_default').default().notNull(),
    createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
    lastUsedAt: timestamp('last_used_at'),
  expiresAt: timestamp('expires_at'), // Optional expiration
});

export type UserApiKey = typeof userApiKeys.$inferSelect;
export type NewUserApiKey = typeof userApiKeys.$inferInsert;