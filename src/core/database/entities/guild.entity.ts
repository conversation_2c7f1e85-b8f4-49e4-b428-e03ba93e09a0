import type { GuildFeatures as DetailedGuildFeatures } from '../types';
import { ActivityTracked, BaseEntity, ConfigurationStorage, CreateEntity, MetadataStorage, UpdateEntity } from '../types/base.interface';

/**
 * Guild features configuration
 * Use the detailed features structure defined in core/database/types.ts
 */
export type GuildFeatures = DetailedGuildFeatures;

/**
 * Guild settings configuration
 */
export type GuildSettings = {
  prefix?: string;
  language?: string;
  timezone?: string;
  moderationLevel?: 'low' | 'medium' | 'high';
  autoModeration?: {
    enabled: boolean,
      filters: string[];
actions: string[]};
  logging?: {
    enabled: boolean;
    channelId?: string;
    events: string[]};
  notifications?: {
    enabled: boolean,
    types: string[]};
}

/**
 * Welcome roles configuration
 */
export type WelcomeRoles = {
  enabled: boolean;
  roles: Array<{
    id: string;
    name: string;
    automatic: boolean;
  }>;
}

/**
 * Guild entity interface - Redis compatible
 */
export interface Guild extends BaseEntity, ActivityTracked, ConfigurationStorage<GuildSettings>, MetadataStorage {
  discordId: string,
      name: string;
  icon?: string;
  ownerDiscordId?: string;
  features?: GuildFeatures;
  // Welcome system
  welcomeEnabled: boolean;
  welcomeChannelId?: string;
  welcomeMessage?: string;
  welcomeRoles?: WelcomeRoles;
  // Starboard system
  starboardEnabled: boolean;
  starboardChannelId?: string;
  starboardThreshold: number;
  // Legacy compatibility
  guildId?: string;
}

/**
 * Type for creating a new guild
 */
export type CreateGuild = CreateEntity<Guild>;

/**
 * Type for updating a guild
 */
export type UpdateGuild = UpdateEntity<Guild>;

/**
 * Legacy compatibility types
 */
export type NewGuild = CreateGuild;

/**
 * Guild with relations type for expanded queries
 */
export interface GuildWithRelations extends Guild {
  aiAgentConfigs?: any[]; // Will be properly typed when AI agent entity is updated
  users?: any[]; // Will be properly typed when user-guild relations are implemented
  channels?: any[]; // Will be properly typed when channel entities are implemented
}

/**
 * Redis key patterns for guild entity
 */
export const GuildKeys = {
  primary: (id: string) => `guild:${id}`,
  byDiscordId: (discordId: string) => `guild:discord:${discordId}`,
  byOwner: (ownerDiscordId: string) => `guilds:owner:${ownerDiscordId}`,
  active: () => 'guilds:active',
  inactive: () => 'guilds:inactive',
  pattern: 'guild:*',
  index: {
    discordId: 'idx:guild:discord_id',
    name: 'idx:guild:name',
    ownerDiscordId: 'idx:guild:owner_discord_id',
    isActive: 'idx:guild:is_active',
    createdAt: 'idx:guild:created_at',
  }
} as const;

/**
 * Compatibility export for Drizzle-style references
 */
export const guilds = {
  $inferSelect: {} as Guild,
  $inferInsert: {} as CreateGuild
} as const;

export type GuildsTable = typeof guilds;

