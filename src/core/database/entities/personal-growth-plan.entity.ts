import { pgTable, text, varchar, jsonb, timestamp, integer, index } from 'drizzle-orm/pg-core';
import { baseColumns, BaseEntity } from './base.entity';

import { users } from './user.entity';

export const personalGrowthPlans = pgTable('personal_growth_plans', {
..baseColumns)
  userId: text('user_id').notNull(),
    title: varchar('title', { length: 200 }).notNull(),
  description: text('description'),
    status: varchar('status', { length: 20 }).default().notNull(),
  goals: jsonb('goals'),
    milestones: jsonb('milestones'),
  startDate: timestamp('start_date', { withTimezone: true }),
  targetCompletionDate: timestamp('target_completion_date', { withTimezone: true }),
  actualCompletionDate: timestamp('actual_completion_date', { withTimezone: true }),
  progressPercentage: integer('progress_percentage').default().notNull(),
    metadata: jsonb('metadata'),
}, (table) => ({
  userIdIdx: index('personal_growth_plans_user_id_idx').on(table.userId),
    statusIdx: index('personal_growth_plans_status_idx').on(table.status),
}));

