import { 
  pgTable, 
  serial, 
  varchar, 
  timestamp, 
  jsonb, 
  integer, 
  boolean,
  uniqueIndex 
} from 'drizzle-orm/pg-core';
import { baseColumns } from './base.entity';

export const panelCleanupLogs = pgTable('panel_cleanup_logs', {
  id: serial('id').primaryKey(),
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow(),
  
  // Cleanup session info
  cleanupSessionId: varchar('cleanup_session_id', { length: 100 }).notNull(),
  cleanupType: varchar('cleanup_type', { length: 20 }).notNull(),
  triggeredBy: varchar('triggered_by', { length: 100 }),
  
  // Target info
  guildId: varchar('guild_id', { length: 50 }),
  channelId: varchar('channel_id', { length: 50 }),
  targetScope: varchar('target_scope', { length: 50 }),
  
  // Results
  messagesScanned: integer('messages_scanned').default(0),
    duplicatesFound: integer('duplicates_found').default(0),
  duplicatesRemoved: integer('duplicates_removed').default(0),
    duplicatesPreserved: integer('duplicates_preserved').default(0),
  errorsCount: integer('errors_count').default(0),
  
  // Timing
  startedAt: timestamp('started_at', { withTimezone: true }).defaultNow(),
  completedAt: timestamp('completed_at', { withTimezone: true }),
  executionTimeMs: integer('execution_time_ms'),
  
  // Configuration and results
  cleanupConfig: jsonb('cleanup_config').default({}),
  resultsSummary: jsonb('results_summary').default({}),
  errorDetails: jsonb('error_details').default({}),
  
  // Status
  status: varchar('status', { length: 20 }).default('running'),
});

export type PanelCleanupLog = typeof panelCleanupLogs.$inferSelect;
export type NewPanelCleanupLog = typeof panelCleanupLogs.$inferInsert;

export const panelVersions = pgTable('panel_versions', {
  id: serial('id').primaryKey(),
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow(),
  
  // Version identification
  panelId: varchar('panel_id', { length: 100 }).notNull(),
  channelId: varchar('channel_id', { length: 50 }).notNull(),
  versionNumber: varchar('version_number', { length: 20 }).notNull(),
  contentHash: varchar('content_hash', { length: 64 }).notNull(),
  
  // Version content
  panelContent: jsonb('panel_content').notNull(),
  
  // Change tracking
  changesFromPrevious: jsonb('changes_from_previous').default([]),
    changeSignificance: varchar('change_significance', { length: 10 }).default('patch'),
  
  // Metadata
  deploymentId: integer('deployment_id'),
    generationTimeMs: integer('generation_time_ms'),
  contentSizeBytes: integer('content_size_bytes'),
    metadata: jsonb('metadata').default({}),
  
  // Status
  isCurrent: boolean('is_current').default(false),
});

export type PanelVersion = typeof panelVersions.$inferSelect;
export type NewPanelVersion = typeof panelVersions.$inferInsert;

export const panelHealthMetrics = pgTable('panel_health_metrics', {
  id: serial('id').primaryKey(),
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow(),
  
  // Target
  guildId: varchar('guild_id', { length: 50 }),
  channelId: varchar('channel_id', { length: 50 }),
  panelId: varchar('panel_id', { length: 100 }),
  
  // Health metrics
  activeDeployments: integer('active_deployments').default(0),
    duplicateDeployments: integer('duplicate_deployments').default(0),
  failedDeployments: integer('failed_deployments').default(0),
    orphanedMessages: integer('orphaned_messages').default(0),
  
  // Performance metrics
  avgResponseTimeMs: integer('avg_response_time_ms').default(0),
    interactionSuccessRate: integer('interaction_success_rate').default(100), // Store as percentage
  lastSuccessfulInteraction: timestamp('last_successful_interaction', { withTimezone: true }),
  lastFailedInteraction: timestamp('last_failed_interaction', { withTimezone: true }),
  
  // System health
  memoryUsageMb: integer('memory_usage_mb').default(0),
    cacheHitRate: integer('cache_hit_rate').default(0), // Store as percentage
  errorRate24h: integer('error_rate_24h').default(0), // Store as percentage
  
  // Cleanup metrics
  cleanupsPerformed: integer('cleanups_performed').default(0),
    lastCleanupAt: timestamp('last_cleanup_at', { withTimezone: true }),
  timeSinceLastCleanupHours: integer('time_since_last_cleanup_hours').default(0),
  
  // Status
  overallHealthScore: integer('overall_health_score').default(100), // Store as percentage
  healthStatus: varchar('health_status', { length: 20 }).default('healthy'),
  
  // Metadata
  metadata: jsonb('metadata').default({}),
});

export type PanelHealthMetric = typeof panelHealthMetrics.$inferSelect;
export type NewPanelHealthMetric = typeof panelHealthMetrics.$inferInsert;