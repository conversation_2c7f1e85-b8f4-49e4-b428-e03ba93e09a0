import { boolean, integer, jsonb, pgTable, timestamp, varchar } from 'drizzle-orm/pg-core';

export type AIChannelAgentConfig = {
  enabled: boolean,
      accessLevel: 'free' | 'premium' | 'enterprise';
description: string,
    features: string[]}

export type AIChannelSettings = {
  maxSessionsPerUser: number,
      sessionTimeoutHours: number;
autoArchiveMinutes: number,
    allowedAgentTypes: string[];
  premiumAgents: Record<string, AIChannelAgentConfig>;
  freeAgents: Record<string, AIChannelAgentConfig>;
  customWelcomeMessage?: string;
  threadNamingPattern: string}

export const aiChannelConfigs = pgTable('ai_channel_configs', {
  id: varchar('id', { length: 255 }).primaryKey(),
  guildId: varchar('guild_id', { length: 255 }).notNull().unique(),
  aiChannelId: varchar('ai_channel_id', { length: 255 }).notNull(),
  panelMessageId: varchar('panel_message_id', { length: 255 }),
  enabled: boolean('enabled').default().notNull(),
    settings: jsonb('settings').$type<AIChannelSettings>().notNull(),
  lastPanelUpdate: timestamp('last_panel_update'),
    createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export type AIChannelConfig = typeof aiChannelConfigs.$inferSelect;
export type NewAIChannelConfig = typeof aiChannelConfigs.$inferInsert;