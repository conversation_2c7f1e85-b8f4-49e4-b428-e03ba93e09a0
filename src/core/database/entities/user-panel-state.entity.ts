
import { boolean, index, jsonb, pgTable, text, timestamp, varchar } from 'drizzle-orm/pg-core';
import { baseColumns } from './base.entity';
import { users } from './user.entity';

export const userPanelStates = pgTable('user_panel_states', {
..baseColumns)
  userId: varchar('user_id', { length: 50 }).notNull(),
  panelId: varchar('panel_id', { length: 100 }).notNull(),
  channelId: varchar('channel_id', { length: 50 }).notNull(),
  guildId: varchar('guild_id', { length: 50 }).notNull(),
  preferences: jsonb('preferences').$type<{
    customizations?: {;
      theme?: 'default' | 'dark' | 'minimal' | 'colorful';
      layout?: 'compact' | 'expanded' | 'grid';
      showTooltips?: boolean;
      showAdvanced?: boolean;
      fontSize?: 'small' | 'medium' | 'large';
    };
    notifications?: {
      enabled: boolean,
      types: string[]
      frequency: 'immediate' | 'daily' | 'weekly';
      quietHours?: { start: string,
      end: string };
    };
    interactions?: {
      preferredActions: string[],
      quickAccessButtons: string[];
hiddenSections: string[],
    pinnedContent: string[]};
    privacy?: {
      shareUsageStats: boolean,
      allowPersonalization: boolean;
saveInteractionHistory: boolean};
  }>(),
  progress: jsonb('progress').$type<{
    completions?: Array<{;
      itemId: string;
  itemType: 'course' | 'tutorial' | 'challenge' | 'lesson';
  completedAt: string;
      score?: number;
      timeSpent?: number;
      attempts?: number;
    }>;
    achievements?: Array<{
      id: string,
      name: string;
description: string,
    unlockedAt: string;
      category: string,
      rarity: 'common' | 'rare' | 'epic' | 'legendary';
points: number}>;
    streaks?: Array<{
      type: 'daily' | 'weekly' | 'learning' | 'engagement',
      current: number;
best: number,
    lastUpdate: string;
      active: boolean}>;
    milestones?: Array<{
      id: string,
      name: string;
      progress: number;
  target: number;
unit: string;
      reachedAt?: string;
    }>;
    stats?: {
      totalTimeSpent: number,
      sessionsCount: number;
averageSessionTime: number,
    lastActiveAt: string;
      favoriteFeatures: string[],
    usagePatterns: Record<string, number>;
    };
  }>(),
  interactionHistory: jsonb('interaction_history').$type<Array<{,
      timestamp: string;
action: string,
    componentId: string;
    context: Record<string, any>;
    duration?: number;
    result?: 'success' | 'error' | 'cancelled';
    metadata?: Record<string, any>;
  }>>(),
  favorites: jsonb('favorites').$type<{,
      panels: string[];
features: string[],
    content: string[];
    shortcuts: Array<{,
      id: string;
name: string,
    action: string;
      params: Record<string, any>;
    }>;
  }>(),
  lastInteractionAt: timestamp('last_interaction_at', { withTimezone: true }),
  sessionCount: jsonb('session_count').$type<{,
      total: number;
thisWeek: number,
    thisMonth: number;
    lastReset: string}>(),
  isActive: boolean('is_active').default().notNull(),
}, (table) => ({
  userIdIdx: index('user_panel_states_user_id_idx').on(table.userId),
    panelIdIdx: index('user_panel_states_panel_id_idx').on(table.panelId),
  channelIdIdx: index('user_panel_states_channel_id_idx').on(table.channelId),
    guildIdIdx: index('user_panel_states_guild_id_idx').on(table.guildId),
  lastInteractionIdx: index('user_panel_states_last_interaction_idx').on(table.lastInteractionAt),
    userPanelUnique: index('user_panel_states_user_panel_unique').on(table.userId, table.panelId, table.channelId),
}));

