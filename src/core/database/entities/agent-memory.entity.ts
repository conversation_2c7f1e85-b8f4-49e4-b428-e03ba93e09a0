import { BaseEntity, CreateEntity, UpdateEntity, UserOwned, ActivityTracked, MetadataStorage, Taggable } from '../types/base.interface';

/**
 * Memory type enumeration
 */
export type MemoryType = 'assessment' | 'profile' | 'preference' | 'goal' | 'achievement' | 'conversation' | 'insight' | 'pattern' | 'interaction' | 'metrics' | 'routing';

/**
 * Memory sentiment enumeration
 */
export type MemorySentiment = 'positive' | 'neutral' | 'negative';

/**
 * Goal status enumeration
 */
export type GoalStatus = 'active' | 'completed' | 'paused';

/**
 * Memory goal interface
 */
export type MemoryGoal = {
  id: string;
  title: string;
  description: string;
  status: GoalStatus;
  progress: number;
  dueDate?: string;
};

/**
 * Memory value interface - flexible structure for different memory types
 */
export type MemoryValue = {
  // For assessment memories
  response?: string;
  score?: number;
  category?: string;

  // For progress tracking
  completed?: number;
  active?: number;
  streak?: number;
  weeklyPercent?: number;
  overall?: string;
  totalCheckins?: number;
  lastCheckin?: string;
  weeklyCheckins?: number;

  // For conversation memories
  context?: string;
  summary?: string;
  sentiment?: MemorySentiment;

  // For goal memories
  goals?: MemoryGoal[];

  // For interaction memories
  interactionType?: string;
  outcome?: string;
  feedback?: string;

  // Generic properties
  data?: Record<string, any>;
  metadata?: Record<string, any>;
}

/**
 * Agent memory entity interface - Redis compatible
 */
export interface AgentMemory extends BaseEntity, UserOwned, ActivityTracked, MetadataStorage, Taggable {
  memoryType: MemoryType;
  key: string;
  value: MemoryValue;
  context?: string;
  importance: number,
    accessCount: number;
  lastAccessedAt?: Date;
  expiresAt?: Date;
}

/**
 * Type for creating a new agent memory
 */
export type CreateAgentMemory = CreateEntity<AgentMemory>;

/**
 * Type for updating an agent memory
 */
export type UpdateAgentMemory = UpdateEntity<AgentMemory>;

/**
 * Legacy compatibility types
 */
export type NewAgentMemory = CreateAgentMemory;

/**
 * Agent memory with relations type for expanded queries
 */
export interface AgentMemoryWithRelations extends AgentMemory {
  user?: any; // Will be properly typed when user relations are implemented
}

/**
 * Redis key patterns for agent memory entity
 */
export const AgentMemoryKeys = {
  primary: (id: string) => `agent_memory:${id}`,
  byUser: (userId: string) => `agent_memories:user:${userId}`,
  byUserAndType: (userId: string, memoryType: MemoryType) => `agent_memories:${userId}:${memoryType}`,
  byUserAndKey: (userId: string, key: string) => `agent_memory:${userId}:${key}`,
  byType: (memoryType: MemoryType) => `agent_memories:type:${memoryType}`,
  byImportance: (importance: number) => `agent_memories:importance:${importance}`,
  expired: () => 'agent_memories:expired',
    pattern: 'agent_memory: *',
      index: {;
userId: 'idx:agent_memory:user_id',
    memoryType: 'idx: agent_memory:memory_type',
    key: 'idx: agent_memory:key',
    importance: 'idx: agent_memory:importance',
    createdAt: 'idx: agent_memory:created_at',
    expiresAt: 'idx:agent_memory:expires_at',
  }
} as const;

/**
 * Compatibility export for Drizzle-style references
 */
export const agentMemory = {
  $inferSelect: {} as AgentMemory,
  $inferInsert: {} as CreateAgentMemory
} as const;

export type AgentMemoryTable = typeof agentMemory;