import { BaseEntity, CreateEntity, UpdateEntity, UserOwned, GuildSpecific, ChannelSpecific, MetadataStorage } from '../types/base.interface';
import { AgentType } from './ai-agent-config.entity';

/**
 * Interaction type enumeration
 */
export type InteractionType = 'message' | 'command' | 'check_in' | 'assessment' | 'goal_setting' | 'progress_update' | 'thread_message';

/**
 * Interaction status enumeration
 */
export type InteractionStatus = 'pending' | 'processing' | 'completed' | 'failed';

/**
 * User mood enumeration
 */
export type UserMood = 'positive' | 'neutral' | 'negative';

/**
 * Interaction context interface
 */
export type InteractionContext = {
  topic?: string;
  channel?: string;
  previousMessages?: string[];
  userMood?: UserMood;
  conversationFlow?: string;
  triggerEvent?: string;
  sessionId?: string;
  relatedInteractions?: string[];
}

/**
 * Interaction processing metadata
 */
export type InteractionMetadata = {
  processingTime?: number;
  confidence?: number;
  model?: string;
  tokens?: number;
  cost?: number;
  retries?: number;
  errorCount?: number;
  source?: string;
  version?: string;
}

/**
 * Agent interaction entity interface - Redis compatible
 */
export interface AgentInteraction extends BaseEntity, UserOwned, GuildSpecific, ChannelSpecific, MetadataStorage<InteractionMetadata> {
  agentType: AgentType;
  interactionType: InteractionType;
  content: string;
  response?: string;
  status: InteractionStatus;
  context?: InteractionContext;
  messageId?: string;
  sentimentScore: number;
  tags?: string[];
}

/**
 * Type for creating a new agent interaction
 */
export type CreateAgentInteraction = CreateEntity<AgentInteraction>;

/**
 * Type for updating an agent interaction
 */
export type UpdateAgentInteraction = UpdateEntity<AgentInteraction>;

/**
 * Legacy compatibility types
 */
export type NewAgentInteraction = CreateAgentInteraction;

/**
 * Agent interaction with relations type for expanded queries
 */
export interface AgentInteractionWithRelations extends AgentInteraction {
  user?: any; // Will be properly typed when user relations are implemented
  guild?: any; // Will be properly typed when guild relations are implemented
  agentConfig?: any; // Will be properly typed when AI agent config relations are implemented
}

/**
 * Redis key patterns for agent interaction entity
 */
export const AgentInteractionKeys = {
  primary: (id: string) => `agent_interaction:${id}`,
  byUser: (userId: string) => `agent_interactions:user:${userId}`,
  byGuild: (guildId: string) => `agent_interactions:guild:${guildId}`,
  byChannel: (channelId: string) => `agent_interactions:channel:${channelId}`,
  byAgentType: (agentType: AgentType) => `agent_interactions:type:${agentType}`,
  byInteractionType: (interactionType: InteractionType) => `agent_interactions:interaction:${interactionType}`,
  byStatus: (status: InteractionStatus) => `agent_interactions:status:${status}`,
  pending: () => 'agent_interactions:pending',
    pattern: 'agent_interaction: *',
      index: {;
userId: 'idx:agent_interaction:user_id',
    guildId: 'idx: agent_interaction:guild_id',
    channelId: 'idx: agent_interaction:channel_id',
    agentType: 'idx: agent_interaction:agent_type',
    interactionType: 'idx: agent_interaction:interaction_type',
    status: 'idx: agent_interaction:status',
    createdAt: 'idx:agent_interaction:created_at',
  }
} as const;

/**
 * Compatibility export for Drizzle-style references
 */
export const agentInteractions = {
  $inferSelect: {} as AgentInteraction,
  $inferInsert: {} as CreateAgentInteraction
} as const;

export type AgentInteractionsTable = typeof agentInteractions;