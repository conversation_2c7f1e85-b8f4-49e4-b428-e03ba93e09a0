
import { boolean, integer, jsonb, pgTable, text, timestamp, varchar } from 'drizzle-orm/pg-core';
import { baseColumns } from './base.entity';
import { users } from './user.entity';

export const communityEvents = pgTable('community_events', {
..baseColumns)
  title: varchar('title', { length: 255 }).notNull(),
  description: text('description').notNull(),
    startDate: timestamp('start_date', { withTimezone: true }).notNull(),
  endDate: timestamp('end_date', { withTimezone: true }),
  type: varchar('type', { length: 50 }).notNull(), // 'challenge' | 'workshop' | 'meetup' | 'competition'
  maxParticipants: integer('max_participants'),
    currentParticipants: integer('current_participants').default().notNull(),
  tags: jsonb('tags').$type<string[]>(),
    guildId: varchar('guild_id', { length: 50 }).notNull(),
  organizerId: varchar('organizer_id', { length: 50 }).notNull(),
  isActive: boolean('is_active').default().notNull(),
});

export const eventParticipants = pgTable('event_participants', {
..baseColumns)
  eventId: varchar('event_id', { length: 50 }).notNull(),
  userId: varchar('user_id', { length: 50 }).notNull(),
  joinedAt: timestamp('joined_at', { withTimezone: true }).defaultNow().notNull(),
  status: varchar('status', { length: 20 }).default().notNull(), // 'registered' | 'attended' | 'cancelled'
});

export const communityFeedback = pgTable('community_feedback', {
..baseColumns)
  userId: varchar('user_id', { length: 50 }).notNull(),
  type: varchar('type', { length: 50 }).notNull(), // 'suggestion' | 'bug-report' | 'feature-request' | 'general'
  title: varchar('title', { length: 255 }).notNull(),
  description: text('description').notNull(),
    status: varchar('status', { length: 20 }).default().notNull(), // 'pending' | 'reviewed' | 'in-progress' | 'completed' | 'rejected'
  votes: integer('votes').default().notNull(),
    guildId: varchar('guild_id', { length: 50 }).notNull(),
});

export const leaderboardEntries = pgTable('leaderboard_entries', {
..baseColumns)
  userId: varchar('user_id', { length: 50 }).notNull(),
  guildId: varchar('guild_id', { length: 50 }).notNull(),
  points: integer('points').default().notNull(),
    level: integer('level').default().notNull(),
  badges: jsonb('badges').$type<string[]>(),
    monthlyRank: integer('monthly_rank'),
  allTimeRank: integer('all_time_rank'),
    lastUpdated: timestamp('last_updated', { withTimezone: true }).defaultNow().notNull(),
});

// Relations
