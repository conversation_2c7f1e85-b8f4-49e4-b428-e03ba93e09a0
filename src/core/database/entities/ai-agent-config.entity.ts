import { BaseEntity, CreateEntity, UpdateEntity, GuildSpecific, ActivityTracked, ConfigurationStorage } from '../types/base.interface';

/**
 * Agent type enumeration
 */
export type AgentType = 'personal_growth_coach' | 'intake_specialist' | 'progress_tracker' | 'general' | 'support' | 'moderation' | 'entertainment';

/**
 * Agent personality configuration
 */
export type AgentPersonality = {
  tone: string;
  style: string;
  expertise: string[];
  responseLength: 'short' | 'medium' | 'long';
};

/**
 * Agent triggers configuration
 */
export type AgentTriggers = {
  keywords?: string[];
  reactions?: string[];
  conditions?: Record<string, any>;
  autoRespond?: boolean;
}

/**
 * Agent limits configuration
 */
export type AgentLimits = {
  maxResponsesPerHour: number,
      maxTokensPerResponse: number;
cooldownSeconds: number}

/**
 * Agent schedule configuration
 */
export type AgentSchedule = {
  type: 'daily' | 'weekly' | 'monthly',
      time: string;
  timezone?: string;
enabled: boolean}

/**
 * Agent configuration interface
 */
export type AgentConfiguration = {
  channels?: string[];
  defaultAgent?: string;
  schedules?: AgentSchedule[];
  prompts?: Record<string, string>;
  personality?: AgentPersonality;
  triggers?: AgentTriggers;
  limits?: AgentLimits;
}

/**
 * Agent permissions interface
 */
export type AgentPermissions = {
  allowedRoles?: string[];
  restrictedRoles?: string[];
  allowedUsers?: string[];
  restrictedUsers?: string[];
}

/**
 * AI Agent Config entity interface - Redis compatible
 */
export interface AIAgentConfig extends BaseEntity, GuildSpecific, ActivityTracked, ConfigurationStorage<AgentConfiguration> {
  agentType: AgentType,
    enabled: boolean;
  configuration?: AgentConfiguration;
  defaultChannelId?: string;
  logChannelId?: string;
  permissions?: AgentPermissions;
  settings?: Record<string, any>;
  lastUsedAt?: Date;
}

/**
 * Type for creating a new AI agent config
 */
export type CreateAIAgentConfig = CreateEntity<AIAgentConfig>;

/**
 * Type for updating an AI agent config
 */
export type UpdateAIAgentConfig = UpdateEntity<AIAgentConfig>;

/**
 * Legacy compatibility types
 */
export type NewAIAgentConfig = CreateAIAgentConfig;

/**
 * AI Agent Config with relations type for expanded queries
 */
export interface AIAgentConfigWithRelations extends AIAgentConfig {
  guild?: any; // Will be properly typed when guild relations are implemented
  interactions?: any[]; // Will be properly typed when agent-interaction entity is updated
}

/**
 * Redis key patterns for AI agent config entity
 */
export const AIAgentConfigKeys = {
  primary: (id: string) => `ai_agent_config:${id}`,
  byGuild: (guildId: string) => `ai_agent_configs:guild:${guildId}`,
  byType: (agentType: AgentType) => `ai_agent_configs:type:${agentType}`,
  byGuildAndType: (guildId: string, agentType: AgentType) => `ai_agent_config:${guildId}:${agentType}`,
  enabled: () => 'ai_agent_configs:enabled',
    pattern: 'ai_agent_config: *',
      index: {;
guildId: 'idx:ai_agent_config:guild_id',
    agentType: 'idx: ai_agent_config:agent_type',
    enabled: 'idx: ai_agent_config:enabled',
    createdAt: 'idx:ai_agent_config:created_at',
  }
} as const;

/**
 * Compatibility export for Drizzle-style references
 */
export const aiAgentConfigs = {
  $inferSelect: {} as AIAgentConfig,
  $inferInsert: {} as CreateAIAgentConfig
} as const;

export type AIAgentConfigsTable = typeof aiAgentConfigs;