import { BaseEntity, CreateEntity, UpdateEntity, ActivityTracked, UserOwned, ConfigurationStorage, MetadataStorage, Auditable } from '../types/base.interface';

/**
 * Organization tier enumeration
 */
export type OrganizationTier = 'free' | 'starter' | 'professional' | 'enterprise' | 'custom';

/**
 * Organization status enumeration
 */
export type OrganizationStatus = 'active' | 'suspended' | 'trial' | 'cancelled';

/**
 * Organization role enumeration
 */
export type OrganizationRole = 'owner' | 'admin' | 'member' | 'viewer';

/**
 * Organization settings interface
 */
export type OrganizationSettings = {
  branding?: {
    primaryColor?: string;
    secondaryColor?: string;
    logoUrl?: string;
  };
  notifications?: {
    emailNotifications: boolean
    slackIntegration?: {
      enabled: boolean;
      webhookUrl?: string;
    };
  };
  security?: {
    twoFactorRequired: boolean,
    sessionTimeout: number;
    ipWhitelist?: string[];
  };
  features?: {
    aiAgents: boolean,
      analytics: boolean;
customBranding: boolean,
    apiAccess: boolean};
}

/**
 * Organization limits interface
 */
export type OrganizationLimits = {
  maxUsers: number,
      maxGuilds: number;
maxApiCalls: number,
    maxStorageGb: number
  rateLimit?: {;
    requests: number,
    window: number // in seconds};
}

/**
 * Billing information interface
 */
export type BillingInfo = {
  customerId?: string;
  subscriptionId?: string;
  planId?: string;
  currentPeriodEnd?: Date;
  cancelAtPeriodEnd: boolean;
  trialEnd?: Date;
  paymentMethod?: {
    type: 'card' | 'bank';
    last4?: string;
    brand?: string;
  };
}

/**
 * Organization entity interface - Redis compatible
 */
export interface Organization extends BaseEntity, ActivityTracked, ConfigurationStorage<OrganizationSettings>, MetadataStorage, Auditable {
  name: string,
      slug: string;
tier: OrganizationTier,
    status: OrganizationStatus;
  limits: OrganizationLimits;
  billing: BillingInfo;
  ownerId: string;
  logoUrl?: string;
  websiteUrl?: string;
  description?: string;
  contactEmail?: string;
  timezone: string}

/**
 * Organization member interface - Redis compatible
 */
export interface OrganizationMember extends BaseEntity, ActivityTracked {
  organizationId: string,
      userId: string;
role: OrganizationRole,
    permissions: string[];
  invitedBy?: string;
  invitedAt?: Date;
  joinedAt?: Date;
  lastActiveAt?: Date;
}

/**
 * Organization invitation interface - Redis compatible
 */
export interface OrganizationInvitation extends BaseEntity, ActivityTracked {
  organizationId: string,
      email: string;
role: OrganizationRole,
    permissions: string[];
  invitedBy: string;
  token: string;
  expiresAt: Date;
  acceptedAt?: Date;
  acceptedBy?: string;
}

/**
 * API Key interface - Redis compatible
 */
export interface APIKey extends BaseEntity, ActivityTracked {
  organizationId: string,
      name: string;
keyHash: string,
    keyPrefix: string;
  permissions: string[],
      rateLimit: {;
requests: number,
    window: number};
  lastUsed?: Date;
  expiresAt?: Date;
  createdBy: string}

/**
 * Audit log interface - Redis compatible
 */
export interface AuditLog extends BaseEntity {
  organizationId: string,
      userId: string;
action: string,
    resource: string;
  resourceId: string,
    details: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  sessionId?: string;
}

/**
 * Security event interface - Redis compatible
 */
export interface SecurityEvent extends BaseEntity {
  organizationId?: string;
  userId?: string;
  type: string,
      severity: 'low' | 'medium' | 'high' | 'critical';
description: string,
    metadata: Record<string, any>;
  resolved: boolean;
  resolvedBy?: string;
  resolvedAt?: Date;
  ipAddress?: string;
  userAgent?: string;
}

/**
 * Feature flag interface - Redis compatible
 */
export interface FeatureFlag extends BaseEntity, ActivityTracked {
  key: string,
      name: string;
  description?: string;
enabled: boolean,
    rolloutPercentage: number;
  conditions: Array<{,
      key: string;
operator: 'equals' | 'contains' | 'matches',
    value: any}>;
  organizationOverrides: Record<string, boolean>;
  userOverrides: Record<string, boolean>;
}

/**
 * Performance metric interface - Redis compatible
 */
export interface PerformanceMetric extends BaseEntity {
  organizationId?: string;
  guildId?: string;
  service: string,
      operation: string;
duration: number // milliseconds,
    success: boolean;
  errorType?: string;
  metadata: Record<string, any>;
  timestamp: Date}

/**
 * Type definitions for create operations
 */
export type CreateOrganization = CreateEntity<Organization>;
export type CreateOrganizationMember = CreateEntity<OrganizationMember>;
export type CreateOrganizationInvitation = CreateEntity<OrganizationInvitation>;
export type CreateAPIKey = CreateEntity<APIKey>;
export type CreateAuditLog = CreateEntity<AuditLog>;
export type CreateSecurityEvent = CreateEntity<SecurityEvent>;
export type CreateFeatureFlag = CreateEntity<FeatureFlag>;
export type CreatePerformanceMetric = CreateEntity<PerformanceMetric>;

/**
 * Type definitions for update operations
 */
export type UpdateOrganization = UpdateEntity<Organization>;
export type UpdateOrganizationMember = UpdateEntity<OrganizationMember>;
export type UpdateOrganizationInvitation = UpdateEntity<OrganizationInvitation>;
export type UpdateAPIKey = UpdateEntity<APIKey>;
export type UpdateAuditLog = UpdateEntity<AuditLog>;
export type UpdateSecurityEvent = UpdateEntity<SecurityEvent>;
export type UpdateFeatureFlag = UpdateEntity<FeatureFlag>;
export type UpdatePerformanceMetric = UpdateEntity<PerformanceMetric>;

/**
 * Organization with relations type for expanded queries
 */
export interface OrganizationWithRelations extends Organization {
  members?: OrganizationMember[];
  invitations?: OrganizationInvitation[];
  apiKeys?: APIKey[];
  auditLogs?: AuditLog[];
  securityEvents?: SecurityEvent[];
  guilds?: any[]; // Will be properly typed when guild relations are implemented
}

/**
 * Redis key patterns for organization-related entities
 */
export const OrganizationKeys = {
  primary: (id: string) => `organization:${id}`,
  bySlug: (slug: string) => `organization:slug:${slug}`,
  byOwner: (ownerId: string) => `organizations:owner:${ownerId}`,
  byTier: (tier: OrganizationTier) => `organizations:tier:${tier}`,
  byStatus: (status: OrganizationStatus) => `organizations:status:${status}`,
  active: () => 'organizations:active',
    pattern: 'organization: *',
      index: {;
slug: 'idx:organization:slug',
    tier: 'idx: organization:tier',
    status: 'idx: organization:status',
    ownerId: 'idx: organization:owner_id',
    isActive: 'idx:organization:is_active',
  }
} as const;

export const OrganizationMemberKeys = {
  primary: (id: string) => `org_member:${id}`,
  byOrg: (orgId: string) => `org_members:org:${orgId}`,
  byUser: (userId: string) => `org_members:user:${userId}`,
  byOrgAndUser: (orgId: string, userId: string) => `org_member:${orgId}:${userId}`,
  byRole: (role: OrganizationRole) => `org_members:role:${role}`,
  pattern: 'org_member:*',
} as const;

export const OrganizationInvitationKeys = {
  primary: (id: string) => `org_invitation:${id}`,
  byToken: (token: string) => `org_invitation:token:${token}`,
  byOrg: (orgId: string) => `org_invitations:org:${orgId}`,
  byEmail: (email: string) => `org_invitations:email:${email}`,
  pattern: 'org_invitation:*',
} as const;

export const APIKeyKeys = {
  primary: (id: string) => `api_key:${id}`,
  byHash: (hash: string) => `api_key:hash:${hash}`,
  byPrefix: (prefix: string) => `api_key:prefix:${prefix}`,
  byOrg: (orgId: string) => `api_keys:org:${orgId}`,
  pattern: 'api_key:*',
} as const;

export const AuditLogKeys = {
  primary: (id: string) => `audit_log:${id}`,
  byOrg: (orgId: string) => `audit_logs:org:${orgId}`,
  byUser: (userId: string) => `audit_logs:user:${userId}`,
  byAction: (action: string) => `audit_logs:action:${action}`,
  pattern: 'audit_log:*',
} as const;

export const SecurityEventKeys = {
  primary: (id: string) => `security_event:${id}`,
  byOrg: (orgId: string) => `security_events:org:${orgId}`,
  byUser: (userId: string) => `security_events:user:${userId}`,
  byType: (type: string) => `security_events:type:${type}`,
  unresolved: () => 'security_events:unresolved',
    pattern: 'security_event:*',
} as const;

export const FeatureFlagKeys = {
  primary: (id: string) => `feature_flag:${id}`,
  byKey: (key: string) => `feature_flag:key:${key}`,
  enabled: () => 'feature_flags:enabled',
    pattern: 'feature_flag:*',
} as const;

export const PerformanceMetricKeys = {
  primary: (id: string) => `performance_metric:${id}`,
  byOrg: (orgId: string) => `performance_metrics:org:${orgId}`,
  byService: (service: string) => `performance_metrics:service:${service}`,
  pattern: 'performance_metric:*',
} as const;

/**
 * Legacy compatibility exports for Drizzle-style references
 */
export const organizations = {
  $inferSelect: {} as Organization,
  $inferInsert: {} as CreateOrganization
} as const;

export const organizationMembers = {
  $inferSelect: {} as OrganizationMember,
  $inferInsert: {} as CreateOrganizationMember
} as const;

export const organizationInvitations = {
  $inferSelect: {} as OrganizationInvitation,
  $inferInsert: {} as CreateOrganizationInvitation
} as const;

export const apiKeys = {
  $inferSelect: {} as APIKey,
  $inferInsert: {} as CreateAPIKey
} as const;

export const auditLogs = {
  $inferSelect: {} as AuditLog,
  $inferInsert: {} as CreateAuditLog
} as const;

export const securityEvents = {
  $inferSelect: {} as SecurityEvent,
  $inferInsert: {} as CreateSecurityEvent
} as const;

export const featureFlags = {
  $inferSelect: {} as FeatureFlag,
  $inferInsert: {} as CreateFeatureFlag
} as const;

export const performanceMetrics = {
  $inferSelect: {} as PerformanceMetric,
  $inferInsert: {} as CreatePerformanceMetric
} as const;

export type OrganizationsTable = typeof organizations;
export type OrganizationMembersTable = typeof organizationMembers;
export type OrganizationInvitationsTable = typeof organizationInvitations;
export type APIKeysTable = typeof apiKeys;
export type AuditLogsTable = typeof auditLogs;
export type SecurityEventsTable = typeof securityEvents;
export type FeatureFlagsTable = typeof featureFlags;
export type PerformanceMetricsTable = typeof performanceMetrics;