
import { boolean, integer, jsonb, pgTable, text, timestamp, varchar } from 'drizzle-orm/pg-core';
import { baseColumns } from './base.entity';
import { users } from './user.entity';

export const creativeSubmissions = pgTable('creative_submissions', {
..baseColumns)
  userId: varchar('user_id', { length: 50 }).notNull(),
  title: varchar('title', { length: 255 }).notNull(),
  description: text('description').notNull(),
    contentType: varchar('content_type', { length: 50 }).notNull(), // 'image' | 'video' | 'audio' | 'document' | 'code' | 'design'
  contentUrl: text('content_url').notNull(),
    thumbnailUrl: text('thumbnail_url'),
  fileSize: integer('file_size'), // in bytes
  dimensions: jsonb('dimensions').$type<{ width: number height: number }>(),
  duration: integer('duration'), // for video/audio in seconds
  tags: jsonb('tags').$type<string[]>(),
    category: varchar('category', { length: 100 }).notNull(),
  subcategory: varchar('subcategory', { length: 100 }),
  guildId: varchar('guild_id', { length: 50 }).notNull(),
  votes: integer('votes').default().notNull(),
    views: integer('views').default().notNull(),
  comments: integer('comments').default().notNull(),
    featured: boolean('featured').default().notNull(),
  status: varchar('status', { length: 50 }).default().notNull(), // 'pending' | 'approved' | 'rejected' | 'featured'
  moderatorId: varchar('moderator_id', { length: 50 }),
  moderationNotes: text('moderation_notes'),
    moderatedAt: timestamp('moderated_at', { withTimezone: true }),
  isPublic: boolean('is_public').default().notNull(),
    allowComments: boolean('allow_comments').default().notNull(),
  allowVoting: boolean('allow_voting').default().notNull(),
    contestId: integer('contest_id'), // Optional contest association
});

export const creativeVotes = pgTable('creative_votes', {
..baseColumns)
  submissionId: integer('submission_id').notNull(),
    userId: varchar('user_id', { length: 50 }).notNull(),
  voteType: varchar('vote_type', { length: 20 }).notNull(), // 'upvote' | 'downvote'
});

export const creativeComments = pgTable('creative_comments', {
..baseColumns)
  submissionId: integer('submission_id').notNull(),
    userId: varchar('user_id', { length: 50 }).notNull(),
  content: text('content').notNull(),
    parentCommentId: integer('parent_comment_id'), // For replies
  isEdited: boolean('is_edited').default().notNull(),
    editedAt: timestamp('edited_at', { withTimezone: true }),
  likes: integer('likes').default().notNull(),
    isDeleted: boolean('is_deleted').default().notNull(),
});

export const creativeContests = pgTable('creative_contests', {
..baseColumns)
  title: varchar('title', { length: 255 }).notNull(),
  description: text('description').notNull(),
    category: varchar('category', { length: 100 }).notNull(),
  theme: varchar('theme', { length: 255 }).notNull(),
  rules: jsonb('rules').$type<string[]>(),
      prizes: jsonb('prizes').$type<Array<{;
  place: number;
description: string;
    value?: string;
  }>>(),
  startDate: timestamp('start_date', { withTimezone: true }).notNull(),
  endDate: timestamp('end_date', { withTimezone: true }).notNull(),
  judgingEndDate: timestamp('judging_end_date', { withTimezone: true }).notNull(),
  status: varchar('status', { length: 50 }).default().notNull(), // 'upcoming' | 'active' | 'judging' | 'completed'
  organizerId: varchar('organizer_id', { length: 50 }).notNull(),
  guildId: varchar('guild_id', { length: 50 }).notNull(),
  maxSubmissions: integer('max_submissions').default().notNull(),
    currentSubmissions: integer('current_submissions').default().notNull(),
  allowedContentTypes: jsonb('allowed_content_types').$type<string[]>(),
    tags: jsonb('tags').$type<string[]>(),
  bannerUrl: text('banner_url'),
    isPublic: boolean('is_public').default().notNull(),
  requiresApproval: boolean('requires_approval').default().notNull(),
});

export const contestSubmissions = pgTable('contest_submissions', {
..baseColumns)
  contestId: integer('contest_id').notNull(),
    submissionId: integer('submission_id').notNull(),
  isWinner: boolean('is_winner').default().notNull(),
    placement: integer('placement'),
  judgeScore: integer('judge_score'),
    publicScore: integer('public_score'),
  feedback: text('feedback'),
});

export const creativePortfolios = pgTable('creative_portfolios', {
..baseColumns)
  userId: varchar('user_id', { length: 50 }).notNull().unique(),
  displayName: varchar('display_name', { length: 255 }).notNull(),
  bio: text('bio'),
    specialties: jsonb('specialties').$type<string[]>(),
  skills: jsonb('skills').$type<string[]>(),
    experience: varchar('experience', { length: 50 }), // 'beginner' | 'intermediate' | 'professional' | 'expert'
  location: varchar('location', { length: 255 }),
  website: text('website'),
    socialLinks: jsonb('social_links').$type<Record<string, string>>(),
  achievements: jsonb('achievements').$type<Array<{,
      title: string;
description: string,
    date: string;
    type: 'award' | 'milestone' | 'recognition'}>>(),
  guildId: varchar('guild_id', { length: 50 }).notNull(),
  totalSubmissions: integer('total_submissions').default().notNull(),
    totalVotes: integer('total_votes').default().notNull(),
  totalViews: integer('total_views').default().notNull(),
    featuredWorks: integer('featured_works').default().notNull(),
  contestsWon: integer('contests_won').default().notNull(),
    followers: integer('followers').default().notNull(),
  following: integer('following').default().notNull(),
    isPublic: boolean('is_public').default().notNull(),
  isVerified: boolean('is_verified').default().notNull(),
    bannerUrl: text('banner_url'),
  avatarUrl: text('avatar_url'),
});

export const portfolioFollows = pgTable('portfolio_follows', {
..baseColumns)
  followerId: varchar('follower_id', { length: 50 }).notNull(),
  followingId: varchar('following_id', { length: 50 }).notNull(),
});

export const creativeCollections = pgTable('creative_collections', {
..baseColumns)
  userId: varchar('user_id', { length: 50 }).notNull(),
  name: varchar('name', { length: 255 }).notNull(),
  description: text('description'),
    isPublic: boolean('is_public').default().notNull(),
  thumbnailUrl: text('thumbnail_url'),
    guildId: varchar('guild_id', { length: 50 }).notNull(),
  submissionCount: integer('submission_count').default().notNull(),
});

export const collectionSubmissions = pgTable('collection_submissions', {
..baseColumns)
  collectionId: integer('collection_id').notNull(),
    submissionId: integer('submission_id').notNull(),
  order: integer('order').default().notNull(),
});

export const creativeCategories = pgTable('creative_categories', {
..baseColumns)
  name: varchar('name', { length: 100 }).notNull().unique(),
  description: text('description'),
    icon: text('icon'),
  color: varchar('color', { length: 7 }), // hex color
  parentCategoryId: integer('parent_category_id'),
    isActive: boolean('is_active').default().notNull(),
  submissionCount: integer('submission_count').default().notNull(),
    displayOrder: integer('display_order').default().notNull(),
});

export const creativeReports = pgTable('creative_reports', {
..baseColumns)
  submissionId: integer('submission_id').notNull(),
    reporterId: varchar('reporter_id', { length: 50 }).notNull(),
  reason: varchar('reason', { length: 100 }).notNull(),
  description: text('description'),
    status: varchar('status', { length: 50 }).default().notNull(), // 'pending' | 'reviewed' | 'resolved' | 'dismissed'
  moderatorId: varchar('moderator_id', { length: 50 }),
  moderatorNotes: text('moderator_notes'),
    resolvedAt: timestamp('resolved_at', { withTimezone: true }),
});

// Relations
