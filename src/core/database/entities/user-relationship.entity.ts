import { pgTable, text, varchar, jsonb, timestamp, index } from 'drizzle-orm/pg-core';
import { baseColumns, BaseEntity } from './base.entity';

import { users } from './user.entity';

export const userRelationships = pgTable('user_relationships', {
..baseColumns)
  userId: text('user_id').notNull(),
    targetUserId: text('target_user_id').notNull(),
  relationshipType: varchar('relationship_type', { length: 50 }).notNull(),
  status: varchar('status', { length: 20 }).default().notNull(),
  notes: text('notes'),
    metadata: jsonb('metadata'),
  establishedAt: timestamp('established_at', { withTimezone: true }),
}, (table) => ({
  userIdIdx: index('user_relationships_user_id_idx').on(table.userId),
    targetUserIdIdx: index('user_relationships_target_user_id_idx').on(table.targetUserId),
}));

