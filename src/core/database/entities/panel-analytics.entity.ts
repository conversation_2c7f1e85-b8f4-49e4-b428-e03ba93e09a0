
import { boolean, index, integer, jsonb, pgTable, text, timestamp, varchar } from 'drizzle-orm/pg-core';
import { baseColumns } from './base.entity';
import { users } from './user.entity';

export const panelAnalytics = pgTable('panel_analytics', {
..baseColumns)
  userId: varchar('user_id', { length: 50 }),
  panelId: varchar('panel_id', { length: 100 }).notNull(),
  channelId: varchar('channel_id', { length: 50 }).notNull(),
  guildId: varchar('guild_id', { length: 50 }).notNull(),
  eventType: varchar('event_type', { length: 50 }).notNull(), // 'view', 'click', 'interaction', 'error', 'performance'
  eventData: jsonb('event_data').$type<{;
    action?: string;
    componentId?: string;
    componentType?: 'button' | 'select' | 'modal' | 'embed' | 'form';
    duration?: number;
    sessionId?: string;
    userAgent?: string;
    errorCode?: string;
    errorMessage?: string;
    stackTrace?: string;
    result?: 'success' | 'error' | 'cancelled' | 'timeout';
    performanceMetrics?: {
      responseTime: number,
      renderTime: number;
dataLoadTime: number,
    memoryUsage: number};
    contextData?: Record<string, any>;
    previousAction?: string;
    targetValue?: string;
    clickPosition?: { x: number y: number };
    scrollPosition?: number;
    viewportSize?: { width: number height: number };
  }>(),
  timestamp: timestamp('timestamp', { withTimezone: true }).defaultNow().notNull(),
  sessionId: varchar('session_id', { length: 100 }),
  ip: varchar('ip', { length: 45 }), // Support IPv6
  userAgent: text('user_agent'),
    metadata: jsonb('metadata').$type<Record<string, any>>(),
}, (table) => ({
  userIdIdx: index('panel_analytics_user_id_idx').on(table.userId),
    panelIdIdx: index('panel_analytics_panel_id_idx').on(table.panelId),
  channelIdIdx: index('panel_analytics_channel_id_idx').on(table.channelId),
    guildIdIdx: index('panel_analytics_guild_id_idx').on(table.guildId),
  eventTypeIdx: index('panel_analytics_event_type_idx').on(table.eventType),
    timestampIdx: index('panel_analytics_timestamp_idx').on(table.timestamp),
  sessionIdIdx: index('panel_analytics_session_id_idx').on(table.sessionId),
}));

export const panelPerformanceMetrics = pgTable('panel_performance_metrics', {
..baseColumns)
  panelId: varchar('panel_id', { length: 100 }).notNull(),
  channelId: varchar('channel_id', { length: 50 }).notNull(),
  guildId: varchar('guild_id', { length: 50 }).notNull(),
  date: timestamp('date', { withTimezone: true }).notNull(),
  metrics: jsonb('metrics').$type<{,
      totalViews: number;
uniqueUsers: number,
    totalInteractions: number;
    averageEngagementTime: number,
      bounceRate: number;
errorRate: number,
    averageResponseTime: number;
    popularActions: Array<{,
      action: string;
count: number,
    avgDuration: number}>;
    userSegments: {
    newUsers: number;
      returningUsers: number,
    powerUsers: number};
    peakUsageHours: Array<{,
      hour: number;
usage: number}>;
    conversionFunnels: Array<{,
      step: string;
users: number,
    conversionRate: number}>;
  }>(),
  isAggregated: boolean('is_aggregated').default().notNull(),
}, (table) => ({
  panelIdIdx: index('panel_performance_metrics_panel_id_idx').on(table.panelId),
    channelIdIdx: index('panel_performance_metrics_channel_id_idx').on(table.channelId),
  guildIdIdx: index('panel_performance_metrics_guild_id_idx').on(table.guildId),
    dateIdx: index('panel_performance_metrics_date_idx').on(table.date),
  aggregatedIdx: index('panel_performance_metrics_aggregated_idx').on(table.isAggregated),
}));

export const abTestResults = pgTable('ab_test_results', {
..baseColumns)
  testId: varchar('test_id', { length: 100 }).notNull(),
  panelId: varchar('panel_id', { length: 100 }).notNull(),
  variant: varchar('variant', { length: 50 }).notNull(), // 'A', 'B', 'C', etc.
  userId: varchar('user_id', { length: 50 }),
  channelId: varchar('channel_id', { length: 50 }).notNull(),
  guildId: varchar('guild_id', { length: 50 }).notNull(),
  outcome: varchar('outcome', { length: 50 }).notNull(), // 'conversion', 'engagement', 'retention'
  value: integer('value').notNull(), // numeric value for the outcome
  testStarted: timestamp('test_started', { withTimezone: true }).notNull(),
  testEnded: timestamp('test_ended', { withTimezone: true }),
  metadata: jsonb('metadata').$type<{,
      testName: string;
testDescription: string,
    hypothesis: string;
    successMetric: string;
  sampleSize: number;
  confidenceLevel: number;
    statisticalSignificance?: number;
    variants: Array<{,
      name: string;
description: string,
    allocation: number // percentage}>;
  }>(),
}, (table) => ({
  testIdIdx: index('ab_test_results_test_id_idx').on(table.testId),
    panelIdIdx: index('ab_test_results_panel_id_idx').on(table.panelId),
  variantIdx: index('ab_test_results_variant_idx').on(table.variant),
    userIdIdx: index('ab_test_results_user_id_idx').on(table.userId),
  outcomeIdx: index('ab_test_results_outcome_idx').on(table.outcome),
    testStartedIdx: index('ab_test_results_test_started_idx').on(table.testStarted),
}));

