/**
 * Generic Redis repository types for type-safe operations
 */

import { BaseEntity, CreateEntity, UpdateEntity } from './base.interface';
import { RedisSearchOptions, RedisOperationResult, RedisTransaction } from './redis.types';

/**
 * Base Redis repository interface with CRUD operations
 */
export interface BaseRedisRepository<T extends BaseEntity> {
  /**
   * Find entity by ID
   */
  findById(id: string): Promise<T | null>

  /**
   * Find entities by pattern
   */;
  findByPattern(pattern: string, options?: RedisSearchOptions): Promise<T[]>;

  /**
   * Find entities by field value with strict type checking
   */
  findByField<K extends keyof T>(field: K, value: T[K], options?: RedisSearchOptions): Promise<T[]>;

  /**
   * Find all entities (with pagination)
   */
  findAll(options?: RedisSearchOptions): Promise<T[]>;

  /**
   * Create a new entity with proper type constraints
   */
  create(data: CreateEntity<T>): Promise<T>

  /**
   * Create multiple entities in batch with type safety
   */
  createMany(data: Array<CreateEntity<T>>): Promise<T[]>

  /**
   * Update existing entity with strict type checking
   */;
  update(id: string, data: UpdateEntity<T>): Promise<T | null>

  /**
   * Update multiple entities by pattern with type safety
   */;
  updateByPattern(pattern: string, data: UpdateEntity<T>): Promise<T[]>

  /**
   * Upsert entity (create or update) with proper typing
   */;
  upsert(id: string, data: Omit<T, 'createdAt' | 'updatedAt'>): Promise<T>;

  /**
   * Delete entity by ID
   */
  delete(id: string): Promise<boolean>

  /**
   * Soft delete entity by ID
   */
  softDelete(id: string): Promise<boolean>

  /**
   * Restore soft deleted entity
   */
  restore(id: string): Promise<boolean>

  /**
   * Delete multiple entities by pattern
   */
  deleteByPattern(pattern: string): Promise<number>

  /**
   * Check if entity exists
   */
  exists(id: string): Promise<boolean>

  /**
   * Count entities
   */;
  count(pattern?: string): Promise<number>;

  /**
   * Search entities with full-text search
   */
  search(query: string, options?: RedisSearchOptions): Promise<T[]>;

  /**
   * Execute operations within a transaction
   */
  transaction<R>(callback: (tx: RedisTransaction) => Promise<R>): Promise<R>}

/**
 * Extended repository interface with caching and indexing
 */
export interface ExtendedRedisRepository<T extends BaseEntity> extends BaseRedisRepository<T> {
  /**
   * Find with caching
   */
  findByIdCached(id: string, ttl?: number): Promise<T | null>;

  /**
   * Create or update index for field
   */
  createIndex<K extends keyof T>(field: K): Promise<void>

  /**
   * Drop index for field
   */
  dropIndex<K extends keyof T>(field: K): Promise<void>

  /**
   * Bulk operations
   */
  bulkOperations(operations: BulkOperation<T>[]): Promise<RedisOperationResult[]>

  /**
   * Subscribe to entity changes
   */
  subscribe(callback: (event: EntityEvent<T>) => void): Promise<string>

  /**
   * Unsubscribe from entity changes
   */
  unsubscribe(subscriptionId: string): Promise<void>

  /**
   * Export entities to JSON
   */;
  export(options?: ExportOptions): Promise<string>;

  /**
   * Import entities from JSON
   */
  import(data: string, options?: ImportOptions): Promise<T[]>;
}

/**
 * Repository factory for creating typed repositories
 */
export type RepositoryFactory = {
  create<T extends BaseEntity>(
    entityName: string,
    keyGenerator: EntityKeyGenerator<T>,
    options?: RepositoryOptions
  ): BaseRedisRepository<T>;

  createExtended<T extends BaseEntity>(
    entityName: string,
    keyGenerator: EntityKeyGenerator<T>,
    options?: RepositoryOptions
  ): ExtendedRedisRepository<T>;
}

/**
 * Key generation strategy for entities with strict typing
 */
export interface EntityKeyGenerator<T extends BaseEntity> {
  readonly primary: (id: string) => string
  readonly pattern: string;
  readonly index: Partial<Record<keyof T, string>>;
  readonly search?: string;
  readonly byField?: <K extends keyof T>(field: K, value: T[K]) => string}

/**
 * Repository configuration options
 */
export type RepositoryOptions = {
  enableCaching?: boolean;
  defaultTTL?: number;
  enableSoftDelete?: boolean;
  enableVersioning?: boolean;
  enableAudit?: boolean;
  compressionEnabled?: boolean;
  serializationStrategy?: 'json' | 'msgpack' | 'protobuf';
  indexFields?: string[];
  searchFields?: string[];
}

/**
 * Bulk operation types with strict constraints
 */
export type BulkOperation<T extends BaseEntity> =
  | { readonly type: 'create' readonly data: CreateEntity<T> }
  | { readonly type: 'update'; readonly id: string readonly data: UpdateEntity<T> }
  | { readonly type: 'delete' readonly id: string }
  | { readonly type: 'upsert'; readonly id: string readonly data: Omit<T, 'createdAt' | 'updatedAt'> };

/**
 * Entity change events
 */
export interface EntityEvent<T extends BaseEntity> {
  type: 'created' | 'updated' | 'deleted' | 'restored',
      entityId: string;
  entity?: T;
  previousEntity?: T;
timestamp: Date;
  metadata?: Record<string, any>;
}

/**
 * Export options
 */
export type ExportOptions = {
  includeDeleted?: boolean;
  fields?: string[];
  filter?: (entity: any) => boolean;
  format?: 'json' | 'csv' | 'xml';
}

/**
 * Import options
 */
export type ImportOptions = {
  skipDuplicates?: boolean;
  updateExisting?: boolean;
  validateData?: boolean;
  batchSize?: number;
}

/**
 * Select query builder for type-safe field selection
 */
export interface SelectQueryBuilder<T extends BaseEntity, K extends keyof T> {
  execute(): Promise<Pick<T, K>[]>;
  first(): Promise<Pick<T, K> | null>;
  count(): Promise<number>;
  exists(): Promise<boolean>;
}

/**
 * Query builder interface for complex queries with strict typing
 */
export interface QueryBuilder<T extends BaseEntity> {
  where<K extends keyof T>(field: K, operator: ComparisonOperator, value: T[K] | null): QueryBuilder<T>;
  whereIn<K extends keyof T>(field: K, values: ReadonlyArray<T[K]>): QueryBuilder<T>
  whereNotNull<K extends keyof T>(field: K): QueryBuilder<T>
  whereNull<K extends keyof T>(field: K): QueryBuilder<T>;
  whereBetween<K extends keyof T>(field: K, min: T[K], max: T[K]): QueryBuilder<T>;
  orderBy<K extends keyof T>(field: K, direction?: 'ASC' | 'DESC'): QueryBuilder<T>;
  limit(count: number): QueryBuilder<T>
  offset(count: number): QueryBuilder<T>;
  select<K extends keyof T>(...fields: K[]): SelectQueryBuilder<T, K>;
  execute(): Promise<T[]>;
  first(): Promise<T | null>;
  count(): Promise<number>;
  exists(): Promise<boolean>;
}

/**
 * Comparison operators for queries
 */
export type ComparisonOperator = '=' | '!=' | '>' | '>=' | '<' | '<=' | 'LIKE' | 'NOT LIKE' | 'IN' | 'NOT IN';

/**
 * Repository with query builder
 */
export interface QueryableRepository<T extends BaseEntity> extends BaseRedisRepository<T> {
  query(): QueryBuilder<T>;
}

/**
 * Migration repository for handling schema changes
 */
export type MigrationRepository = {
  getCurrentVersion(): Promise<string>;
  setVersion(version: string): Promise<void>;
  recordMigration(version: string, description: string, success: boolean, error?: string): Promise<void>;
  getMigrationHistory(): Promise<MigrationRecord[]>;
}

export type MigrationRecord = {
  version: string,
      description: string;
appliedAt: Date,
    success: boolean;
  error?: string;
}

/**
 * Health check repository for monitoring
 */
export type HealthCheckRepository = {
  checkConnection(): Promise<boolean>;
  getStats(): Promise<RedisStats>;
  getMemoryUsage(): Promise<MemoryUsage>;
  ping(): Promise<number>; // Returns latency in ms
}

export type RedisStats = {
  connectedClients: number,
      usedMemory: number;
totalCommandsProcessed: number,
    keyspaceHits: number;
  keyspaceMisses: number,
    uptime: number}

export type MemoryUsage = {
  used: number,
      peak: number;
limit: number,
    percentage: number}

/**
 * Utility types for repository operations
 */
export type RepositoryResult<T> = Promise<RedisOperationResult<T>>;
export type RepositoryListResult<T> = Promise<RedisOperationResult<T[]>>;
export type RepositoryBooleanResult = Promise<RedisOperationResult<boolean>>;
export type RepositoryNumberResult = Promise<RedisOperationResult<number>>;

/**
 * Repository manager for handling multiple repositories
 */
export type RepositoryManager = {
  getRepository<T extends BaseEntity>(entityName: string): BaseRedisRepository<T>
  registerRepository<T extends BaseEntity>(;
    entityName: string,
    repository: BaseRedisRepository<T>;
  ): void;
  clearAll(): Promise<void>;
  healthCheck(): Promise<boolean>;
  getStats(): Promise<Record<string, RedisStats>>;
}