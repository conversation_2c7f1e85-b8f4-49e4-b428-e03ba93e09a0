/**
 * Redis-specific types for type-safe operations
 */

import type { RedisOptions, Redis, Pipeline, ChainableCommander } from 'ioredis';

/**
 * Redis configuration interface that matches ioredis RedisOptions
 * Based on ioredis v5.6.1 RedisOptions interface
 */
export interface RedisConfig extends Omit<RedisOptions, 'retryDelayOnFailover'> {
  host?: string;
  port?: number;
  password?: string;
  username?: string;
  db?: number;
  url?: string;
  
  // Connection options
  connectTimeout?: number;
  commandTimeout?: number;
  socketTimeout?: number;
  lazyConnect?: boolean;
  keepAlive?: number;
  noDelay?: boolean;
  connectionName?: string;
  family?: 4 | 6;
  
  // Retry and resilience options
  retryStrategy?: (times: number) => number | void | null;
  maxRetriesPerRequest?: number | null;
  reconnectOnError?: ((err: Error) => boolean | 1 | 2) | null;
  
  // Queue and command options
  enableOfflineQueue?: boolean;
  enableReadyCheck?: boolean;
  maxLoadingRetryTime?: number;
  
  // Pub/Sub options
  autoResubscribe?: boolean;
  autoResendUnfulfilledCommands?: boolean;
  
  // Pipeline options
  enableAutoPipelining?: boolean;
  autoPipeliningIgnoredCommands?: string[];
  
  // Other options
  readOnly?: boolean;
  stringNumbers?: boolean;
  monitor?: boolean;
  offlineQueue?: boolean;
  commandQueue?: boolean;
  keyPrefix?: string;
}

/**
 * Base Redis entity with timestamps - strict typing
 */
export interface BaseRedisEntity {
  readonly id: string;
  readonly createdAt: Date;
  readonly updatedAt: Date;
  readonly deletedAt?: Date | null;
}

/**
 * Redis key patterns for consistent naming
 */
export type RedisKeyPattern = 
  | `user:${string}`
  | `session:${string}`
  | `guild:${string}`
  | `agent:${string}`
  | `chat:${string}`
  | `panel:${string}`
  | `support:${string}`
  | `community:${string}`
  | `config:${string}`
  | `cache:${string}`
  | `lock:${string}`
  | `queue:${string}`;

/**
 * Redis key suffix patterns
 */
export type RedisKeySuffix = 
  | 'data'
  | 'meta'
  | 'config'
  | 'stats'
  | 'activity'
  | 'members'
  | 'channels'
  | 'roles'
  | 'achievements'
  | 'memory'
  | 'interactions';

/**
 * Redis operation result types with strict constraints
 */
export interface RedisOperationResult<T = unknown> {
  readonly success: boolean;
  readonly data?: T;
  readonly error?: string;
  readonly timestamp: Date;
  readonly operation: string;
  readonly duration?: number;
}

/**
 * Generic Redis repository operations with strict typing
 */
export interface RedisRepository<T extends BaseRedisEntity> {
  findById(id: string): Promise<T | null>;
  findByPattern(pattern: string): Promise<T[]>;
  findAll(): Promise<T[]>;
  create(data: Omit<T, 'id' | 'createdAt' | 'updatedAt'>): Promise<T>;
  update(id: string, data: Partial<Omit<T, 'id' | 'createdAt'>>): Promise<T | null>;
  delete(id: string): Promise<boolean>;
  softDelete(id: string): Promise<boolean>;
  exists(id: string): Promise<boolean>;
  count(): Promise<number>;
}

/**
 * Redis search options with proper typing
 */
export interface RedisSearchOptions {
  readonly limit?: number;
  readonly offset?: number;
  readonly sortBy?: string;
  readonly sortOrder?: 'ASC' | 'DESC';
  readonly filters?: Readonly<Record<string, unknown>>;
}

/**
 * Redis transaction context
 */
export interface RedisTransactionContext {
  multi(): RedisTransaction;
  pipeline(): RedisPipeline;
}

/**
 * Redis transaction operations with proper typing
 */
export interface RedisTransaction extends ChainableCommander {
  exec(): Promise<Array<[Error | null, unknown]> | null>;
  discard(): Promise<'OK'>;
}

/**
 * Redis pipeline operations with proper typing
 */
export interface RedisPipeline extends Pipeline {
  exec(): Promise<Array<[Error | null, unknown]> | null>;
}

/**
 * Redis cache options
 */
export interface RedisCacheOptions {
  readonly ttl?: number; // Time to live in seconds
  readonly compress?: boolean;
  readonly serialize?: boolean;
  readonly tags?: readonly string[];
  readonly consistency?: CacheConsistencyLevel;
  readonly strategy?: CacheInvalidationStrategy;
}

/**
 * Redis pub/sub types
 */
export interface RedisPubSubMessage<T = unknown> {
  readonly channel: string;
  readonly pattern?: string;
  readonly data: T;
  readonly timestamp: Date;
  readonly messageId?: string;
}

export type RedisEventListener<T = unknown> = (message: RedisPubSubMessage<T>) => void | Promise<void>;

/**
 * Redis pub/sub channel manager
 */
export interface RedisPubSubManager {
  subscribe<T = unknown>(channel: string, listener: RedisEventListener<T>): Promise<void>;
  unsubscribe(channel: string, listener?: RedisEventListener): Promise<void>;
  publish<T = unknown>(channel: string, data: T): Promise<number>;
  psubscribe<T = unknown>(pattern: string, listener: RedisEventListener<T>): Promise<void>;
  punsubscribe(pattern: string, listener?: RedisEventListener): Promise<void>;
  getSubscriptions(): readonly string[];
  getPatternSubscriptions(): readonly string[];
}

/**
 * Redis collection types for different data structures with strict typing
 */
export type RedisValue = string | number | boolean | Record<string, unknown> | null | undefined;
export type RedisHash = Readonly<Record<string, RedisValue>>;
export type RedisList = readonly RedisValue[];
export type RedisSet = ReadonlySet<RedisValue>;
export type RedisSortedSet = ReadonlyMap<RedisValue, number>;

/**
 * Redis serialization strategies
 */
export interface RedisSerializationStrategy<T = unknown> {
  readonly name: string;
  serialize(value: T): string;
  deserialize(value: string): T;
  canHandle(value: unknown): boolean;
}

/**
 * Built-in serialization types
 */
export type RedisSerializationType = 'json' | 'string' | 'number' | 'boolean' | 'date' | 'buffer';

/**
 * Redis cache strategies
 */
export interface RedisCacheStrategy {
  readonly name: string;
  readonly description: string;
  get<T>(key: string): Promise<T | null>;
  set<T>(key: string, value: T, options?: RedisCacheOptions): Promise<boolean>;
  delete(key: string): Promise<boolean>;
  clear(pattern?: string): Promise<number>;
  exists(key: string): Promise<boolean>;
  ttl(key: string): Promise<number>;
  mget<T>(keys: readonly string[]): Promise<Array<T | null>>;
  mset<T>(entries: ReadonlyArray<readonly [string, T]>, options?: RedisCacheOptions): Promise<boolean>;
  mdel(keys: readonly string[]): Promise<number>;
}

/**
 * Cache invalidation strategies
 */
export type CacheInvalidationStrategy = 
  | 'time-based'    // TTL expiration
  | 'event-based'   // Invalidate on events
  | 'tag-based'     // Invalidate by tags
  | 'dependency'    // Invalidate by dependencies
  | 'manual';       // Manual invalidation

/**
 * Cache consistency levels
 */
export type CacheConsistencyLevel = 
  | 'eventual'      // Eventually consistent
  | 'strong'        // Strongly consistent
  | 'weak';         // Weakly consistent

/**
 * Redis session storage interface
 */
export interface RedisSessionStorage {
  get<T = unknown>(sessionId: string): Promise<T | null>;
  set<T = unknown>(sessionId: string, data: T, ttl?: number): Promise<boolean>;
  destroy(sessionId: string): Promise<boolean>;
  touch(sessionId: string, ttl?: number): Promise<boolean>;
  exists(sessionId: string): Promise<boolean>;
  clear(): Promise<number>;
  length(): Promise<number>;
  getAllSessions<T = unknown>(): Promise<Record<string, T>>;
  regenerateId(oldSessionId: string, newSessionId: string): Promise<boolean>;
}

/**
 * Redis lock interface for distributed locking
 */
export interface RedisLock {
  readonly key: string;
  readonly value: string;
  readonly ttl: number;
  readonly acquired: boolean;
  extend(ttl: number): Promise<boolean>;
  release(): Promise<boolean>;
}

/**
 * Redis lock manager
 */
export interface RedisLockManager {
  acquire(key: string, ttl?: number, retryOptions?: RedisLockRetryOptions): Promise<RedisLock | null>;
  release(lock: RedisLock): Promise<boolean>;
  extend(lock: RedisLock, ttl: number): Promise<boolean>;
  isLocked(key: string): Promise<boolean>;
  forceClear(key: string): Promise<boolean>;
}

/**
 * Redis lock retry options
 */
export interface RedisLockRetryOptions {
  readonly retryAttempts?: number;
  readonly retryDelay?: number;
  readonly exponentialBackoff?: boolean;
  readonly maxRetryDelay?: number;
}

/**
 * Redis error types
 */
export class RedisError extends Error {
  constructor(
    message: string,
    public readonly code?: string,
    public readonly operation?: string,
    public readonly key?: string
  ) {
    super(message);
    this.name = 'RedisError';
  }
}

export class RedisConnectionError extends RedisError {
  constructor(message: string) {
    super(message, 'CONNECTION_ERROR');
    this.name = 'RedisConnectionError';
  }
}

export class RedisTimeoutError extends RedisError {
  constructor(operation: string, key?: string) {
    super(`Operation ${operation} timed out`, 'TIMEOUT_ERROR', operation, key);
    this.name = 'RedisTimeoutError';
  }
}

export class RedisSerializationError extends RedisError {
  constructor(message: string, key?: string) {
    super(message, 'SERIALIZATION_ERROR', 'serialize', key);
    this.name = 'RedisSerializationError';
  }
}

export class RedisDeserializationError extends RedisError {
  constructor(message: string, key?: string) {
    super(message, 'DESERIALIZATION_ERROR', 'deserialize', key);
    this.name = 'RedisDeserializationError';
  }
}

/**
 * Redis performance metrics
 */
export interface RedisPerformanceMetrics {
  readonly operations: {
    readonly reads: number;
    readonly writes: number;
    readonly deletes: number;
    readonly total: number;
  };
  readonly performance: {
    readonly averageLatency: number;
    readonly slowQueries: number;
    readonly errorRate: number;
    readonly throughput: number;
  };
  readonly memory: {
    readonly used: string;
    readonly peak: string;
    readonly fragmentation: number;
    readonly efficiency: number;
  };
  readonly connections: {
    readonly active: number;
    readonly peak: number;
    readonly refused: number;
    readonly total: number;
  };
  readonly keyspace: {
    readonly totalKeys: number;
    readonly expires: number;
    readonly avgTtl: number;
    readonly hitRate: number;
  };
  readonly replication: {
    readonly role: 'master' | 'slave';
    readonly connectedSlaves: number;
    readonly replBacklogSize: number;
    readonly replLag: number;
  };
}

/**
 * Redis health check result
 */
export interface RedisHealthCheck {
  readonly status: 'healthy' | 'unhealthy' | 'degraded';
  readonly latency: number;
  readonly memory: {
    readonly used: string;
    readonly peak: string;
    readonly fragmentation: number;
  };
  readonly connections: {
    readonly active: number;
    readonly total: number;
  };
  readonly uptime: number;
  readonly version: string;
  readonly checks: {
    readonly connectivity: boolean;
    readonly memory: boolean;
    readonly performance: boolean;
    readonly replication: boolean;
  };
  readonly timestamp: Date;
}

/**
 * Redis monitoring interface
 */
export interface RedisMonitoring {
  getHealthCheck(): Promise<RedisHealthCheck>;
  getPerformanceMetrics(): Promise<RedisPerformanceMetrics>;
  getSlowQueries(limit?: number): Promise<RedisSlowQuery[]>;
  getConnectionInfo(): Promise<RedisConnectionInfo[]>;
  getMemoryInfo(): Promise<RedisMemoryInfo>;
  getReplicationInfo(): Promise<RedisReplicationInfo>;
  clearSlowLog(): Promise<void>;
  executeCommand(command: string, ...args: unknown[]): Promise<unknown>;
}

/**
 * Redis slow query information
 */
export interface RedisSlowQuery {
  readonly id: number;
  readonly timestamp: Date;
  readonly duration: number;
  readonly command: readonly string[];
  readonly clientInfo: string;
}

/**
 * Redis connection information
 */
export interface RedisConnectionInfo {
  readonly id: string;
  readonly addr: string;
  readonly name: string;
  readonly age: number;
  readonly idle: number;
  readonly flags: string;
  readonly db: number;
  readonly sub: number;
  readonly psub: number;
  readonly multi: number;
  readonly qbuf: number;
  readonly qbufFree: number;
  readonly obl: number;
  readonly oll: number;
  readonly omem: number;
  readonly events: string;
  readonly cmd: string;
}

/**
 * Redis memory information
 */
export interface RedisMemoryInfo {
  readonly used: number;
  readonly peak: number;
  readonly rss: number;
  readonly overhead: number;
  readonly datasetSize: number;
  readonly fragmentation: number;
  readonly defragRunning: boolean;
}

/**
 * Redis replication information
 */
export interface RedisReplicationInfo {
  readonly role: 'master' | 'slave';
  readonly connectedSlaves: number;
  readonly masterHost?: string;
  readonly masterPort?: number;
  readonly masterLinkStatus?: 'up' | 'down';
  readonly masterLastIO?: Date;
  readonly masterSyncInProgress?: boolean;
  readonly slavePriority?: number;
  readonly slaveReadonly?: boolean;
}

/**
 * Redis migration types
 */
export interface RedisMigration {
  readonly version: string;
  readonly description: string;
  readonly up: (redis: Redis) => Promise<void>;
  readonly down: (redis: Redis) => Promise<void>;
}

export interface MigrationStatus {
  readonly version: string;
  readonly appliedAt: Date;
  readonly success: boolean;
  readonly error?: string;
  readonly duration?: number;
}

/**
 * Redis data export/import types
 */
export interface RedisExportData {
  readonly version: string;
  readonly timestamp: Date;
  readonly keys: Record<string, RedisExportValue>;
  readonly meta: {
    readonly totalKeys: number;
    readonly totalSize: number;
    readonly patterns: readonly string[];
  };
}

export interface RedisExportValue {
  readonly type: 'string' | 'hash' | 'list' | 'set' | 'zset' | 'stream';
  readonly value: unknown;
  readonly ttl?: number;
  readonly size: number;
}

/**
 * Redis batch operation types
 */
export interface RedisBatchOperation<T = unknown> {
  readonly type: 'get' | 'set' | 'del' | 'exists' | 'expire';
  readonly key: string;
  readonly value?: T;
  readonly ttl?: number;
  readonly options?: RedisCacheOptions;
}

export interface RedisBatchResult<T = unknown> {
  readonly operations: readonly RedisBatchOperation<T>[];
  readonly results: readonly RedisOperationResult<T>[];
  readonly duration: number;
  readonly errors: readonly RedisError[];
}

/**
 * Redis client interface wrapper
 */
export interface RedisClient extends Redis {
  readonly isConnected: boolean;
  readonly config: RedisConfig;
  healthCheck(): Promise<RedisHealthCheck>;
  getMetrics(): Promise<RedisPerformanceMetrics>;
}

/**
 * Type-safe Redis command builder
 */
export interface RedisCommandBuilder {
  key(pattern: RedisKeyPattern): RedisCommandBuilder;
  suffix(suffix: RedisKeySuffix): RedisCommandBuilder;
  ttl(seconds: number): RedisCommandBuilder;
  build(): string;
}