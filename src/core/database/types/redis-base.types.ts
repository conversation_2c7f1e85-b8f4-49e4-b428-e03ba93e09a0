/**
 * Base interface for all Redis entities
 */
export type RedisBaseEntity = {
  id: string | number;
  createdAt: Date | string;
  updatedAt: Date | string;
  deletedAt?: Date | string | null;
}

/**
 * Type for creating new entities (without generated fields)
 */
export type RedisCreateEntity<T extends RedisBaseEntity> = Omit<T, 'id' | 'createdAt' | 'updatedAt'>;

/**
 * Type for updating entities (without readonly fields)
 */
export type RedisUpdateEntity<T extends RedisBaseEntity> = Partial<Omit<T, 'id' | 'createdAt'>>;

/**
 * Redis query conditions
 */
export interface RedisQueryConditions<T = any> {
  eq?: Partial<T>;
  ne?: Partial<T>;
  gt?: Partial<T>;
  gte?: Partial<T>;
  lt?: Partial<T>;
  lte?: Partial<T>;
  in?: Partial<Record<keyof T, any[]>>;
  nin?: Partial<Record<keyof T, any[]>>;
  like?: Partial<Record<keyof T, string>>;
  exists?: Array<keyof T>;
  nexists?: Array<keyof T>;
}

/**
 * Redis pagination options
 */
export type RedisPaginationOptions = {
  page?: number;
  limit?: number;
  offset?: number;
}

/**
 * Redis sorting options
 */
export interface RedisSortOptions<T = any> {
  field: keyof T,
    direction: 'ASC' | 'DESC'}

/**
 * Redis query options
 */
export interface RedisQueryOptions<T = any> extends RedisPaginationOptions {
  select?: Array<keyof T>;
  where?: RedisQueryConditions<T>;
  orderBy?: RedisSortOptions<T> | Array<RedisSortOptions<T>>;
  include?: string[];
}

/**
 * Redis transaction result
 */
export interface RedisTransactionResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  affected?: number;
}

/**
 * Redis batch operation
 */
export type RedisBatchOperation = {
  operation: 'create' | 'update' | 'delete';
  entityType: string;
  data: any;
  id?: string | number;
}

/**
 * Redis search options
 */
export interface RedisSearchOptions<T = any> {
  query: string;
  fields?: Array<keyof T>;
  fuzzy?: boolean;
  limit?: number;
  boost?: Partial<Record<keyof T, number>>;
}

/**
 * Redis aggregation options
 */
export interface RedisAggregationOptions<T = any> {
  groupBy?: keyof T;
  functions?: Array<{
    type: 'count' | 'sum' | 'avg' | 'min' | 'max';
    field?: keyof T;
    alias?: string;
  }>;
  having?: RedisQueryConditions<T>;
}

/**
 * Redis index definition
 */
export type RedisIndexDefinition = {
  name: string;
  fields: string[];
  type: 'text' | 'numeric' | 'geo' | 'tag'
  options?: {;
    sortable?: boolean;
    noindex?: boolean;
    weight?: number;
  };
}

/**
 * Redis connection options
 */
export type RedisConnectionOptions = {
  host?: string;
  port?: number;
  password?: string;
  database?: number;
  url?: string;
  retryAttempts?: number;
  retryDelay?: number;
  timeout?: number;
}

/**
 * Redis cache options
 */
export type RedisCacheOptions = {
  ttl?: number;
  prefix?: string;
  serialize?: boolean;
  compress?: boolean;
}

/**
 * Redis lock options
 */
export type RedisLockOptions = {
  ttl?: number;
  retryAttempts?: number;
  retryDelay?: number;
}

/**
 * Redis pub/sub message
 */
export interface RedisPubSubMessage<T = any> {
  channel: string;
  pattern?: string;
  data: T,
    timestamp: string}

/**
 * Redis health check result
 */
export type RedisHealthCheck = {
  status: 'healthy' | 'unhealthy' | 'degraded';
  latency?: number;
  memory?: {
    used: string,
      peak: string;
fragmentation: number};
  connections?: {
    active: number,
    total: number};
  uptime?: number;
  version?: string;
}

/**
 * Redis metrics
 */
export type RedisMetrics = {
  operations: {
    reads: number;
    writes: number,
    deletes: number};
  performance: {
    averageLatency: number;
    slowQueries: number,
    errorRate: number};
  storage: {
    totalKeys: number;
    memoryUsage: string,
    hitRate: number};
  connections: {
    active: number;
    peak: number,
    refused: number};
}