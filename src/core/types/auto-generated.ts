
// Auto-generated interfaces to fix compilation errors
import { Request } from 'express';

export interface AuthenticatedRequest extends Request {
  user?: any;
}

export type AgentCluster = {
  coordinator: PersonalAgentCoordinator;
  isolation: any;
  metrics: any;
  createdAt?: Date;
  lastActivity?: Date;
};

export type PersonalAgentCoordinator = {
  shutdown?(): Promise<void>;
};

export type IMemberAgentIsolation = {
  getMemberAgents(memberId: string): Promise<PersonalAgentSet>;
  isolateAgentMemory(memberId: string): Promise<void>;
  destroyMemberAgents(memberId: string): Promise<void>;
  upgradeMemberTier(memberId: string, tier: string): Promise<void>;
  downgradeMemberTier(memberId: string): Promise<void>;
  getMemberTier(memberId: string): Promise<string>;
  validateMemberAccess(memberId: string): Promise<boolean>;
};

export type PersonalAgentSet = {
  [key: string]: any;
};

export type AuthenticatedRequest = Request & {
  user?: any};

export type MemberContext = {
  memberId: string;
  guildId?: string;
  channelId?: string;
  [key: string]: any;
};

export type PersonalizedResponse = {
  content: string;
  adaptedTone?: string;
  [key: string]: any;
};

export type AgentType = {
  [key: string]: string;
};

export type InteractionType = {
  [key: string]: string;
};
