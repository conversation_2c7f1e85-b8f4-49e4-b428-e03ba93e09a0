/**
 * Comprehensive validation types for the EnergeX Discord bot
 * Provides type-safe validation schemas and utilities
 */

/**
 * Core validation types
 */
export type ValidationValue = string | number | boolean | Date | object | any[] | null | undefined;

export type ValidationType = 
  | 'string'
  | 'number' 
  | 'integer'
  | 'boolean'
  | 'date'
  | 'email'
  | 'url'
  | 'uuid'
  | 'array'
  | 'object'
  | 'enum'
  | 'custom';

/**
 * Validation rule definition
 */
export interface ValidationRule {
  type: ValidationType;
  required?: boolean;
  nullable?: boolean;
  
  // String validations
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp | string;
  format?: 'email' | 'url' | 'uuid' | 'phone' | 'password';
  
  // Number validations
  min?: number;
  max?: number;
  integer?: boolean;
  positive?: boolean;
  negative?: boolean;
  
  // Array validations
  minItems?: number;
  maxItems?: number;
  uniqueItems?: boolean;
  items?: ValidationRule | ValidationSchema;
  
  // Object validations
  properties?: Record<string, ValidationRule>;
  additionalProperties?: boolean;
  requiredProperties?: string[];
  
  // Enum validation
  enum?: any[];
  
  // Custom validation
  validator?: CustomValidator;
  validators?: CustomValidator[];
  
  // Transformation
  transform?: ValueTransformer;
  default?: any;
  
  // Error handling
  message?: string;
  messages?: Record<string, string>;
}

/**
 * Validation schema
 */
export interface ValidationSchema {
  type: 'object';
  properties: Record<string, ValidationRule>;
  required?: string[];
  additionalProperties?: boolean;
  strict?: boolean;
  transform?: ObjectTransformer;
}

/**
 * Custom validator function
 */
export type CustomValidator = (
  value: any,
  context?: ValidationContext
) => ValidationError | ValidationError[] | null | Promise<ValidationError | ValidationError[] | null>;

/**
 * Value transformer function
 */
export type ValueTransformer = (value: any, context?: ValidationContext) => any;

/**
 * Object transformer function  
 */
export type ObjectTransformer = (obj: any, context?: ValidationContext) => any;

/**
 * Validation context
 */
export interface ValidationContext {
  path: string[];
  root: any;
  parent?: any;
  key?: string | number;
  schema: ValidationRule | ValidationSchema;
  options: ValidationOptions;
}

/**
 * Validation options
 */
export interface ValidationOptions {
  strict?: boolean;
  abortEarly?: boolean;
  allowUnknown?: boolean;
  stripUnknown?: boolean;
  transformValues?: boolean;
  contextPath?: string[];
  customValidators?: Record<string, CustomValidator>;
}

/**
 * Validation result
 */
export interface ValidationResult<T = any> {
  valid: boolean;
  data?: T;
  errors: ValidationError[];
  warnings?: ValidationWarning[];
  transformed?: boolean;
}

/**
 * Validation error
 */
export class ValidationError extends Error {
  public readonly code: string;
  public readonly path: string[];
  public readonly value: any;
  public readonly rule: ValidationRule;
  public readonly severity: 'error' | 'warning';

  constructor(
    message: string,
    path: string[] = [],
    value: any = undefined,
    rule: ValidationRule,
    code: string = 'VALIDATION_ERROR',
    severity: 'error' | 'warning' = 'error'
  ) {
    super(message);
    this.name = 'ValidationError';
    this.code = code;
    this.path = path;
    this.value = value;
    this.rule = rule;
    this.severity = severity;
  }

  get pathString(): string {
    return this.path.length > 0 ? this.path.join('.') : 'root';
  }
}

/**
 * Validation warning
 */
export interface ValidationWarning {
  message: string;
  path: string[];
  value: any;
  code: string;
}

/**
 * Built-in validators
 */
export interface BuiltinValidators {
  // String validators
  email: CustomValidator;
  url: CustomValidator;
  uuid: CustomValidator;
  phone: CustomValidator;
  password: CustomValidator;
  alphanumeric: CustomValidator;
  alpha: CustomValidator;
  numeric: CustomValidator;
  
  // Number validators
  positive: CustomValidator;
  negative: CustomValidator;
  integer: CustomValidator;
  port: CustomValidator;
  
  // Discord-specific validators
  discordId: CustomValidator;
  discordTag: CustomValidator;
  discordInvite: CustomValidator;
  guildId: CustomValidator;
  channelId: CustomValidator;
  roleId: CustomValidator;
  
  // Date validators
  futureDate: CustomValidator;
  pastDate: CustomValidator;
  dateRange: (min?: Date, max?: Date) => CustomValidator;
  
  // Array validators
  uniqueArray: CustomValidator;
  nonEmpty: CustomValidator;
  
  // Object validators
  hasProperty: (property: string) => CustomValidator;
  hasAnyProperty: (properties: string[]) => CustomValidator;
  hasAllProperties: (properties: string[]) => CustomValidator;
}

/**
 * Schema builder interface
 */
export interface ISchemaBuilder {
  string(): IStringSchema;
  number(): INumberSchema;
  integer(): IIntegerSchema;
  boolean(): IBooleanSchema;
  date(): IDateSchema;
  array(): IArraySchema;
  object(): IObjectSchema;
  enum<T>(values: T[]): IEnumSchema<T>;
  custom(validator: CustomValidator): ICustomSchema;
}

export interface IBaseSchema<T = any> {
  required(): this;
  optional(): this;
  nullable(): this;
  default(value: T): this;
  transform(transformer: ValueTransformer): this;
  validate(validator: CustomValidator): this;
  message(message: string): this;
  messages(messages: Record<string, string>): this;
  build(): ValidationRule;
}

export interface IStringSchema extends IBaseSchema<string> {
  min(length: number): this;
  max(length: number): this;
  length(length: number): this;
  pattern(regex: RegExp | string): this;
  email(): this;
  url(): this;
  uuid(): this;
  alphanum(): this;
  alpha(): this;
  numeric(): this;
}

export interface INumberSchema extends IBaseSchema<number> {
  min(value: number): this;
  max(value: number): this;
  positive(): this;
  negative(): this;
  port(): this;
}

export interface IIntegerSchema extends IBaseSchema<number> {
  min(value: number): this;
  max(value: number): this;
  positive(): this;
  negative(): this;
}

export interface IBooleanSchema extends IBaseSchema<boolean> {
  truthy(): this;
  falsy(): this;
}

export interface IDateSchema extends IBaseSchema<Date> {
  min(date: Date): this;
  max(date: Date): this;
  future(): this;
  past(): this;
}

export interface IArraySchema extends IBaseSchema<any[]> {
  min(items: number): this;
  max(items: number): this;
  length(items: number): this;
  unique(): this;
  items(schema: ValidationRule): this;
}

export interface IObjectSchema extends IBaseSchema<object> {
  keys(schema: Record<string, ValidationRule>): this;
  required(...keys: string[]): this;
  unknown(allow?: boolean): this;
  strict(enabled?: boolean): this;
}

export interface IEnumSchema<T> extends IBaseSchema<T> {
  // Enum-specific methods if needed
}

export interface ICustomSchema extends IBaseSchema<any> {
  // Custom-specific methods if needed
}

/**
 * Common validation schemas
 */
export interface CommonSchemas {
  // User schemas
  userInput: ValidationSchema;
  userProfile: ValidationSchema;
  userSettings: ValidationSchema;
  
  // Guild schemas
  guildConfig: ValidationSchema;
  guildSettings: ValidationSchema;
  channelConfig: ValidationSchema;
  
  // API schemas
  apiRequest: ValidationSchema;
  apiResponse: ValidationSchema;
  webhookPayload: ValidationSchema;
  
  // System schemas
  configFile: ValidationSchema;
  logEntry: ValidationSchema;
  errorReport: ValidationSchema;
  
  // Discord schemas
  discordUser: ValidationSchema;
  discordGuild: ValidationSchema;
  discordChannel: ValidationSchema;
  discordMessage: ValidationSchema;
  discordEmbed: ValidationSchema;
}

/**
 * Validation service interface
 */
export interface IValidationService {
  // Core validation methods
  validate<T>(data: unknown, schema: ValidationSchema | ValidationRule): Promise<ValidationResult<T>>;
  validateSync<T>(data: unknown, schema: ValidationSchema | ValidationRule): ValidationResult<T>;
  
  // Schema management
  registerSchema(name: string, schema: ValidationSchema): void;
  getSchema(name: string): ValidationSchema | null;
  removeSchema(name: string): boolean;
  
  // Validator management
  registerValidator(name: string, validator: CustomValidator): void;
  getValidator(name: string): CustomValidator | null;
  removeValidator(name: string): boolean;
  
  // Utility methods
  isValid<T>(data: unknown, schema: ValidationSchema | ValidationRule): data is T;
  sanitize<T>(data: unknown, schema: ValidationSchema): T;
  transform<T>(data: unknown, transformer: ObjectTransformer): T;
  
  // Schema builder
  schema(): ISchemaBuilder;
  
  // Common schemas
  getCommonSchemas(): CommonSchemas;
}

/**
 * Validation middleware types
 */
export interface ValidationMiddleware {
  body?: ValidationSchema;
  query?: ValidationSchema;
  params?: ValidationSchema;
  headers?: ValidationSchema;
  options?: ValidationOptions;
}

/**
 * Validation decorator types
 */
export interface ValidationDecorator {
  target: any;
  propertyKey: string | symbol;
  parameterIndex?: number;
  schema: ValidationSchema | ValidationRule;
  options?: ValidationOptions;
}

/**
 * Error collection and reporting
 */
export interface ValidationErrorCollection {
  errors: ValidationError[];
  warnings: ValidationWarning[];
  hasErrors(): boolean;
  hasWarnings(): boolean;
  getErrorsForPath(path: string): ValidationError[];
  getWarningsForPath(path: string): ValidationWarning[];
  toString(): string;
  toJSON(): object;
}

/**
 * Async validation support
 */
export interface AsyncValidationRule extends Omit<ValidationRule, 'validator'> {
  asyncValidator?: AsyncCustomValidator;
}

export type AsyncCustomValidator = (
  value: any,
  context?: ValidationContext
) => Promise<ValidationError | ValidationError[] | null>;

/**
 * Conditional validation
 */
export interface ConditionalValidation {
  when: (value: any, context?: ValidationContext) => boolean;
  then: ValidationRule;
  otherwise?: ValidationRule;
}

/**
 * Cross-field validation
 */
export interface CrossFieldValidation {
  fields: string[];
  validator: (values: Record<string, any>, context?: ValidationContext) => ValidationError | null;
}

/**
 * Validation performance metrics
 */
export interface ValidationMetrics {
  totalValidations: number;
  successfulValidations: number;
  failedValidations: number;
  averageValidationTime: number;
  slowValidations: Array<{
    schema: string;
    duration: number;
    timestamp: Date;
  }>;
}

/**
 * Export utility functions
 */
export type ValidatedType<T extends ValidationSchema> = {
  [K in keyof T['properties']]: T['properties'][K] extends { type: 'string' }
    ? string
    : T['properties'][K] extends { type: 'number' }
    ? number
    : T['properties'][K] extends { type: 'boolean' }
    ? boolean
    : T['properties'][K] extends { type: 'date' }
    ? Date
    : T['properties'][K] extends { type: 'array' }
    ? any[]
    : T['properties'][K] extends { type: 'object' }
    ? object
    : any;
};

export type RequiredFields<T extends ValidationSchema> = T['required'] extends readonly (keyof T['properties'])[]
  ? T['required'][number]
  : never;

export type OptionalFields<T extends ValidationSchema> = Exclude<keyof T['properties'], RequiredFields<T>>;

export type ValidatedObject<T extends ValidationSchema> = Required<Pick<ValidatedType<T>, RequiredFields<T>>> &
  Partial<Pick<ValidatedType<T>, OptionalFields<T>>>;