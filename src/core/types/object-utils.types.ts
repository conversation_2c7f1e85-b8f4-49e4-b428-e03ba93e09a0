/**
 * TypeScript utility types for safe object operations
 * These types replace the need for delete operations
 */

/**
 * Safely omit properties from an object type
 */
export type SafeOmit<T, K extends keyof T> = Omit<T, K>;

/**
 * Create a type with optional properties marked as optional
 */
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

/**
 * Utility functions for safe object manipulation without delete operations
 */
export class ObjectUtils {
  /**
   * Safely remove properties from an object without using delete operator
   * @param obj - Source object
   * @param keys - Keys to remove
   * @returns New object without the specified keys
   */
  static omit<T extends Record<string, any>, K extends keyof T>(
    obj: T,
    keys: K[]
  ): Omit<T, K> {
    const result = { ...obj };
    const keysSet = new Set(keys);
    
    return Object.keys(result).reduce((acc, key) => {
      if (!keysSet.has(key as K)) {
        acc[key as Exclude<keyof T, K>] = result[key];
      }
      return acc;
    }, {} as Omit<T, K>);
  }

  /**
   * Safely pick properties from an object
   * @param obj - Source object
   * @param keys - Keys to pick
   * @returns New object with only the specified keys
   */
  static pick<T extends Record<string, any>, K extends keyof T>(
    obj: T,
    keys: K[]
  ): Pick<T, K> {
    const keysSet = new Set(keys);
    
    return Object.keys(obj).reduce((acc, key) => {
      if (keysSet.has(key as K)) {
        acc[key as K] = obj[key];
      }
      return acc;
    }, {} as Pick<T, K>);
  }

  /**
   * Remove sensitive fields from an object for logging/responses
   * @param obj - Source object
   * @param sensitiveFields - Array of field names to remove
   * @returns Sanitized object
   */
  static sanitize<T extends Record<string, any>>(
    obj: T,
    sensitiveFields: string[]
  ): Partial<T> {
    if (!obj || typeof obj !== 'object') {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.sanitize(item, sensitiveFields)) as any;
    }

    const sensitiveSet = new Set(sensitiveFields);
    const result = {} as Partial<T>;

    Object.keys(obj).forEach(key => {
      if (!sensitiveSet.has(key)) {
        const value = obj[key];
        // Recursively sanitize nested objects
        result[key as keyof T] = typeof value === 'object' && value !== null 
          ? this.sanitize(value, sensitiveFields)
          : value;
      }
    });

    return result;
  }

  /**
   * Rename object properties safely
   * @param obj - Source object
   * @param mapping - Object mapping old keys to new keys
   * @returns New object with renamed properties
   */
  static renameProperties<T extends Record<string, any>>(
    obj: T,
    mapping: Record<string, string>
  ): any {
    if (!obj || typeof obj !== 'object') {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.renameProperties(item, mapping));
    }

    const result = { ...obj };
    
    Object.entries(mapping).forEach(([oldKey, newKey]) => {
      if (oldKey in result) {
        result[newKey] = result[oldKey];
        // Use destructuring to safely remove the old property
        const { [oldKey]: removed, ...rest } = result;
        Object.assign(result, rest);
      }
    });

    return result;
  }
}

/**
 * Type-safe property removal using destructuring
 * Example usage: const {password, ...safe} = removeProperty(user, 'password');
 */
export function removeProperty<T extends Record<string, any>, K extends keyof T>(
  obj: T,
  key: K
): { removed: T[K]; remaining: Omit<T, K> } {
  const { [key]: removed, ...remaining } = obj;
  return { removed, remaining: remaining as Omit<T, K> };
}

/**
 * Remove multiple properties safely
 */
export function removeProperties<T extends Record<string, any>, K extends keyof T>(
  obj: T,
  keys: K[]
): Omit<T, K> {
  return ObjectUtils.omit(obj, keys);
}