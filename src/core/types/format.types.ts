/**
 * Formatting utility types for the EnergeX Discord bot
 * Provides type-safe formatting functions and configurations
 */

/**
 * Supported formatting types
 */
export type FormatType = 
  | 'currency'
  | 'number'
  | 'percentage'
  | 'date'
  | 'time'
  | 'datetime'
  | 'duration'
  | 'bytes'
  | 'distance'
  | 'weight'
  | 'temperature'
  | 'ordinal'
  | 'scientific'
  | 'compact';

/**
 * Locale and region settings
 */
export type SupportedLocale = 
  | 'en-US'
  | 'en-GB'
  | 'en-CA'
  | 'en-AU'
  | 'fr-FR'
  | 'de-DE'
  | 'es-ES'
  | 'it-IT'
  | 'ja-JP'
  | 'ko-KR'
  | 'zh-CN'
  | 'pt-BR'
  | 'ru-RU'
  | 'ar-SA'
  | 'hi-IN';

export type SupportedCurrency = 
  | 'USD'
  | 'EUR'
  | 'GBP'
  | 'JPY'
  | 'CAD'
  | 'AUD'
  | 'CHF'
  | 'CNY'
  | 'SEK'
  | 'NZD'
  | 'MXN'
  | 'SGD'
  | 'HKD'
  | 'NOK'
  | 'TRY'
  | 'ZAR'
  | 'BRL'
  | 'INR'
  | 'KRW'
  | 'RUB';

export type SupportedTimezone = 
  | 'UTC'
  | 'America/New_York'
  | 'America/Chicago'
  | 'America/Denver'
  | 'America/Los_Angeles'
  | 'America/Toronto'
  | 'America/Vancouver'
  | 'Europe/London'
  | 'Europe/Paris'
  | 'Europe/Berlin'
  | 'Europe/Madrid'
  | 'Europe/Rome'
  | 'Europe/Amsterdam'
  | 'Europe/Stockholm'
  | 'Asia/Tokyo'
  | 'Asia/Seoul'
  | 'Asia/Shanghai'
  | 'Asia/Kolkata'
  | 'Asia/Dubai'
  | 'Australia/Sydney'
  | 'Australia/Melbourne'
  | 'Pacific/Auckland';

/**
 * Base formatting options
 */
export interface BaseFormatOptions {
  locale?: SupportedLocale;
  fallbackLocale?: SupportedLocale;
  throwOnError?: boolean;
  cache?: boolean;
}

/**
 * Number formatting options
 */
export interface NumberFormatOptions extends BaseFormatOptions {
  minimumIntegerDigits?: number;
  minimumFractionDigits?: number;
  maximumFractionDigits?: number;
  minimumSignificantDigits?: number;
  maximumSignificantDigits?: number;
  useGrouping?: boolean;
  notation?: 'standard' | 'scientific' | 'engineering' | 'compact';
  compactDisplay?: 'short' | 'long';
  signDisplay?: 'auto' | 'never' | 'always' | 'exceptZero';
  roundingMode?: 'ceil' | 'floor' | 'expand' | 'trunc' | 'halfCeil' | 'halfFloor' | 'halfExpand' | 'halfTrunc' | 'halfEven';
  roundingPriority?: 'auto' | 'morePrecision' | 'lessPrecision';
  roundingIncrement?: number;
  trailingZeroDisplay?: 'auto' | 'stripIfInteger';
}

/**
 * Currency formatting options
 */
export interface CurrencyFormatOptions extends NumberFormatOptions {
  currency: SupportedCurrency;
  currencyDisplay?: 'symbol' | 'narrowSymbol' | 'code' | 'name';
  currencySign?: 'standard' | 'accounting';
}

/**
 * Percentage formatting options
 */
export interface PercentageFormatOptions extends NumberFormatOptions {
  style?: 'percent' | 'ratio';
  multiplier?: number;
}

/**
 * Date/Time formatting options
 */
export interface DateTimeFormatOptions extends BaseFormatOptions {
  timezone?: SupportedTimezone;
  era?: 'narrow' | 'short' | 'long';
  year?: 'numeric' | '2-digit';
  month?: 'numeric' | '2-digit' | 'narrow' | 'short' | 'long';
  day?: 'numeric' | '2-digit';
  weekday?: 'narrow' | 'short' | 'long';
  hour?: 'numeric' | '2-digit';
  minute?: 'numeric' | '2-digit';
  second?: 'numeric' | '2-digit';
  fractionalSecondDigits?: 0 | 1 | 2 | 3;
  timeZoneName?: 'short' | 'long' | 'shortOffset' | 'longOffset' | 'shortGeneric' | 'longGeneric';
  formatMatcher?: 'basic' | 'best fit';
  hour12?: boolean;
  dayPeriod?: 'narrow' | 'short' | 'long';
  calendar?: 'buddhist' | 'chinese' | 'coptic' | 'ethiopia' | 'ethiopic' | 'gregory' | 'hebrew' | 'indian' | 'islamic' | 'islamic-umalqura' | 'islamic-tbla' | 'islamic-civil' | 'islamic-rgsa' | 'iso8601' | 'japanese' | 'persian' | 'roc';
  numberingSystem?: 'arab' | 'arabext' | 'bali' | 'beng' | 'deva' | 'fullwide' | 'gujr' | 'guru' | 'hanidec' | 'khmr' | 'knda' | 'laoo' | 'latn' | 'limb' | 'mlym' | 'mong' | 'mymr' | 'orya' | 'tamldec' | 'telu' | 'thai' | 'tibt';
}

/**
 * Duration formatting options
 */
export interface DurationFormatOptions extends BaseFormatOptions {
  style?: 'long' | 'short' | 'narrow' | 'digital';
  units?: DurationUnit[];
  precision?: number;
  largestUnit?: DurationUnit;
  smallestUnit?: DurationUnit;
  fractionalDigits?: number;
  separator?: string;
  conjunction?: string;
  space?: boolean;
}

export type DurationUnit = 
  | 'years'
  | 'months' 
  | 'weeks'
  | 'days'
  | 'hours'
  | 'minutes'
  | 'seconds'
  | 'milliseconds'
  | 'microseconds'
  | 'nanoseconds';

/**
 * Byte formatting options
 */
export interface ByteFormatOptions extends BaseFormatOptions {
  standard?: 'binary' | 'decimal';
  unit?: ByteUnit;
  precision?: number;
  separator?: string;
  spacer?: string;
  unitDisplay?: 'short' | 'long' | 'narrow';
}

export type ByteUnit = 
  | 'bit' | 'byte'
  | 'kilobit' | 'kilobyte' 
  | 'megabit' | 'megabyte'
  | 'gigabit' | 'gigabyte'
  | 'terabit' | 'terabyte'
  | 'petabit' | 'petabyte';

/**
 * Distance formatting options
 */
export interface DistanceFormatOptions extends BaseFormatOptions {
  unit?: DistanceUnit;
  precision?: number;
  unitDisplay?: 'short' | 'long' | 'narrow';
}

export type DistanceUnit = 
  | 'millimeter' | 'centimeter' | 'meter' | 'kilometer'
  | 'inch' | 'foot' | 'yard' | 'mile'
  | 'nautical-mile';

/**
 * Weight formatting options  
 */
export interface WeightFormatOptions extends BaseFormatOptions {
  unit?: WeightUnit;
  precision?: number;
  unitDisplay?: 'short' | 'long' | 'narrow';
}

export type WeightUnit = 
  | 'gram' | 'kilogram' | 'ton' | 'stone' | 'pound' | 'ounce';

/**
 * Temperature formatting options
 */
export interface TemperatureFormatOptions extends BaseFormatOptions {
  unit?: TemperatureUnit;
  precision?: number;
  unitDisplay?: 'short' | 'long' | 'narrow';
}

export type TemperatureUnit = 'celsius' | 'fahrenheit' | 'kelvin';

/**
 * Format result
 */
export interface FormatResult {
  formatted: string;
  original: any;
  options: any;
  locale: SupportedLocale;
  success: boolean;
  error?: FormatError;
  metadata?: FormatMetadata;
}

export interface FormatError {
  code: string;
  message: string;
  input: any;
  options: any;
}

export interface FormatMetadata {
  processingTime?: number;
  cacheHit?: boolean;
  fallbackUsed?: boolean;
  transformations?: string[];
}

/**
 * Format patterns
 */
export interface FormatPattern {
  name: string;
  pattern: string;
  example: string;
  description: string;
  locale?: SupportedLocale;
  type: FormatType;
}

/**
 * Custom formatter interface
 */
export interface ICustomFormatter<TInput = any, TOptions = any> {
  name: string;
  format(value: TInput, options?: TOptions): string;
  validate?(value: TInput, options?: TOptions): boolean;
  parse?(formatted: string, options?: TOptions): TInput;
  getDefaultOptions?(): TOptions;
}

/**
 * Format service interface
 */
export interface IFormatService {
  // Core formatting methods
  format<T extends FormatType>(
    value: any, 
    type: T, 
    options?: FormatOptionsForType<T>
  ): string;
  
  formatSafe<T extends FormatType>(
    value: any, 
    type: T, 
    options?: FormatOptionsForType<T>
  ): FormatResult;

  // Specific formatters
  formatNumber(value: number, options?: NumberFormatOptions): string;
  formatCurrency(value: number, options?: CurrencyFormatOptions): string;
  formatPercentage(value: number, options?: PercentageFormatOptions): string;
  formatDate(value: Date | string | number, options?: DateTimeFormatOptions): string;
  formatTime(value: Date | string | number, options?: DateTimeFormatOptions): string;
  formatDateTime(value: Date | string | number, options?: DateTimeFormatOptions): string;
  formatDuration(value: number, unit?: DurationUnit, options?: DurationFormatOptions): string;
  formatBytes(value: number, options?: ByteFormatOptions): string;
  formatDistance(value: number, options?: DistanceFormatOptions): string;
  formatWeight(value: number, options?: WeightFormatOptions): string;
  formatTemperature(value: number, options?: TemperatureFormatOptions): string;

  // Relative formatting
  formatRelativeTime(value: Date | number, options?: RelativeTimeFormatOptions): string;
  formatTimeAgo(value: Date | number, options?: RelativeTimeFormatOptions): string;
  
  // Utility methods
  parseNumber(formatted: string, locale?: SupportedLocale): number;
  parseDate(formatted: string, format?: string, locale?: SupportedLocale): Date;
  parseCurrency(formatted: string, currency?: SupportedCurrency, locale?: SupportedLocale): number;
  
  // Pattern management
  registerPattern(pattern: FormatPattern): void;
  getPattern(name: string): FormatPattern | null;
  getPatterns(type?: FormatType): FormatPattern[];
  
  // Custom formatter management
  registerFormatter<TInput, TOptions>(formatter: ICustomFormatter<TInput, TOptions>): void;
  getFormatter(name: string): ICustomFormatter | null;
  removeFormatter(name: string): boolean;
  
  // Configuration
  setDefaultLocale(locale: SupportedLocale): void;
  getDefaultLocale(): SupportedLocale;
  setDefaultCurrency(currency: SupportedCurrency): void;
  getDefaultCurrency(): SupportedCurrency;
  setDefaultTimezone(timezone: SupportedTimezone): void;
  getDefaultTimezone(): SupportedTimezone;
  
  // Validation
  isValidLocale(locale: string): locale is SupportedLocale;
  isValidCurrency(currency: string): currency is SupportedCurrency;
  isValidTimezone(timezone: string): timezone is SupportedTimezone;
  
  // Cache management
  clearCache(): void;
  getCacheStats(): CacheStats;
}

/**
 * Relative time formatting options
 */
export interface RelativeTimeFormatOptions extends BaseFormatOptions {
  style?: 'long' | 'short' | 'narrow';
  numeric?: 'always' | 'auto';
  unit?: RelativeTimeUnit;
}

export type RelativeTimeUnit = 
  | 'second' | 'minute' | 'hour' | 'day' | 'week' | 'month' | 'quarter' | 'year';

/**
 * Cache statistics
 */
export interface CacheStats {
  size: number;
  hits: number;
  misses: number;
  hitRate: number;
  evictions: number;
}

/**
 * Type mapping for format options
 */
export type FormatOptionsForType<T extends FormatType> = 
  T extends 'currency' ? CurrencyFormatOptions :
  T extends 'number' ? NumberFormatOptions :
  T extends 'percentage' ? PercentageFormatOptions :
  T extends 'date' ? DateTimeFormatOptions :
  T extends 'time' ? DateTimeFormatOptions :
  T extends 'datetime' ? DateTimeFormatOptions :
  T extends 'duration' ? DurationFormatOptions :
  T extends 'bytes' ? ByteFormatOptions :
  T extends 'distance' ? DistanceFormatOptions :
  T extends 'weight' ? WeightFormatOptions :
  T extends 'temperature' ? TemperatureFormatOptions :
  BaseFormatOptions;

/**
 * Format presets
 */
export interface FormatPresets {
  // Number presets
  integer: NumberFormatOptions;
  decimal: NumberFormatOptions;
  compact: NumberFormatOptions;
  scientific: NumberFormatOptions;
  
  // Currency presets
  usd: CurrencyFormatOptions;
  eur: CurrencyFormatOptions;
  gbp: CurrencyFormatOptions;
  accounting: CurrencyFormatOptions;
  
  // Date presets
  shortDate: DateTimeFormatOptions;
  mediumDate: DateTimeFormatOptions;
  longDate: DateTimeFormatOptions;
  fullDate: DateTimeFormatOptions;
  
  // Time presets
  shortTime: DateTimeFormatOptions;
  mediumTime: DateTimeFormatOptions;
  longTime: DateTimeFormatOptions;
  
  // Duration presets
  shortDuration: DurationFormatOptions;
  longDuration: DurationFormatOptions;
  preciseTime: DurationFormatOptions;
  
  // Byte presets
  binaryBytes: ByteFormatOptions;
  decimalBytes: ByteFormatOptions;
  storage: ByteFormatOptions;
  bandwidth: ByteFormatOptions;
}

/**
 * Format context for conditional formatting
 */
export interface FormatContext {
  user?: {
    id: string;
    locale?: SupportedLocale;
    timezone?: SupportedTimezone;
    currency?: SupportedCurrency;
    preferences?: UserFormatPreferences;
  };
  guild?: {
    id: string;
    locale?: SupportedLocale;
    timezone?: SupportedTimezone;
    currency?: SupportedCurrency;
  };
  channel?: {
    id: string;
    type: string;
  };
  platform?: 'discord' | 'web' | 'mobile' | 'api';
  theme?: 'light' | 'dark';
}

export interface UserFormatPreferences {
  dateFormat?: string;
  timeFormat?: '12h' | '24h';
  numberFormat?: 'standard' | 'compact';
  currencySymbol?: boolean;
  relativeTime?: boolean;
  precision?: number;
}

/**
 * Smart formatting interface
 */
export interface ISmartFormatter {
  formatInContext<T extends FormatType>(
    value: any,
    type: T,
    context: FormatContext,
    options?: FormatOptionsForType<T>
  ): string;
  
  getContextualOptions<T extends FormatType>(
    type: T,
    context: FormatContext,
    baseOptions?: FormatOptionsForType<T>
  ): FormatOptionsForType<T>;
  
  detectFormatType(value: any): FormatType | null;
  suggestFormat(value: any, context?: FormatContext): FormatResult;
}

/**
 * Formatting middleware types
 */
export interface FormatMiddleware {
  before?: (value: any, type: FormatType, options: any) => { value: any; options: any };
  after?: (result: string, original: any, type: FormatType, options: any) => string;
  error?: (error: FormatError, value: any, type: FormatType, options: any) => string | null;
}

/**
 * Export all types
 */
export * from './utility.types';
export * from './validation.types';