import { Injectable, Logger, OnApplicationShutdown } from '@nestjs/common';
import * as fs from 'fs/promises';
import * as path from 'path';

export type UserData = {
  id: string;
  discordId: string;
  username: string;
  discriminator?: string;
  avatar?: string;
  createdAt: Date;
  lastActivity: Date;
  [key: string]: any;
};

export type GuildData = {
  id: string;
  discordId: string;
  name: string;
  icon?: string;
  ownerId: string;
  settings: Record<string, any>;
  createdAt: Date;
  lastActivity: Date;
  [key: string]: any;
};

export type SessionData = {
  id: string;
  userId: string;
  token: string;
  expiresAt: Date;
  createdAt: Date;
  [key: string]: any;
};

/**
 * Simple in-memory data service to replace Redis
 * Stores data in memory with optional JSON file persistence
 */
@Injectable()
export class SimpleDataService implements OnApplicationShutdown {
  private readonly logger = new Logger(SimpleDataService.name);
  
  // In-memory storage
  private users = new Map<string, UserData>();
  private guilds = new Map<string, GuildData>();
  private sessions = new Map<string, SessionData>();
  private cache = new Map<string, any>();
  
  private readonly dataFile = path.join(process.cwd(), 'data', 'bot-state.json');

  constructor() {
    this.loadState();
  }

  async onApplicationShutdown(): Promise<void> {
    await this.saveState();
  }

  // User operations
  getUser(discordId: string): UserData | undefined {
    return Array.from(this.users.values()).find(user => user.discordId === discordId);
  }

  getUserById(id: string): UserData | undefined {
    return this.users.get(id);
  }

  setUser(user: UserData): void {
    this.users.set(user.id, { ...user, lastActivity: new Date() });
  }

  deleteUser(id: string): boolean {
    return this.users.delete(id);
  }

  getAllUsers(): UserData[] {
    return Array.from(this.users.values());
  }

  // Guild operations
  getGuild(discordId: string): GuildData | undefined {
    return Array.from(this.guilds.values()).find(guild => guild.discordId === discordId);
  }

  getGuildById(id: string): GuildData | undefined {
    return this.guilds.get(id);
  }

  setGuild(guild: GuildData): void {
    this.guilds.set(guild.id, { ...guild, lastActivity: new Date() });
  }

  deleteGuild(id: string): boolean {
    return this.guilds.delete(id);
  }

  getAllGuilds(): GuildData[] {
    return Array.from(this.guilds.values());
  }

  // Session operations
  getSession(token: string): SessionData | undefined {
    return Array.from(this.sessions.values()).find(session =>
      session.token === token && session.expiresAt > new Date()
    );
  }

  getSessionById(id: string): SessionData | undefined {
    const session = this.sessions.get(id);
    return session && session.expiresAt > new Date() ? session : undefined;
  }

  setSession(session: SessionData): void {
    this.sessions.set(session.id, session);
    this.cleanupExpiredSessions();
  }

  deleteSession(id: string): boolean {
    return this.sessions.delete(id);
  }

  private cleanupExpiredSessions(): void {
    const now = new Date();
    for (const [id, session] of Array.from(this.sessions.entries())) {
      if (session.expiresAt <= now) {
        this.sessions.delete(id);
      }
    }
  }

  // Cache operations
  get(key: string): any {
    return this.cache.get(key);
  }

  set(key: string, value: any, ttlMs?: number): void {
    this.cache.set(key, value);
    
    if (ttlMs) {
      setTimeout(() => {
        this.cache.delete(key);
      }, ttlMs);
    }
  }

  del(key: string): boolean {
    return this.cache.delete(key);
  }

  exists(key: string): boolean {
    return this.cache.has(key);
  }

  keys(pattern?: string): string[] {
    const allKeys = Array.from(this.cache.keys());
    if (!pattern) return allKeys;
    
    const regex = new RegExp(pattern.replace(/\*/g, '.*'));
    return allKeys.filter(key => regex.test(key));
  }

  // Statistics
  getStats() {
    return {
      users: this.users.size,
      guilds: this.guilds.size,
      sessions: this.sessions.size,
      cache: this.cache.size,
      uptime: process.uptime()
    };
  }

  // Persistence (optional)
  private async loadState(): Promise<void> {
    try {
      const data = await fs.readFile(this.dataFile, 'utf8');
      const state = JSON.parse(data);
      
      if (state.users) {
        this.users = new Map(state.users.map((user: any) => [user?.id, {
          ...user,
          createdAt: new Date(user.createdAt),
          lastActivity: new Date(user.lastActivity)
        }]));
      }
      
      if (state.guilds) {
        this.guilds = new Map(state.guilds.map((guild: any) => [guild?.id, {
          ...guild,
          createdAt: new Date(guild.createdAt),
          lastActivity: new Date(guild.lastActivity)
        }]));
      }
      
      this.logger.log(`Loaded state: ${this.users.size} users, ${this.guilds.size} guilds`);
    } catch (error) {
      this.logger.log('No existing state file found, starting with empty state');
    }
  }

  private async saveState(): Promise<void> {
    try {
      // Ensure data directory exists
      await fs.mkdir(path.dirname(this.dataFile), { recursive: true });
      
      const state = {
        users: Array.from(this.users.values()),
        guilds: Array.from(this.guilds.values()),
        savedAt: new Date().toISOString()
      };
      
      await fs.writeFile(this.dataFile, JSON.stringify(state, null, 2));
      this.logger.log('State saved successfully');
    } catch (error) {
      this.logger.error('Failed to save state:', error);
    }
  }

  // Health check
  async healthCheck(): Promise<{ status: string; details: any }> {
    const stats = this.getStats();
    return {
      status: 'healthy',
      details: {
        ...stats,
        memory: {
          heapUsed: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
          heapTotal: Math.round(process.memoryUsage().heapTotal / 1024 / 1024)
        }
      }
    };
  }
}