import { MembershipTier } from './whop.interface';
import { ChannelConfig, ChannelPermission } from './discord.interface';

export type ChannelTemplate = {
  id: string,
      name: string,description: string,
    requiredTier: MembershipTier;
  category?: string;
  permissions: ChannelPermission[],
    autoCreate: boolean}

export type IChannelService = {
  updateUserAccess(userId: string, guildId: string, tier: MembershipTier): Promise<void>;
  createTierChannels(guildId: string, tier: MembershipTier): Promise<string[]>;
  removeTierAccess(userId: string, guildId: string, oldTier: MembershipTier): Promise<void>;
  getChannelTemplates(guildId: string, tier: MembershipTier): Promise<ChannelTemplate[]>;
  createPrivateChannel(guildId: string, name: string, members: string[]): Promise<string>
  archiveChannel(channelId: string): Promise<void>;
  getAccessibleChannels(userId: string, guildId: string): Promise<string[]>}