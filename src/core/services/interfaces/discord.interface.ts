// Discord Channel Types
export type DiscordChannelType = 
  | 'GUILD_TEXT'
  | 'DM'
  | 'GUILD_VOICE'
  | 'GROUP_DM'
  | 'GUILD_CATEGORY'
  | 'GUILD_ANNOUNCEMENT'
  | 'GUILD_STORE'
  | 'ANNOUNCEMENT_THREAD'
  | 'PUBLIC_THREAD'
  | 'PRIVATE_THREAD'
  | 'GUILD_STAGE_VOICE'
  | 'GUILD_FORUM';

// Discord Permission Flags
export type DiscordPermissionFlag =
  | 'CREATE_INSTANT_INVITE'
  | 'KICK_MEMBERS'
  | 'BAN_MEMBERS'
  | 'ADMINISTRATOR'
  | 'MANAGE_CHANNELS'
  | 'MANAGE_GUILD'
  | 'ADD_REACTIONS'
  | 'VIEW_AUDIT_LOG'
  | 'PRIORITY_SPEAKER'
  | 'STREAM'
  | 'VIEW_CHANNEL'
  | 'SEND_MESSAGES'
  | 'SEND_TTS_MESSAGES'
  | 'MANAGE_MESSAGES'
  | 'EMBED_LINKS'
  | 'ATTACH_FILES'
  | 'READ_MESSAGE_HISTORY'
  | 'MENTION_EVERYONE'
  | 'USE_EXTERNAL_EMOJIS'
  | 'VIEW_GUILD_INSIGHTS'
  | 'CONNECT'
  | 'SPEAK'
  | 'MUTE_MEMBERS'
  | 'DEAFEN_MEMBERS'
  | 'MOVE_MEMBERS'
  | 'USE_VAD'
  | 'CHANGE_NICKNAME'
  | 'MANAGE_NICKNAMES'
  | 'MANAGE_ROLES'
  | 'MANAGE_WEBHOOKS'
  | 'MANAGE_EMOJIS_AND_STICKERS'
  | 'USE_APPLICATION_COMMANDS'
  | 'REQUEST_TO_SPEAK'
  | 'MANAGE_EVENTS'
  | 'MANAGE_THREADS'
  | 'CREATE_PUBLIC_THREADS'
  | 'CREATE_PRIVATE_THREADS'
  | 'USE_EXTERNAL_STICKERS'
  | 'SEND_MESSAGES_IN_THREADS'
  | 'USE_EMBEDDED_ACTIVITIES'
  | 'MODERATE_MEMBERS';

// Permission Overwrite Types
export type PermissionOverwriteType = 'role' | 'member';

export interface ChannelPermissionOverwrite {
  id: string;
  type: PermissionOverwriteType;
  allow: DiscordPermissionFlag[];
  deny: DiscordPermissionFlag[];
}

// Channel Configuration
export interface ChannelConfig {
  name: string;
  type: DiscordChannelType;
  parent?: string;
  permissions?: ChannelPermissionOverwrite[];
  topic?: string;
  nsfw?: boolean;
  bitrate?: number;
  userLimit?: number;
  rateLimitPerUser?: number;
  position?: number;
  defaultAutoArchiveDuration?: number;
}

// Legacy support - will be deprecated
export type ChannelPermission = ChannelPermissionOverwrite;

// Message Configuration
export interface MessageConfig {
  content?: string;
  embeds?: MessageEmbed[];
  components?: MessageComponent[];
  files?: MessageAttachment[];
  allowedMentions?: AllowedMentions;
  tts?: boolean;
  ephemeral?: boolean;
}

// Message Components
export interface MessageEmbed {
  title?: string;
  description?: string;
  url?: string;
  timestamp?: string | Date;
  color?: number;
  footer?: {
    text: string;
    iconURL?: string;
  };
  image?: {
    url: string;
  };
  thumbnail?: {
    url: string;
  };
  author?: {
    name: string;
    url?: string;
    iconURL?: string;
  };
  fields?: {
    name: string;
    value: string;
    inline?: boolean;
  }[];
}

export interface MessageComponent {
  type: number;
  components?: MessageComponent[];
  customId?: string;
  disabled?: boolean;
  style?: number;
  label?: string;
  emoji?: {
    id?: string;
    name?: string;
    animated?: boolean;
  };
  url?: string;
  options?: {
    label: string;
    value: string;
    description?: string;
    emoji?: {
      id?: string;
      name?: string;
      animated?: boolean;
    };
    default?: boolean;
  }[];
  placeholder?: string;
  minValues?: number;
  maxValues?: number;
}

export interface MessageAttachment {
  name: string;
  description?: string;
  contentType?: string;
  attachment: Buffer | string;
}

export interface AllowedMentions {
  parse?: ('roles' | 'users' | 'everyone')[];
  roles?: string[];
  users?: string[];
  repliedUser?: boolean;
}

// Discord Service Interface
export interface IDiscordService {
  // Role Management
  assignRole(userId: string, roleId: string): Promise<void>;
  removeRole(userId: string, roleId: string): Promise<void>;
  getUserRoles(guildId: string, userId: string): Promise<string[]>;
  
  // Channel Management
  createChannel(guildId: string, config: ChannelConfig): Promise<string>;
  updateChannel(channelId: string, config: Partial<ChannelConfig>): Promise<void>;
  deleteChannel(channelId: string): Promise<void>;
  updateChannelPermissions(channelId: string, permissions: ChannelPermissionOverwrite[]): Promise<void>;
  getChannelPermissions(channelId: string, targetId: string): Promise<DiscordPermissionFlag[]>;
  
  // Message Management
  sendMessage(channelId: string, config: MessageConfig): Promise<string>;
  sendDirectMessage(userId: string, config: MessageConfig): Promise<void>;
  editMessage(channelId: string, messageId: string, config: MessageConfig): Promise<void>;
  deleteMessage(channelId: string, messageId: string): Promise<void>;
  
  // Thread Management
  createPrivateThread(channelId: string, name: string, members: string[]): Promise<string>;
  createPublicThread(channelId: string, name: string, messageId?: string): Promise<string>;
  addThreadMember(threadId: string, userId: string): Promise<void>;
  removeThreadMember(threadId: string, userId: string): Promise<void>;
  
  // Guild Management
  getGuildMember(guildId: string, userId: string): Promise<GuildMember | null>;
  getGuildChannel(guildId: string, channelId: string): Promise<GuildChannel | null>;
  getGuildChannels(guildId: string): Promise<GuildChannel[]>;
  getGuildRoles(guildId: string): Promise<Role[]>;
}

// Guild Member Information
export interface GuildMember {
  id: string;
  username: string;
  displayName: string;
  avatar?: string;
  roles: string[];
  joinedAt: Date;
  premiumSince?: Date;
  nickname?: string;
  pending?: boolean;
  permissions: DiscordPermissionFlag[];
}

// Guild Channel Information
export interface GuildChannel {
  id: string;
  name: string;
  type: DiscordChannelType;
  position: number;
  parentId?: string;
  topic?: string;
  nsfw: boolean;
  permissions: ChannelPermissionOverwrite[];
  createdAt: Date;
  lastMessageId?: string;
}

// Role Information
export interface Role {
  id: string;
  name: string;
  color: number;
  hoist: boolean;
  position: number;
  permissions: DiscordPermissionFlag[];
  managed: boolean;
  mentionable: boolean;
  createdAt: Date;
}