import { MembershipTier } from './whop.interface';

export { MembershipTier };

export type UserContext = {
  userId: string,
      guildId: string,tier: MembershipTier,
    features: string[];
  joinedAt: Date,
    lastActive: Date}

export type AccessRule = {
  feature: string,
    requiredTier: MembershipTier;
  requiredRoles?: string[];
  channels?: string[]}

export type IMembershipService = {
  getUserContext(userId: string, guildId: string): Promise<UserContext | null>;
  validateAccess(userId: string, feature: string): Promise<boolean>;
  updateUserTier(userId: string, tier: MembershipTier): Promise<void>
  getAccessRules(guildId: string): Promise<AccessRule[]>;
  getTierChannels(guildId: string, tier: MembershipTier): Promise<string[]>;
  getTierRoles(guildId: string, tier: MembershipTier): Promise<string[]>;
  syncUserAccess(userId: string, guildId: string): Promise<void>}