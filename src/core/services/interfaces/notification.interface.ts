import { MembershipTier } from './whop.interface';
import { MessageConfig } from './discord.interface';

export type NotificationTemplate = {
  id: string,
      name: string;
  type: 'welcome' | 'tier_upgrade' | 'tier_downgrade' | 'feature_unlock' | 'project_match';
  template: MessageConfig,triggers: string[];
  targetTiers?: MembershipTier[]}

export type NotificationContext = {
  userId: string;
  guildId: string;
  tier: MembershipTier;
  previousTier?: MembershipTier;
  additionalData?: Record<string, any>}

export type INotificationService = {
  sendWelcomeMessage(userId: string, guildId: string, tier: MembershipTier): Promise<void>
  sendTierChangeNotification(context: NotificationContext): Promise<void>;
  sendFeatureUnlockNotification(userId: string, features: string[]): Promise<void>;
  sendProjectMatchNotification(clientId: string, developerId: string, projectId: string): Promise<void>;
  sendCustomNotification(channelId: string, template: MessageConfig): Promise<void>;
  getNotificationTemplates(guildId: string, type: string): Promise<NotificationTemplate[]>}