import { MembershipTier } from './whop.interface';
import { 
  ChannelConfig, 
  ChannelPermissionOverwrite, 
  DiscordChannelType,
  DiscordPermissionFlag,
  GuildChannel,
  Role
} from './discord.interface';

// Channel Lifecycle States
export type ChannelLifecycleState = 
  | 'active'
  | 'archived'
  | 'disabled'
  | 'maintenance'
  | 'scheduled_deletion';

// Channel Categories and Organization
export type ChannelCategoryType = 
  | 'general'
  | 'premium'
  | 'enterprise'
  | 'ai-mastery'
  | 'development'
  | 'trading'
  | 'community'
  | 'support'
  | 'announcements'
  | 'custom';

// Channel Template Configuration
export interface ChannelTemplate {
  id: string;
  name: string;
  description: string;
  type: DiscordChannelType;
  category?: ChannelCategoryType;
  parentCategory?: string;
  requiredTier: MembershipTier;
  permissions: ChannelPermissionOverwrite[];
  autoCreate: boolean;
  position?: number;
  topic?: string;
  rateLimitPerUser?: number;
  nsfw?: boolean;
  defaultAutoArchiveDuration?: number;
  // Automation settings
  autoModeration?: boolean;
  allowedMentions?: string[];
  restrictedWords?: string[];
  maxAttachmentSize?: number;
}

// Channel Management Configuration
export interface ChannelManagementConfig {
  guildId: string;
  channelId?: string;
  template?: ChannelTemplate;
  lifecycle: ChannelLifecycleState;
  metadata: {
    createdBy: string;
    createdAt: Date;
    lastModified: Date;
    modifiedBy: string;
    accessCount: number;
    lastAccessed?: Date;
  };
  automation: {
    autoArchive: boolean;
    archiveAfterDays?: number;
    autoCleanup: boolean;
    cleanupCriteria?: ChannelCleanupCriteria;
    monitorActivity: boolean;
  };
}

// Channel Cleanup Configuration
export interface ChannelCleanupCriteria {
  inactiveDays?: number;
  minMessages?: number;
  minMembers?: number;
  keepIfPinned?: boolean;
  archiveBeforeDelete?: boolean;
}

// Permission Matrix for Advanced Access Control
export interface ChannelPermissionMatrix {
  guildId: string;
  channelId: string;
  permissions: {
    [roleId: string]: {
      allow: DiscordPermissionFlag[];
      deny: DiscordPermissionFlag[];
      inherited: boolean;
      source: 'role' | 'member' | 'category';
      priority: number;
    };
  };
  memberOverrides: {
    [userId: string]: {
      allow: DiscordPermissionFlag[];
      deny: DiscordPermissionFlag[];
      reason?: string;
      expiresAt?: Date;
    };
  };
  lastUpdated: Date;
  updatedBy: string;
}

// Channel Event Types for Automation
export type ChannelEventType =
  | 'channel_created'
  | 'channel_updated'
  | 'channel_deleted'
  | 'channel_archived'
  | 'permissions_updated'
  | 'member_added'
  | 'member_removed'
  | 'message_count_threshold'
  | 'inactivity_detected'
  | 'auto_moderation_triggered';

export interface ChannelEvent {
  id: string;
  type: ChannelEventType;
  channelId: string;
  guildId: string;
  timestamp: Date;
  data: Record<string, any>;
  triggeredBy?: string;
  processed: boolean;
  processingResults?: {
    success: boolean;
    actions: string[];
    errors?: string[];
  };
}

// Channel Analytics and Monitoring
export interface ChannelAnalytics {
  channelId: string;
  guildId: string;
  period: {
    start: Date;
    end: Date;
  };
  metrics: {
    messageCount: number;
    uniqueUsers: number;
    avgMessagesPerUser: number;
    peakActivity: {
      timestamp: Date;
      messageCount: number;
    };
    quietPeriods: number;
    attachmentCount: number;
    reactionCount: number;
  };
  membershipChanges: {
    joined: number;
    left: number;
    netChange: number;
  };
  permissionChanges: number;
  moderationActions: number;
}

// Channel Automation Rules
export interface ChannelAutomationRule {
  id: string;
  name: string;
  description: string;
  channelId?: string; // if null, applies to all channels
  guildId: string;
  enabled: boolean;
  trigger: {
    event: ChannelEventType;
    conditions: Record<string, any>;
    cooldown?: number; // seconds
  };
  actions: ChannelAutomationAction[];
  schedule?: {
    type: 'interval' | 'cron';
    value: string;
  };
  metadata: {
    createdBy: string;
    createdAt: Date;
    lastRun?: Date;
    runCount: number;
    successCount: number;
    errorCount: number;
  };
}

export interface ChannelAutomationAction {
  type: 
    | 'send_message'
    | 'update_permissions'
    | 'archive_channel'
    | 'create_channel'
    | 'notify_admins'
    | 'run_cleanup'
    | 'update_role'
    | 'log_event';
  parameters: Record<string, any>;
  retryPolicy?: {
    maxRetries: number;
    backoffMs: number;
  };
}

// Enhanced Channel Service Interface
export interface IChannelService {
  // Basic Channel Operations
  updateUserAccess(userId: string, guildId: string, tier: MembershipTier): Promise<void>;
  createTierChannels(guildId: string, tier: MembershipTier): Promise<string[]>;
  removeTierAccess(userId: string, guildId: string, oldTier: MembershipTier): Promise<void>;
  getChannelTemplates(guildId: string, tier: MembershipTier): Promise<ChannelTemplate[]>;
  createPrivateChannel(guildId: string, name: string, members: string[]): Promise<string>;
  archiveChannel(channelId: string): Promise<void>;
  getAccessibleChannels(userId: string, guildId: string): Promise<string[]>;

  // Advanced Channel Management
  createChannelFromTemplate(guildId: string, template: ChannelTemplate): Promise<string>;
  updateChannelTemplate(templateId: string, updates: Partial<ChannelTemplate>): Promise<void>;
  cloneChannel(sourceChannelId: string, targetGuildId: string, options?: { includeMembershipPerms: boolean; includeMessages: boolean; }): Promise<string>;
  bulkUpdateChannelPermissions(channelIds: string[], permissions: ChannelPermissionOverwrite[]): Promise<void>;
  scheduleChannelDeletion(channelId: string, deleteAt: Date, reason?: string): Promise<void>;
  cancelScheduledDeletion(channelId: string): Promise<void>;

  // Permission Management
  getChannelPermissionMatrix(channelId: string): Promise<ChannelPermissionMatrix>;
  updatePermissionMatrix(channelId: string, matrix: Partial<ChannelPermissionMatrix>): Promise<void>;
  validatePermissionChanges(channelId: string, changes: ChannelPermissionOverwrite[]): Promise<{ valid: boolean; conflicts?: string[]; warnings?: string[]; }>;
  inheritCategoryPermissions(channelId: string): Promise<void>;
  getEffectivePermissions(channelId: string, userId: string): Promise<DiscordPermissionFlag[]>;

  // Lifecycle Management
  getChannelLifecycleState(channelId: string): Promise<ChannelLifecycleState>;
  updateChannelLifecycleState(channelId: string, state: ChannelLifecycleState, reason?: string): Promise<void>;
  processChannelCleanup(guildId: string, criteria?: ChannelCleanupCriteria): Promise<{ archived: string[]; deleted: string[]; }>;
  getInactiveChannels(guildId: string, daysInactive: number): Promise<GuildChannel[]>;

  // Analytics and Monitoring
  getChannelAnalytics(channelId: string, startDate: Date, endDate: Date): Promise<ChannelAnalytics>;
  getGuildChannelAnalytics(guildId: string, startDate: Date, endDate: Date): Promise<ChannelAnalytics[]>;
  trackChannelEvent(event: Omit<ChannelEvent, 'id' | 'timestamp' | 'processed'>): Promise<void>;
  getChannelEvents(channelId: string, eventType?: ChannelEventType, limit?: number): Promise<ChannelEvent[]>;

  // Automation
  createAutomationRule(rule: Omit<ChannelAutomationRule, 'id' | 'metadata'>): Promise<string>;
  updateAutomationRule(ruleId: string, updates: Partial<ChannelAutomationRule>): Promise<void>;
  deleteAutomationRule(ruleId: string): Promise<void>;
  getAutomationRules(guildId: string, channelId?: string): Promise<ChannelAutomationRule[]>;
  executeAutomationAction(action: ChannelAutomationAction, context: Record<string, any>): Promise<boolean>;
  processAutomationTriggers(): Promise<void>;

  // Bulk Operations
  bulkArchiveChannels(channelIds: string[], reason?: string): Promise<{ success: string[]; failed: { channelId: string; error: string }[]; }>;
  bulkUpdateChannelSettings(updates: { channelId: string; settings: Partial<ChannelConfig>; }[]): Promise<{ success: string[]; failed: { channelId: string; error: string }[]; }>;
  migrateChannelCategory(fromCategory: string, toCategory: string, guildId: string): Promise<string[]>;
}