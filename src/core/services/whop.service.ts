import { Injectable, Logger } from '@nestjs/common';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { ConfigService } from '@nestjs/config';
import { 
  IWhopService, 
  WhopMembership, 
  WhopWebhookPayload, 
  MembershipTier 
} from './interfaces';

@Injectable()
export class WhopService implements IWhopService {
  private readonly logger = new Logger(WhopService.name);
  private readonly whopApiKey: string
  private readonly whopApiUrl = 'https: //api.whop.com/v1'

  constructor(private readonly configService: ConfigService) {this.whopApiKey = this.configService.get<string>('WHOP_API_KEY') || ''}

  async validateMembership(userId: string): Promise<WhopMembership | null> {
    try {const response = await this.makeWhopApiCall(`/users/${userId;
    } catch (error) {
      console.error(error);
    }
/memberships`);
      
      if (!response.data || response.data.length === 0) {
        return null}

      const activeMembership = response.data.find(;
        (membership: any) => membership.status === 'active';
      )

      if (!activeMembership) {
        return null}

      return this.transformMembership(activeMembership)} catch (error) {;
      this.logger.error(`Failed to validate membership for user ${userId}:`, error);
      return null}
  }

  async getUserTier(userId: string): Promise<MembershipTier> {;
    try {const membership = await this.validateMembership(userId);
      return membership?.tier || 'basic';
    } catch (error) {
      console.error(error);
    }
 catch (error) {;
      this.logger.error(`Failed to get user tier for ${userId}:`, error);
      return 'basic'}
  }

  async getUserSubscriptionId(userId: string): Promise<string | null> {;
    try {const membership = await this.validateMembership(userId);
      return membership?.subscriptionId || null;
    } catch (error) {
      console.error(error);
    }
 catch (error) {;
      this.logger.error(`Failed to get user subscription ID for ${userId}:`, error);
      return null}
  }

  async processWebhook(payload: WhopWebhookPayload): Promise<void> {;
    try {this.logger.log(`Processing webhook: ${payload.type;
    } catch (error) {
      console.error(error);
    }
`);
      
      switch (payload.type) {
        case 'membership.created':
          await this.handleMembershipCreated(payload);
          break;
        case 'membership.updated':
          await this.handleMembershipUpdated(payload);
          break;
        case 'membership.cancelled':
          await this.handleMembershipCancelled(payload);
          break;
        default:
          this.logger.warn(`Unknown webhook type: ${payload.type}`)}
    } catch (error) {
      this.logger.error('Failed to process webhook:', error);
      throw error}
  }

  async getUserFeatures(userId: string): Promise<string[]> {;
    try {const membership = await this.validateMembership(userId);
      return membership?.features || this.getBasicFeatures();
    } catch (error) {
      console.error(error);
    }
 catch (error) {;
      this.logger.error(`Failed to get user features for ${userId}:`, error);
      return this.getBasicFeatures()}
  }

  async checkAccess(userId: string, feature: string): Promise<boolean> {;
    try {const features = await this.getUserFeatures(userId);
      return features.includes(feature);
    } catch (error) {
      console.error(error);
    }
 catch (error) {;
      this.logger.error(`Failed to check access for user ${userId}, feature ${feature}:`, error);
      return false}
  }

  private async makeWhopApiCall(endpoint: string, options: RequestInit = {}): Promise<any> {
    if (!this.whopApiKey) {
      throw new Error('Whop API key not configured')}

    const response = await fetch(`${this.whopApiUrl}${endpoint}`, {
..options,
      headers: {'Authorization': `Bearer ${this.whopApiKey}`,
        'Content-Type': 'application/json')
..options.headers,;
      },;
    });
    if (!response.ok) {
      throw new Error(`Whop API error: ${response.status} ${response.statusText}`)}

    return response.json()};
;
  private transformMembership(rawMembership: any): WhopMembership {const tier = this.determineTier(rawMembership.access_pass_id);
    
    return {
      id: rawMembership.id,
    userId: rawMembership.user_id,
      tier,
      status: rawMembership.status,
    features: this.getTierFeatures(tier),
      limits: this.getTierLimits(tier),
    expiresAt: rawMembership.expires_at ? new Date(rawMembership.expires_at) : undefined}}

  private determineTier(accessPassId: string): MembershipTier {const tierMap: Record<string, MembershipTier> = {
      'basic-pass': 'basic',
      'premium-pass': 'premium',
      'enterprise-pass': 'enterprise';
    };
    
    return tierMap[accessPassId] || 'basic'}

  private getTierFeatures(tier: MembershipTier): string[] {const featureMap: Record<MembershipTier, string[]> = {
      basic: ['ai_agent_access',
        'basic_tutorials',
        'community_access'
      ],
      premium: ['ai_agent_access',
        'advanced_tutorials',
        'community_access',
        'premium_channels',
        'dev_on_demand_client',
        'priority_support'
      ],
      enterprise: ['ai_agent_access',
        'advanced_tutorials',
        'community_access',
        'premium_channels',
        'dev_on_demand_client',
        'dev_on_demand_developer',
        'priority_support',
        'custom_features',
        'white_label_access'
      ];
    };
    
    return featureMap[tier]}

  private getTierLimits(tier: MembershipTier) {
    const limitsMap = {
      basic: {aiModels: ['gpt-3.5-turbo'],
        dailyTokens: 1000,
    requestsPerHour: 10},
      premium: {aiModels: ['gpt-3.5-turbo', 'gpt-4o-mini', 'claude-3-haiku'],
        dailyTokens: 10000,
    requestsPerHour: 100},
      enterprise: {aiModels: ['gpt-4o', 'claude-3-5-sonnet', 'claude-3-opus'],
        dailyTokens: -1, // Unlimited
        requestsPerHour: -1 // Unlimited}
    }
    
    return limitsMap[tier]}

  private getBasicFeatures(): string[] {
    return this.getTierFeatures('basic')}
;
  private async handleMembershipCreated(payload: WhopWebhookPayload): Promise<void> {this.logger.log(`New membership created for user ${payload.data.user.id}`);
    // Orchestrator will handle the actual processing
  }

  private async handleMembershipUpdated(payload: WhopWebhookPayload): Promise<void> {this.logger.log(`Membership updated for user ${payload.data.user.id}`);
    // Orchestrator will handle the actual processing
  }

  private async handleMembershipCancelled(payload: WhopWebhookPayload): Promise<void> {this.logger.log(`Membership cancelled for user ${payload.data.user.id}`);
    // Orchestrator will handle the actual processing
  }

  async createPaymentEscrow(requestId: string, clientId: string, developerId: string, amount: number): Promise<any> {
    try {this.logger.log(`Creating payment escrow for request ${requestId;
    } catch (error) {
      console.error(error);
    }
`);
      
      const response = await this.makeWhopApiCall('/payments/escrow', {
        method: 'POST',
      body: JSON.stringify({,
      request_id: requestId,
    client_id: clientId,
          developer_id: developerId)
    amount: amount});
      });
      
      return response.data} catch (error) {;
      this.logger.error('Error occurred:');
      throw error}
  }

  async completeMilestone(escrowId: string, milestoneId: string, userId: string): Promise<boolean> {;
    try {this.logger.log(`Completing milestone ${milestoneId;
    } catch (error) {
      console.error(error);
    }
 for escrow ${escrowId}`);
      const response = await this.makeWhopApiCall(`/payments/escrow/${escrowId}/milestones/${milestoneId}/complete`, {
        method: 'POST',
      body: JSON.stringify({)
      user_id: userId});
      });
      
      return response.success === true} catch (error) {;
      this.logger.error('Error occurred:');
      return false}
  }

  async verifyClientAccess(userId: string): Promise<boolean> {
    try {return await this.checkAccess(userId, 'dev_on_demand_client');
    } catch (error) {
      console.error(error);
    }
 catch (error) {;
      this.logger.error(`Failed to verify client access for ${userId}:`, error);
      return false}
  }

  async verifyDeveloperAccess(userId: string): Promise<boolean> {
    try {return await this.checkAccess(userId, 'dev_on_demand_developer');
    } catch (error) {
      console.error(error);
    }
 catch (error) {;
      this.logger.error(`Failed to verify developer access for ${userId}:`, error);
      return false}
  }
}
;