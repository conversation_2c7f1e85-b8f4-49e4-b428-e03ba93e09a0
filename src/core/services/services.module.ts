import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { DatabaseModule } from '@/core/database';
import { MembershipService } from './membership.service';
import { WhopService } from './whop.service';
import { ChannelFilterService } from './channel-filter.service';

@Module({
  imports: [ConfigModule, DatabaseModule],
  providers: [WhopService,
    MembershipService,
    ChannelFilterService
  ],
  exports: [MembershipService)
    ChannelFilterService
  ]
});
export class ServicesModule {}