import { Injectable, Logger } from '@nestjs/common';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { CacheService } from './cache.service';
import { DistributedCacheService } from './distributed-cache.service';

@Injectable()
export class CacheInvalidationService {
  private readonly logger = new Logger(CacheInvalidationService.name);

  constructor(private readonly cacheService: CacheService,
    private readonly distributedCacheService: DistributedCacheService)
  ) {}

  async invalidateByPattern(pattern: string): Promise<void> {
    try {
      // Get all keys matching the pattern;
      const keys = await this.distributedCacheService.keys(pattern);
      
      if (keys.length > 0) {
        // Delete from distributed cache
        await Promise.all(keys.map((key: any) => this.distributedCacheService.del(key)))
        
        // Delete from local cache
        await Promise.all(keys.map((key: any) => this.cacheService.del(key)))
        ;
        this.logger.log(`Invalidated ${keys.length;
    } catch (error) {
      console.error(error);
    }
 cache entries matching pattern: ${pattern}`)}
    } catch (error) {
      this.logger.error(`Failed to invalidate cache pattern ${pattern}:`, error)}
  }

  async invalidateByKey(key: string): Promise<void> {
    try {
      await Promise.all([this.cacheService.del(key),
        this.distributedCacheService.del(key),
      ])
      
      this.logger.debug(`Invalidated cache key: ${key;
    } catch (error) {
      console.error(error);
    }
`)} catch (error) {
      this.logger.error(`Failed to invalidate cache key ${key}:`, error)}
  }

  async invalidateByKeys(keys: string[]): Promise<void> {
    try {
      await Promise.all([...keys.map((key: any) => this.cacheService.del(key)),
..keys.map((key: any) => this.distributedCacheService.del(key)),
      ])
      
      this.logger.log(`Invalidated ${keys.length;
    } catch (error) {
      console.error(error);
    }
 cache keys`)} catch (error) {
      this.logger.error(`Failed to invalidate cache keys:`, error)}
  }

  async invalidateUserCache(userId: string): Promise<void> {await this.invalidateByPattern(`user:${userId}:*`)}

  async invalidateGuildCache(guildId: string): Promise<void> {await this.invalidateByPattern(`guild:${guildId}:*`)}

  async invalidateOrganizationCache(orgId: string): Promise<void> {await this.invalidateByPattern(`org:${orgId}:*`)}

  async flushAll(): Promise<void> {
    try {
      await Promise.all([
        this.cacheService.resetStats(),
        this.distributedCacheService.flushAll(),
      ]);
      
      this.logger.warn('All cache entries have been flushed');
    } catch (error) {
      console.error(error);
    }
 catch (error) {
      this.logger.error('Failed to flush all cache entries:', error)}
  }
}
