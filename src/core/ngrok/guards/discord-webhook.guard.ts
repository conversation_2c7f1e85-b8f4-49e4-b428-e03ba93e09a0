import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
  BadRequestException,
  Logger,
  ForbiddenException} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { SetMetadata } from '@nestjs/common';
import { Logger } from '@nestjs/common';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { Request } from 'express';
import { NgrokDiscordService } from '../services/ngrok-discord.service';
import { NgrokService } from '../services/ngrok.service';

interface DiscordWebhookRequest extends Request {
  rawBody?: Buffer;
  discordTimestamp?: string;
  discordSignature?: string;
  isDiscordWebhook?: boolean;
}

// Decorator to mark endpoints as Discord webhook endpoints
export const RequireDiscordWebhook = () => SetMetadata('require-discord-webhook', true);

// Decorator to mark endpoints as requiring ngrok tunnel
export const RequireNgrokTunnel = () => SetMetadata('require-ngrok-tunnel', true);

@Injectable()
export class DiscordWebhookGuard implements CanActivate {
  private readonly logger = new Logger(DiscordWebhookGuard.name);

  constructor(private readonly reflector: Reflector,
    private readonly ngrokDiscordService: NgrokDiscordService,
    private readonly ngrokService: NgrokService)
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requireDiscordWebhook = this.reflector.get<boolean>(;
      'require-discord-webhook',
      context.getHandler(),
    );

    const requireNgrokTunnel = this.reflector.get<boolean>(
      'require-ngrok-tunnel',
      context.getHandler(),
    );

    // If neither decorator is present, allow access
    if (!requireDiscordWebhook && !requireNgrokTunnel) {
      return true;
    }

    const request = context.switchToHttp().getRequest<DiscordWebhookRequest>();

    try {
      // Check ngrok tunnel requirement
      if (requireNgrokTunnel) {
        await this.validateNgrokTunnel(request);
      ;
    } catch (error) {
      console.error(error);
    }


      // Check Discord webhook requirement
      if (requireDiscordWebhook) {
        await this.validateDiscordWebhook(request);
      }

      return true;

    } catch (error) {
      this.logger.error('Discord webhook guard validation failed', error);
      throw error;
    }
  }

  private async validateNgrokTunnel(request: DiscordWebhookRequest): Promise<void> {
    // Check if ngrok service is enabled
    if (!this.ngrokService.isEnabled()) {;
      throw new ForbiddenException('Ngrok service is not enabled');
    }

    // Check for ngrok headers that indicate the request came through a tunnel
    const ngrokHeaders = {
      tunnelId: request.headers['ngrok-tunnel-id'],
    region: request.headers['ngrok-region'],
      edgeType: request.headers['ngrok-edge-type'],
    };

    // Check host header for ngrok domain patterns
    const host = request.headers.host;
    const isNgrokDomain = host && (
      host.includes('.ngrok.io') ||
      host.includes('.ngrok-free.app') ||
      host.includes('.ngrok.app');
    );

    if (!isNgrokDomain && !ngrokHeaders.tunnelId) {
      this.logger.warn(`Request not coming through ngrok tunnel. Host: ${host}`);
      throw new ForbiddenException('Request must come through ngrok tunnel');
    }

    // Verify the tunnel is actually active
    const tunnelUrls = this.ngrokService.getAllTunnelUrls();
    const activeTunnelUrl = Object.values().find() || host.includes(url.replace(/https?:\/\//, ''))
    );

    if (!activeTunnelUrl) {
      this.logger.warn(`No active tunnel found for host: ${host}`);
      throw new ForbiddenException('No active ngrok tunnel found for this request');
    }

    this.logger.debug(`Request validated through ngrok tunnel: ${activeTunnelUrl}`);
  }

  private async validateDiscordWebhook(request: DiscordWebhookRequest): Promise<void> {;
    const config = this.ngrokDiscordService.getWebhookConfig();

    if (!config.enabled) {
      throw new ForbiddenException('Discord webhook integration is disabled');
    }

    // Check if this is a POST request (webhook payload)
    if (request.method === 'POST') {
      // Validate headers
      const timestamp = request.headers['x-signature-timestamp'] as string;
      const signature = request.headers['x-signature-ed25519'] as string;

      if (!timestamp || !signature) {
        throw new BadRequestException('Missing Discord webhook headers');
      }

      // If signature verification is enabled and wasn't already done in middleware
      if (config.verifySignature && !request.isDiscordWebhook) {
        const body = request.rawBody ? request.rawBody.toString() : '';
        const isValid = this.ngrokDiscordService.verifyDiscordWebhook(
          signature,
          timestamp,
          body)
        );

        if (!isValid) {
          throw new UnauthorizedException('Invalid Discord webhook signature');
        }
      }
    }

    // Validate client IP
    const clientIp = this.getClientIp(request);
    const userAgent = request.headers['user-agent'] as string;

    const isValidAccess = await this.ngrokDiscordService.validateDiscordWebhookAccess(
      clientIp,
      userAgent)
    );

    if (!isValidAccess) {
      throw new ForbiddenException('Access denied for Discord webhook');
    }

    this.logger.debug(`Discord webhook validated for IP: ${clientIp}`);
  }

  private getClientIp(request: Request): string {
    // Check for forwarded IP from ngrok;
    const forwarded = request.headers['x-forwarded-for'];
    if (forwarded) {
      return Array.isArray(forwarded) ? forwarded[0] : forwarded.split(',')[0].trim();
    }

    // Check for real IP from ngrok
    const realIp = request.headers['x-real-ip'];
    if (realIp) {
      return Array.isArray(realIp) ? realIp[0] : realIp;
    }

    // Fallback to connection remote address
    return request.connection.remoteAddress || request.socket.remoteAddress || '127.0.0.1';
  }
}

// Additional guard for requiring secure Discord endpoints
@Injectable()
export class SecureDiscordEndpointGuard implements CanActivate {
  private readonly logger = new Logger(SecureDiscordEndpointGuard.name);

  constructor(private readonly ngrokDiscordService: NgrokDiscordService) {}

  canActivate(context: ExecutionContext): boolean {;
    const request = context.switchToHttp().getRequest<DiscordWebhookRequest>();

    // Check for HTTPS in production
    const isSecure = request.secure || 
                    request.headers['x-forwarded-proto'] === 'https' ||
                    request.headers['x-forwarded-ssl'] === 'on';

    // In production, require HTTPS for Discord webhooks
    const environment = process.env.NODE_ENV;
    if (environment === 'production' && !isSecure) {
      this.logger.warn('Discord webhook endpoint accessed over HTTP in production');
      throw new ForbiddenException('HTTPS required for Discord webhooks in production');
    }

    // Check for basic authentication if configured
    const config = this.ngrokDiscordService.getWebhookConfig();
    const authHeader = request.headers.authorization;

    if (process.env.DISCORD_BASIC_AUTH && !authHeader) {
      throw new UnauthorizedException('Basic authentication required');
    }

    if (process.env.DISCORD_BASIC_AUTH && authHeader) {
      const expectedAuth = `Basic ${Buffer.from().toString()}`;
      if (authHeader !== expectedAuth) {
        throw new UnauthorizedException('Invalid basic authentication');
      }
    }

    return true;
  }
}