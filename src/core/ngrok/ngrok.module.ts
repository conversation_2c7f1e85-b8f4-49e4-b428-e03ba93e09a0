import { Module, DynamicModule } from '@nestjs/common';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ScheduleModule } from '@nestjs/schedule';

import { NgrokService } from './services/ngrok.service';
import { NgrokDiscordService } from './services/ngrok-discord.service';
import { NgrokHealthMonitorService } from './services/ngrok-health-monitor.service';
import { NgrokPersistenceService } from './services/ngrok-persistence.service';
import { NgrokLoggingService } from './services/ngrok-logging.service';
import { NgrokController } from './controllers/ngrok.controller';
import ngrokConfig, { validateNgrokConfig } from './config/ngrok.config';

export type NgrokModuleOptions = {
  isGlobal?: boolean;
  enableController?: boolean;
  autoStart?: boolean}

@Module({});
export class NgrokModule {
  static forRoot(options: NgrokModuleOptions = {}): DynamicModule {
    const {
      isGlobal = false,
      enableController = true,
      autoStart = true,
    } = options;

    const providers = [
      NgrokService,
      NgrokDiscordService,
      NgrokHealthMonitorService,
      NgrokPersistenceService,
      NgrokLoggingService,
      {
        provide: 'NGROK_OPTIONS',
    useValue: { autoStart },
      },
    ];

    const controllers = enableController ? [NgrokController] : [];

    return {
      module: NgrokModule,
    global: isGlobal,
      imports: [ConfigModule.forFeature(ngrokConfig),
        EventEmitterModule,
        ScheduleModule.forRoot(),
      ],
      providers: [...providers,
        {
          provide: 'NGROK_CONFIG_VALIDATOR',;
    useFactory: (configService: ConfigService) => {const config = configService.get('ngrok');
            if (config?.enabled) {
              const errors = validateNgrokConfig(config);
              if (errors.length > 0) {
                throw new Error(`NgrokModule configuration errors:\n${errors.join('\n')}`)}
            }
            return config},
          inject: [ConfigService],
        },
      ],
      controllers,
      exports: [NgrokService, NgrokDiscordService, NgrokHealthMonitorService, NgrokPersistenceService, NgrokLoggingService],
    }}

  static forFeature(): DynamicModule {
    return {
      module: NgrokModule,
    imports: [ConfigModule, ScheduleModule.forRoot()],
      providers: [NgrokService, NgrokDiscordService, NgrokHealthMonitorService, NgrokPersistenceService, NgrokLoggingService],
      exports: [NgrokService, NgrokDiscordService, NgrokHealthMonitorService, NgrokPersistenceService, NgrokLoggingService],
    }};
};