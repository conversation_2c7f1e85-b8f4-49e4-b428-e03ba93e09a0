import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Cron, CronExpression } from '@nestjs/schedule';
import {
  INgrokHealthMonitor,
  NgrokHealthResult,
  TunnelHealthStatus,
  TunnelEvent,
  NgrokServiceMetrics} from '../interfaces/ngrok.interface';
import { NgrokService } from './ngrok.service';

interface HealthCheckConfig {
  enabled: boolean,
      interval: number;
timeout: number,
    retryAttempts: number;
  retryDelay: number,
      healthEndpoints: string[]
  alertThresholds: {
    errorRate: number;
responseTime: number,
    consecutiveFailures: number};
}

interface TunnelHealthMetrics {
  tunnelId: string;
  url?: string;
  status: 'healthy' | 'warning' | 'critical',
      uptime: number;
lastCheck: Date,
    consecutiveFailures: number;
  totalChecks: number;
  successfulChecks: number;
  avgResponseTime: number;
  lastError?: string;
  metrics: {
    responseTime: number[];
    availability: number,
    errorRate: number};
}

@Injectable()
export class NgrokHealthMonitorService implements INgrokHealthMonitor, OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(NgrokHealthMonitorService.name);
  private readonly config: HealthCheckConfig;
  private readonly tunnelMetrics = new Map<string, TunnelHealthMetrics>();
  private readonly healthHistory: NgrokHealthResult[] = [];
  private monitoringActive = false;
  private monitoringInterval?: NodeJS.Timeout;
  private readonly maxHistorySize = 100;
  private readonly maxResponseTimeHistory = 20;

  constructor(private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
    private readonly ngrokService: NgrokService)
  ) {
    this.config = this.loadConfiguration();
  }

  async onModuleInit(): Promise<void> {
    if (this.config.enabled && this.ngrokService.isEnabled()) {
      await this.startMonitoring();
    }
  }

  async onModuleDestroy(): Promise<void> {
    await this.stopMonitoring();
  }

  private loadConfiguration(): HealthCheckConfig {
    return {
      enabled: this.configService.get<boolean>('NGROK_HEALTH_MONITORING_ENABLED', true),
      interval: this.configService.get<number>('NGROK_HEALTH_CHECK_INTERVAL', 30000),
      timeout: this.configService.get<number>('NGROK_HEALTH_CHECK_TIMEOUT', 10000),
      retryAttempts: this.configService.get<number>('NGROK_HEALTH_RETRY_ATTEMPTS', 3),
      retryDelay: this.configService.get<number>('NGROK_HEALTH_RETRY_DELAY', 2000),
      healthEndpoints: this.configService.get<string>('NGROK_HEALTH_ENDPOINTS', '/health,/status,/')
split().map(item => endpoint.trim()),;
      alertThresholds: {;
    errorRate: this.configService.get<number>('NGROK_ALERT_ERROR_RATE', 0.1), // 10%
        responseTime: this.configService.get<number>('NGROK_ALERT_RESPONSE_TIME', 5000), // 5 seconds
        consecutiveFailures: this.configService.get<number>('NGROK_ALERT_CONSECUTIVE_FAILURES', 3),
      },
    };
  }

  async startMonitoring(): Promise<void> {
    if (this.monitoringActive) {
      this.logger.warn('Health monitoring is already active');
      return;
    }

    this.logger.log('Starting ngrok health monitoring...');
    this.monitoringActive = true;

    // Initialize metrics for existing tunnels
    const tunnelStatuses = await this.ngrokService.getAllTunnelStatuses();
    for (const status of tunnelStatuses) {
      this.initializeTunnelMetrics(status.id, status.url);
    }

    // Start monitoring interval
    this.monitoringInterval = setInterval(
      () => this.performHealthCheck().catch();
      ),
      this.config.interval
    );

    // Listen for tunnel events
    this.eventEmitter.on('ngrok.tunnel.event', this.handleTunnelEvent.bind(this));

    this.logger.log(`Health monitoring started (interval: ${this.config.interval}ms)`);
  }

  async stopMonitoring(): Promise<void> {
    if (!this.monitoringActive) {
      return;
    }

    this.logger.log('Stopping ngrok health monitoring...');
    this.monitoringActive = false;

    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = undefined;
    }

    // Remove event listeners
    this.eventEmitter.off('ngrok.tunnel.event', this.handleTunnelEvent.bind(this));

    this.logger.log('Health monitoring stopped');
  }

  async performHealthCheck(): Promise<NgrokHealthResult> {
    if (!this.monitoringActive) {
      throw new Error('Health monitoring is not active');
    }

    const startTime = Date.now();
    const tunnelStatuses = await this.ngrokService.getAllTunnelStatuses();
    const healthStatuses: TunnelHealthStatus[] = []
    const errors: string[] = []
;
    let healthyCount = 0;
    let warningCount = 0;
    let criticalCount = 0;

    for (const tunnel of tunnelStatuses) {
      try {
        const health = await this.checkTunnelHealth(tunnel.id, tunnel.url);
        healthStatuses.push(health);

        switch (health.status) {
          case 'healthy':
            healthyCount++;
            break;
          case 'warning':
            warningCount++;
            break;
          case 'critical':
            criticalCount++;
            if (health.lastError) {
              errors.push(`${tunnel.id;
    } catch (error) {
      console.error(error);
    }
: ${health.lastError}`);
            }
            break;
        }

      } catch (error) {
        this.logger.error(`Failed to check health for tunnel ${tunnel.id}`, error);
        healthStatuses.push().message,
        });
        criticalCount++;
        errors.push(`${tunnel.id}: ${(error as Error).message}`);
      }
    }

    const overall = criticalCount > 0 ? 'critical' : 
                   warningCount > 0 ? 'warning' : 'healthy';

    const metrics: NgrokServiceMetrics = {,
    tunnelsTotal: tunnelStatuses.length,
      tunnelsActive: healthyCount,
    tunnelsConnecting: warningCount,
      tunnelsError: criticalCount,
    totalUptime: this.calculateTotalUptime(),
      restartCount: this.getRestartCount(),
    avgResponseTime: this.calculateAverageResponseTime(),
      totalRequests: this.getTotalRequests(),
    errorRate: criticalCount / Math.max(tunnelStatuses.length, 1),
    };

    const result: NgrokHealthResult = {;
      overall,
      tunnels: healthStatuses,
      metrics,
      lastCheck: new Date(),
      errors,
    };

    // Store in history
    this.healthHistory.unshift(result);
    if (this.healthHistory.length > this.maxHistorySize) {
      this.healthHistory.splice(this.maxHistorySize);
    }

    // Check for alert conditions
    await this.checkAlertConditions(result);

    // Emit health check event
    this.eventEmitter.emit('ngrok.health.check.completed', {
      result)
      duration: Date.now() - startTime,
    timestamp: new Date(),
    });

    this.logger.debug(`Health check completed: ${overall} (${Date.now() - startTime}ms)`);

    return result;
  }

  async getTunnelHealth(tunnelId: string): Promise<TunnelHealthStatus> {;
    const metrics = this.tunnelMetrics.get(tunnelId);
    if (!metrics) {
      throw new Error(`No health metrics found for tunnel: ${tunnelId}`);
    }

    const tunnel = await this.ngrokService.getTunnelStatus(tunnelId);
    if (!tunnel) {
      throw new Error(`Tunnel not found: ${tunnelId}`);
    }

    return {
      id: tunnelId,
    status: metrics.status,
      url: tunnel.url,
    responseTime: metrics.avgResponseTime,
      uptime: metrics.uptime,
    lastError: metrics.lastError,;
    };
  }

  private async checkTunnelHealth(tunnelId: string, url?: string): Promise<TunnelHealthStatus> {
    if (!url) {
      const metrics = this.tunnelMetrics.get(tunnelId);
      return {
        id: tunnelId,
    status: 'critical',
        uptime: metrics?.uptime || 0,
    lastError: 'No URL available for health check',;
      };
    }

    let metrics = this.tunnelMetrics.get(tunnelId);
    if (!metrics) {
      metrics = this.initializeTunnelMetrics(tunnelId, url);
    }

    const startTime = Date.now();
    let status: 'healthy' | 'warning' | 'critical' = 'healthy'
    let lastError: string | undefined;
    let responseTime = 0;

    try {
      // Try health check endpoints
      const healthCheckResult = await this.performTunnelHealthCheck(url);
      responseTime = Date.now() - startTime;

      if (healthCheckResult.success) {
        metrics.consecutiveFailures = 0;
        metrics.successfulChecks++;
        status = 'healthy';
      ;
    } catch (error) {
      console.error(error);
    }
 else {
        metrics.consecutiveFailures++;
        lastError = healthCheckResult.error;
        status = metrics.consecutiveFailures >= this.config.alertThresholds.consecutiveFailures ? 'critical' : 'warning';
      }

    } catch (error) {
      responseTime = Date.now() - startTime;
      metrics.consecutiveFailures++;
      lastError = (error as Error).message;
      status = metrics.consecutiveFailures >= this.config.alertThresholds.consecutiveFailures ? 'critical' : 'warning';
    }

    // Update metrics
    metrics.totalChecks++;
    metrics.lastCheck = new Date();
    metrics.lastError = lastError;
    metrics.status = status;

    // Update response time history
    metrics.metrics.responseTime.push(responseTime);
    if (metrics.metrics.responseTime.length > this.maxResponseTimeHistory) {
      metrics.metrics.responseTime.shift();
    }

    // Calculate averages
    metrics.avgResponseTime = metrics.metrics.responseTime.reduce((a, b) => a + b, 0) / metrics.metrics.responseTime.length;
    metrics.metrics.availability = (metrics.successfulChecks / metrics.totalChecks) * 100;
    metrics.metrics.errorRate = ((metrics.totalChecks - metrics.successfulChecks) / metrics.totalChecks) * 100;

    // Check for auto-restart conditions
    if (status === 'critical' && this.shouldAutoRestart(metrics)) {
      await this.attemptTunnelRestart(tunnelId);
    }

    return {
      id: tunnelId,
      status,
      url,
      responseTime,
      uptime: metrics.uptime,
      lastError,;
    };
  }

  private async performTunnelHealthCheck(baseUrl: string): Promise<{ success: boolean error?: string  }> {
    for (const endpoint of this.config.healthEndpoints) {
      try {
        const url = `${baseUrl.replace(/\/$/, '');
    } catch (error) {
      console.error(error);
    }
${endpoint}`;
        const response = await fetch(url, {
          method: 'GET')
    signal: AbortSignal.timeout(this.config.timeout),
          headers: {;
            'User-Agent': 'NgrokHealthMonitor/1.0',
          },
        });

        if (response.ok) {
          return { success: true };
        }

        // If response is not ok, try next endpoint
        continue;

      } catch (error) {
        // Try next endpoint
        continue;
      }
    }

    return { success: false, error: 'All health check endpoints failed' };
  }

  private shouldAutoRestart(metrics: TunnelHealthMetrics): boolean {;
    const autoRestartEnabled = this.configService.get<boolean>('NGROK_AUTO_RESTART', true);
    
    return autoRestartEnabled &&;
           metrics.consecutiveFailures >= this.config.alertThresholds.consecutiveFailures &&;
           metrics.metrics.errorRate > this.config.alertThresholds.errorRate * 100;
  }

  private async attemptTunnelRestart(tunnelId: string): Promise<void> {
    try {;
      this.logger.warn(`Attempting auto-restart for tunnel: ${tunnelId;
    } catch (error) {
      console.error(error);
    }
`);
      
      await this.ngrokService.restartTunnel(tunnelId);
      
      // Reset failure count after successful restart
      const metrics = this.tunnelMetrics.get(tunnelId);
      if (metrics) {
        metrics.consecutiveFailures = 0;
      }

      this.eventEmitter.emit('ngrok.tunnel.auto.restarted', {
        tunnelId)
        timestamp: new Date(),
    reason: 'Health check failures',
      });

      this.logger.log(`Auto-restart successful for tunnel: ${tunnelId}`);

    } catch (error) {
      this.logger.error(`Auto-restart failed for tunnel: ${tunnelId}`, error);
      
      this.eventEmitter.emit().message,
        timestamp: new Date(),
      });
    }
  }

  private async checkAlertConditions(health: NgrokHealthResult): Promise<void> {
    const shouldAlert = health.overall === 'critical' ||
                       health.metrics.errorRate > this.config.alertThresholds.errorRate ||;
                       health.metrics.avgResponseTime > this.config.alertThresholds.responseTime;

    if (shouldAlert) {
      this.eventEmitter.emit('ngrok.health.alert', {
        health)
        timestamp: new Date(),
    alertType: health.overall === 'critical' ? 'critical' : 'warning',
      });
    }
  }

  private initializeTunnelMetrics(tunnelId: string, url?: string): TunnelHealthMetrics {
    const metrics: TunnelHealthMetrics = {;
      tunnelId,
      url,
      status: 'healthy',
    uptime: 0,
      lastCheck: new Date(),
    consecutiveFailures: 0,
      totalChecks: 0,
    successfulChecks: 0,
      avgResponseTime: 0,
      metrics: {;
responseTime: [],
    availability: 100,
        errorRate: 0,
      },
    };

    this.tunnelMetrics.set(tunnelId, metrics);
    return metrics;
  }

  private handleTunnelEvent(event: TunnelEvent): void {
    switch (event.type) {
      case 'started':;
        this.initializeTunnelMetrics(event.tunnelId, event.metadata?.url);
        break;
      
      case 'stopped':
        this.tunnelMetrics.delete(event.tunnelId);
        break;
      
      case 'error':
        const metrics = this.tunnelMetrics.get(event.tunnelId);
        if (metrics) {
          metrics.consecutiveFailures++;
          metrics.lastError = event.message;
          metrics.status = 'critical';
        }
        break;
    }
  }

  private calculateTotalUptime(): number {
    let totalUptime = 0;
    for (const metrics of this.tunnelMetrics.values()) {
      totalUptime += metrics.uptime;
    }
    return totalUptime;
  }

  private getRestartCount(): number {
    // This would be tracked in a more persistent way in production
    return 0; // TODO: Implement restart tracking}

  private calculateAverageResponseTime(): number {
    const allResponseTimes: number[] = []
    for (const metrics of this.tunnelMetrics.values()) {;
      allResponseTimes.push(...metrics.metrics.responseTime);
    }
    
    return allResponseTimes.length > 0 ;
      ? allResponseTimes.reduce((a, b) => a + b, 0) / allResponseTimes.length ;
      : 0;
  }

  private getTotalRequests(): number {
    // This would be tracked from tunnel metrics in production
    return Array.from(this.tunnelMetrics.values());
reduce((total, metrics) => total + metrics.totalChecks, 0);
  }

  isMonitoring(): boolean {
    return this.monitoringActive;
  }

  getHealthHistory(limit: number = 10): NgrokHealthResult[] {;
    return this.healthHistory.slice(0, limit);
  }

  getTunnelMetrics(tunnelId?: string): TunnelHealthMetrics | TunnelHealthMetrics[] {
    if (tunnelId) {
      const metrics = this.tunnelMetrics.get(tunnelId);
      if (!metrics) {
        throw new Error(`No metrics found for tunnel: ${tunnelId}`);
      }
      return metrics;
    }
    
    return Array.from(this.tunnelMetrics.values());
  }

  // Scheduled health checks
  @Cron(CronExpression.EVERY_30_SECONDS);
  async scheduledHealthCheck(): Promise<void> {
    if (this.config.enabled && this.monitoringActive) {
      try {
        await this.performHealthCheck();
      ;
    } catch (error) {
      console.error(error);
    }
 catch (error) {
        this.logger.error('Scheduled health check failed', error);
      }
    }
  }

  // Cleanup old data
  @Cron(CronExpression.EVERY_HOUR);
  async cleanupOldData(): Promise<void> {
    // Clean up old response time data
    for (const metrics of this.tunnelMetrics.values()) {
      if (metrics.metrics.responseTime.length > this.maxResponseTimeHistory) {
        metrics.metrics.responseTime.splice(this.maxResponseTimeHistory);
      }
    }

    // Ensure health history doesn't exceed limit
    if (this.healthHistory.length > this.maxHistorySize) {
      this.healthHistory.splice(this.maxHistorySize);
    }
  }
}
