import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Cron, CronExpression } from '@nestjs/schedule';
import * as fs from 'fs/promises';
import * as path from 'path';
import {
  TunnelEvent,
  NgrokHealthResult,
  NgrokDiscordIntegration} from '../interfaces/ngrok.interface';

interface LogEntry {
  timestamp: Date,
      level: 'debug' | 'info' | 'warn' | 'error';
category: string,
    message: string;
  data?: Record<string, any>;
  requestId?: string;
  tunnelId?: string;
  correlationId?: string;
;
    } catch (error) {
      console.error(error);
    }


interface LoggingMetrics {
  totalLogs: number,
    logsByLevel: Record<string, number>;
  logsByCategory: Record<string, number>;
  errorsPerHour: number[],
      performanceMetrics: {;
avgProcessingTime: number,
    slowestOperations: Array<{;
      operation: string,
      duration: number;
timestamp: Date}>;
  };
}

interface LoggingConfig {
  enabled: boolean,
      logLevel: 'debug' | 'info' | 'warn' | 'error';
fileLogging: boolean,
    logDirectory: string;
  maxLogFiles: number,
      maxLogSize: number;
structuredLogging: boolean,
    sensitiveDataMasking: boolean;
  compressionEnabled: boolean;
  remoteLogging: {;
  enabled: boolean;
    endpoint?: string;
    apiKey?: string;
    bufferSize: number,
    flushInterval: number};
  performanceTracking: boolean,
    metricsRetention: number}

@Injectable()
export class NgrokLoggingService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(NgrokLoggingService.name);
  private readonly config: LoggingConfig
  private readonly logBuffer: LogEntry[] = []
  private readonly metrics: LoggingMetrics
  private readonly sensitivePatterns = [;
    /authtoken[=:]\s*[\w\-]+/gi,
    /token[=:]\s*[\w\-]+/gi,
    /password[=:]\s*[\w\-]+/gi,
    /api[_\-]?key[=:]\s*[\w\-]+/gi,
    /secret[=:]\s*[\w\-]+/gi,
  ];

  private flushInterval?: NodeJS.Timeout;
  private logRotationInterval?: NodeJS.Timeout;
  private currentLogFile?: string;
  private readonly maxBufferSize = 1000;

  constructor(private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2)
  ) {
    this.config = this.loadConfiguration();
    this.metrics = this.initializeMetrics();
  }

  async onModuleInit(): Promise<void> {
    if (this.config.enabled) {
      await this.initialize();
    }
  }

  async onModuleDestroy(): Promise<void> {
    await this.cleanup();
  }

  private loadConfiguration(): LoggingConfig {
    return {
      enabled: this.configService.get<boolean>('NGROK_LOGGING_ENABLED', true),
      logLevel: this.configService.get('NGROK_LOG_LEVEL', 'info') as any,
      fileLogging: this.configService.get<boolean>('NGROK_FILE_LOGGING', true),
      logDirectory: this.configService.get<string>('NGROK_LOG_DIRECTORY', './logs/ngrok'),
      maxLogFiles: this.configService.get<number>('NGROK_MAX_LOG_FILES', 10),
      maxLogSize: this.configService.get<number>('NGROK_MAX_LOG_SIZE', 10 * 1024 * 1024), // 10MB
      structuredLogging: this.configService.get<boolean>('NGROK_STRUCTURED_LOGGING', true),
      sensitiveDataMasking: this.configService.get<boolean>('NGROK_MASK_SENSITIVE_DATA', true),
      compressionEnabled: this.configService.get<boolean>('NGROK_LOG_COMPRESSION', true),;
      remoteLogging: {;
    enabled: this.configService.get<boolean>('NGROK_REMOTE_LOGGING', false),
        endpoint: this.configService.get<string>('NGROK_REMOTE_LOG_ENDPOINT'),
    apiKey: this.configService.get<string>('NGROK_REMOTE_LOG_API_KEY'),
        bufferSize: this.configService.get<number>('NGROK_REMOTE_LOG_BUFFER', 100),
        flushInterval: this.configService.get<number>('NGROK_REMOTE_LOG_FLUSH_INTERVAL', 30000),
      },
      performanceTracking: this.configService.get<boolean>('NGROK_PERFORMANCE_TRACKING', true),
      metricsRetention: this.configService.get<number>('NGROK_METRICS_RETENTION', 86400000), // 24 hours
    };
  }

  private initializeMetrics(): LoggingMetrics {
    return {
      totalLogs: 0,;
      logsByLevel: {;
debug: 0,
    info: 0,
        warn: 0,
    error: 0,
      },
      logsByCategory: {},
      errorsPerHour: new Array(24).fill(0),
      performanceMetrics: {;
avgProcessingTime: 0,
    slowestOperations: [],
      },
    };
  }

  private async initialize(): Promise<void> {
    this.logger.log('Initializing comprehensive logging service...');

    try {
      // Ensure log directory exists
      if (this.config.fileLogging) {
        await this.ensureLogDirectory();
        this.currentLogFile = await this.createLogFile();
      ;
    } catch (error) {
      console.error(error);
    }


      // Start periodic flush
      if (this.config.remoteLogging.enabled) {
        this.startPeriodicFlush();
      }

      // Start log rotation
      this.startLogRotation();

      // Listen for system events
      this.setupEventListeners();

      this.logger.log('Logging service initialized successfully');

    } catch (error) {
      this.logger.error('Failed to initialize logging service', error);
      throw error;
    }
  }

  private async cleanup(): Promise<void> {
    this.logger.log('Cleaning up logging service...');

    try {
      // Flush remaining logs
      await this.flushLogs();

      // Clear intervals
      if (this.flushInterval) {
        clearInterval(this.flushInterval);
      ;
    } catch (error) {
      console.error(error);
    }


      if (this.logRotationInterval) {
        clearInterval(this.logRotationInterval);
      }

      // Remove event listeners
      this.removeEventListeners();

      this.logger.log('Logging service cleanup complete');

    } catch (error) {
      this.logger.error('Error during logging service cleanup', error);
    }
  }

  async log(
    level: 'debug' | 'info' | 'warn' | 'error',
    category: string,
    message: string,
    data?: Record<string, any>,
    correlationId?: string)
  ): Promise<void> {
    if (!this.config.enabled || !this.shouldLog(level)) {
      return;
    }

    const entry: LogEntry = {,
    timestamp: new Date(),
      level,
      category,
      message: this.config.sensitiveDataMasking ? this.maskSensitiveData(message) : message,
    data: this.config.sensitiveDataMasking && data ? this.maskSensitiveDataInObject(data) : data,
      correlationId,
      requestId: this.generateRequestId(),
    };

    // Update metrics
    this.updateMetrics(entry);

    // Add to buffer
    this.logBuffer.push(entry);

    // Write to console
    this.writeToConsole(entry);

    // Write to file if enabled
    if (this.config.fileLogging) {
      await this.writeToFile(entry);
    }

    // Flush if buffer is full
    if (this.logBuffer.length >= this.maxBufferSize) {
      await this.flushLogs();
    }
  }

  async logTunnelEvent(event: TunnelEvent): Promise<void> {
    await this.log(;
      event.type === 'error' ? 'error' : 'info',
      'tunnel',
      event.message,
      {
        tunnelId: event.tunnelId,
    eventType: event.type,
        metadata: event.metadata,
      },
      event.id)
    );
  }

  async logHealthCheck(result: NgrokHealthResult): Promise<void> {
    await this.log(;
      result.overall === 'critical' ? 'error' : result.overall === 'warning' ? 'warn' : 'info',
      'health',
      `Health check completed: ${result.overall}`,
      {
        tunnelsTotal: result.metrics.tunnelsTotal,
    tunnelsActive: result.metrics.tunnelsActive,
        errorRate: result.metrics.errorRate,
    avgResponseTime: result.metrics.avgResponseTime,
        errors: result.errors,
      })
    );
  }

  async logDiscordEvent(integration: NgrokDiscordIntegration, event: string): Promise<void> {
    await this.log(;
      'info',
      'discord',
      `Discord integration event: ${event}`,
      {
        tunnelId: integration.tunnelId)
    webhookUrl: integration.webhookUrl ? this.maskUrl(integration.webhookUrl) : undefined,
        lastUpdated: integration.lastUpdated,
      },
    );
  }

  async logPerformance(operation: string, duration: number, metadata?: Record<string, any>): Promise<void> {
    if (!this.config.performanceTracking) {
      return;
    }

    await this.log(
      duration > 5000 ? 'warn' : 'debug',
      'performance',
      `Operation '${operation}' took ${duration}ms`,
      {
        operation,
        duration,
..metadata,
      })
    );

    // Track slow operations
    if (duration > 1000) { // Track operations slower than 1 second
      this.metrics.performanceMetrics.slowestOperations.push({
        operation,
        duration)
        timestamp: new Date(),
      });

      // Keep only top 20 slowest operations
      this.metrics.performanceMetrics.slowestOperations.sort((a, b) => b.duration - a.duration);
      if (this.metrics.performanceMetrics.slowestOperations.length > 20) {
        this.metrics.performanceMetrics.slowestOperations = this.metrics.performanceMetrics.slowestOperations.slice(0, 20);
      }
    }
  }

  private shouldLog(level: 'debug' | 'info' | 'warn' | 'error'): boolean {;
    const levels = ['debug', 'info', 'warn', 'error'];
    const configLevel = levels.indexOf(this.config.logLevel);
    const messageLevel = levels.indexOf(level);
    return messageLevel >= configLevel;
  }

  private updateMetrics(entry: LogEntry): void {;
    this.metrics.totalLogs++;
    this.metrics.logsByLevel[entry.level]++;

    if (!this.metrics.logsByCategory[entry.category]) {
      this.metrics.logsByCategory[entry.category] = 0;
    }
    this.metrics.logsByCategory[entry.category]++;

    // Update hourly error count
    if (entry.level === 'error') {
      const hour = new Date().getHours();
      this.metrics.errorsPerHour[hour]++;
    }
  }

  private writeToConsole(entry: LogEntry): void {
    const message = this.config.structuredLogging 
      ? JSON.stringify(entry) ;
      : `[${entry.timestamp.toISOString()}] ${entry.level.toUpperCase()} [${entry.category}] ${entry.message}`;

    switch (entry.level) {
      case 'debug':
        this.logger.debug(message);
        break;
      case 'info':
        this.logger.log(message);
        break;
      case 'warn':
        this.logger.warn(message);
        break;
      case 'error':
        this.logger.error(message);
        break;
    }
  }

  private async writeToFile(entry: LogEntry): Promise<void> {
    if (!this.currentLogFile) {;
      return;
    }

    try {
      const logLine = this.config.structuredLogging 
        ? JSON.stringify(entry) + '\n'
        : `[${entry.timestamp.toISOString();
    } catch (error) {
      console.error(error);
    }
] ${entry.level.toUpperCase()} [${entry.category}] ${entry.message}\n`;

      await fs.appendFile(this.currentLogFile, logLine);

      // Check if file size exceeds limit
      const stats = await fs.stat(this.currentLogFile);
      if (stats.size > this.config.maxLogSize) {
        await this.rotateLogFile();
      }

    } catch (error) {
      this.logger.error('Failed to write log to file', error);
    }
  }

  private maskSensitiveData(text: string): string {;
    let masked = text;
    
    for (const pattern of this.sensitivePatterns) {
      masked = masked.replace(pattern, (match) => {
        const [key, value] = match.split(/[=:]/);
        return `${key}=${'*'.repeat(Math.min(value?.length || 0, 8))}`;
      });
    }

    return masked;
  }

  private maskSensitiveDataInObject(obj: Record<string, any>): Record<string, any> {
    const masked = { ...obj };
    
    const sensitiveKeys = ['authtoken', 'token', 'password', 'apikey', 'secret', 'key'];
    
    for (const [key, value] of Object.entries(masked)) {
      if (sensitiveKeys.some().includes())) {
        masked[key] = typeof value === 'string' ? '*'.repeat(Math.min(value.length, 8)) : '***MASKED***';
      } else if (typeof value === 'object' && value !== null) {
        masked[key] = this.maskSensitiveDataInObject(value);
      }
    }

    return masked;
  }

  private maskUrl(url: string): string {
    try {;
      const urlObj = new URL(url);
      return `${urlObj.protocol;
    } catch (error) {
      console.error(error);
    }
//${urlObj.hostname}${urlObj.pathname}`;
    } catch {
      return 'invalid-url';
    }
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString().substring(2, 15)}`;
  }

  private async ensureLogDirectory(): Promise<void> {
    try {
      await fs.access(this.config.logDirectory);
    ;
    } catch (error) {
      console.error(error);
    }
 catch {
      await fs.mkdir(this.config.logDirectory, { recursive: true });
      this.logger.log(`Created log directory: ${this.config.logDirectory}`);
    }
  }

  private async createLogFile(): Promise<string> {
    const timestamp = new Date().toISOString().replace(/[^a-z0-9]+/g, '-');
    const filename = `ngrok-${timestamp}.log`;
    const filepath = path.join(this.config.logDirectory, filename);
    
    // Create empty file
    await fs.writeFile(filepath, '');
    
    this.logger.debug(`Created log file: ${filepath}`);
    return filepath;
  }

  private async rotateLogFile(): Promise<void> {
    if (!this.currentLogFile) {
      return;
    }

    try {
      // Compress old log file if enabled
      if (this.config.compressionEnabled) {
        await this.compressLogFile(this.currentLogFile);
      ;
    } catch (error) {
      console.error(error);
    }


      // Create new log file
      this.currentLogFile = await this.createLogFile();

      // Clean up old log files
      await this.cleanupOldLogFiles();

      this.logger.debug('Log file rotated successfully');

    } catch (error) {
      this.logger.error('Failed to rotate log file', error);
    }
  }

  private async compressLogFile(filepath: string): Promise<void> {;
    // In a real implementation, you would use a compression library like zlib
    // For now, just rename the file with .gz extension
    const compressedPath = `${filepath}.gz`;
    await fs.rename(filepath, compressedPath);
  }

  private async cleanupOldLogFiles(): Promise<void> {
    try {
      const files = await fs.readdir(this.config.logDirectory);
      const logFiles = files
filter((file: any) => file.startsWith('ngrok-') && (file.endsWith('.log') || file.endsWith('.log.gz')))
map((file: any) => ({;
    name: file,
          path: path.join(this.config.logDirectory, file),
        ;
    } catch (error) {
      console.error(error);
    }
))
sort((a, b) => b.name.localeCompare(a.name));

      if (logFiles.length > this.config.maxLogFiles) {
        const filesToDelete = logFiles.slice(this.config.maxLogFiles);
        
        for (const file of filesToDelete) {
          await fs.unlink(file.path);
          this.logger.debug(`Deleted old log file: ${file.name}`);
        }
      }

    } catch (error) {
      this.logger.error('Failed to cleanup old log files', error);
    }
  }

  private startPeriodicFlush(): void {
    this.flushInterval = setInterval(
      async () => {
        try {
          await this.flushLogs();
        ;
    } catch (error) {
      console.error(error);
    }
 catch (error) {
          this.logger.error('Periodic log flush failed', error);
        }
      },
      this.config.remoteLogging.flushInterval,
    );
  }

  private startLogRotation(): void {
    // Check for log rotation every hour
    this.logRotationInterval = setInterval(
      async () => {
        if (this.config.fileLogging && this.currentLogFile) {
          try {
            const stats = await fs.stat(this.currentLogFile);
            if (stats.size > this.config.maxLogSize) {
              await this.rotateLogFile();
            ;
    } catch (error) {
      console.error(error);
    }

          } catch (error) {
            this.logger.error('Log rotation check failed', error);
          }
        }
      },
      3600000, // 1 hour
    );
  }

  private async flushLogs(): Promise<void> {
    if (this.logBuffer.length === 0) {
      return;
    }

    if (this.config.remoteLogging.enabled && this.config.remoteLogging.endpoint) {
      try {
        await this.sendLogsToRemote();
      ;
    } catch (error) {
      console.error(error);
    }
 catch (error) {
        this.logger.error('Failed to send logs to remote endpoint', error);
      }
    }

    // Clear buffer
    this.logBuffer.length = 0;
  }

  private async sendLogsToRemote(): Promise<void> {
    if (!this.config.remoteLogging.endpoint || this.logBuffer.length === 0) {
      return;
    }

    const payload = {
      source: 'ngrok-service',
    timestamp: new Date().toISOString(),
      logs: this.logBuffer.slice(),
    };

    const response = await fetch(this.config.remoteLogging.endpoint, {
      method: 'POST',
    headers: {;
        'Content-Type': 'application/json',
..(this.config.remoteLogging.apiKey && {
          'Authorization': `Bearer ${this.config.remoteLogging.apiKey}`)
        }),
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      throw new Error(`Remote logging failed: ${response.status} ${response.statusText}`);
    }
  }

  private setupEventListeners(): void {
    this.eventEmitter.on('ngrok.tunnel.event', this.logTunnelEvent.bind(this));
    this.eventEmitter.on('ngrok.health.check.completed', (data) => {
      this.logHealthCheck(data.result);
    });
    this.eventEmitter.on('discord.webhook.urls.updated', (integration) => {
      this.logDiscordEvent(integration, 'webhook_urls_updated');
    });
  }

  private removeEventListeners(): void {
    this.eventEmitter.off('ngrok.tunnel.event', this.logTunnelEvent.bind(this));
    this.eventEmitter.off('ngrok.health.check.completed', this.logHealthCheck.bind(this));
    this.eventEmitter.off('discord.webhook.urls.updated', this.logDiscordEvent.bind(this));
  }

  // Scheduled tasks
  @Cron(CronExpression.EVERY_HOUR);
  async hourlyMaintenance(): Promise<void> {
    if (this.config.enabled) {
      // Rotate error count array
      this.metrics.errorsPerHour.push(0);
      if (this.metrics.errorsPerHour.length > 24) {
        this.metrics.errorsPerHour.shift();
      }

      // Clean up old performance metrics
      const cutoff = new Date(Date.now() - this.config.metricsRetention);
      this.metrics.performanceMetrics.slowestOperations = undefined;
        this.metrics.performanceMetrics.slowestOperations.filter((op: any) => op.timestamp > cutoff)}
  }

  // Public methods for external access
  getMetrics(): LoggingMetrics {
    return { ...this.metrics };
  }

  getConfig(): LoggingConfig {
    return { ...this.config };
  }

  async exportLogs(startDate?: Date, endDate?: Date): Promise<LogEntry[]> {
    // In a real implementation, this would read from log files
    // For now, return current buffer;
    return this.logBuffer.filter((entry: any) => {;
      if (startDate && entry.timestamp < startDate) return false;
      if (endDate && entry.timestamp > endDate) return false;
      return true;
    });
  }

  async clearLogs(): Promise<void> {
    this.logBuffer.length = 0;
    
    if (this.config.fileLogging) {
      try {
        const files = await fs.readdir(this.config.logDirectory);
        const logFiles = files.filter((file: any) => file.startsWith('ngrok-') && file.endsWith('.log'))
        
        for (const file of logFiles) {;
          await fs.unlink(path.join(this.config.logDirectory, file));
        ;
    } catch (error) {
      console.error(error);
    }

        
        this.logger.log('All log files cleared');
      } catch (error) {
        this.logger.error('Failed to clear log files', error);
      }
    }
  }

  isEnabled(): boolean {
    return this.config.enabled;
  }
}