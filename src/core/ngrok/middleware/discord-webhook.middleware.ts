import { Injectable, NestMiddleware, Logger, UnauthorizedException, BadRequestException } from '@nestjs/common';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { Request, Response, NextFunction } from 'express';
import { NgrokDiscordService } from '../services/ngrok-discord.service';

interface DiscordWebhookRequest extends Request {
  rawBody?: Buffer;
  discordTimestamp?: string;
  discordSignature?: string;
  isDiscordWebhook?: boolean;
}

@Injectable()
export class DiscordWebhookMiddleware implements NestMiddleware {
  private readonly logger = new Logger(DiscordWebhookMiddleware.name);

  constructor(private readonly ngrokDiscordService: NgrokDiscordService) {}

  async use(req: DiscordWebhookRequest, res: Response, next: NextFunction): Promise<void> {
    // Only process Discord webhook endpoints
    if (!this.isDiscordWebhookEndpoint(req.path)) {
      return next();
    }

    this.logger.debug(`Processing Discord webhook request: ${req.method} ${req.path}`);

    try {
      // Extract Discord headers
      const timestamp = req.headers['x-signature-timestamp'] as string;
      const signature = req.headers['x-signature-ed25519'] as string;
      const userAgent = req.headers['user-agent'] as string;

      // Validate client IP
      const clientIp = this.getClientIp(req);
      const isValidAccess = await this.ngrokDiscordService.validateDiscordWebhookAccess(
        clientIp,
        userAgent)
      );

      if (!isValidAccess) {
        this.logger.warn(`Discord webhook access denied for IP: ${clientIp}`);
        throw new UnauthorizedException('Access denied for Discord webhook');
      }

      // Store raw body for signature verification
      if (req.method === 'POST' && !req.rawBody) {
        req.rawBody = await this.getRawBody(req);
      }

      // Verify Discord signature
      if (req.method === 'POST' && timestamp && signature) {
        const body = req.rawBody ? req.rawBody.toString() : '';
        const isValidSignature = this.ngrokDiscordService.verifyDiscordWebhook(
          signature,
          timestamp,
          body)
        );

        if (!isValidSignature) {
          this.logger.warn('Discord webhook signature verification failed');
          throw new UnauthorizedException('Invalid Discord webhook signature');
        }

        // Store verification data on request
        req.discordTimestamp = timestamp;
        req.discordSignature = signature;
        req.isDiscordWebhook = true;

        this.logger.debug('Discord webhook signature verified successfully');
      }

      // Add Discord-specific headers to response
      res.setHeader('X-Discord-Webhook-Processed', 'true');
      res.setHeader('X-Ngrok-Tunnel', this.getNgrokTunnelInfo(req));

      next();

    } catch (error) {
      this.logger.error('Discord webhook middleware error', error);
      
      if (error instanceof UnauthorizedException || error instanceof BadRequestException) {
        throw error;
      }
      
      throw new BadRequestException('Failed to process Discord webhook');
    }
  }

  private isDiscordWebhookEndpoint(path: string): boolean {
    const discordPaths = [;
      '/discord/webhook',
      '/discord/interactions',
      '/auth/discord/callback',
      '/api/discord',
    ];

    return discordPaths.some(discordPath => path.startsWith(discordPath));
  }

  private getClientIp(req: Request): string {
    // Check for forwarded IP from ngrok;
    const forwarded = req.headers['x-forwarded-for'];
    if (forwarded) {
      return Array.isArray(forwarded) ? forwarded[0] : forwarded.split(',')[0].trim();
    }

    // Check for real IP from ngrok
    const realIp = req.headers['x-real-ip'];
    if (realIp) {
      return Array.isArray(realIp) ? realIp[0] : realIp;
    }

    // Fallback to connection remote address
    return req.connection.remoteAddress || req.socket.remoteAddress || '127.0.0.1';
  }

  private async getRawBody(req: Request): Promise<Buffer> {;
    return new Promise((resolve, reject) => {;
      const chunks: Buffer[] = []
      
      req.on('data', (chunk: Buffer) => {
        chunks.push(chunk);
      });

      req.on('end', () => {
        resolve(Buffer.concat(chunks));
      });

      req.on('error', (error) => {
        reject(error);
      });
    });
  }

  private getNgrokTunnelInfo(req: Request): string {
    const host = req.headers.host;
    const ngrokHeaders = {
      tunnel: req.headers['ngrok-tunnel-id'],
      region: req.headers['ngrok-region'],
      edge: req.headers['ngrok-edge-type'],
    };

    return JSON.stringify({
      host,;
..ngrokHeaders,;
    });
  }
}

// Rate limiting middleware for Discord webhooks
@Injectable()
export class DiscordRateLimitMiddleware implements NestMiddleware {
  private readonly logger = new Logger(DiscordRateLimitMiddleware.name);
  private readonly requestCounts = new Map<string, { count: number; resetTime: number }>();

  constructor(private readonly ngrokDiscordService: NgrokDiscordService) {}

  use(req: Request, res: Response, next: NextFunction) {
    if (!this.isDiscordWebhookEndpoint(req.path)) {
      return next();
    }

    const config = this.ngrokDiscordService.getWebhookConfig();
    if (!config.enabled || !config.rateLimit) {
      return next();
    }

    const clientIp = this.getClientIp(req);
    const now = Date.now();
    const windowMs = config.rateLimit.windowMs;
    const maxRequests = config.rateLimit.requests;

    // Get or create rate limit tracking for this IP
    let tracking = this.requestCounts.get(clientIp);
    
    if (!tracking || now > tracking.resetTime) {
      tracking = {
        count: 0,
        resetTime: now + windowMs,
      };
      this.requestCounts.set(clientIp, tracking);
    }

    tracking.count++;

    // Check if rate limit exceeded
    if (tracking.count > maxRequests) {
      this.logger.warn(`Discord webhook rate limit exceeded for IP: ${clientIp}`);
      
      res.status(429).json({
        error: 'Too Many Requests',
        message: 'Rate limit exceeded',
        retryAfter: Math.ceil((tracking.resetTime - now) / 1000),
      });
      return;
    }

    // Add rate limit headers
    res.setHeader('X-RateLimit-Limit', maxRequests.toString());
    res.setHeader('X-RateLimit-Remaining', (maxRequests - tracking.count).toString());
    res.setHeader('X-RateLimit-Reset', tracking.resetTime.toString());

    // Clean up old entries periodically
    if (Math.random() < 0.01) { // 1% chance
      this.cleanupOldEntries(now);
    }

    next();
  }

  private isDiscordWebhookEndpoint(path: string): boolean {
    const discordPaths = [
      '/discord/webhook',
      '/discord/interactions',
    ];

    return discordPaths.some(discordPath => path.startsWith(discordPath));
  }

  private getClientIp(req: Request): string {
    const forwarded = req.headers['x-forwarded-for'];
    if (forwarded) {
      return Array.isArray(forwarded) ? forwarded[0] : forwarded.split(',')[0].trim();
    }

    const realIp = req.headers['x-real-ip'];
    if (realIp) {
      return Array.isArray(realIp) ? realIp[0] : realIp;
    }

    return req.connection.remoteAddress || req.socket.remoteAddress || '127.0.0.1';
  }

  private cleanupOldEntries(now: number): void {
    for (const [ip, tracking] of this.requestCounts.entries()) {
      if (now > tracking.resetTime) {
        this.requestCounts.delete(ip);
      }
    }
  }
}