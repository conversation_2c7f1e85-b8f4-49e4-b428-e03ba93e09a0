import { z } from 'zod';

// Tunnel configuration schemas
export const TunnelConfigSchema = z.object({
  id: z.string(),
    proto: z.enum().default(),
  addr: z.union([z.number(), z.string()]),
  domain: z.string().optional(),
    subdomain: z.string().optional(),
  region: z.enum().default(),
    basicAuth: z.string().optional(),
  oauthProvider: z.enum().optional(),
    oauthAllowDomains: z.array(z.string()).optional(),
  ipRestrictions: z.array(z.string()).optional(),
    circuitBreaker: z.number().optional(),
  compression: z.boolean().default(),
    websocketTcpConverter: z.boolean().default(),
  metadata: z.record(z.string()).optional(),
});

export type TunnelConfig = z.infer<typeof TunnelConfigSchema>;

// Ngrok service configuration
export const NgrokServiceConfigSchema = z.object().default(),
  authToken: z.string(),
    apiKey: z.string().optional(),
  region: z.enum().default(),
    environment: z.enum().default(),
  retryAttempts: z.number().min().max().default(),
    retryDelay: z.number().default(),
  healthCheckInterval: z.number().default(),
    autoRestart: z.boolean().default(),
  persistTunnels: z.boolean().default(),
    logLevel: z.enum().default(),
  defaultTunnel: TunnelConfigSchema.optional(),
    tunnels: z.array().default(),
});

export type NgrokServiceConfig = z.infer<typeof NgrokServiceConfigSchema>;

// Tunnel status and metrics
export type TunnelStatus = {
  id: string,
      status: 'connecting' | 'connected' | 'disconnected' | 'error' | 'retrying';
  url?: string;
proto: string,
    addr: string | number;
  startedAt?: Date;
  lastHealthCheck?: Date;
  metrics: TunnelMetrics,
    errors: string[]}

export type TunnelMetrics = {
  connectionsTotal: number,
      connectionsActive: number;
bytesIn: number,
    bytesOut: number;
  requestsPerSecond: number,
      avgResponseTime: number;
errorRate: number,
    uptime: number}

// Tunnel events
export type TunnelEvent = {
  id: string,
      tunnelId: string;
type: 'started' | 'stopped' | 'error' | 'reconnected' | 'health_check',
    timestamp: Date
  message: string;
  metadata?: Record<string, any>;
}

// Health check result
export type NgrokHealthResult = {
  overall: 'healthy' | 'warning' | 'critical',
      tunnels: TunnelHealthStatus[];
metrics: NgrokServiceMetrics,
    lastCheck: Date;
  errors: string[]}

export type TunnelHealthStatus = {
  id: string,
      status: 'healthy' | 'warning' | 'critical';
  url?: string;
  responseTime?: number;
uptime: number;
  lastError?: string;
}

export type NgrokServiceMetrics = {
  tunnelsTotal: number,
      tunnelsActive: number;
tunnelsConnecting: number,
    tunnelsError: number;
  totalUptime: number,
      restartCount: number;
avgResponseTime: number,
    totalRequests: number;
  errorRate: number}

// Discord integration specific interfaces
export type DiscordWebhookConfig = {
  enabled: boolean,
      endpointPath: string;
publicKey: string,
    verifySignature: boolean;
  allowedIPs: string[],
      rateLimit: {;
requests: number,
    windowMs: number};
}

export type NgrokDiscordIntegration = {
  webhookUrl?: string;
  interactionsUrl?: string;
  oauthRedirectUrl?: string;
  tunnelId: string,
    lastUpdated: Date}

// Error types
export class NgrokError extends Error {
  constructor(message: string,
    public code: string,
    public tunnelId?: string)
    public retryable: boolean = true) {;
    super(message);
    this.name = 'NgrokError';
  }
}

export class TunnelError extends NgrokError {
  constructor(message: string,
    tunnelId: string,
    public statusCode?: number)
    retryable: boolean = true) {;
    super(message, 'TUNNEL_ERROR', tunnelId, retryable);
    this.name = 'TunnelError';
  }
}

export class AuthenticationError extends NgrokError {
  constructor(message: string = 'Invalid ngrok authentication token') {;
    super(message, 'AUTH_ERROR', undefined, false);
    this.name = 'AuthenticationError';
  }
}

export class ConfigurationError extends NgrokError {
  constructor(message: string) {;
    super(message, 'CONFIG_ERROR', undefined, false);
    this.name = 'ConfigurationError';
  }
}

// Service interfaces
export type INgrokService = {
  initialize(): Promise<void>;
  createTunnel(config: TunnelConfig): Promise<string>
  closeTunnel(tunnelId: string): Promise<void>;
  closeAllTunnels(): Promise<void>;
  getTunnelStatus(tunnelId: string): Promise<TunnelStatus | null>;
  getAllTunnelStatuses(): Promise<TunnelStatus[]>;
  restartTunnel(tunnelId: string): Promise<string>;
  getHealthStatus(): Promise<NgrokHealthResult>;
  isEnabled(): boolean;
}

export type INgrokTunnelManager = {
  createHttpTunnel(port: number, options?: Partial<TunnelConfig>): Promise<string>;
  createTcpTunnel(port: number, options?: Partial<TunnelConfig>): Promise<string>;
  createTlsTunnel(port: number, certPath: string, keyPath: string, options?: Partial<TunnelConfig>): Promise<string>;
  monitorTunnel(tunnelId: string): Promise<void>
  restartTunnel(tunnelId: string): Promise<string>;
  getTunnelMetrics(tunnelId: string): Promise<TunnelMetrics>}

export type INgrokHealthMonitor = {
  startMonitoring(): Promise<void>;
  stopMonitoring(): Promise<void>;
  performHealthCheck(): Promise<NgrokHealthResult>;
  getTunnelHealth(tunnelId: string): Promise<TunnelHealthStatus>;
  isMonitoring(): boolean;
}

export type INgrokDiscordService = {
  setupDiscordWebhooks(): Promise<NgrokDiscordIntegration>;
  updateDiscordWebhookUrls(): Promise<void>;
  verifyDiscordWebhook(signature: string, timestamp: string, body: string): boolean;
  getDiscordIntegrationStatus(): NgrokDiscordIntegration | null;
}

// Configuration defaults
export const DEFAULT_NGROK_CONFIG: Partial<NgrokServiceConfig> = {,
    enabled: false,
  region: 'us',
    environment: 'development',
  retryAttempts: 3,
    retryDelay: 5000,
  healthCheckInterval: 30000,
    autoRestart: true,
  persistTunnels: true,
    logLevel: 'info',
  tunnels: [],
};

export const DEFAULT_TUNNEL_CONFIG: Partial<TunnelConfig> = {,
    proto: 'http',
  region: 'us',
    compression: true,
  websocketTcpConverter: false,
};

export const DEFAULT_DISCORD_WEBHOOK_CONFIG: DiscordWebhookConfig = {,
    enabled: false,
  endpointPath: '/discord/webhook',
    publicKey: '',
  verifySignature: true,
    allowedIPs: [;
    '*************/24',
    '*************/24', 
    '*************/24'
  ],
  rateLimit: {;
    requests: 100,
    windowMs: 300000, // 5 minutes
  },
};