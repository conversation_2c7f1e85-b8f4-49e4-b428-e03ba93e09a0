import { Request } from 'express';

// Core services
export * from './services/ngrok.service';
export * from './services/ngrok-discord.service';
export * from './services/ngrok-health-monitor.service';
export * from './services/ngrok-persistence.service';
export * from './services/ngrok-logging.service';

// Module
export * from './ngrok.module';

// Controllers
export * from './controllers/ngrok.controller';

// Interfaces and types
export * from './interfaces/ngrok.interface';

// Configuration
export * from './config/ngrok.config';

// Guards
export * from './guards/discord-webhook.guard';

// Middleware
export * from './middleware/discord-webhook.middleware';

// Decorators
export * from './decorators/discord-webhook.decorator';

// Interceptors
export * from './interceptors/discord-webhook.interceptor';

// Common interface for Discord webhook requests
export interface DiscordWebhookRequest extends Request {
  rawBody?: Buffer;
  discordTimestamp?: string;
  discordSignature?: string;
  isDiscordWebhook?: boolean}

// Re-export common types for convenience
export type {
  TunnelConfig,
  TunnelStatus,
  NgrokServiceConfig,
  NgrokHealthResult,
  DiscordWebhookConfig,
  NgrokDiscordIntegration,
  TunnelEvent,
  TunnelMetrics,
} from './interfaces/ngrok.interface';

// Re-export error classes
export {
  NgrokError,
  TunnelError,
  AuthenticationError,
  ConfigurationError} from './interfaces/ngrok.interface';

// Re-export constants
export {
  DEFAULT_NGROK_CONFIG,
  DEFAULT_TUNNEL_CONFIG,
  DEFAULT_DISCORD_WEBHOOK_CONFIG} from './interfaces/ngrok.interface';

// Re-export Discord constants
export {
  DISCORD_INTERACTION_TYPES,
  DISCORD_RESPONSE_TYPES} from './decorators/discord-webhook.decorator';