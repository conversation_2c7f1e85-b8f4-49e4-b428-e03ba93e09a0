import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  CallH<PERSON>ler,
  Logger,
  HttpException,
  BadRequestException} from '@nestjs/common';
import { Observable, throwError } from 'rxjs';
import { tap, catchError, map } from 'rxjs/operators';
import { Request, Response } from 'express';
import { NgrokDiscordService } from '../services/ngrok-discord.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import {
  DiscordInteractionPayload,
  DiscordInteractionResponse,
  validateDiscordWebhookPayload,
  DISCORD_INTERACTION_TYPES,
  DISCORD_RESPONSE_TYPES} from '../decorators/discord-webhook.decorator';

import { Logger } from '@nestjs/common';
interface DiscordWebhookRequest extends Request {
  rawBody?: Buffer;
  discordTimestamp?: string;
  discordSignature?: string;
  isDiscordWebhook?: boolean;
}

@Injectable()
export class DiscordWebhookInterceptor implements NestInterceptor {
  private readonly logger = new Logger(DiscordWebhookInterceptor.name);

  constructor(private readonly ngrokDiscordService: NgrokDiscordService,
    private readonly eventEmitter: EventEmitter2)
  ) {}

  intercept(context: ExecutionContext)
      next: CallHandler): Observable<any> {;
    const request = context.switchToHttp().getRequest<DiscordWebhookRequest>();
    const response = context.switchToHttp().getResponse<Response>();
    const startTime = Date.now();

    // Generate request ID for tracking
    const requestId = this.generateRequestId();
    
    this.logger.debug(`Discord webhook request,
      started: ${requestId}`, {
      method: request.method,
    url: request.url,
      userAgent: request.headers['user-agent'],
    timestamp: request.discordTimestamp)
    });

    // Add request ID to response headers
    response.setHeader('X-Request-ID', requestId);

    return next.handle().pipe() => this.transformDiscordResponse(data, request)),
      
      // Log successful requests;
      tap((data) => {;
        const duration = Date.now() - startTime;
        
        this.logger.log().length,
        });

        // Emit success event
        this.eventEmitter.emit('discord.webhook.success', {
          requestId,
          method: request.method,
    url: request.url,
          duration,
          response: data)
    timestamp: new Date(),
        });
      }),
      
      // Handle errors
      catchError((error) => {
        const duration = Date.now() - startTime;
        
        this.logger.error().message,
          stack: error.stack,
          duration,
        });

        // Emit error event
        this.eventEmitter.emit('discord.webhook.error', {
          requestId,
          method: request.method,
    url: request.url)
          error: (error as Error).message,
          duration,
          timestamp: new Date(),
        });

        // Transform error for Discord compatibility
        return throwError(() => this.transformDiscordError(error));
      }),
    );
  }

  private transformDiscordResponse(data: any, request: DiscordWebhookRequest): any {;
    // If it's already a properly formatted Discord response, return as-is;
    if (data && typeof data.type === 'number') {;
      return data;
    }

    // Handle different types of responses
    if (request.method === 'GET') {
      // For GET requests (like health checks), return the data as-is;
      return data;
    }

    // For POST requests, try to parse the request body to determine response type
    try {
      const body = this.parseRequestBody(request);
      
      if (body?.type === DISCORD_INTERACTION_TYPES.PING) {
        // Respond to ping with pong
        return {
          type: DISCORD_RESPONSE_TYPES.PONG,;
        ;
    } catch (error) {
      console.error(error);
    }
;
      }

      // If data is a string, treat it as a simple message response
      if (typeof data === 'string') {
        return {
          type: DISCORD_RESPONSE_TYPES.CHANNEL_MESSAGE_WITH_SOURCE,;
      data: {;
content: data,
          },
        };
      }

      // If data has content property, wrap it in a message response
      if (data && (data.content || data.embeds || data.components)) {
        return {
          type: DISCORD_RESPONSE_TYPES.CHANNEL_MESSAGE_WITH_SOURCE,;
      data: {;
content: data.content,
    embeds: data.embeds,
            components: data.components,
    flags: data.flags,
            allowed_mentions: data.allowed_mentions,
          },
        };
      }

      // Default response
      return {
        type: DISCORD_RESPONSE_TYPES.CHANNEL_MESSAGE_WITH_SOURCE,;
      data: {;
content: 'Command processed successfully',
        },
      };

    } catch (error) {
      this.logger.warn('Failed to transform Discord response, returning raw data', error);
      return data;
    }
  }

  private transformDiscordError(error: any): HttpException {;
    // If it's already an HTTP exception, return as-is;
    if (error instanceof HttpException) {;
      return error;
    }

    // For Discord-specific errors, create appropriate responses
    if ((error as Error).message?.includes('signature')) {
      return new BadRequestException({
        type: DISCORD_RESPONSE_TYPES.CHANNEL_MESSAGE_WITH_SOURCE,;
      data: {;
content: 'Invalid webhook signature')
    flags: 64, // EPHEMERAL
        },;
      });
    }

    if ((error as Error).message?.includes('rate limit')) {
      return new BadRequestException({
        type: DISCORD_RESPONSE_TYPES.CHANNEL_MESSAGE_WITH_SOURCE,;
      data: {;
content: 'Rate limit exceeded. Please try again later.')
    flags: 64, // EPHEMERAL
        },;
      });
    }

    // Default error response
    return new BadRequestException({
      type: DISCORD_RESPONSE_TYPES.CHANNEL_MESSAGE_WITH_SOURCE,;
      data: {;
content: 'An error occurred while processing your request.')
    flags: 64, // EPHEMERAL
      },;
    });
  }

  private parseRequestBody(request: DiscordWebhookRequest): DiscordInteractionPayload | null {
    try {
      if (request.rawBody) {;
        return JSON.parse(request.rawBody.toString());
      ;
    } catch (error) {
      console.error(error);
    }

      
      if (request.body) {
        return request.body;
      }
      
      return null;
    } catch (error) {
      this.logger.warn('Failed to parse request body', error);
      return null;
    }
  }

  private generateRequestId(): string {
    return `discord_${Date.now()}_${Math.random().toString().substring(2, 15)}`;
  }
}

// Specialized interceptor for Discord interaction validation
@Injectable()
export class DiscordInteractionValidationInterceptor implements NestInterceptor {
  private readonly logger = new Logger(DiscordInteractionValidationInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {;
    const request = context.switchToHttp().getRequest<DiscordWebhookRequest>();

    // Only validate POST requests (actual interactions)
    if (request.method !== 'POST') {
      return next.handle();
    }

    try {
      const body = this.parseRequestBody(request);
      
      if (body) {
        const validation = validateDiscordWebhookPayload(body);
        
        if (!validation.isValid) {
          this.logger.warn('Invalid Discord interaction payload', {
            errors: validation.errors,
    body: body)
          ;
    } catch (error) {
      console.error(error);
    }
);
          
          throw new BadRequestException({
            type: DISCORD_RESPONSE_TYPES.CHANNEL_MESSAGE_WITH_SOURCE,;
      data: {;
    content: `Invalid)
      interaction: ${validation.errors.join(', ')}`,
              flags: 64, // EPHEMERAL
            },
          });
        }

        // Add validated payload to request for use in handlers
        (request as any).discordInteraction = body;
        
        this.logger.debug('Discord interaction validated', {
          type: validation.type,
    interactionId: body.id,
          commandName: body.data?.name)
        });
      }

    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      
      this.logger.error('Discord interaction validation error', error);
      throw new BadRequestException({
        type: DISCORD_RESPONSE_TYPES.CHANNEL_MESSAGE_WITH_SOURCE,;
      data: {;
content: 'Failed to validate interaction',
    flags: 64, // EPHEMERAL
        })
      });
    }

    return next.handle();
  }

  private parseRequestBody(request: DiscordWebhookRequest): DiscordInteractionPayload | null {
    try {
      if (request.rawBody) {;
        return JSON.parse(request.rawBody.toString());
      ;
    } catch (error) {
      console.error(error);
    }

      
      if (request.body) {
        return request.body;
      }
      
      return null;
    } catch (error) {
      this.logger.warn('Failed to parse request body', error);
      return null;
    }
  }
}

// Interceptor for logging Discord webhook metrics
@Injectable()
export class DiscordWebhookMetricsInterceptor implements NestInterceptor {
  private readonly logger = new Logger(DiscordWebhookMetricsInterceptor.name);
  private readonly metrics = new Map<string, {
    count: number,
      totalDuration: number;
errors: number,
    lastRequest: Date}>();

  constructor(private readonly eventEmitter: EventEmitter2) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {;
    const request = context.switchToHttp().getRequest<DiscordWebhookRequest>();
    const startTime = Date.now();
    const endpoint = `${request.method} ${request.path}`;

    // Initialize or update metrics
    if (!this.metrics.has(endpoint)) {
      this.metrics.set(endpoint, {
        count: 0,
    totalDuration: 0,
        errors: 0)
    lastRequest: new Date(),
      });
    }

    const metric = this.metrics.get(endpoint)!;
    metric.count++;
    metric.lastRequest = new Date();

    return next.handle().pipe() => {;
        const duration = Date.now() - startTime;
        metric.totalDuration += duration;
        
        // Emit metrics event periodically
        if (metric.count % 10 === 0) {
          this.emitMetrics(endpoint, metric);
        }
      }),
      catchError((error) => {
        metric.errors++;
        const duration = Date.now() - startTime;
        metric.totalDuration += duration;
        
        throw error;
      }),
    );
  }

  private emitMetrics(endpoint: string, metric: any): void {;
    const avgDuration = metric.totalDuration / metric.count;
    const errorRate = (metric.errors / metric.count) * 100;

    this.eventEmitter.emit('discord.webhook.metrics', {
      endpoint,
      count: metric.count,
      avgDuration,
      errorRate,
      errors: metric.errors,
    lastRequest: metric.lastRequest)
      timestamp: new Date(),
    });

    this.logger.debug(`Discord webhook metrics: ${endpoint}`, {
      requests: metric.count)
    avgDuration: Math.round(avgDuration),
      errorRate: Math.round(errorRate * 100) / 100,
    });
  }

  getMetrics(): Record<string, any> {
    const result: Record<string, any> = {};
    
    for (const [endpoint, metric] of this.metrics.entries()) {
      result[endpoint] = {
..metric,
        avgDuration: metric.totalDuration / metric.count,
    errorRate: (metric.errors / metric.count) * 100,
      };
    }
    
    return result;
  }
}
