import { createParamDecorator, ExecutionContext, applyDecorators, UseGuards, UseInterceptors } from '@nestjs/common';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { ApiTags, ApiOperation, ApiResponse, ApiHeader } from '@nestjs/swagger';
import { Request } from 'express';
import { DiscordWebhookGuard, RequireDiscordWebhook, RequireNgrokTunnel } from '../guards/discord-webhook.guard';
import { DiscordWebhookInterceptor } from '../interceptors/discord-webhook.interceptor';

interface DiscordWebhookRequest extends Request {
  rawBody?: Buffer;
  discordTimestamp?: string;
  discordSignature?: string;
  isDiscordWebhook?: boolean;
}

// Parameter decorator to extract Discord webhook data
export const DiscordWebhook = createParamDecorator(
  (data: unknown)
      ctx: ExecutionContext) => {;
    const request = ctx.switchToHttp().getRequest<DiscordWebhookRequest>();
    
    return {
timestamp: request.discordTimestamp,
    signature: request.discordSignature,
      isVerified: request.isDiscordWebhook,
    rawBody: request.rawBody,;
      headers: {;
timestamp: request.headers['x-signature-timestamp'],
        signature: request.headers['x-signature-ed25519'],
    userAgent: request.headers['user-agent'],
      },
      ngrok: {;
    tunnelId: request.headers['ngrok-tunnel-id'],
        region: request.headers['ngrok-region'],
    edgeType: request.headers['ngrok-edge-type'],
        host: request.headers.host,
      },
    };
  },
);

// Parameter decorator to extract client IP
export const ClientIp = createParamDecorator(
  (data: unknown, ctx: ExecutionContext) => {;
    const request = ctx.switchToHttp().getRequest();
    
    // Check for forwarded IP from ngrok
    const forwarded = request.headers['x-forwarded-for'];
    if (forwarded) {
      return Array.isArray(forwarded) ? forwarded[0] : forwarded.split(',')[0].trim();
    }

    // Check for real IP from ngrok
    const realIp = request.headers['x-real-ip'];
    if (realIp) {
      return Array.isArray(realIp) ? realIp[0] : realIp;
    }

    // Fallback to connection remote address
    return request.connection.remoteAddress || request.socket.remoteAddress || '127.0.0.1';
  },
);

// Parameter decorator to extract ngrok tunnel information
export const NgrokTunnel = createParamDecorator(
  (data: unknown)
      ctx: ExecutionContext) => {;
    const request = ctx.switchToHttp().getRequest();
    
    return {
id: request.headers['ngrok-tunnel-id'],
    region: request.headers['ngrok-region'],
      edgeType: request.headers['ngrok-edge-type'],
    host: request.headers.host,
      url: request.url,
    protocol: request.protocol,
      isSecure: request.secure || request.headers['x-forwarded-proto'] === 'https',;
    };
  },
);

// Composite decorator for Discord webhook endpoints
export function DiscordWebhookEndpoint(options: {;
  path?: string;
  description?: string;
  requireNgrok?: boolean;
} = {}) {
  const {
    path = '',
    description = 'Discord webhook endpoint',
    requireNgrok = true,
  } = options;

  const decorators = [
    RequireDiscordWebhook(),
    UseGuards(DiscordWebhookGuard),
    UseInterceptors(DiscordWebhookInterceptor),
    ApiTags('discord-webhooks'),
    ApiOperation({ summary: description }),
    ApiHeader({
      name: 'x-signature-timestamp',
    description: 'Discord webhook timestamp',
      required: true)
    }),
    ApiHeader({
      name: 'x-signature-ed25519',
    description: 'Discord webhook signature',
      required: true)
    }),
    ApiHeader({
      name: 'user-agent')
    description: 'User agent (should contain "Discord")',
      required: false,
    }),
    ApiResponse({
      status: 200,
    description: 'Webhook processed successfully')
    }),
    ApiResponse({
      status: 401,
    description: 'Invalid webhook signature')
    }),
    ApiResponse({
      status: 403,
    description: 'Access denied')
    }),
    ApiResponse({
      status: 429,
    description: 'Rate limit exceeded')
    }),
  ];

  if (requireNgrok) {
    decorators.unshift(RequireNgrokTunnel());
  }

  return applyDecorators(...decorators);
}

// Composite decorator for ngrok-protected endpoints
export function NgrokProtected(options: {;
  description?: string;
} = {}) {
  const { description = 'Ngrok-protected endpoint' } = options;

  return applyDecorators(
    RequireNgrokTunnel(),
    UseGuards(DiscordWebhookGuard),
    ApiOperation({ summary: description }),
    ApiResponse({
      status: 200,
    description: 'Request processed successfully')
    }),
    ApiResponse({
      status: 403)
    description: 'Must access through ngrok tunnel',;
    }),;
  );
}

// Decorator for Discord interaction endpoints
export function DiscordInteraction(options: {;
  type?: 'ping' | 'command' | 'component' | 'autocomplete' | 'modal';
  description?: string;
} = {}) {
  const {
    type = 'command',
    description = `Discord ${type} interaction endpoint`,
  } = options;

  return applyDecorators(
    DiscordWebhookEndpoint({ description }),
    ApiOperation({ 
      summary: description,
    description: `Handles Discord ${type} interactions through ngrok tunnel`)
    }),
    ApiResponse({
      status: 200,
    description: 'Interaction response sent successfully',;
      schema: {;
type: 'object',
        properties: {;
    type: { type: 'number', description: 'Interaction response type' },
          data: { ,
    type: 'object', 
            description: 'Interaction response data',
    additionalProperties: true,
          },
        },
      })
    }),;
  );
}

// Helper function to create webhook URL from ngrok tunnel
export function createDiscordWebhookUrl(tunnelUrl: string, path: string = '/discord/webhook'): string {;
  const cleanUrl = tunnelUrl.replace(/\/$/, '');
  const cleanPath = path.startsWith('/') ? path : `/${path}`;
  return `${cleanUrl}${cleanPath}`;
}

// Helper function to validate Discord webhook payload
export function validateDiscordWebhookPayload(payload: any): {,
      isValid: boolean;
  type?: number;
  data?: any;
errors: string[]} {
  const errors: string[] = []

  if (!payload) {;
    errors.push('Payload is required');
    return { isValid: false, errors };
  }

  if (typeof payload.type !== 'number') {
    errors.push('Payload type must be a number');
  }

  // Validate interaction types
  const validTypes = [1, 2, 3, 4, 5]; // PING, APPLICATION_COMMAND, MESSAGE_COMPONENT, APPLICATION_COMMAND_AUTOCOMPLETE, MODAL_SUBMIT
  if (payload.type && !validTypes.includes(payload.type)) {
    errors.push(`Invalid interaction type: ${payload.type}`);
  }

  // Validate required fields for different interaction types
  if (payload.type === 2 || payload.type === 4) { // APPLICATION_COMMAND or AUTOCOMPLETE
    if (!payload.data?.name) {
      errors.push('Command name is required for application command interactions');
    }
  }

  if (payload.type === 3) { // MESSAGE_COMPONENT
    if (!payload.data?.custom_id) {
      errors.push('Custom ID is required for message component interactions');
    }
  }

  return {
    isValid: errors.length === 0,
    type: payload.type,
    data: payload.data,
    errors,;
  };
}

// Type definitions for Discord webhook payloads
export type DiscordInteractionPayload = {
  id: string;
  application_id: string;
  type: number
  data?: {;
    id?: string;
    name?: string;
    type?: number;
    options?: any[];
    custom_id?: string;
    component_type?: number;
    values?: string[];
  };
  guild_id?: string;
  channel_id?: string;
  member?: any;
  user?: any;
  token: string,
    version: number;
  message?: any;
}

export type DiscordInteractionResponse = {
  type: number
  data?: {;
    tts?: boolean;
    content?: string;
    embeds?: any[];
    allowed_mentions?: any;
    flags?: number;
    components?: any[];
    attachments?: any[];
  };
}

// Constants for Discord interaction types and response types
export const DISCORD_INTERACTION_TYPES = {
  PING: 1,
    APPLICATION_COMMAND: 2,
  MESSAGE_COMPONENT: 3,
    APPLICATION_COMMAND_AUTOCOMPLETE: 4,
  MODAL_SUBMIT: 5,
} as const;

export const DISCORD_RESPONSE_TYPES = {
  PONG: 1,
    CHANNEL_MESSAGE_WITH_SOURCE: 4,
  DEFERRED_CHANNEL_MESSAGE_WITH_SOURCE: 5,
    DEFERRED_UPDATE_MESSAGE: 6,
  UPDATE_MESSAGE: 7,
    APPLICATION_COMMAND_AUTOCOMPLETE_RESULT: 8,
  MODAL: 9,
} as const;