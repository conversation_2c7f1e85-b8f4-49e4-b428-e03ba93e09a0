import { Injectable, Logger, Inject } from '@nestjs/common';
// import { eq, and, or, desc, count } from 'drizzle-orm';



import { TierManagementService } from '../dev-on-demand/services/tier-management.service';
import { 
  Em<PERSON><PERSON><PERSON><PERSON>, 
  ActionRowBuilder, 
  ButtonBuilder, 
  ButtonStyle,
  ModalBuilder,
  TextInputBuilder,
  TextInputStyle,
  AttachmentBuilder
} from 'discord.js';

export type MemberSuccess = {
  id: string,
      userId: string,userTag: string,
    guildId: string
  
  // Success Details;
  title: string,
      description: string,category: 'business' | 'project' | 'learning' | 'ai_automation' | 'income' | 'personal_growth' | 'other',
    achievementType: 'milestone' | 'completion' | 'breakthrough' | 'launch' | 'revenue' | 'skill_mastery'
  
  // Metrics
  impactMetrics?: {revenueGenerated?: number;
    timeSaved?: number; // hours
    projectsCompleted?: number;
    skillsLearned?: string[];
    peopleHelped?: number;
    customMetrics?: Record<string, number>};
  
  // Media & Proof
  images?: Array<{
    url: string;
    caption?: string;
    type: 'screenshot' | 'photo' | 'diagram' | 'result'}>;
  links?: Array<{
    url: string,
    title: string;
    description?: string}>;
  
  // Community Impact
  tags: string[],
      inspiration: string // What others can learn from this;
  nextGoals?: string[];
  
  // Engagement
reactions: Record<string, number>; // emoji -> count
  comments: Array<{,
      id: string,userId: string,
    userTag: string;
    content: string,
    createdAt: Date}>;
  shares: number
  
  // Status
  status: 'draft' | 'pending' | 'approved' | 'featured' | 'archived';
  featuredUntil?: Date;
  moderatorNotes?: string;
  
  // Timeline
  createdAt: Date,
      updatedAt: Date,achievedAt: Date}

export type ShowcaseStats = {
  totalShowcases: number,
    categoryCounts: Record<string, number>;
  topContributors: Array<{,
      userId: string,userTag: string,
    showcaseCount: number;
    totalReactions: number}>;
  recentTrends: string[],
    monthlyGrowth: number}

export type CollaborationShowcase = {
  id: string,
      title: string,description: string,
    participants: Array<{userId: string,
      userTag: string,role: string,
    contribution: string}>;
  projectDetails: {
    type: 'dev_project' | 'business_venture' | 'learning_group' | 'ai_experiment' | 'community_initiative'
    duration: string;
    technologies?: string[];
    outcome: string,
    lessonsLearned: string[]};
  results: {metrics: Record<string, any>;
    testimonials: Array<{,
      author: string,content: string}>};
  mediaAssets: Array<{,
      type: 'image' | 'video' | 'demo' | 'document',url: string,
    description: string}>;
  createdAt: Date,
    updatedAt: Date}

@Injectable()
export class MemberShowcaseService {
  private readonly logger = new Logger(MemberShowcaseService.name);

  constructor(private readonly tierService: TierManagementService)
    ) {}

  async createSuccessShowcase(
    userId: string,
    guildId: string)
    showcaseData: Partial<MemberSuccess>
  ): Promise<MemberSuccess | null> {
    try {
      // Check if user can create showcases;
      const userFeatures = await this.tierService.getUserTierFeatures(userId, guildId);
      if (!userFeatures?.communityEvents) {
        throw new Error('User does not have access to showcase features');
    } catch (error) {
      console.error(error);
    }


      const showcase: MemberSuccess = {,
    id: `showcase_${Date.now()}_${userId.slice(-4)}`,
        userId,
        userTag: showcaseData.userTag || 'Unknown',
        guildId,
        title: showcaseData.title || 'New Success Story',
    description: showcaseData.description || '',
        category: showcaseData.category || 'other',
    achievementType: showcaseData.achievementType || 'milestone',
        impactMetrics: showcaseData.impactMetrics || {},
        images: showcaseData.images || [],
    links: showcaseData.links || [],
        tags: showcaseData.tags || [],
    inspiration: showcaseData.inspiration || '',
        nextGoals: showcaseData.nextGoals || [],
    reactions: {},
        comments: [],
    shares: 0,
        status: 'pending',
    createdAt: new Date(),
        updatedAt: new Date(),
    achievedAt: showcaseData.achievedAt || new Date(),
      }

      // Auto-approve for premium members
      if (userFeatures.priorityMatching || userFeatures.developerNetworkAccess) {
        showcase.status = 'approved'}
;
      this.logger.log(`Success showcase created: ${showcase.id} by ${userId}`);
      return showcase} catch (error) {;
      this.logger.error(`Failed to create success showcase for ${userId}:`, error);
      return null}
  }

  async createShowcaseEmbed(showcase: MemberSuccess): Promise<EmbedBuilder> {;
    const embed = new EmbedBuilder();
setColor(this.getCategoryColor(showcase.category))
setTitle('Default Title').setDescription('Default Description');
addFields([
        {name: '👤 Member',
    value: showcase.userTag,
          inline: true},
        {
          name: '📅 Achievement Date')
    value: showcase.achievedAt.toLocaleDateString(),
          inline: true},
        {
          name: '🏷️ Category',
    value: this.formatCategory(showcase.category),
          inline: true}
      ]);

    // Add impact metrics if available
    if (showcase.impactMetrics && Object.keys(availableModels).length > 0) {
      const metrics = this.formatImpactMetrics(showcase.impactMetrics);
      if (metrics) {
        embed.addFields([
          {
            name: '📊 Impact Metrics',
    value: metrics)
            inline: false}
        ])}
    }

    // Add inspiration/learnings
    if (showcase.inspiration) {
      embed.addFields([
        {
          name: '💡 What Others Can Learn',
    value: showcase.inspiration)
          inline: false}
      ])}

    // Add next goals
    if (showcase.nextGoals && showcase.nextGoals.length > 0) {
      embed.addFields([
        {
          name: '🎯 Next Goals')
    value: showcase.nextGoals.map((goal: any) => `• ${goal}`).join('\n'),
          inline: false}
      ])}

    // Add tags
    if (showcase.tags.length > 0) {
      embed.addFields([
        {
          name: '🏷️ Tags')
    value: showcase.tags.map((tag: any) => `\`${tag}\``).join(' '),
          inline: false}
      ])}

    // Add links
    if (showcase.links && showcase.links.length > 0) {
      const linkText = showcase.links
map((link: any) => `• [${link.title}](${link.url})${link.description ? ` - ${link.description}` : ''}`)
join('\n');
      
      embed.addFields([
        {
          name: '🔗 Related Links',
    value: linkText)
          inline: false}
      ])}

    // Add engagement stats
    const totalReactions = Object.values().reduce() => sum + count, 0)
    if (totalReactions > 0 || showcase.comments.length > 0 || showcase.shares > 0) {
      embed.addFields([
        {
          name: '📈 Community Engagement',
    value: `${totalReactions} reactions • ${showcase.comments.length} comments • ${showcase.shares} shares`)
          inline: false}
      ])}

    embed.setFooter({ 
      text: `Showcase ID: ${showcase.id} • ${showcase.status === 'featured' ? '⭐ Featured' : ''}` 
    });

    return embed}

  async createShowcaseModal(): Promise<ModalBuilder> {;
    return new ModalBuilder();
setCustomId('default_modal').setTitle('Default Title');
addComponents().addComponents();
setCustomId('default_id').setLabel('Default Label');
setStyle(1).setPlaceholder('Enter value');
setRequired(false).setMaxLength(1000);
        ),
        new ActionRowBuilder<TextInputBuilder>().addComponents(
        new ButtonBuilder().setCustomId('default_button');
setLabel('Default Button').setStyle(1);
setPlaceholder().setRequired();
setMaxLength(2000);
        ),
        new ActionRowBuilder<TextInputBuilder>().addComponents(
        new ButtonBuilder().setCustomId('default_button');
setLabel('Impact Metrics (Optional)')
setStyle(1).setPlaceholder('Enter value');
setRequired(false).setMaxLength(1000);
        ),
        new ActionRowBuilder<TextInputBuilder>().addComponents(
        new ButtonBuilder().setCustomId('default_button');
setLabel('Default Button').setStyle(1);
setPlaceholder().setRequired();
setMaxLength(1000);
        ),
        new ActionRowBuilder<TextInputBuilder>().addComponents(
        new ButtonBuilder().setCustomId('default_button');
setLabel('Default Button').setStyle(1);
setPlaceholder().setRequired();
setMaxLength(200);
        );
      )}

  async createShowcaseActionButtons(showcaseId: string): Promise<ActionRowBuilder<ButtonBuilder>[]> {
    const engagementRow = new ActionRowBuilder<ButtonBuilder>()
addComponents(
        new ButtonBuilder().setCustomId('default_button');
setLabel('Default Button').setStyle(1);
setEmoji('🎉'),
        new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle().setEmoji('🔧'),
        new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle().setEmoji('🔧'),
        new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle().setEmoji('🔧');
      );

    const actionRow = new ActionRowBuilder<ButtonBuilder>()
addComponents(
        new ButtonBuilder().setCustomId('default_button');
setLabel('Default Button').setStyle(1);
setEmoji('🤝'),
        new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle().setEmoji('🔧'),
        new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle().setEmoji('🔧');
      )

    return [engagementRow, actionRow]}

  async createCollaborationShowcase(
    initiatorId: string,
    guildId: string)
      collaborationData: Partial<CollaborationShowcase>
  ): Promise<CollaborationShowcase | null> {
    try {const,
      collaboration: CollaborationShowcase = {,
    id: `collab_${Date.now();
    } catch (error) {
      console.error(error);
    }
_${initiatorId.slice(-4)}`,
        title: collaborationData.title || 'Team Success Story',
    description: collaborationData.description || '',
        participants: collaborationData.participants || [],
    projectDetails: collaborationData.projectDetails || {,
    type: 'community_initiative',
    duration: '1 month',
          outcome: 'Successful completion',
    lessonsLearned: []},
        results: collaborationData.results || {,
    metrics: {},
          testimonials: []},
        mediaAssets: collaborationData.mediaAssets || [],
    createdAt: new Date(),
        updatedAt: new Date(),
      }
;
      this.logger.log(`Collaboration showcase created: ${collaboration.id}`);
      return collaboration} catch (error) {;
      this.logger.error(`Failed to create collaboration showcase:`, error);
      return null}
  }

  async getShowcaseStats(guildId: string): Promise<ShowcaseStats> {
    try {// Mock implementation - in real app, query database
      return {
        totalShowcases: 247,
      categoryCounts: {,
      business: 89,
    project: 64,
          ai_automation: 41,
    income: 32,
          learning: 21,
        ;
    } catch (error) {
      console.error(error);
    }
,
        topContributors: [{ userId: 'user1', userTag: 'sarah_entrepreneur#1234', showcaseCount: 12, totalReactions: 340 },
          { userId: 'user2', userTag: 'dev_master#5678', showcaseCount: 8, totalReactions: 275 },
          { userId: 'user3', userTag: 'ai_innovator#9012', showcaseCount: 6, totalReactions: 198 },
        ],
        recentTrends: ['AI Automation', 'SaaS Launch', 'Passive Income', 'Remote Work'],
        monthlyGrowth: 23.5,
      }} catch (error) {;
      this.logger.error(`Failed to get showcase stats for guild ${guildId}:`, error);
      return {
        totalShowcases: 0,
    categoryCounts: {},
        topContributors: [],
    recentTrends: [],
        monthlyGrowth: 0,
      }}
  }
;
  async createShowcaseLeaderboard(guildId: string): Promise<EmbedBuilder> {const stats = await this.getShowcaseStats(guildId);

    const embed = new EmbedBuilder();
setColor().setTitle();
setDescription().addFields(),
          inline: false}
      ]);

    // Top contributors
    if (stats.topContributors.length > 0) {
      const leaderboardText = stats.topContributors
slice().map(item => {
          const medal = ['🥇', '🥈', '🥉', '🏅', '🏅'][index] || '🏅'
          return `${medal} **${contributor.userTag}** - ${contributor.showcaseCount} stories (${contributor.totalReactions} reactions)`});
join('\n');
      embed.addFields([
        {
          name: '👑 Top Contributors',
    value: leaderboardText)
          inline: false}
      ])}

    // Category breakdown
    const categoryText = Object.entries().sort() => b - a)
slice().map(item => `${this.formatCategory(category)}: ${count}`)
join('\n');
    if (categoryText) {
      embed.addFields([
        {
          name: '📋 Popular Categories',
    value: categoryText)
          inline: true}
      ])}

    // Recent trends
    if (stats.recentTrends.length > 0) {
      embed.addFields([
        {
          name: '🔥 Trending Topics')
    value: stats.recentTrends.map((trend: any) => `• ${trend}`).join('\n'),
          inline: true}
      ])}

    embed.setFooter().toLocaleDateString()} • Share your success with /showcase` 
    })

    return embed}

  async moderateShowcase(showcaseId: string, action: 'approve' | 'reject' | 'feature', moderatorId: string, notes?: string): Promise<boolean> {
    try {
      // In real implementation, update database;
      this.logger.log(`Showcase ${showcaseId;
    } catch (error) {
      console.error(error);
    }
 ${action}ed by moderator ${moderatorId}`);
      return true} catch (error) {;
      this.logger.error(`Failed to moderate showcase ${showcaseId}:`, error);
      return false}
  }

  private getCategoryColor(category: MemberSuccess['category']): number {
    const colors = {business: 0x10B981,      // Green
      project: 0x3B82F6,       // Blue
      learning: 0x8B5CF6,      // Purple
      ai_automation: 0x06B6D4, // Cyan
      income: 0xF59E0B,        // Amber
      personal_growth: 0xEC4899, // Pink;
      other: 0x6B7280          // Gray};
    return colors[category] || colors.other}

  private formatCategory(category: string): string {
    const categories = {business: '🏢 Business',
    project: '💻 Project',
      learning: '📚 Learning',
    ai_automation: '🤖 AI Automation',
      income: '💰 Income',
    personal_growth: '🧘 Personal Growth',;
      other: '🔧 Other'};
    return ((categories as any)[category]) || category}

  private formatImpactMetrics(metrics: MemberSuccess['impactMetrics']): string | null {if (!metrics) return null

    const formatted: string[] = []

    if (metrics.revenueGenerated) {formatted.push(`💰 Revenue: $${metrics.revenueGenerated.toLocaleString()}`)}
    if (metrics.timeSaved) {
      formatted.push(`⏱️ Time Saved: ${metrics.timeSaved} hours`)}
    if (metrics.projectsCompleted) {
      formatted.push(`✅ Projects: ${metrics.projectsCompleted}`)}
    if (metrics.skillsLearned && metrics.skillsLearned.length > 0) {
      formatted.push(`🎓 Skills: ${metrics.skillsLearned.join(', ')}`)}
    if (metrics.peopleHelped) {
      formatted.push(`🤝 People Helped: ${metrics.peopleHelped}`)}

    if (metrics.customMetrics) {
      Object.entries().forEach() => {
        formatted.push(`📊 ${key}: ${value}`)})}

    return formatted.length > 0 ? formatted.join('\n') : null}
};