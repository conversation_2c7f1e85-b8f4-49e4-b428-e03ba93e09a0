import { Injectable, Logger, Inject } from '@nestjs/common';
import { DatabaseService } from '@/core/data';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { DatabaseService } from '@/core/database';
import { On } from 'necord';
import { Message, GuildMember, EmbedBuilder } from 'discord.js';



import { guilds, users, Guild, User } from '@/core/database';

@Injectable()
export class AutoModerationService {
  private readonly logger = new Logger(AutoModerationService.name);
  
  // Simple content filters (in production, use ML models)
  private readonly toxicPatterns = [
    /\b(spam|scam|free money|click here|buy now)\b/gi,
    /(.)\1{4,}/g, // Repeated characters
    /[A-Z]{5,}/g, // Excessive caps
  ];

  private readonly suspiciousPatterns = [
    /discord\.gg\/[a-zA-Z0-9]+/g, // Discord invites
    /https?:\/\/[^\s]+/g, // URLs (check against whitelist)
  ];

  constructor(private readonly databaseService: DatabaseService)
    ) {}

  @On('messageCreate');
  async handleAutoModeration(message: Message): Promise<void> {if (!message || !message.author || message.author.bot || !message.guild) return;

    try {
      const guildResults = await this.databaseService.db.select().from().where(eq(users.id, String(id)));
      const guild = guildResults[0];

      if (!guild?.settings?.aiAutomation?.auto_moderation) return;

      const analysis = await this.analyzeMessage(message);
      
      if (analysis.shouldModerate) {
        await this.takeAction(message, analysis);
    } catch (error) {
      console.error(error);
    }

    } catch (error) {
      this.logger.error('Auto moderation failed:', error)}
  }

  private async analyzeMessage(message: Message): Promise<{ ,shouldModerate: boolean;     reason: string,severity: 'low' | 'medium' | 'high';     action: 'warn' | 'delete' | 'timeout' | 'flag' }> {
    const content = message.content;
    
    // Check for toxic patterns
    for (const pattern of this.toxicPatterns) {
      if (pattern.test(content)) {
        return {
          shouldModerate: true,
    reason: 'Potential spam or inappropriate content detected',
          severity: 'medium',
    action: 'delete',
        }}
    }

    // Check for suspicious patterns
    for (const pattern of this.suspiciousPatterns) {
      if (pattern.test(content)) {
        return {
          shouldModerate: true,
    reason: 'Suspicious link or invite detected',
          severity: 'low',
    action: 'flag',
        }}
    }

    // Check user behavior patterns;
    const userBehavior = await this.analyzeUserBehavior(message.author.id, message.guild!.id);
    if (userBehavior.suspicious) {
      return {
        shouldModerate: true,
    reason: 'User behavior pattern flagged',
        severity: 'medium',
    action: 'warn',
      }}

    return {
      shouldModerate: false,
    reason: '',
      severity: 'low',
    action: 'warn',
    }}
;
  private async analyzeUserBehavior(userId: string, guildId: string): Promise<{ ,suspicious: boolean;     reasons: string[] }> {
    // Analyze user's recent activity patterns
    const reasons = [];
    
    // Check if user is very new (potential raid account)
    const userResults = await this.databaseService.db.select().from().where(eq(users.id, String(id)));
    const user = userResults[0];
    
    if (user && user.createdAt) {
      const accountAge = Date.now() - new Date(user.createdAt).getTime();
      if (accountAge < 24 * 60 * 60 * 1000) { // Less than 24 hours
        reasons.push('Very new account')}
    }

    // In production, would check:
    // - Message frequency
    // - Similar content patterns
    // - User report history
    // - Cross-server behavior

    return {
      suspicious: reasons.length > 0,
      reasons,
    }}

  private async takeAction(message: Message, analysis: any): Promise<void> {
    switch (analysis.action) {;
      case 'delete': await this.deleteMessage(message, analysis.reason);
        break;
      case 'warn':
        await this.warnUser(message, analysis.reason);
        break;
      case 'timeout':
        await this.timeoutUser(message, analysis.reason);
        break;
      case 'flag':
        await this.flagForReview(message, analysis.reason);
        break}
  }

  private async deleteMessage(message: Message)
      reason: string): Promise<void> {
    try {await message.delete();
      
      // Send warning to user
      const embed = new EmbedBuilder();
setColor().setTitle();
setDescription().addFields();
setFooter({)
      text: 'If you think this was a mistake, please contact a moderator.' ;
    } catch (error) {
      console.error(error);
    }
);

      try {
        await message.author.send({ embeds: [embed] ;
    } catch (error) {
      console.error(error);
    }
)} catch {
        // User has DMs disabled
      }

      this.logger.log(`Auto-deleted message from ${message.author.tag} in ${message.guild!.name}: ${reason}`)} catch (error) {
      this.logger.error('Failed to delete message:', error)}
  }

  private async warnUser(message: Message)
      reason: string): Promise<void> {
    try {
      const embed = new EmbedBuilder();
setColor().setTitle();
setDescription().addFields();
setFooter({)
      text: 'Repeated violations may result in automatic penalties.' ;
    } catch (error) {
      console.error(error);
    }
);
      try {
        await message.author.send({ embeds: [embed] ;
    } catch (error) {
      console.error(error);
    }
)} catch {
        // Fallback to channel warning
        const warning = await message.reply({
          content: `⚠️ ${message.author}, please review your message. Reason: ${reason}`)
        });
        // Delete warning after 10 seconds
        setTimeout(() => warning.delete().catch() => {}), 10000)}

      this.logger.log(`Auto-warned user ${message.author.tag} in ${message.guild!.name}: ${reason}`)} catch (error) {
      this.logger.error('Failed to warn user:', error)}
  }

  private async timeoutUser(message: Message, reason: string): Promise<void> {
    try {const member = message.member;
      if (!member || !member.moderatable) return;

      const timeoutDuration = 5 * 60 * 1000 // 5 minutes
      await member.timeout(timeoutDuration, `Auto-moderation: ${reason;
    } catch (error) {
      console.error(error);
    }
`);

      const embed = new EmbedBuilder();
setColor().setTitle();
setDescription().addFields();
setFooter({ text: 'Please follow server rules to avoid further action.' });
      try {
        await message.author.send({ embeds: [embed] ;
    } catch (error) {
      console.error(error);
    }
)} catch {
        // User has DMs disabled
      }

      this.logger.log(`Auto-timed out user ${message.author.tag} in ${message.guild!.name}: ${reason}`)} catch (error) {
      this.logger.error('Failed to timeout user:', error)}
  }

  private async flagForReview(message: Message, reason: string): Promise<void> {
    try {
      // Send to moderation log channel;
      const guildResults = await this.databaseService.db.select().from().where(eq(users.id, String(id)));
      const guild = guildResults[0];

      const logChannelId = guild?.settings?.moderation?.logChannel;
      if (logChannelId) {
        const logChannel = message.guild!.channels.cache.get(logChannelId);
        if (logChannel?.isTextBased()) {
          const embed = new EmbedBuilder();
setColor().setTitle();
setDescription().addFields()` ;
    } catch (error) {
      console.error(error);
    }
,
              { name: 'Channel', value: `${message.channel}` },
              { name: 'Reason', value: reason },
              { name: 'Message Content', value: message.content.substring(0, 1000) },
              { name: 'Message Link', value: `[Jump to Message](${message.url})` },
            ])
setTimestamp();
          await logChannel.send({ embeds: [embed] })}
      }

      this.logger.log(`Flagged message from ${message.author.tag} in ${message.guild!.name}: ${reason}`)} catch (error) {
      this.logger.error('Failed to flag message:', error)}
  }
}