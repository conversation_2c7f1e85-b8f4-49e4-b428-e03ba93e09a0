import { Module } from '@nestjs/common';
import { ComprehensiveAutomationService } from './comprehensive-automation.service';
import { AutomationCommands } from './automation.commands';
import { DatabaseModule } from '../../core/database/database.module';
import { WelcomeModule } from '../welcome/welcome.module';
import { RoleAccessModule } from '../role-access/role-access.module';
import { ReactionRoleModule } from '../reaction-role/reaction-role.module';
import { AIChannelModule } from '../ai-channel/ai-channel.module';
import { AIAutomationModule } from '../ai-automation/ai-automation.module';

@Module({
  imports: [DatabaseModule,
    WelcomeModule,
    RoleAccessModule,
    ReactionRoleModule,
    AIChannelModule,
    AIAutomationModule,
  ],
  providers: [ComprehensiveAutomationService,
    AutomationCommands,
  ],
  exports: [ComprehensiveAutomationService,
  ])
});
export class AutomationModule {}