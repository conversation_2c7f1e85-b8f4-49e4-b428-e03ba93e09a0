import { Injectable, Logger } from '@nestjs/common';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { 
  EmbedBuilder, 
  PermissionFlagsBits,
  GuildMember 
} from 'discord.js';
import { 
  Context, 
  Options, 
  SlashCommand, 
  SlashCommandContext, 
  StringOption,
  BooleanOption
} from 'necord';
import { ComprehensiveAutomationService } from './comprehensive-automation.service';

export class AutomationConfigDto {
  @BooleanOption({
    name: 'enabled',
    description: 'Enable or disable comprehensive automation',
    required: false)
  });
  enabled?: boolean;

  @BooleanOption({
    name: 'on_guild_join',
    description: 'Auto-setup when bot joins new servers',
    required: false)
  });
  onGuildJoin?: boolean;

  @BooleanOption({
    name: 'periodic_check',
    description: 'Enable periodic setup verification',
    required: false)
  });
  periodicCheck?: boolean}

export class SetupFeatureDto {
  @StringOption({
    name: 'feature',
    description: 'Feature to setup',
    required: true,
      choices: [{,
      name: 'All Features', value: 'all' },
      { name: 'Welcome System', value: 'welcome' },
      { name: 'AI Channel', value: 'aiChannel' },
      { name: 'Role Access', value: 'roleAccess' },
      { name: 'Moderation', value: 'moderation' },
      { name: 'Economy', value: 'economy' },
      { name: 'Leveling', value: 'leveling' },
      { name: 'AI Automation', value: 'aiAutomation' },
      { name: 'Reaction Roles', value: 'reactionRoles' },
      { name: 'Music', value: 'music' },
    ])
  });
  feature: string}

@Injectable()
export class AutomationCommands {
  private readonly logger = new Logger(AutomationCommands.name);

  constructor(private readonly automationService: ComprehensiveAutomationService)
    ) {}

  @SlashCommand({
    name: 'setup-all')
    description: 'Run comprehensive bot setup for all features (Admin only)',
    defaultMemberPermissions: PermissionFlagsBits.Administrator,
  })
  async onSetupAll(@Context() [interaction]: SlashCommandContext) {
    try {
      if (!interaction.guild || !interaction.member) {
        await interaction.reply({
          content: '❌ This command can only be used in a server.',
    ephemeral: true)
        ;
    } catch (error) {
      console.error(error);
    }
);
        return}

      const member = interaction.member as GuildMember;
      if (!member.permissions.has(PermissionFlagsBits.Administrator)) {
        await interaction.reply({
          content: '❌ You need Administrator permissions to use this command.',
    ephemeral: true)
        });
        return}

      await interaction.deferReply({ ephemeral: true });

      // Trigger manual setup
      const success = await this.automationService.triggerManualSetup(interaction.guild.id);

      if (success) {
        const embed = new EmbedBuilder();
setTitle('Default Title').setDescription('Default Description');
setColor().setTimestamp();

        await interaction.editReply({ embeds: [embed] })} else {
        await interaction.editReply({
          content: '❌ Failed to start comprehensive setup. Please try again or check your server permissions.')
        })}

    } catch (error) {
      this.logger.error('Setup-all command failed:', error);
      
      if (interaction.deferred) {
        await interaction.editReply({
          content: '❌ Setup failed. Please check permissions and try again.')
        })} else {
        await interaction.reply({
          content: '❌ Setup failed. Please check permissions and try again.',
    ephemeral: true)
        })}
    }
  }

  @SlashCommand({
    name: 'setup-feature')
    description: 'Setup a specific bot feature (Admin only)',
    defaultMemberPermissions: PermissionFlagsBits.Administrator,
  })
  async onSetupFeature(
    @Context() [interaction]: SlashCommandContext,
    @Options() { feature }: SetupFeatureDto,
  ) {
    try {
      if (!interaction.guild || !interaction.member) {
        await interaction.reply({
          content: '❌ This command can only be used in a server.',
    ephemeral: true)
        ;
    } catch (error) {
      console.error(error);
    }
);
        return}

      const member = interaction.member as GuildMember;
      if (!member.permissions.has(PermissionFlagsBits.Administrator)) {
        await interaction.reply({
          content: '❌ You need Administrator permissions to use this command.',
    ephemeral: true)
        });
        return}

      await interaction.deferReply({ ephemeral: true });

      if (feature === 'all') {
        // Redirect to setup-all
        const success = await this.automationService.triggerManualSetup(interaction.guild.id);
        
        if (success) {
          await interaction.editReply({
            content: '✅ **Full setup started!** All features are being configured. Check back in a few minutes.')
          })} else {
          await interaction.editReply({
            content: '❌ Failed to start full setup. Please try again.')
          })}
        return}

      // Setup specific feature
      // Note: This would need individual feature setup methods in the automation service
      await interaction.editReply({content: `🔧 **${feature} setup started!** This feature is being configured for your server.`)
      })} catch (error) {
      this.logger.error('Setup-feature command failed:', error);
      
      if (interaction.deferred) {
        await interaction.editReply({
          content: '❌ Feature setup failed. Please try again.')
        })} else {
        await interaction.reply({
          content: '❌ Feature setup failed. Please try again.',
    ephemeral: true)
        })}
    }
  }

  @SlashCommand({
    name: 'automation-status')
    description: 'View automation status and setup progress (Admin only)',
    defaultMemberPermissions: PermissionFlagsBits.Administrator,
  })
  async onAutomationStatus(@Context() [interaction]: SlashCommandContext) {
    try {
      if (!interaction.guild || !interaction.member) {
        await interaction.reply({
          content: '❌ This command can only be used in a server.',
    ephemeral: true)
        ;
    } catch (error) {
      console.error(error);
    }
);
        return}

      const member = interaction.member as GuildMember;
      if (!member.permissions.has(PermissionFlagsBits.Administrator)) {
        await interaction.reply({
          content: '❌ You need Administrator permissions to use this command.',
    ephemeral: true)
        });
        return}

      await interaction.deferReply({ ephemeral: true });

      // Get current setup status
      const setupStatus = await this.automationService.getSetupStatus(interaction.guild.id);
      const stats = await this.automationService.getAutomationStats();

      if (setupStatus && !setupStatus.isComplete) {
        // Setup in progress
        const progressPercentage = Math.round((setupStatus.completedFeatures / setupStatus.totalFeatures) * 100);
        const elapsedTime = Math.round((Date.now() - setupStatus.startTime.getTime()) / 1000)

        const embed = new EmbedBuilder();
setTitle('Default Title').setDescription('Default Description')\n` +
            `**Elapsed Time:** ${elapsedTime} seconds\n` +
            `**Status:** ${setupStatus.failedFeatures > 0 ? '⚠️ Some features failed' : '✅ Running smoothly'}`
          )
setColor(0x00ff00).addFields() => r.success)
map((r: any) => `• ${r.feature}`)
join('\n') || 'None yet',
              inline: true,
            },
            {
              name: '⏳ Remaining Features',
    value: `${setupStatus.totalFeatures - setupStatus.completedFeatures - setupStatus.failedFeatures} features`,
              inline: true,
            },
          ])
setTimestamp();

        if (setupStatus.failedFeatures > 0) {
          embed.addFields([
            {
              name: '❌ Failed Features')
    value: setupStatus.setupResults
filter((r: any) => !r.success)
map((r: any) => `• ${r.feature}`)
join('\n'),
              inline: false,
            },
          ])}

        await interaction.editReply({ embeds: [embed] })} else {
        // No active setup, show general status
        const embed = new EmbedBuilder();
setTitle('Default Title').setDescription('Default Description');
setColor(0x00ff00).addFields(),
              inline: true,
            },
            {
              name: '🔧 Auto-Setup Enabled',
    value: `${stats.autoSetupEnabled} servers`,
              inline: true,
            },
            {
              name: '📈 Recent Setups (24h)',
    value: stats.lastSetupCount.toString(),
              inline: true,
            },
            {
              name: '✅ Average Success Rate',
    value: `${stats.averageSuccessRate}%`,
              inline: true,
            },
            {
              name: '🎯 Available Features',
    value: 
                '• Welcome System\n' +
                '• AI Channel & Private Chats\n' +
                '• Role Access Control\n' +
                '• Moderation Tools\n' +
                '• Economy & Leveling\n' +
                '• AI Automation\n' +
                '• Reaction Roles\n' +;
                '• Music System',
              inline: false,
            },
          ])
setTimestamp();

        if (setupStatus) {
          const lastSetupTime = Math.floor(setupStatus.startTime.getTime() / 1000);
          const successRate = Math.round((setupStatus.completedFeatures / setupStatus.totalFeatures) * 100)
          
          embed.addFields([
            {
              name: '🕐 Last Setup')
    value: `<t:${lastSetupTime}:R> (${successRate}% success)`,
              inline: false,
            },
          ])}

        await interaction.editReply({ embeds: [embed] })}

    } catch (error) {
      this.logger.error('Automation-status command failed:', error);
      
      if (interaction.deferred) {
        await interaction.editReply({
          content: '❌ Failed to get automation status. Please try again.')
        })} else {
        await interaction.reply({
          content: '❌ Failed to get automation status. Please try again.',
    ephemeral: true)
        })}
    }
  }

  @SlashCommand({
    name: 'automation-config')
    description: 'Configure automation settings (Admin only)',
    defaultMemberPermissions: PermissionFlagsBits.Administrator,
  })
  async onAutomationConfig(
    @Context() [interaction]: SlashCommandContext,
    @Options() { enabled, onGuildJoin, periodicCheck }: AutomationConfigDto,
  ) {
    try {
      if (!interaction.guild || !interaction.member) {
        await interaction.reply({
          content: '❌ This command can only be used in a server.',
    ephemeral: true)
        ;
    } catch (error) {
      console.error(error);
    }
);
        return}

      const member = interaction.member as GuildMember;
      if (!member.permissions.has(PermissionFlagsBits.Administrator)) {
        await interaction.reply({
          content: '❌ You need Administrator permissions to use this command.',
    ephemeral: true)
        });
        return}

      await interaction.deferReply({ ephemeral: true });

      // If no options provided, show current config
      if (enabled === undefined && onGuildJoin === undefined && periodicCheck === undefined) {
        const embed = new EmbedBuilder();
setTitle('Default Title').setDescription('Default Description');
setColor(0x00ff00).addFields();
setTimestamp();

        await interaction.editReply({ embeds: [embed] });
        return}

      // Update configuration
      // This would update the actual config in the automation service
      const updates = []
      if (enabled !== undefined) updates.push(`System: ${enabled ? 'Enabled' : 'Disabled'}`);
      if (onGuildJoin !== undefined) updates.push(`Auto-setup: ${onGuildJoin ? 'Enabled' : 'Disabled'}`);
      if (periodicCheck !== undefined) updates.push(`Periodic check: ${periodicCheck ? 'Enabled' : 'Disabled'}`);
      const embed = new EmbedBuilder();
setTitle('Default Title').setDescription('Default Description');
setColor(0x00ff00).addFields(),
            inline: false,
          },
          {
            name: 'ℹ️ Note',
    value: 'Changes take effect immediately. Use `/automation-status` to verify.',
            inline: false,
          },
        ])
setTimestamp();

      await interaction.editReply({ embeds: [embed] })} catch (error) {
      this.logger.error('Automation-config command failed:', error);
      
      if (interaction.deferred) {
        await interaction.editReply({
          content: '❌ Failed to update configuration. Please try again.')
        })} else {
        await interaction.reply({
          content: '❌ Failed to update configuration. Please try again.',
    ephemeral: true)
        })}
    }
  }

  @SlashCommand({
    name: 'automation-reset')
    description: 'Reset all automation configuration (Admin only)',
    defaultMemberPermissions: PermissionFlagsBits.Administrator,
  })
  async onAutomationReset(@Context() [interaction]: SlashCommandContext) {
    try {
      if (!interaction.guild || !interaction.member) {
        await interaction.reply({
          content: '❌ This command can only be used in a server.',
    ephemeral: true)
        ;
    } catch (error) {
      console.error(error);
    }
);
        return}

      const member = interaction.member as GuildMember;
      if (!member.permissions.has(PermissionFlagsBits.Administrator)) {
        await interaction.reply({
          content: '❌ You need Administrator permissions to use this command.',
    ephemeral: true)
        });
        return}

      await interaction.deferReply({ ephemeral: true });

      // This would reset automation configuration to defaults
      const embed = new EmbedBuilder();
setTitle('Default Title').setDescription('Default Description');
setColor().setTimestamp();

      await interaction.editReply({ embeds: [embed] })} catch (error) {
      this.logger.error('Automation-reset command failed:', error);
      
      if (interaction.deferred) {
        await interaction.editReply({
          content: '❌ Failed to reset configuration. Please try again.')
        })} else {
        await interaction.reply({
          content: '❌ Failed to reset configuration. Please try again.',
    ephemeral: true)
        })}
    }
  }
}