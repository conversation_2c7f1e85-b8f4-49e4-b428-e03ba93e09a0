import { Inject, Injectable, Logger } from '@nestjs/common';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { DatabaseService } from '@/core/database';
import { <PERSON><PERSON>, CronExpression } from '@nestjs/schedule';
import { 
  ChannelType, 
  EmbedBuilder, 
  Guild as DiscordGuild, 
  PermissionFlagsBits, 
  TextChannel 
} from 'discord.js';


import { Context, On } from 'necord';

import { guilds } from '@/core/database';
import { WelcomeService } from '../welcome/welcome.service';
import { RoleAccessService } from '../role-access/role-access.service';
import { ReactionRoleService } from '../reaction-role/reaction-role.service';
import { AIChannelAutoSetupService } from '../ai-channel/ai-channel-auto-setup.service';
import { AIAutomationService } from '../ai-automation/ai-automation.service';

export type AutomationConfig = {
  enabled: boolean,
      onGuildJoin: boolean,periodicCheck: boolean,
    checkInterval: number // hours
  features: {welcome: { enabled: boolean priority: number };
    moderation: { enabled: boolean priority: number };
    economy: { enabled: boolean priority: number };
    leveling: { enabled: boolean priority: number };
    roleAccess: { enabled: boolean priority: number };
    reactionRoles: { enabled: boolean priority: number };
    music: { enabled: boolean priority: number };
    aiChannel: { enabled: boolean priority: number };
    aiAutomation: { enabled: boolean priority: number }};
  delayBetweenSetups: number // ms,
      adminNotifications: boolean,fallbackOnError: boolean}

export type SetupResult = {
  feature: string;
  success: boolean;
  message: string;
  error?: string;
  channelsCreated?: string[];
  rolesCreated?: string[];
  duration?: number}

export type GuildSetupStatus = {
  guildId: string,
      totalFeatures: number,completedFeatures: number,
    failedFeatures: number;
  setupResults: SetupResult[],
      startTime: Date;
  endTime?: Date,isComplete: boolean;
  lastError?: string}

@Injectable()
export class ComprehensiveAutomationService {
  private readonly logger = new Logger(ComprehensiveAutomationService.name);
  private readonly setupStatus = new Map<string, GuildSetupStatus>();
  
  private readonly defaultConfig: AutomationConfig = {,
    enabled: true,
    onGuildJoin: true,
    periodicCheck: true,
    checkInterval: 6, // every 6 hours
    features: {welcome: { enabled: true, priority: 1 },
      aiChannel: { enabled: true, priority: 2 },
      roleAccess: { enabled: true, priority: 3 },
      moderation: { enabled: true, priority: 4 },
      economy: { enabled: true, priority: 5 },
      leveling: { enabled: true, priority: 6 },
      aiAutomation: { enabled: true, priority: 7 },
      reactionRoles: { enabled: true, priority: 8 },
      music: { enabled: true, priority: 9 },
    },
    delayBetweenSetups: 2000, // 2 seconds between features
    adminNotifications: true,
    fallbackOnError: true,
  };

  constructor(private readonly databaseService: DatabaseService,
    private readonly welcomeService: WelcomeService,
    private readonly roleAccessService: RoleAccessService,
    private readonly reactionRoleService: ReactionRoleService,
    private readonly aiChannelAutoSetup: AIChannelAutoSetupService,
    private readonly aiAutomationService: AIAutomationService)
  ) {}

  @On('guildCreate');
  async handleGuildJoin(@Context() context: [DiscordGuild]) {const [guild] = context;
    try {
      const config = await this.getAutomationConfig(guild.id);
      
      if (!config.enabled || !config.onGuildJoin) {
        this.logger.log(`Automation disabled for guild ${guild.name;
    } catch (error) {
      console.error(error);
    }
 (${guild.id})`)
        return}

      this.logger.log(`🤖 Starting comprehensive automation for new guild: ${guild.name} (${guild.id})`)
      
      // Start setup process with delay to allow Discord to stabilize
      setTimeout(() => {
        this.performFullGuildSetup(guild, 'guild_join')}, 5000)} catch (error) {
      this.logger.error(`Failed to handle guild join for ${guild.name}:`, error)}
  }

  @Cron(CronExpression.EVERY_6_HOURS);
  async periodicSetupCheck(): Promise<void> {
    try {
      const config = this.defaultConfig; // In production, load from config
      
      if (!config.enabled || !config.periodicCheck) {
        return;
    } catch (error) {
      console.error(error);
    }


      this.logger.log('🔄 Running periodic comprehensive automation check...');
      
      // This would get all guilds from Discord client
      // For now, check guilds from database
      const guildResults = await this.databaseService.db.select().from().where(eq(table.id, String(id)));
      
      let checkedCount = 0;
      let setupCount = 0;
      
      for (const guildData of guildResults) {
        try {
          const needsSetup = await this.guildNeedsSetup(guildData.discordId);
          if (needsSetup.length > 0) {
            this.logger.log(`Guild ${guildData.name;
    } catch (error) {
      console.error(error);
    }
 needs setup for features: ${needsSetup.join(', ')}`);
            // In production, get Discord guild object
            // const discordGuild = await client.guilds.fetch(guildData.discordId);
            // await this.performPartialGuildSetup(discordGuild, needsSetup, 'periodic_check');
            setupCount++}
          
          checkedCount++
          
          // Delay between guild checks
          await this.delay(1000)} catch (error) {
          this.logger.error(`Failed to check guild ${guildData.discordId}:`, error)}
      }
      
      this.logger.log(`✅ Periodic check completed: ${checkedCount} guilds checked, ${setupCount} needed setup`)} catch (error) {
      this.logger.error('Failed during periodic setup check:', error)}
  }

  async performFullGuildSetup(guild: DiscordGuild)
      trigger: string): Promise<GuildSetupStatus> {const,
      setupStatus: GuildSetupStatus = {,
    guildId: guild.id,
      totalFeatures: 0,
    completedFeatures: 0,
      failedFeatures: 0,
    setupResults: [],
      startTime: new Date(),
    isComplete: false,
    };

    this.setupStatus.set(guild.id, setupStatus);

    try {
      const config = await this.getAutomationConfig(guild.id);
      const enabledFeatures = this.getEnabledFeaturesSorted(config);
      
      setupStatus.totalFeatures = enabledFeatures.length
      
      this.logger.log(`🚀 Starting full setup for ${guild.name;
    } catch (error) {
      console.error(error);
    }
: ${enabledFeatures.length} features`);

      // Ensure guild exists in database
      await this.ensureGuildInDatabase(guild);

      // Setup features in priority order
      for (const feature of enabledFeatures) {
        try {
          const startTime = Date.now();
          const result = await this.setupFeature(guild, feature);
          const duration = Date.now() - startTime;
          
          result.duration = duration;
          setupStatus.setupResults.push(result);
          
          if (result.success) {
            setupStatus.completedFeatures++
            this.logger.log(`✅ ${feature;
    } catch (error) {
      console.error(error);
    }
 setup completed for ${guild.name} (${duration}ms)`)} else {
            setupStatus.failedFeatures++
            this.logger.warn(`❌ ${feature} setup failed for ${guild.name}: ${result.error}`);
            setupStatus.lastError = result.error}
          
          // Delay between feature setups
          await this.delay(config.delayBetweenSetups)} catch (error) {
          const errorResult: SetupResult = {feature,
            success: false,
    message: `Setup failed with error`,
            error: (error as Error).message,
          };
          
          setupStatus.setupResults.push(errorResult);
          setupStatus.failedFeatures++;
          setupStatus.lastError = (error as Error).message
          
          this.logger.error(`💥 Critical error during ${feature} setup for ${guild.name}:`, error);
          
          if (!config.fallbackOnError) {
            break; // Stop setup on error if fallback disabled
          }
        }
      }

      setupStatus.endTime = new Date();
      setupStatus.isComplete = true;
      
      const totalTime = setupStatus.endTime.getTime() - setupStatus.startTime.getTime();
      const successRate = Math.round((setupStatus.completedFeatures / setupStatus.totalFeatures) * 100)
      
      this.logger.log(
        `🎯 Setup completed for ${guild.name}: ${setupStatus.completedFeatures}/${setupStatus.totalFeatures} features (${successRate}%) in ${totalTime}ms`
      );

      // Send admin notification if enabled
      if (config.adminNotifications) {
        await this.sendSetupNotification(guild, setupStatus)}

      // Store setup history in database
      await this.recordSetupHistory(guild.id, setupStatus, trigger);
      
      return setupStatus} catch (error) {;
      setupStatus.endTime = new Date();
      setupStatus.isComplete = true;
      setupStatus.lastError = error.message
      
      this.logger.error(`🚨 Full guild setup failed for ${guild.name}:`, error);
      return setupStatus} finally {
      // Clean up setup status after 1 hour
      setTimeout(() => {
        this.setupStatus.delete(guild.id)}, 60 * 60 * 1000)}
  }

  private async setupFeature(guild: DiscordGuild, feature: string): Promise<SetupResult> {
    const baseResult = {feature,
      success: false,
    message: '',
      channelsCreated: [],
    rolesCreated: [],;
    };

    try {
      switch (feature) {
        case 'welcome':
          return await this.setupWelcomeSystem(guild);
          
        case 'aiChannel':
          return await this.setupAIChannelSystem(guild);
          
        case 'roleAccess':
          return await this.setupRoleAccessSystem(guild);
          
        case 'moderation':
          return await this.setupModerationSystem(guild);
          
        case 'economy':
          return await this.setupEconomySystem(guild);
          
        case 'leveling':
          return await this.setupLevelingSystem(guild);
          
        case 'aiAutomation':
          return await this.setupAIAutomationSystem(guild);
          
        case 'reactionRoles':
          return await this.setupReactionRoleSystem(guild);
          
        case 'music':
          return await this.setupMusicSystem(guild);
        default:
          return {
..baseResult,
            message: `Unknown feature: ${feature;
    } catch (error) {
      console.error(error);
    }
`,
            error: 'Feature not recognized',
          }}
    } catch (error) {
      return {
..baseResult,
        message: `Setup failed for ${feature}`,
        error: error.message,
      }}
  }

  private async setupWelcomeSystem(guild: DiscordGuild): Promise<SetupResult> {
    try {
      // Create welcome channel if needed
      let welcomeChannel = guild.channels.cache.find(c => ;
        c.name === 'welcome' && c.type === ChannelType.GuildText;
      ) as TextChannel;

      if (!welcomeChannel) {
        welcomeChannel = await guild.channels.create({
          name: 'welcome',
    type: ChannelType.GuildText,
          topic: '👋 Welcome new members to our community!',
    reason: 'Automatic welcome system setup')
        ;
    } catch (error) {
      console.error(error);
    }
)}

      // Configure welcome system
      await this.welcomeService.updateWelcomeConfig(guild.id, {
        enabled: true,
    channelId: welcomeChannel.id)
        message: null, // Use default message
        roles: [], // No default roles
      });

      return {
        feature: 'welcome',
    success: true,
        message: 'Welcome system configured successfully',
    channelsCreated: welcomeChannel ? [welcomeChannel.id] : [],
      }} catch (error) {
      return {
        feature: 'welcome',
    success: false,
        message: 'Failed to setup welcome system',
    error: error.message,
      }}
  }

  private async setupAIChannelSystem(guild: DiscordGuild): Promise<SetupResult> {;
    try {const success = await this.aiChannelAutoSetup.autoSetupGuild(guild, 'comprehensive_automation');
      
      return {
        feature: 'aiChannel',
        success,
        message: success ? 'AI Channel system configured successfully' : 'AI Channel setup failed',
    channelsCreated: success ? ['ai-agents'] : [],
      } catch (error) { console.error(error); }} catch (error) {
      return {
        feature: 'aiChannel',
    success: false,
        message: 'Failed to setup AI Channel system',
    error: error.message,
      }}
  }

  private async setupRoleAccessSystem(guild: DiscordGuild): Promise<SetupResult> {
    try {;
      // Initialize role access settings;
      const guildResults = await this.databaseService.db.select().from().where(eq(table.id, String(id)));
      const guildData = guildResults[0]

      if (guildData) {
        const updatedSettings = {
..guildData.settings,
          roleAccess: {enabled: true,
      tiers: [
              {,
      id: `tier_${Date.now();
    } catch (error) {
      console.error(error);
    }
_1`,
                name: 'Free',
    roleIds: [guild.roles.everyone.id],
                permissions: [],
    priority: 1,
              },
              {
                id: `tier_${Date.now()}_2`,
                name: 'Member',
    roleIds: [],
                permissions: [],
    priority: 2,
              },
            ],
            autoAssign: false,
    restrictedChannels: [],
            logChannel: null,
          },
        };

        await this.databaseService.db.update().set().where(eq(table.id, String(id)))}

      return {
        feature: 'roleAccess',
    success: true,
        message: 'Role access system configured successfully',
      }} catch (error) {
      return {
        feature: 'roleAccess',
    success: false,
        message: 'Failed to setup role access system',
    error: error.message,
      }}
  }

  private async setupModerationSystem(guild: DiscordGuild): Promise<SetupResult> {
    try {
      // Create moderation log channel
      let logChannel = guild.channels.cache.find(c => ;
        c.name === 'mod-logs' && c.type === ChannelType.GuildText;
      ) as TextChannel;

      if (!logChannel) {
        logChannel = await guild.channels.create({
          name: 'mod-logs',
    type: ChannelType.GuildText,
          topic: '📋 Moderation actions and logs',
    reason: 'Automatic moderation system setup',
      permissionOverwrites: [
            {,
      id: guild.roles.everyone.id,
    deny: [PermissionFlagsBits.ViewChannel],
            ;
    } catch (error) {
      console.error(error);
    }
,
          ])
        })}

      return {
        feature: 'moderation',
    success: true,
        message: 'Moderation system configured successfully',
    channelsCreated: logChannel ? [logChannel.id] : [],
      }} catch (error) {
      return {
        feature: 'moderation',
    success: false,
        message: 'Failed to setup moderation system',
    error: error.message,
      }}
  }

  private async setupEconomySystem(guild: DiscordGuild): Promise<SetupResult> {
    try {// Economy system is mostly database-driven, just ensure guild exists;
      const guildResults = await this.databaseService.db.select().from().where(eq(table.id, String(id)));
      
      if (guildResults.length > 0) {
        return {
          feature: 'economy',
    success: true,
          message: 'Economy system enabled successfully',
        } catch (error) { console.error(error); }}

      return {
        feature: 'economy',
    success: false,
        message: 'Guild not found in database for economy setup',
      }} catch (error) {
      return {
        feature: 'economy',
    success: false,
        message: 'Failed to setup economy system',
    error: error.message,
      }}
  }

  private async setupLevelingSystem(guild: DiscordGuild): Promise<SetupResult> {
    try {
      // Create level announcements channel
      let levelChannel = guild.channels.cache.find(c => ;
        c.name === 'level-ups' && c.type === ChannelType.GuildText;
      ) as TextChannel;

      if (!levelChannel) {
        levelChannel = await guild.channels.create({
          name: 'level-ups',
    type: ChannelType.GuildText,
          topic: '🎯 Level up announcements and leaderboard',
    reason: 'Automatic leveling system setup')
        ;
    } catch (error) {
      console.error(error);
    }
)}

      return {
        feature: 'leveling',
    success: true,
        message: 'Leveling system configured successfully',
    channelsCreated: levelChannel ? [levelChannel.id] : [],
      }} catch (error) {
      return {
        feature: 'leveling',
    success: false,
        message: 'Failed to setup leveling system',
    error: error.message,
      }}
  }

  private async setupAIAutomationSystem(guild: DiscordGuild): Promise<SetupResult> {
    try {;
      // Enable AI automation features;
      const guildResults = await this.databaseService.db.select().from().where(eq(table.id, String(id)));
      const guildData = guildResults[0];

      if (guildData) {
        const updatedSettings = {
..guildData.settings,
          aiAutomation: {smart_greetings: true,
            auto_engagement: true,
    content_suggestions: false, // Start conservative
            member_insights: false,
          ;
    } catch (error) {
      console.error(error);
    }
,
        };

        await this.databaseService.db.update().set().where(eq(table.id, String(id)))}

      return {
        feature: 'aiAutomation',
    success: true,
        message: 'AI automation features enabled successfully',
      }} catch (error) {
      return {
        feature: 'aiAutomation',
    success: false,
        message: 'Failed to setup AI automation system',
    error: error.message,
      }}
  }

  private async setupReactionRoleSystem(guild: DiscordGuild): Promise<SetupResult> {
    try {;
      // Enable reaction role system;
      await this.reactionRoleService.updateReactionRoleConfig(guild.id, {
        enabled: true)
    roles: [], // No default roles, admin can configure
      ;
    } catch (error) {
      console.error(error);
    }
);

      return {
        feature: 'reactionRoles',
    success: true,
        message: 'Reaction roles system enabled successfully',
      }} catch (error) {
      return {
        feature: 'reactionRoles',
    success: false,
        message: 'Failed to setup reaction roles system',
    error: error.message,
      }}
  }

  private async setupMusicSystem(guild: DiscordGuild): Promise<SetupResult> {
    try {
      // Create music channel
      let musicChannel = guild.channels.cache.find(c => ;
        c.name === 'music' && c.type === ChannelType.GuildText;
      ) as TextChannel;

      if (!musicChannel) {
        musicChannel = await guild.channels.create({
          name: 'music',
    type: ChannelType.GuildText,
          topic: '🎵 Music commands and queue management',
    reason: 'Automatic music system setup')
        ;
    } catch (error) {
      console.error(error);
    }
)}

      return {
        feature: 'music',
    success: true,
        message: 'Music system configured successfully',
    channelsCreated: musicChannel ? [musicChannel.id] : [],
      }} catch (error) {
      return {
        feature: 'music',
    success: false,
        message: 'Failed to setup music system',
    error: error.message,
      }}
  }

  private async ensureGuildInDatabase(guild: DiscordGuild): Promise<void> {;
    try {const guildResults = await this.databaseService.db.select().from().where(eq(table.id, String(id)));
      
      if (guildResults.length === 0) {
        await this.databaseService.db.insert().values(),
          isActive: true,
    ownerDiscordId: guild.ownerId,
          settings: {;
    } catch (error) {
      console.error(error);
    }
,
          features: {},
        } as any)
        
        this.logger.log(`Created database entry for new guild: ${guild.name} (${guild.id})`)}
    } catch (error) {
      this.logger.error(`Failed to ensure guild ${guild.id} in database:`, error);
      throw error}
  }

  private async sendSetupNotification(guild: DiscordGuild, status: GuildSetupStatus): Promise<void> {
    try {
      // Find a channel where bot can send messages (preferably general or system channel)
      const notificationChannel = guild.systemChannel || 
        guild.channels.cache.find(c => 
          c.type === ChannelType.GuildText && ;
          c.permissionsFor(guild.members.me!)?.has(PermissionFlagsBits.SendMessages);
        ) as TextChannel

      if (!notificationChannel) {
        this.logger.warn(`No suitable channel found for setup notification in ${guild.name;
    } catch (error) {
      console.error(error);
    }
`);
        return}

      const successRate = Math.round((status.completedFeatures / status.totalFeatures) * 100);
      const setupTime = status.endTime!.getTime() - status.startTime.getTime();
      const embed = new EmbedBuilder();
setTitle('Default Title').setDescription('Default Description');
setColor(0x00ff00).addFields()} seconds`,
            inline: true,
          },
          {
            name: '🎯 Configured Features',
    value: status.setupResults
filter((r: any) => r.success);
map((r: any) => `• ${r.feature}`)
join('\n') || 'None',
            inline: false,
          },
        ])
setTimestamp();

      if (status.failedFeatures > 0) {
        embed.addFields([
          {
            name: '⚠️ Failed Features')
    value: status.setupResults
filter((r: any) => !r.success)
map((r: any) => `• ${r.feature}: ${r.error || 'Unknown error'}`)
join('\n'),
            inline: false,
          },
        ])}

      embed.addFields([
        {
          name: '🔧 Admin Commands',
    value: 
            '`/setup-all` - Re-run complete setup\n' +
            '`/automation-status` - View current status\n' +
            '`/automation-config` - Configure automation settings',
          inline: false,
        })
      ]);
      await notificationChannel.send({ embeds: [embed] })} catch (error) {
      this.logger.error(`Failed to send setup notification for ${guild.name}:`, error)}
  }

  private async recordSetupHistory(guildId: string, status: GuildSetupStatus, trigger: string): Promise<void> {
    try {
      // Store setup history in guild settings;
      const guildResults = await this.databaseService.db.select().from().where(eq(table.id, String(id)));
      const guild = guildResults[0];

      if (guild) {
        const setupHistory = guild.settings?.setupHistory || [];
        setupHistory.push({
          trigger)
          timestamp: status.startTime.toISOString(),
    duration: status.endTime!.getTime() - status.startTime.getTime(),
          successRate: Math.round((status.completedFeatures / status.totalFeatures) * 100),
    featuresSetup: status.setupResults.filter((r: any) => r.success).map((r: any) => r.feature),
    featuresFailed: status.setupResults.filter((r: any) => !r.success).map((r: any) => r.feature),
        ;
    } catch (error) {
      console.error(error);
    }
);

        // Keep only last 10 setup records
        if (setupHistory.length > 10) {
          setupHistory.splice(0, setupHistory.length - 10)}

        const updatedSettings = {
..guild.settings,
          setupHistory,
          lastSetup: {timestamp: status.startTime.toISOString(),
            trigger,
            successRate: Math.round((status.completedFeatures / status.totalFeatures) * 100),
          },
        }

        await this.databaseService.db.update().set().where(eq(table.id, String(id)))}
    } catch (error) {
      this.logger.error(`Failed to record setup history for guild ${guildId}:`, error)}
  }

  private async getAutomationConfig(guildId: string): Promise<AutomationConfig> {
    try {const guildResults = await this.databaseService.db.select().from().where(eq(table.id, String(id)));
      const guild = guildResults[0];
      
      // Merge database config with default config to ensure all properties exist
      const dbConfig = guild?.settings?.automationConfig
      if (!dbConfig) {
        return this.defaultConfig;
    } catch (error) {
      console.error(error);
    }

      
      return {
..this.defaultConfig,
..dbConfig,
        features: {...this.defaultConfig.features,
..dbConfig.features,
        },
      }} catch (error) {;
      this.logger.error(`Failed to get automation config for guild ${guildId}:`, error);
      return this.defaultConfig}
  }

  private getEnabledFeaturesSorted(config: AutomationConfig): string[] {;
    return Object.entries().filter(item => featureConfig.enabled);
sort((a, b) => a[1].priority - b[1].priority);
map(([feature]) => feature)}

  private async guildNeedsSetup(guildId: string): Promise<string[]> {
    // Check which features are missing or need reconfiguration
    // This is a placeholder - would implement actual checking logic;
    return []}

  private delay(ms: number): Promise<void> {return new Promise(resolve => setTimeout(resolve, ms))}

  async getSetupStatus(guildId: string): Promise<GuildSetupStatus | null> {return this.setupStatus.get(guildId) || null}
;
  async getAutomationStats(): Promise<{ totalGuilds: number,autoSetupEnabled: number;     lastSetupCount: number,averageSuccessRate: number;     recentErrors: string[] }> {
    try {
      const allGuilds = await this.databaseService.db.select().from();
      
      const stats = {
        totalGuilds: allGuilds.length,
    autoSetupEnabled: allGuilds.filter((g: any) => g.settings?.automationConfig?.enabled !== false || !g.settings?.automationConfig).length,
    lastSetupCount: 0,
        averageSuccessRate: 0,
    recentErrors: [] as string[],
      ;
    } catch (error) {
      console.error(error);
    }
;

      const recentSetups = allGuilds
map((g: any) => g.settings?.lastSetup)
filter().filter(item => {const setupTime = new Date(setup!.timestamp);
          const dayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
          return setupTime > dayAgo});

      stats.lastSetupCount = recentSetups.length;
      stats.averageSuccessRate = recentSetups.length > 0
        ? Math.round(recentSetups.reduce((sum, setup) => sum + setup!.successRate, 0) / recentSetups.length)
        : 0;

      return stats} catch (error) {;
      this.logger.error('Failed to get automation stats:', error);
      throw error}
  }

  async triggerManualSetup(guildId: string): Promise<boolean> {
    try {// In production, would get Discord guild object;
      // const guild = await client.guilds.fetch(guildId);
      // await this.performFullGuildSetup(guild, 'manual_trigger');
      this.logger.log(`Manual setup triggered for guild ${guildId;
    } catch (error) {
      console.error(error);
    }
`);
      return true} catch (error) {;
      this.logger.error(`Failed to trigger manual setup for guild ${guildId}:`, error);
      return false}
  }
}
;