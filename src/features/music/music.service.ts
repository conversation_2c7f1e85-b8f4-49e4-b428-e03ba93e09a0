import { Injectable, Logger } from '@nestjs/common';
// import { eq, and, or, desc, count } from 'drizzle-orm';
// @ts-nocheck
import { AudioPlayerStatus, createAudioPlayer, createAudioResource } from '@discordjs/voice';
import { GuildMember, VoiceChannel } from 'discord.js';
import { createReadStream } from 'fs';
import { Context, Options, SlashCommand, SlashCommandContext, StringOption } from 'necord';
import { join } from 'path';

export class PlayDto {
  @StringOption({
    name: 'query',
    description: 'Song name or URL to play',
    required: true)
  });
  query!: string}

@Injectable()
export class MusicService {
  private readonly logger = new Logger(MusicService.name);
  private readonly queues = new Map<string, any>(); // Guild ID -> queue data

  @SlashCommand({
    name: 'play',
    description: 'Play music from YouTube or other sources')
  });
  async onPlayCommand(
    @Context() [interaction]: SlashCommandContext,
    @Options() { query }: PlayDto
  ) {
    if (!interaction.guild) {
      await interaction.reply({
          content: '❌ This command can only be used in a server.',
    ephemeral: true)
      });
      return}

    const member = interaction.member as GuildMember;
    const voiceChannel = member.voice.channel as VoiceChannel;

    if (!voiceChannel) {
      await interaction.reply({
          content: '❌ You need to be in a voice channel to play music!',
    ephemeral: true)
      });
      return}

    if (!voiceChannel.permissionsFor(interaction.guild.members.me!)?.has(['Connect', 'Speak'])) {
      await interaction.reply({
          content: '❌ I need permission to connect and speak in your voice channel!',
    ephemeral: true)
      });
      return}

    try {
      // For now, just simulate adding to queue
      const guildId = interaction.guild.id;
      if (!this.queues.has(guildId)) {
        this.queues.set(guildId, { songs: [], playing: false, player: null, connection: null ;
    } catch (error) {
      console.error(error);
    }
)}

      const queue = this.queues.get(guildId)!;
      const song = {
        title: query.startsWith('http') ? 'YouTube Song' : query,
    url: query,
        requestedBy: interaction.user.tag,
    duration: '3:30', // Placeholder
      };

      queue.push(song);

      if (queue.length === 1) {
        await interaction.reply({
          content: `🎵 **Now Playing:** ${song.title}\n🎤 **Requested by:** ${song.requestedBy}\n\n⚠️ **Note: ** This is a basic implementation. Full music functionality with voice connection coming soon!`,
    ephemeral: false)
        })} else {
        await interaction.reply({
          content: `📝 **Added to queue:** ${song.title}\n📊 **Position in queue:** ${queue.length}\n🎤 **Requested by:** ${song.requestedBy}`,
          ephemeral: true)
        })}

      this.logger.log(`Song added to queue in ${interaction.guild.name}: ${song.title}`)} catch (error) {
      this.logger.error('Failed to play music:', error);
      await interaction.reply({
          content: '❌ Failed to play music. Please try again.',
    ephemeral: true)
      })}
  }

  @SlashCommand({
    name: 'stop',
    description: 'Stop the current music playback')
  });
  async onStopCommand(@Context() [interaction]: SlashCommandContext) {
    if (!interaction.guild) {
      await interaction.reply({
          content: '❌ This command can only be used in a server.',
    ephemeral: true)
      });
      return}

    const member = interaction.member as GuildMember;
    if (!member.voice.channel) {
      await interaction.reply({
          content: '❌ You need to be in a voice channel to stop music!',
    ephemeral: true)
      });
      return}

    const guildId = interaction.guild.id;
    const queue = this.queues.get(guildId);

    if (!queue || queue.length === 0) {
      await interaction.reply({
          content: '❌ There is no music currently playing!',
    ephemeral: true)
      });
      return}

    // Clear the queue
    this.queues.delete(guildId);

    await interaction.reply({
          content: '⏹️ **Music stopped** and queue cleared!\n\n⚠️ **Note:** Full music functionality with voice connection coming soon!',
    ephemeral: false)
    });
    this.logger.log(`Music stopped in ${interaction.guild.name} by ${interaction.user.tag}`)}

  @SlashCommand({
    name: 'queue',
    description: 'Show the current music queue')
  });
  async onQueueCommand(@Context() [interaction]: SlashCommandContext) {
    if (!interaction.guild) {
      await interaction.reply({
          content: '❌ This command can only be used in a server.',
    ephemeral: true)
      });
      return}

    const guildId = interaction.guild.id;
    const queue = this.queues.get(guildId);
    if (!queue || queue.length === 0) {
      await interaction.reply({
          content: '📋 **Music Queue is empty!**\n\nUse `/play <song>` to add songs to the queue.',
    ephemeral: true)
      });
      return}

    const queueData = this.queues.get(guildId);
    const songs = queueData?.songs || []
    
    let queueText = '📋 **Current Music Queue: **\n\n'

    if (queue.length > 0) {queueText += `🎵 **Now Playing:**\n**${queue[0].title}** (${queue[0].duration})\nRequested by: ${queue[0].requestedBy}\n\n`}

    if (queue.length > 1) {
      queueText += '📝 **Up Next: **\n'
      queue.slice().forEach() => {queueText += `**${index + 1}.** ${song.title} (${song.duration}) - ${song.requestedBy}\n`})

      if (queue.length > 11) {
        queueText += `\n*...and ${queue.length - 11} more songs*`}
    }

    const { EmbedBuilder } = await import('discord.js');
    const embed = new EmbedBuilder();
setColor().setTitle();
setDescription(queueText);

    embed.addFields([
      { name: '📊 Total Songs', value: songs.length.toString(), inline: true },
      { name: '🔊 Status', value: queueData.playing ? '▶️ Playing' : '⏸️ Paused', inline: true },
    ]);

    await interaction.reply({ embeds: [embed], ephemeral: true })}

  private async playNext(guildId: string): Promise<void> {const queueData = this.queues.get(guildId);
    if (!queueData || queueData.songs.length === 0) {
      this.cleanup(guildId);
      return}

    const song = queueData.songs[0];
    
    try {
      // Create audio player if it doesn't exist
      if (!queueData.player) {
        queueData.player = createAudioPlayer();
        
        queueData.player.on(AudioPlayerStatus.Playing, () => {
          queueData.playing = true
          this.logger.log(`Now playing: ${song.title;
    } catch (error) {
      console.error(error);
    }
 in guild ${guildId}`)});

        queueData.player.on(AudioPlayerStatus.Idle, () => {
          queueData.playing = false;
          // Remove finished song and play next
          queueData.songs.shift();
          setTimeout(() => this.playNext(guildId), 1000)})

        queueData.player.on('error', (error: any) => {this.logger.error(`Audio player error in guild ${guildId}:`, error);
          queueData.songs.shift();
          this.playNext(guildId)})}

      // In production, integrate with:
      // - youtube-dl-exec for YouTube downloads
      // - ytdl-core for YouTube streaming  
      // - spotify-web-api-node for Spotify integration
      // - SoundCloud API for SoundCloud support
      
      try {
        // For demo purposes, attempt to use a placeholder audio file
        const audioPath = join(__dirname, '../../../assets/placeholder-audio.mp3');
        const resource = createAudioResource(createReadStream(audioPath));
        
        queueData.player.play(resource);
        queueData.connection.subscribe(queueData.player);
        this.logger.log(`Playing audio resource for: ${song.title;
    } catch (error) {
      console.error(error);
    }
`)} catch (audioError) {
        this.logger.warn(`Audio resource not available for: ${song.title}, skipping to next`);
        queueData.songs.shift();
        this.playNext(guildId)}
      
    } catch (error) {
      this.logger.error(`Failed to play song ${song.title} in guild ${guildId}:`, error);
      queueData.songs.shift();
      this.playNext(guildId)}
  }

  private cleanup(guildId: string) {const queueData = this.queues.get(guildId);
    if (!queueData) return;

    // Stop player
    if (queueData.player) {
      queueData.player.stop()}

    // Disconnect from voice channel
    if (queueData.connection) {
      queueData.connection.destroy()}

    // Clear queue data
    this.queues.delete(guildId);
    this.logger.log(`Cleaned up music queue for guild ${guildId}`)}

  // Clean up connections when bot shuts down
  onModuleDestroy() {
    for (const [guildId] of this.queues) {
      this.cleanup(guildId)}
  }
}