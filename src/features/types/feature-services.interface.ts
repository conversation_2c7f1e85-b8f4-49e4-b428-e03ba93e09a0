import type { BaseEntity, CreateEntity, UpdateEntity } from '@/core/database/types';
import type { User, GuildMember, Role, TextChannel, MessageReaction, EmbedBuilder, Guild as DiscordGuild } from 'discord.js';
import type { SlashCommandContext } from 'necord';

/**
 * Base feature service interface
 */
export interface BaseFeatureService {
  readonly name: string;
  readonly version: string;
  readonly enabled: boolean;
  
  initialize?(): Promise<void>;
  shutdown?(): Promise<void>;
  getStatus(): FeatureStatus;
  getConfig(): FeatureConfig;
}

/**
 * Feature status enumeration
 */
export enum FeatureStatus {
  DISABLED = 'disabled',
  INITIALIZING = 'initializing',
  ACTIVE = 'active',
  ERROR = 'error',
  MAINTENANCE = 'maintenance'
}

/**
 * Base feature configuration interface
 */
export interface FeatureConfig {
  enabled: boolean;
  version: string;
  settings?: Record<string, unknown>;
  permissions?: string[];
  roles?: string[];
  channels?: string[];
  metadata?: Record<string, unknown>;
}

/**
 * User interaction types for feature services
 */
export interface UserInteraction {
  userId: string;
  guildId: string;
  channelId: string;
  timestamp: Date;
  type: InteractionType;
  data?: Record<string, unknown>;
}

export enum InteractionType {
  COMMAND = 'command',
  REACTION = 'reaction',
  MESSAGE = 'message',
  BUTTON = 'button',
  MODAL = 'modal',
  SELECT = 'select'
}

/**
 * Database integration types
 */
export interface DatabaseIntegration {
  entityName: string;
  operations: DatabaseOperation[];
  indexes?: DatabaseIndex[];
  migrations?: DatabaseMigration[];
}

export interface DatabaseOperation {
  name: string;
  type: 'create' | 'read' | 'update' | 'delete' | 'query';
  parameters: Record<string, unknown>;
  returnType?: string;
}

export interface DatabaseIndex {
  name: string;
  fields: string[];
  unique?: boolean;
  sparse?: boolean;
}

export interface DatabaseMigration {
  version: string;
  description: string;
  up: () => Promise<void>;
  down: () => Promise<void>;
}

/**
 * Feature lifecycle interface
 */
export interface FeatureLifecycle {
  onEnable(): Promise<void>;
  onDisable(): Promise<void>;
  onGuildJoin(guildId: string): Promise<void>;
  onGuildLeave(guildId: string): Promise<void>;
  onConfigUpdate(config: FeatureConfig): Promise<void>;
  onMemberJoin(member: GuildMember): Promise<void>;
  onMemberLeave(member: GuildMember): Promise<void>;
}

/**
 * Welcome service types
 */
export interface WelcomeServiceConfig extends FeatureConfig {
  channelId?: string;
  message?: string;
  roles?: string[];
  embedEnabled?: boolean;
  dmWelcome?: boolean;
  autoDeleteDelay?: number;
}

export interface WelcomeRole {
  id: string;
  name: string;
  automatic: boolean;
  temporary?: boolean;
  duration?: number;
}

export interface WelcomeConfigResponse {
  enabled: boolean;
  channelId: string | null;
  message: string | null;
  roles: WelcomeRole[];
  embedEnabled?: boolean;
  dmWelcome?: boolean;
}

export interface WelcomeService extends BaseFeatureService, FeatureLifecycle {
  handleMemberJoin(member: GuildMember): Promise<void>;
  sendWelcomeMessage(member: GuildMember, config: WelcomeServiceConfig): Promise<void>;
  assignWelcomeRoles(member: GuildMember, roles: WelcomeRole[]): Promise<void>;
  updateWelcomeConfig(guildId: string, config: Partial<WelcomeServiceConfig>): Promise<{ message: string; config: WelcomeConfigResponse }>;
  getWelcomeConfig(guildId: string): Promise<WelcomeConfigResponse>;
}

/**
 * Utility service types
 */
export interface UtilityServiceConfig extends FeatureConfig {
  pingEnabled?: boolean;
  serverInfoEnabled?: boolean;
  userInfoEnabled?: boolean;
  helpEnabled?: boolean;
  customCommands?: UtilityCommand[];
}

export interface UtilityCommand {
  name: string;
  description: string;
  response: string | ((context: SlashCommandContext) => Promise<string>);
  permissions?: string[];
  cooldown?: number;
  ephemeral?: boolean;
}

export interface ServerInfo {
  name: string;
  memberCount: number;
  createdAt: Date;
  ownerId: string;
  features?: string[];
  boostLevel?: number;
  boostCount?: number;
}

export interface UserInfo {
  id: string;
  tag: string;
  username: string;
  discriminator: string;
  avatarUrl?: string;
  createdAt: Date;
  joinedAt?: Date;
  roles?: string[];
  permissions?: string[];
}

export interface UtilityService extends BaseFeatureService {
  onPingCommand(context: SlashCommandContext): Promise<void>;
  onServerInfoCommand(context: SlashCommandContext): Promise<void>;
  onUserInfoCommand(context: SlashCommandContext, targetUser?: User): Promise<void>;
  onHelpCommand(context: SlashCommandContext): Promise<void>;
  getServerInfo(guild: DiscordGuild): Promise<ServerInfo>;
  getUserInfo(user: User, member?: GuildMember): Promise<UserInfo>;
}

/**
 * User command service types
 */
export interface UserCommandServiceConfig extends FeatureConfig {
  prefix?: string;
  allowedChannels?: string[];
  commands?: UserCommand[];
  moderationEnabled?: boolean;
  logCommands?: boolean;
}

export interface UserCommand {
  name: string;
  response: string;
  description?: string;
  permissions?: string[];
  cooldown?: number;
  enabled: boolean;
  aliases?: string[];
  category?: string;
  usage?: string;
  examples?: string[];
}

export interface UserCommandExecution {
  command: UserCommand;
  user: User;
  guild: DiscordGuild;
  channel: TextChannel;
  args: string[];
  timestamp: Date;
}

export interface UserCommandService extends BaseFeatureService {
  handleMessage(message: any): Promise<void>;
  executeCommand(execution: UserCommandExecution): Promise<void>;
  updateUserCommandConfig(guildId: string, config: Partial<UserCommandServiceConfig>): Promise<{ message: string; config: UserCommandServiceConfig }>;
  getUserCommandConfig(guildId: string): Promise<UserCommandServiceConfig>;
  addUserCommand(guildId: string, command: UserCommand): Promise<{ message: string; command: UserCommand }>;
  removeUserCommand(guildId: string, commandName: string): Promise<{ message: string }>;
  toggleUserCommand(guildId: string, commandName: string, enabled: boolean): Promise<{ message: string }>;
  isOnCooldown(userId: string, commandName: string, cooldownTime: number): boolean;
  setCooldown(userId: string, commandName: string): void;
}

/**
 * Starboard service types
 */
export interface StarboardServiceConfig extends FeatureConfig {
  channelId?: string;
  threshold?: number;
  emoji?: string;
  embedColor?: number;
  ignoreBots?: boolean;
  ignoreChannels?: string[];
  requireUniqueReactions?: boolean;
}

export interface StarboardEntry {
  messageId: string;
  channelId: string;
  guildId: string;
  authorId: string;
  starCount: number;
  starboardMessageId?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface StarboardService extends BaseFeatureService {
  handleReactionAdd(reaction: MessageReaction, user: User): Promise<void>;
  handleReactionRemove(reaction: MessageReaction, user: User): Promise<void>;
  addToStarboard(reaction: MessageReaction, config: StarboardServiceConfig): Promise<void>;
  updateStarboard(entry: StarboardEntry, newStarCount: number): Promise<void>;
  removeFromStarboard(entry: StarboardEntry): Promise<void>;
  updateStarboardConfig(guildId: string, config: Partial<StarboardServiceConfig>): Promise<{ message: string; config: StarboardServiceConfig }>;
  getStarboardConfig(guildId: string): Promise<StarboardServiceConfig>;
  getStarboardEntry(messageId: string): Promise<StarboardEntry | null>;
}

/**
 * Reaction role service types
 */
export interface ReactionRoleServiceConfig extends FeatureConfig {
  rules?: ReactionRoleRule[];
  requireBothActions?: boolean;
  ignoreBots?: boolean;
  logActions?: boolean;
  maxRolesPerUser?: number;
}

export interface ReactionRoleRule {
  id?: string;
  messageId: string;
  channelId: string;
  emoji: string;
  roleId: string;
  description?: string;
  temporary?: boolean;
  duration?: number;
  category?: string;
}

export interface ReactionRoleAction {
  userId: string;
  guildId: string;
  messageId: string;
  roleId: string;
  emoji: string;
  action: 'add' | 'remove';
  timestamp: Date;
  success: boolean;
  error?: string;
}

export interface ReactionRoleService extends BaseFeatureService {
  handleReactionAdd(reaction: MessageReaction, user: User): Promise<void>;
  handleReactionRemove(reaction: MessageReaction, user: User): Promise<void>;
  addRole(member: GuildMember, role: Role, rule: ReactionRoleRule): Promise<void>;
  removeRole(member: GuildMember, role: Role, rule: ReactionRoleRule): Promise<void>;
  updateReactionRoleConfig(guildId: string, config: Partial<ReactionRoleServiceConfig>): Promise<{ message: string; config: ReactionRoleServiceConfig }>;
  getReactionRoleConfig(guildId: string): Promise<ReactionRoleServiceConfig>;
  addReactionRole(guildId: string, rule: ReactionRoleRule): Promise<{ message: string; rule: ReactionRoleRule }>;
  removeReactionRole(guildId: string, messageId: string, emoji: string): Promise<{ message: string }>;
  getRuleByReaction(messageId: string, emoji: string): Promise<ReactionRoleRule | null>;
  validateRule(rule: ReactionRoleRule): Promise<{ valid: boolean; errors: string[] }>;
}

/**
 * Feature service registry interface
 */
export interface FeatureServiceRegistry {
  register(name: string, service: BaseFeatureService): void;
  unregister(name: string): void;
  get(name: string): BaseFeatureService | undefined;
  getAll(): Map<string, BaseFeatureService>;
  getEnabled(): Map<string, BaseFeatureService>;
  getByCategory(category: string): Map<string, BaseFeatureService>;
  initialize(): Promise<void>;
  shutdown(): Promise<void>;
}

/**
 * Feature service factory interface
 */
export interface FeatureServiceFactory {
  create(type: string, config: FeatureConfig): BaseFeatureService;
  createWelcomeService(config: WelcomeServiceConfig): WelcomeService;
  createUtilityService(config: UtilityServiceConfig): UtilityService;
  createUserCommandService(config: UserCommandServiceConfig): UserCommandService;
  createStarboardService(config: StarboardServiceConfig): StarboardService;
  createReactionRoleService(config: ReactionRoleServiceConfig): ReactionRoleService;
}

/**
 * Feature service event types
 */
export interface FeatureServiceEvent {
  type: string;
  serviceName: string;
  data?: unknown;
  timestamp: Date;
  guildId?: string;
  userId?: string;
}

export interface FeatureServiceEventHandler {
  handle(event: FeatureServiceEvent): Promise<void>;
}

/**
 * Feature service metrics interface
 */
export interface FeatureServiceMetrics {
  commandsExecuted: number;
  reactionsProcessed: number;
  messagesHandled: number;
  errorsEncountered: number;
  averageResponseTime: number;
  uptime: number;
  lastActivity?: Date;
}

export interface FeatureServiceHealth {
  status: FeatureStatus;
  metrics: FeatureServiceMetrics;
  lastHealthCheck: Date;
  errors?: string[];
  warnings?: string[];
}

/**
 * Feature service monitoring interface
 */
export interface FeatureServiceMonitor {
  checkHealth(serviceName: string): Promise<FeatureServiceHealth>;
  getMetrics(serviceName: string): Promise<FeatureServiceMetrics>;
  recordEvent(event: FeatureServiceEvent): Promise<void>;
  getEvents(serviceName: string, limit?: number): Promise<FeatureServiceEvent[]>;
}

/**
 * Export all types
 */
export type {
  BaseFeatureService,
  FeatureConfig,
  UserInteraction,
  DatabaseIntegration,
  FeatureLifecycle,
  WelcomeService,
  WelcomeServiceConfig,
  WelcomeConfigResponse,
  UtilityService,
  UtilityServiceConfig,
  UserCommandService,
  UserCommandServiceConfig,
  UserCommand,
  StarboardService,
  StarboardServiceConfig,
  StarboardEntry,
  ReactionRoleService,
  ReactionRoleServiceConfig,
  ReactionRoleRule,
  ReactionRoleAction,
  FeatureServiceRegistry,
  FeatureServiceFactory,
  FeatureServiceEvent,
  FeatureServiceEventHandler,
  FeatureServiceMetrics,
  FeatureServiceHealth,
  FeatureServiceMonitor
};
