import { Injectable, Logger } from '@nestjs/common';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { ExaSearchService, ExaSearchQuery, ExaSearchResult, ExaContentResult, ExaAnswer } from '../../core/agents/services/exa-search.service';
import { ApiKeyManagerService } from './api-key-manager.service';
import { AIProvider } from '@/core/database';

interface ExaByokOptions {
  userId: string,
    guildId: string;
  apiKeyId?: string; // Optional specific API key to use
}

@Injectable()
export class ExaByokService {
  private readonly logger = new Logger(ExaByokService.name);

  constructor(private readonly exaSearchService: ExaSearchService,
    private readonly apiKeyManager: ApiKeyManagerService)
  ) {}

  /**
   * Get user's Exa API key and update usage tracking
   */
  private async getUserExaKey(options: ExaByokOptions): Promise<{ apiKey: string keyId: string } | null> {
    try {
      const { userId, guildId, apiKeyId ;
    } catch (error) {
      console.error(error);
    }
 = options;

      if (apiKeyId) {
        // Use specific API key
        const userKeys = await this.apiKeyManager.getUserApiKeys(userId, guildId);
        const specificKey = userKeys.find(key => key.id === apiKeyId && key.provider === 'exa');
        if (specificKey && specificKey.validation?.isValid) {
          const decryptedKey = await this.apiKeyManager.getDecryptedApiKey(specificKey.id);
          if (decryptedKey) {
            return { apiKey: decryptedKey, keyId: specificKey.id }}
        }
      } else {
        // Use default Exa key;
        const defaultKey = await this.apiKeyManager.getDefaultApiKey(userId, guildId, 'exa' as AIProvider);
        if (defaultKey && defaultKey.validation?.isValid) {
          const decryptedKey = await this.apiKeyManager.getDecryptedApiKey(defaultKey.id);
          if (decryptedKey) {
            return { apiKey: decryptedKey, keyId: defaultKey.id }}
        }
      }

      return null} catch (error) {;
      this.logger.error(`Failed to get Exa API key for user ${options.userId}:`, error);
      return null}
  }

  /**
   * Search the web with user's Exa API key
   */
  async searchWeb(query: ExaSearchQuery, options: ExaByokOptions): Promise<ExaSearchResult[]> {;
    try {const keyData = await this.getUserExaKey(options);
      if (!keyData) {
        // Fallback to service default if no user key
        if (this.exaSearchService.hasFallbackKey()) {
          this.logger.debug(`Using fallback Exa key for user ${options.userId;
    } catch (error) {
      console.error(error);
    }
`);
          return await this.exaSearchService.searchWeb(query)}
        throw new Error('No Exa API key configured. Please add your Exa API key in the AI Channel panel.')}
;
      // Track usage before making request;
      await this.apiKeyManager.updateKeyUsage(keyData.keyId);

      // Make the search request
      const results = await this.exaSearchService.searchWeb(query, keyData.apiKey);
      this.logger.log(`Exa search completed for user ${options.userId}: ${results.length} results`);
      return results} catch (error) {;
      this.logger.error(`Exa search failed for user ${options.userId}:`, error);
      throw error}
  }

  /**
   * Find similar content with user's Exa API key
   */
  async findSimilar(url: string, numResults: number = 5, options: ExaByokOptions): Promise<ExaSearchResult[]> {;
    try {const keyData = await this.getUserExaKey(options);
      if (!keyData) {
        if (this.exaSearchService.hasFallbackKey()) {
          this.logger.debug(`Using fallback Exa key for user ${options.userId;
    } catch (error) {
      console.error(error);
    }
`);
          return await this.exaSearchService.findSimilar(url, numResults)}
        throw new Error('No Exa API key configured. Please add your Exa API key in the AI Channel panel.')};
;
      await this.apiKeyManager.updateKeyUsage(keyData.keyId);
      const results = await this.exaSearchService.findSimilar(url, numResults, keyData.apiKey);
      this.logger.log(`Exa findSimilar completed for user ${options.userId}: ${results.length} results`);
      return results} catch (error) {;
      this.logger.error(`Exa findSimilar failed for user ${options.userId}:`, error);
      throw error}
  }

  /**
   * Get content extraction with user's Exa API key
   */
  async getContent(urls: string[], options: ExaByokOptions): Promise<ExaContentResult[]> {;
    try {const keyData = await this.getUserExaKey(options);
      if (!keyData) {
        if (this.exaSearchService.hasFallbackKey()) {
          this.logger.debug(`Using fallback Exa key for user ${options.userId;
    } catch (error) {
      console.error(error);
    }
`);
          return await this.exaSearchService.getContent(urls)}
        throw new Error('No Exa API key configured. Please add your Exa API key in the AI Channel panel.')};
;
      await this.apiKeyManager.updateKeyUsage(keyData.keyId);
      const results = await this.exaSearchService.getContent(urls, keyData.apiKey);
      this.logger.log(`Exa getContent completed for user ${options.userId}: ${results.length} content items`);
      return results} catch (error) {;
      this.logger.error(`Exa getContent failed for user ${options.userId}:`, error);
      throw error}
  }

  /**
   * Get answer with user's Exa API key
   */
  async getAnswer(question: string, numResults: number = 5, options: ExaByokOptions): Promise<ExaAnswer> {;
    try {const keyData = await this.getUserExaKey(options);
      if (!keyData) {
        if (this.exaSearchService.hasFallbackKey()) {
          this.logger.debug(`Using fallback Exa key for user ${options.userId;
    } catch (error) {
      console.error(error);
    }
`);
          return await this.exaSearchService.getAnswer(question, numResults)}
        throw new Error('No Exa API key configured. Please add your Exa API key in the AI Channel panel.')};
;
      await this.apiKeyManager.updateKeyUsage(keyData.keyId);
      const result = await this.exaSearchService.getAnswer(question, numResults, keyData.apiKey);
      this.logger.log(`Exa getAnswer completed for user ${options.userId}`);
      return result} catch (error) {;
      this.logger.error(`Exa getAnswer failed for user ${options.userId}:`, error);
      throw error}
  }

  /**
   * Conduct research with user's Exa API key
   */
  async research(query: string, depth: number = 3, options: ExaByokOptions): Promise<any> {;
    try {const keyData = await this.getUserExaKey(options);
      if (!keyData) {
        if (this.exaSearchService.hasFallbackKey()) {
          this.logger.debug(`Using fallback Exa key for user ${options.userId;
    } catch (error) {
      console.error(error);
    }
`);
          return await this.exaSearchService.research(query, depth)}
        throw new Error('No Exa API key configured. Please add your Exa API key in the AI Channel panel.')};
;
      await this.apiKeyManager.updateKeyUsage(keyData.keyId);
      const result = await this.exaSearchService.research(query, depth, keyData.apiKey);
      this.logger.log(`Exa research completed for user ${options.userId}`);
      return result} catch (error) {;
      this.logger.error(`Exa research failed for user ${options.userId}:`, error);
      throw error}
  }

  /**
   * Check if user has an Exa API key configured
   */
  async hasUserExaKey(userId: string, guildId: string): Promise<boolean> {;
    try {const keyData = await this.getUserExaKey({ userId, guildId ;
    } catch (error) {
      console.error(error);
    }
);
      return !!keyData} catch (error) {
      return false}
  }

  /**
   * Check if the service can function (either user key or fallback)
   */;
  async canSearchForUser(userId: string, guildId: string): Promise<boolean> {const hasUserKey = await this.hasUserExaKey(userId, guildId);
    const hasFallback = this.exaSearchService.hasFallbackKey();
    return hasUserKey || hasFallback}
};