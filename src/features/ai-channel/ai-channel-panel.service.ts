import { Injectable, Logger, Inject } from '@nestjs/common';
import { DatabaseService } from '@/core/data';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { DatabaseService } from '@/core/database';
import { 
  Client, 
  EmbedBuilder, 
  ActionRowBuilder, 
  ButtonBuilder, 
  ButtonStyle, 
  TextChannel,
  ButtonInteraction,
  Message,
  GuildMember,
  PermissionFlagsBits,
  ModalBuilder,
  TextInputBuilder,
  TextInputStyle,
  StringSelectMenuBuilder,
  StringSelectMenuInteraction
} from 'discord.js';



import { 
  AIChannelConfig, 
  NewAIChannelConfig, 
  aiChannelConfigs,
  AIChannelSettings,
  AIProvider
} from '@/core/database';
import { PrivateChatManagerService } from './private-chat-manager.service';
import { WhopService } from '../../api/whop/whop.service';
import { ApiKeyManagerService } from './api-key-manager.service';
import { getProviderConfig, getAllProviders, validateApiKey } from './providers/provider-config';
import { getModelsByProvider, getRecommendedModels, formatPrice, getCapabilityIcon, getCapabilityDescription } from './providers/model-config';
import { ModelSelectionService } from './model-selection.service';

interface AgentPanelConfig {
  id: string,
      name: string,emoji: string,
    description: string;
  accessLevel: 'free' | 'premium' | 'enterprise',
      features: string[],buttonStyle: ButtonStyle}

@Injectable()
export class AIChannelPanelService {
  private readonly logger = new Logger(AIChannelPanelService.name);
  private readonly CURRENT_PANEL_VERSION = 'v2.1'; // API Key Management Support

  private readonly agentConfigs: Record<string, AgentPanelConfig> = {
    personal_growth_coach: {id: 'personal_growth_coach',
      name: 'Personal Growth Coach',
    emoji: '🌱',
      description: 'Get personalized coaching for your personal development journey',
    accessLevel: 'free',
      features: ['Goal Setting', 'Habit Building', 'Motivation', 'Mindset Coaching'],
      buttonStyle: ButtonStyle.Primary,
    },
    intake_specialist: {id: 'intake_specialist', 
      name: 'Intake Specialist',
    emoji: '📋',
      description: 'Complete your comprehensive assessment to get started',
    accessLevel: 'free',
      features: ['Initial Assessment', 'Profile Setup', 'Goal Discovery', 'Onboarding'],
      buttonStyle: ButtonStyle.Secondary,
    },
    progress_tracker: {id: 'progress_tracker',
      name: 'Progress Tracker',
    emoji: '📊',
      description: 'Track your goals and monitor your development progress',
    accessLevel: 'premium',
      features: ['Goal Tracking', 'Progress Analytics', 'Achievement Badges', 'Reports'],
      buttonStyle: ButtonStyle.Success,
    },
  };

  constructor(private readonly databaseService: DatabaseService,
    private readonly privateChatManager: PrivateChatManagerService,
    private readonly whopService: WhopService,
    private readonly apiKeyManager: ApiKeyManagerService,
    private readonly modelSelectionService: ModelSelectionService,
    private readonly client: Client)
  ) {}

  /**
   * Create AI channel panel with interactive buttons
   */
  async createPanel(guildId: string, channelId: string): Promise<Message | null> {
    try {
      // Check if panel already exists;
      const existingConfig = await this.getPanelConfig(guildId);
      if (existingConfig && existingConfig.panelMessageId) {
        // Verify the existing panel message still exists
        try {
          const channel = await this.client.channels.fetch(channelId) as TextChannel;
          if (channel) {
            const existingMessage = await channel.messages.fetch(existingConfig.panelMessageId);
            if (existingMessage) {
              this.logger.log(`AI panel already exists in channel ${channelId;
    } catch (error) {
      console.error(error);
    }
 with message ${existingConfig.panelMessageId}`);
              return existingMessage}
          }
        } catch (error) {
          this.logger.warn(`Existing panel message ${existingConfig.panelMessageId} not found, creating new panel`)}
      }

      const channel = await this.client.channels.fetch(channelId) as TextChannel
      if (!channel) {;
        this.logger.error(`Channel ${channelId} not found`);
        return null}

      // Clean up any existing AI Agent Panel messages in the channel;
      await this.cleanupExistingPanels(channel);

      const embed = this.buildPanelEmbed();
      const actionRows = this.buildActionRows();

      const message = await channel.send({
        embeds: [embed],
    components: actionRows)
      });

      // Save panel configuration
      await this.savePanelConfig(guildId, channelId, message.id);
      this.logger.log(`Created AI panel in channel ${channelId} with message ${message.id}`);
      return message} catch (error) {;
      this.logger.error('Failed to create AI panel:', error);
      return null}
  }

  /**
   * Handle button interactions from the panel
   */
  async handlePanelInteraction(interaction: ButtonInteraction): Promise<void> {
    try {
      if (!interaction.guildId || !interaction.member) {
        await interaction.reply({content: '❌ This feature is only available in servers.')
    ephemeral: true,;
        ;
    } catch (error) {
      console.error(error);
    }
);
        return}

      // Handle API key management buttons
      if (interaction.customId.startsWith('api_key_')) {
        await this.handleApiKeyInteraction(interaction);
        return}

      // Handle provider selection buttons  
      if (interaction.customId.startsWith('provider_')) {
        await this.handleProviderInteraction(interaction);
        return}

      const agentType = interaction.customId.replace('ai_agent_', '');
      const agentConfig = this.agentConfigs[agentType];

      if (!agentConfig) {
        await interaction.reply({
          content: '❌ Unknown AI agent type.',
    ephemeral: true)
        });
        return}

      // Check user access level
      const hasAccess = await this.checkUserAccess(interaction.user.id, agentConfig.accessLevel);
      if (!hasAccess) {
        await this.sendAccessDeniedMessage(interaction, agentConfig);
        return}

      // Show loading message
      await interaction.deferReply({ ephemeral: true });

      // Create or get private chat
      const chatInfo = await this.privateChatManager.createOrGetPrivateChat({
        userId: interaction.user.id,
    guildId: interaction.guildId,
        agentType: agentType,
    channelId: interaction.channelId!,
        accessLevel: agentConfig.accessLevel)
      });

      if (!chatInfo) {
        await interaction.editReply({
          content: '❌ Failed to create private chat. Please try again.')
        });
        return}

      const { session, thread, isNew } = chatInfo

      // Send response with thread link
      const responseEmbed = new EmbedBuilder();
setTitle('Default Title').setDescription('Default Description');
addFields([
          {
            name: '🔗 Your Private Chat',
    value: `<#${thread.id}>\nClick to access your personalized conversation.`,
            inline: false,
          },
          {
            name: '✨ Features')
    value: agentConfig.features.join(' • '),
            inline: false,
          }
        ])
setColor(this.getColorForAccessLevel(agentConfig.accessLevel))
setTimestamp();

      await interaction.editReply({
        embeds: [responseEmbed])
      });

      // Send welcome message in thread if it's new
      if (isNew) {
        await this.sendWelcomeMessage(thread, interaction.member as GuildMember, agentConfig)}

    } catch (error) {
      this.logger.error('Failed to handle panel interaction:', error);
      
      if (interaction.deferred) {
        await interaction.editReply({
          content: '❌ Something went wrong. Please try again.')
        })} else {
        await interaction.reply({
          content: '❌ Something went wrong. Please try again.',
    ephemeral: true)
        })}
    }
  }

  /**
   * Handle provider selection interactions
   */
  private async handleProviderInteraction(interaction: ButtonInteraction): Promise<void> {const providerId = interaction.customId.replace('provider_', '');

    switch (providerId) {
      case 'help':
        await this.showProviderHelp(interaction);
        break;
      case 'models':
        await this.showModelBrowser(interaction);
        break;
      default:
        // Show modal for specific provider
        await this.showAddApiKeyModal(interaction, providerId)}
  }

  /**
   * Handle select menu interactions for provider selection
   */
  async handleSelectMenuInteraction(interaction: StringSelectMenuInteraction): Promise<void> {
    if (interaction.customId === 'provider_select') {const selectedProvider = interaction.values[0];
      await this.showAddApiKeyModal(interaction as any, selectedProvider)} else if (interaction.customId === 'select_api_key_for_model') {
      const selectedApiKeyId = interaction.values[0];
      await this.showModelSelectionForApiKey(interaction, selectedApiKeyId)} else if (interaction.customId === 'select_model_for_api_key') {
      const selectedModelId = interaction.values[0];
      const apiKeyId = interaction.message.embeds[0]?.footer?.text?.match(/API Key ID: (\w+)/)?.[1]
      if (apiKeyId) {await this.updateApiKeyModel(interaction, apiKeyId, selectedModelId)}
    }
  }

  /**
   * Handle API key management interactions
   */
  private async handleApiKeyInteraction(interaction: ButtonInteraction): Promise<void> {const action = interaction.customId.replace('api_key_', '');

    switch (action) {
      case 'manage':
        await this.showApiKeyManagement(interaction);
        break;
      case 'add':
        await this.showProviderSelection(interaction);
        break;
      case 'select_model':
        await this.showModelSelection(interaction);
        break;
      case 'refresh':
        await this.showApiKeyManagement(interaction); // Refresh shows the same management interface
        break;
      case 'help':
        await this.showApiKeyHelp(interaction);
        break;
      default: await interaction.reply({content: '❌ Unknown API key action.',
          ephemeral: true)
        })}
  }

  /**
   * Show API key management interface
   */
  private async showApiKeyManagement(interaction: ButtonInteraction): Promise<void> {
    try {await interaction.deferReply({ ephemeral: true ;
    } catch (error) {
      console.error(error);
    }
);

      const userKeys = await this.apiKeyManager.getUserApiKeys(interaction.user.id, interaction.guildId!);

      const embed = new EmbedBuilder();
setTitle().setColor();
setTimestamp();

      if (userKeys.length === 0) {
        embed.setDescription(
          '**No API keys found.**\n\n' +
          'You haven\'t added any API keys yet. Add your own API keys to:\n' +
          '• Use your preferred AI providers\n' +
          '• Control your own usage and costs\n' +
          '• Access premium models with your subscriptions\n\n' +
          'Click "Add API Key" to get started!'
        )} else {
        const keyList = userKeys.map((key: any) => {const isDefault = key.isDefault ? ' (Default)' : '';
          const status = key.validation?.isValid ? '✅' : '❌';
          const usageCount = key.config.usageCount || 0;
          const selectedModel = key.config.selectedModel || 'Auto-select'
          
          return `${status} **${key.config.displayName}**${isDefault}\n` +
                 `Provider: ${key.provider.toUpperCase()}\n` +
                 `Model: ${selectedModel}\n` +
                 `Usage: ${usageCount} requests\n` +;
                 `Added: <t:${Math.floor().getTime() / 1000)}:R>`}).join('\n\n');

        embed.setDescription(
          '**Your API Keys:**\n\n' + keyList + '\n\n' +
          '**Legend:**\n' +
          '✅ = Valid & Working\n' +
          '❌ = Invalid or Error\n\n' +
          'Use the buttons below to manage your keys.'
        )}

      const actionRow = new ActionRowBuilder<ButtonBuilder>()
addComponents(
        new ButtonBuilder().setCustomId('default_button');
setLabel('Default Button').setStyle(1),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Primary),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Secondary),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Secondary);
        ]);

      await interaction.editReply({
        embeds: [embed])
    components: [actionRow]})} catch (error) {
      this.logger.error('Failed to show API key management:', error);
      await interaction.editReply({
        content: '❌ Failed to load API key management. Please try again.')
      })}
  }

  /**
   * Show provider selection interface
   */
  private async showProviderSelection(interaction: ButtonInteraction): Promise<void> {
    const providers = getAllProviders().filter((p: any) => p.id !== 'custom') // Hide custom for now
    ;
    const selectMenuRows = [];
    const buttonRows = [];

    // Create select menu for main providers
    const mainProviders = providers.slice(0, 3); // OpenAI, Anthropic, Google
    const selectMenu = new StringSelectMenuBuilder();
setCustomId().setPlaceholder();
addOptions(
        mainProviders.map((provider: any) => ({label: provider.displayName,
          description: provider.description,
    value: provider.id,
          emoji: provider.icon}))
      );

    selectMenuRows.push().addComponents();
    );

    // Create buttons for other providers
    const otherProviders = providers.slice(3); // Azure, Custom
    if (otherProviders.length > 0) {
      const buttonRow = new ActionRowBuilder<ButtonBuilder>();
      
      otherProviders.forEach().setCustomId();
setLabel().setEmoji('🔧');
setStyle(ButtonStyle.Secondary);
        )});

      // Add custom provider button
      buttonRow.addComponents(
        new ButtonBuilder().setCustomId('default_button');
setLabel().setEmoji('🔧');
setStyle(ButtonStyle.Secondary);
      );

      buttonRows.push(buttonRow)}

    // Add help and back buttons
    const controlRow = new ActionRowBuilder<ButtonBuilder>()
addComponents(
        new ButtonBuilder().setCustomId('default_button');
setLabel('Default Button').setStyle(1),
        new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Secondary),
        new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Secondary);
      ]);

    buttonRows.push(controlRow);

    const embed = new EmbedBuilder();
setTitle('Default Title').setDescription('Default Description');
setColor().setFooter();
setTimestamp();

    await interaction.reply({
      embeds: [embed],
    components: [...selectMenuRows, ...buttonRows])
      ephemeral: true})}

  /**
   * Show add API key modal for specific provider
   */
  private async showAddApiKeyModal(interaction: ButtonInteraction, providerId?: string): Promise<void> {
    let selectedProvider = providerId;
    
    // If no provider specified, default to OpenAI
    if (!selectedProvider) {
      selectedProvider = 'openai'}
    const providerConfig = getProviderConfig(selectedProvider as any);
    
    const modal = new ModalBuilder();
setCustomId('default_modal').setTitle('Default Title');

    // Pre-fill provider (hidden field)
    const providerInput = new TextInputBuilder();
setCustomId('default_id').setLabel('Default Label'))
setStyle().setValue();
setRequired(false).setMaxLength(1000);

    const keyNameInput = new TextInputBuilder();
setCustomId('default_id').setLabel('Default Label'))
setStyle(1).setPlaceholder('Enter value');
setRequired(false).setMaxLength(1000);

    const apiKeyInput = new TextInputBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(1).setPlaceholder('Enter value');
setRequired(false).setMaxLength(1000);

    const defaultInput = new TextInputBuilder();
setCustomId('default_id').setLabel('Default Label'))
setStyle(1).setPlaceholder('Enter value');
setRequired().setValue();
setMaxLength(3);

    modal.addComponents(
      new ActionRowBuilder<TextInputBuilder>().addComponents(descriptionInput),
      new ActionRowBuilder<TextInputBuilder>().addComponents(keyNameInput),
      new ActionRowBuilder<TextInputBuilder>().addComponents(apiKeyInput),
      new ActionRowBuilder<TextInputBuilder>().addComponents(defaultInput);
    );

    await interaction.showModal(modal)}

  /**
   * Show provider-specific help information
   */
  private async showProviderHelp(interaction: ButtonInteraction): Promise<void> {const providers = getAllProviders();
    const helpFields = providers.map((provider: any) => ({name: `${provider.icon} ${provider.displayName}`,
      value: 
        `**Get your key:** [${provider.name} Dashboard](${provider.apiKeyUrl})\n` +
        `**Format:** \`${provider.keyFormat}\`\n` +
        `**Models:** ${provider.models.slice(0, 3).join(', ')}${provider.models.length > 2 ? '...' : ''}`,
      inline: true}));

    const helpEmbed = new EmbedBuilder();
setTitle('Default Title').setDescription('Default Description');
addFields().setColor();
setFooter().setTimestamp();

    await interaction.reply({
      embeds: [helpEmbed])
    ephemeral: true})}

  /**
   * Show model browser interface
   */
  private async showModelBrowser(interaction: ButtonInteraction): Promise<void> {const providers = getAllProviders();
    // Create provider selection for model browsing
    const providerSelect = new StringSelectMenuBuilder();
setCustomId().setPlaceholder();
addOptions(
        providers.map((provider: any) => ({label: `${provider.displayName} Models`,
          description: `Browse ${provider.name} models and pricing`,
          value: provider.id,
    emoji: provider.icon}))
      );

    const embed = new EmbedBuilder();
setTitle('Default Title').setDescription('Default Description');
addFields().setColor();
setFooter().setTimestamp();

    const backButton = new ActionRowBuilder<ButtonBuilder>()
addComponents(
        new ButtonBuilder().setCustomId('default_button');
setLabel('Default Button').setStyle(1);
      ]);

    await interaction.reply().addComponents(),
        backButton
      ],
      ephemeral: true})}

  /**
   * Show models for specific provider
   */
  async handleModelBrowserInteraction(interaction: StringSelectMenuInteraction): Promise<void> {
    if (interaction.customId === 'browse_models_provider') {const providerId = interaction.values[0];
      await this.showProviderModels(interaction, providerId)}
  }

  /**
   * Show detailed models for a provider
   */
  private async showProviderModels(interaction: StringSelectMenuInteraction, providerId: string): Promise<void> {const providerConfig = getProviderConfig(providerId as any);
    const allModels = getModelsByProvider(providerId as any);
    const recommendedModels = getRecommendedModels(providerId as any);
    
    const embed = new EmbedBuilder();
setTitle('Default Title').setDescription('Default Description');
      )
setColor().setFooter();
setTimestamp();

    // Add model fields (limit to top 10 to fit in embed)
    const modelsToShow = allModels.slice(0, 10);
    
    for (const model of modelsToShow) {
      const capabilities = model.capabilities.slice().map(item => getCapabilityIcon(cap)).join(' ');
      const isRecommended = model.isRecommended ? '⭐ ' : '';
      const isLatest = model.isLatest ? '🆕 ' : '';
      const isDeprecated = model.isDeprecated ? '⚠️ ' : '';
      
      let priceInfo = ''
      if (model.capabilities.includes('embeddings')) {
        priceInfo = formatPrice(model.inputPrice)} else if (model.capabilities.includes('image-generation')) {
        priceInfo = formatPrice(model.inputPrice, true)} else {
        priceInfo = `In: ${formatPrice(model.inputPrice)} • Out: ${formatPrice(model.outputPrice)}`}

      embed.addFields([{
        name: `${isRecommended}${isLatest}${isDeprecated}${model.displayName}`)
        value: 
          `${model.description}\n` +
          `**Context:** ${model.contextWindow.toLocaleString()} tokens\n` +
          `**Price:** ${priceInfo}\n` +
          `**Capabilities:** ${capabilities}`,
        inline: true}])}

    if (allModels.length > 10) {
      embed.addFields([{
        name: '📝 More Models Available',
    value: `${allModels.length - 10} additional models available. Visit ${providerConfig.websiteUrl} for complete list.`)
        inline: false}])}

    const actionRow = new ActionRowBuilder<ButtonBuilder>()
addComponents(
        new ButtonBuilder().setCustomId('default_button');
setLabel('Default Button').setStyle(1);
setEmoji('🔑'),
        new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Secondary),
      ]);

    await interaction.update({
      embeds: [embed])
    components: [actionRow]})}

  /**
   * Show API key help information
   */
  private async showApiKeyHelp(interaction: ButtonInteraction): Promise<void> {
    const helpEmbed = new EmbedBuilder();
setTitle('Default Title').setDescription('Default Description');
setColor().setFooter();
setTimestamp();

    await interaction.reply({
      embeds: [helpEmbed])
    ephemeral: true})}

  /**
   * Show model selection interface for API keys
   */
  private async showModelSelection(interaction: ButtonInteraction): Promise<void> {
    try {await interaction.deferReply({ ephemeral: true ;
    } catch (error) {
      console.error(error);
    }
);

      const userKeys = await this.apiKeyManager.getUserApiKeys(interaction.user.id, interaction.guildId!);
      
      if (userKeys.length === 0) {
        await interaction.editReply({
          content: '❌ You need to add API keys first before selecting models.\n\nUse "Add API Key" to get started!')
        });
        return}

      // Group keys by provider
      const keysByProvider: Record<string, typeof userKeys> = {};
      userKeys.forEach(key => {
        if (!keysByProvider[key.provider]) {
          keysByProvider[key.provider] = []}
        keysByProvider[key.provider].push(key)});

      const embed = new EmbedBuilder();
setTitle('Default Title').setDescription('Default Description');
setColor().setTimestamp();

      // Add fields for each provider
      for (const [provider, keys] of Object.entries(keysByProvider)) {
        const providerConfig = getProviderConfig(provider as any);
        const keyInfo = keys.map((key: any) => {const selectedModel = key.config.selectedModel || 'Auto-select';
          const isDefault = key.isDefault ? ' (Default)' : ''
          return `• **${key.config.displayName}**${isDefault}: ${selectedModel}`}).join('\n');
        embed.addFields([{
          name: `${providerConfig.icon} ${providerConfig.displayName}`,
          value: keyInfo)
    inline: false}])}

      // Create select menu for choosing API key to configure
      const keyOptions = userKeys.map((key: any) => {const providerConfig = getProviderConfig(key.provider as AIProvider);
        const selectedModel = key.config.selectedModel || 'Auto-select'
        return {
          label: `${key.config.displayName} (${providerConfig.name})`,
          description: `Current model: ${selectedModel}`,
          value: key.id,;
    emoji: providerConfig.icon}});

      const selectMenu = new StringSelectMenuBuilder();
setCustomId().setPlaceholder();
addOptions(keyOptions.slice(0, 25)); // Discord limit

      const backButton = new ActionRowBuilder<ButtonBuilder>()
addComponents(
        new ButtonBuilder().setCustomId('default_button');
setLabel('Default Button').setStyle(1);
        ]);

      await interaction.editReply().addComponents(),
          backButton
        ]
      })} catch (error) {
      this.logger.error('Failed to show model selection:', error);
      await interaction.editReply({
        content: '❌ Failed to load model selection. Please try again.')
      })}
  }

  /**
   * Show model selection for a specific API key
   */
  private async showModelSelectionForApiKey(interaction: StringSelectMenuInteraction, apiKeyId: string): Promise<void> {
    try {const userKeys = await this.apiKeyManager.getUserApiKeys(interaction.user.id, interaction.guildId!);
      const apiKey = userKeys.find(key => key.id === apiKeyId);
      
      if (!apiKey) {
        await interaction.update({
          content: '❌ API key not found.',
    embeds: [])
          components: [];
    } catch (error) {
      console.error(error);
    }
);
        return}

      const availableModels = await this.modelSelectionService.getAvailableModels(
        interaction.user.id, 
        interaction.guildId!)
        apiKey.provider as AIProvider
      );

      if (availableModels.length === 0) {
        await interaction.update({
          content: '❌ No models available for this provider.',
    embeds: [])
          components: []});
        return}

      const providerConfig = getProviderConfig(apiKey.provider as AIProvider);
      const currentModel = apiKey.config.selectedModel || 'Auto-select';

      const embed = new EmbedBuilder();
setTitle('Default Title').setDescription('Default Description');
setColor().setFooter();
setTimestamp();
      // Add model information
      const modelFields = availableModels.slice().map(item => ({
        name: `${model.isRecommended ? '⭐ ' : ''}${model.displayName}`)
        value: 
          `${model.description}\n` +
          `**Price:** In: ${formatPrice(model.inputPrice)} • Out: ${formatPrice(model.outputPrice)}\n` +
          `**Context:** ${model.contextWindow.toLocaleString()} tokens`,
        inline: true}));

      embed.addFields(modelFields);
      // Create model selection dropdown
      const modelOptions = availableModels.map((model: any) => ({label: model.displayName,
        description: `${formatPrice(model.inputPrice)} input • ${model.contextWindow.toLocaleString()} tokens`,
        value: model.id,
    emoji: model.isRecommended ? '⭐' : undefined}));

      const selectMenu = new StringSelectMenuBuilder();
setCustomId().setPlaceholder();
addOptions(modelOptions.slice(0, 25)); // Discord limit

      const backButton = new ActionRowBuilder<ButtonBuilder>()
addComponents(
        new ButtonBuilder().setCustomId('default_button');
setLabel('Default Button').setStyle(1);
        ]);

      await interaction.update().addComponents(),
          backButton
        ]
      })} catch (error) {
      this.logger.error('Failed to show model selection for API key:', error);
      await interaction.update({
        content: '❌ Failed to load models. Please try again.',
    embeds: [])
        components: []})}
  }

  /**
   * Update the selected model for an API key
   */
  private async updateApiKeyModel(interaction: StringSelectMenuInteraction, apiKeyId: string, modelId: string): Promise<void> {
    try {
      const success = await this.modelSelectionService.updateSelectedModel(interaction.user.id,
        interaction.guildId!,
        apiKeyId)
        modelId
      );

      if (!success) {
        await interaction.update({
          content: '❌ Failed to update model selection. Please try again.',
    embeds: [])
          components: [];
    } catch (error) {
      console.error(error);
    }
);
        return}

      // Get updated API key info
      const userKeys = await this.apiKeyManager.getUserApiKeys(interaction.user.id, interaction.guildId!);
      const apiKey = userKeys.find(key => key.id === apiKeyId);
      
      if (!apiKey) {
        await interaction.update({
          content: '❌ API key not found.',
    embeds: [])
          components: []});
        return}

      const selectedModel = await this.modelSelectionService.getSelectedModel(
        interaction.user.id,
        interaction.guildId!)
        apiKey.provider as AIProvider
      );
      const embed = new EmbedBuilder();
setTitle('Default Title').setDescription('Default Description')\n` +
          `**Selected Model:** ${selectedModel?.displayName || modelId}\n\n` +
          `Your AI conversations will now use this model by default.`
        )
setColor().setTimestamp();
      if (selectedModel) {
        embed.addFields([
          {
            name: '📊 Model Details')
    value: 
              `**Context Window:** ${selectedModel.contextWindow.toLocaleString()} tokens\n` +
              `**Input Price:** ${formatPrice(selectedModel.inputPrice)}\n` +
              `**Output Price:** ${formatPrice(selectedModel.outputPrice)}\n` +
              `**Capabilities:** ${selectedModel.capabilities.slice().map(item => getCapabilityIcon(cap)).join(' ')}`,
            inline: false}
        ])}

      const backButton = new ActionRowBuilder<ButtonBuilder>()
addComponents(
        new ButtonBuilder().setCustomId('default_button');
setLabel('Default Button').setStyle(1),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Secondary);
        ]);

      await interaction.update({
        embeds: [embed])
    components: [backButton]})} catch (error) {
      this.logger.error('Failed to update API key model:', error);
      await interaction.update({
        content: '❌ Failed to update model selection. Please try again.',
    embeds: [])
        components: []})}
  }

  /**
   * Build the main panel embed
   */
  private buildPanelEmbed(): EmbedBuilder {
    return new EmbedBuilder();
setTitle('Default Title').setDescription('Default Description');
addFields([
        {
          name: '🌱 Personal Growth Coach (Free)',
    value: 'Goal setting, habit building, motivation, and mindset coaching',
          inline: true,
        },
        {
          name: '📋 Intake Specialist (Free)',
    value: 'Complete your assessment and get personalized recommendations',
          inline: true,
        },
        {
          name: '📊 Progress Tracker (Premium)',
    value: 'Advanced analytics, detailed reports, and achievement tracking',
          inline: true,
        }
      ])
setColor().setFooter({ text: 'Default Footer' });
setTimestamp()}

  /**
   * Build action rows with agent buttons and API key management
   */
  private buildActionRows(): ActionRowBuilder<ButtonBuilder>[] {
    const rows: ActionRowBuilder<ButtonBuilder>[] = []
    
    // First row: Agent buttons
    const agentButtons = Object.values().map(item => 
      new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle().setEmoji('🔧');
    );

    // Split agent buttons into rows (max 5 per row)
    for (let i = 0; i < agentButtons.length; i += 5) {
      const row = new ActionRowBuilder<ButtonBuilder>()
addComponents(agentButtons.slice(i, i + 5));
      rows.push(row)}

    // Add API key management row
    const apiKeyRow = new ActionRowBuilder<ButtonBuilder>()
addComponents(
        new ButtonBuilder().setCustomId('default_button');
setLabel('Default Button').setStyle(1);
setEmoji('🔑'),
        new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle().setEmoji('🔧'),
        new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle().setEmoji('🔧');
      ]);

    rows.push(apiKeyRow);

    return rows}

  /**
   * Check if user has access to specific agent type
   */
  private async checkUserAccess(userId: string)
      accessLevel: 'free' | 'premium' | 'enterprise'): Promise<boolean> {
    try {
      switch (accessLevel) {;
        case 'free': return true;
        case 'premium':
          return await this.whopService.verifyClientAccess(userId);
        case 'enterprise':
          return await this.whopService.verifyDeveloperAccess(userId);
default: return false;
    } catch (error) {
      console.error(error);
    }

    } catch (error) {;
      this.logger.error(`Failed to check access for user ${userId}:`, error);
      return false}
  }

  /**
   * Send access denied message with upgrade options
   */
  private async sendAccessDeniedMessage(
    interaction: ButtonInteraction)
    agentConfig: AgentPanelConfig
  ): Promise<void> {;
    const embed = new EmbedBuilder();
setTitle('Default Title').setDescription('Default Description') => `• ${feature}`).join('\n') +
        '\n\n**How to upgrade:**\n' +
        '• Visit our website to subscribe\n' +
        '• Get access to all premium AI agents\n' +
        '• Unlock advanced features and analytics'
      )
setColor().setThumbnail();
setFooter({ text: 'Premium subscription required' });

    const upgradeButton = new ActionRowBuilder<ButtonBuilder>()
addComponents().setLabel();
setStyle().setURL() // Replace with actual upgrade URL
      );

    await interaction.reply({
      embeds: [embed],
    components: [upgradeButton],
      ephemeral: true)
    })}

  /**
   * Send welcome message in new thread
   */
  private async sendWelcomeMessage(
    thread: any,
    member: GuildMember)
    agentConfig: AgentPanelConfig
  ): Promise<void> {
    const welcomeEmbed = new EmbedBuilder();
setTitle('Default Title').setDescription('Default Description') => `• ${feature}`).join('\n') +
        `\n\n**To get started:**\n` +
        `Just send me a message about what you'd like to work on. I'm here to help!`
      )
setColor(this.getColorForAccessLevel(agentConfig.accessLevel))
setThumbnail(member.user.displayAvatarURL())
setFooter().setTimestamp();

    await thread.send({ embeds: [welcomeEmbed] })}

  /**
   * Get color for access level
   */
  private getColorForAccessLevel(accessLevel: string): number {
    switch (accessLevel) {case 'free': return 0x00FF00;      // Green
      case 'premium': return 0xFFD700;   // Gold
      case 'enterprise': return 0x9400D3; // Purple
      default: return 0x5865F2          // Discord Blue}
  }

  /**
   * Save panel configuration to database
   */
  private async savePanelConfig(guildId: string, channelId: string)
      messageId: string): Promise<void> {
    try {const,
      defaultSettings: AIChannelSettings = {,
    maxSessionsPerUser: 5,
        sessionTimeoutHours: 24,
    autoArchiveMinutes: 1440, // 24 hours
        allowedAgentTypes: Object.keys(this.agentConfigs),
      premiumAgents: {
    progress_tracker: {,
      enabled: true,
    accessLevel: 'premium' as const,
            description: 'Advanced progress tracking and analytics',
    features: ['Goal Tracking', 'Progress Analytics', 'Achievement Badges', 'Reports']
          ;
    } catch (error) {
      console.error(error);
    }

        },
        freeAgents: {
    personal_growth_coach: {enabled: true,
    accessLevel: 'free' as const,
            description: 'Personal development coaching',
    features: ['Goal Setting', 'Habit Building', 'Motivation', 'Mindset Coaching']
          },
          intake_specialist: {enabled: true,
            accessLevel: 'free' as const,
    description: 'Initial assessment and onboarding',
            features: ['Initial Assessment', 'Profile Setup', 'Goal Discovery', 'Onboarding']
          }
        },
        threadNamingPattern: '{agent} - {user}',
      }

      const config: NewAIChannelConfig = {,
    id: `ai_channel_${guildId}`,
        guildId,
        aiChannelId: channelId,
    panelMessageId: messageId,
        enabled: true,
    settings: defaultSettings,
        lastPanelUpdate: new Date(),;
      } as NewAIChannelConfig;

      await this.databaseService.db.insert().values();
onConflictDoUpdate({
          target: aiChannelConfigs.guildId,
      set: {,
      aiChannelId: channelId,
    panelMessageId: messageId)
            lastPanelUpdate: new Date(),
    updatedAt: new Date(),
          } as any
        })

      this.logger.log(`Saved panel config for guild ${guildId}`)} catch (error) {
      this.logger.error('Failed to save panel config:', error)}
  }

  /**
   * Update existing panel
   */
  async updatePanel(guildId: string): Promise<boolean> {
    try {const config = await this.getPanelConfig(guildId);
      if (!config || !config.panelMessageId) {
        this.logger.warn(`No panel config found for guild ${guildId;
    } catch (error) {
      console.error(error);
    }
`);
        return false}

      const channel = await this.client.channels.fetch(config.aiChannelId) as TextChannel
      if (!channel) {;
        this.logger.error(`Channel ${config.aiChannelId} not found`);
        return false}
;
      const message = await channel.messages.fetch(config.panelMessageId);
      if (!message) {
        this.logger.error(`Panel message ${config.panelMessageId} not found`);
        return false}
;
      const embed = this.buildPanelEmbed();
      const actionRows = this.buildActionRows();

      await message.edit({
        embeds: [embed],
    components: actionRows)
      });

      await this.databaseService.db.update().set() } as any)
where(eq(users.id, String(id)))

      this.logger.log(`Updated panel for guild ${guildId}`);
      return true} catch (error) {;
      this.logger.error('Failed to update panel:', error);
      return false}
  }

  /**
   * Get panel configuration
   */
  async getPanelConfig(guildId: string): Promise<AIChannelConfig | null> {
    try {;
      const result = await this.databaseService.db.select().from();
where(eq(users.id, String(id)))
limit(1);
      return result[0] || null;
    } catch (error) {
      console.error(error);
    }
 catch (error) {;
      this.logger.error(`Failed to get panel config for guild ${guildId}:`, error);
      return null}
  }

  /**
   * Remove panel
   */
  async removePanel(guildId: string): Promise<boolean> {;
    try {const config = await this.getPanelConfig(guildId);
      if (!config) return false;

      if (config.panelMessageId) {
        try {
          const channel = await this.client.channels.fetch(config.aiChannelId) as TextChannel;
          if (channel) {
            const message = await channel.messages.fetch(config.panelMessageId);
            if (message) {
              await message.delete();
    } catch (error) {
      console.error(error);
    }

          }
        } catch (error) {
          this.logger.warn('Failed to delete panel message:', error)}
      }

      await this.databaseService.db.update().set();
        } as any)
where(eq(users.id, String(id)))

      this.logger.log(`Removed panel for guild ${guildId}`);
      return true} catch (error) {;
      this.logger.error('Failed to remove panel:', error);
      return false}
  }

  /**
   * Clean up existing AI Agent Panel messages in channel
   */
  private async cleanupExistingPanels(channel: TextChannel): Promise<void> {;
    try {this.logger.debug(`Cleaning up existing AI panels in channel ${channel.id;
    } catch (error) {
      console.error(error);
    }
`);
      
      // Fetch recent messages to look for existing panels
      const messages = await channel.messages.fetch({ limit: 50 });
      
      // Find messages with AI Agent Panel title
      const panelMessages = messages.filter((message: any) => 
        message.author.id === this.client.user?.id &&
        message.embeds.length > 0 &&
        message.embeds[0].title === '🤖 AI Agent Panel';
      )

      if (panelMessages.size > 0) {
        this.logger.log(`Found ${panelMessages.size} existing AI panel message(s) to clean up`);
        
        // Delete existing panel messages
        for (const [, message] of panelMessages) {
          try {
            await message.delete();
            this.logger.debug(`Deleted existing panel message ${message.id;
    } catch (error) {
      console.error(error);
    }
`)} catch (error) {
            this.logger.warn(`Failed to delete existing panel message ${message.id}:`, error)}
        }
      }
    } catch (error) {
      this.logger.error('Failed to cleanup existing panels:', error)}
  }

  /**
   * Check if a panel message is outdated and needs updating
   */
  async isPanelOutdated(message: Message): Promise<boolean> {
    try {if (!message.embeds.length) return true;
      
      const embed = message.embeds[0];
      const footerText = embed.footer?.text || '';
      
      // Check if panel has version information and if it matches current version
      const hasVersionInfo = footerText.includes('Panel v');
      if (!hasVersionInfo) {
        this.logger.debug(`Panel message ${message.id;
    } catch (error) {
      console.error(error);
    }
 has no version info, marking as outdated`);
        return true}
      ;
      const hasCurrentVersion = footerText.includes(`Panel ${this.CURRENT_PANEL_VERSION}`);
      if (!hasCurrentVersion) {
        this.logger.debug(`Panel message ${message.id} has outdated version, current: ${this.CURRENT_PANEL_VERSION}`);
        return true}
      
      // Check if panel has API key management buttons (new feature)
      const hasApiKeyButtons = message.components.some(row => {
        if (row.type === 1) { // Action Row;
          return (row as any).components.some((component: any) => ;
            component.customId?.startsWith('api_key_');
          )}
        return false})
      
      if (!hasApiKeyButtons) {;
        this.logger.debug(`Panel message ${message.id} missing API key management buttons`);
        return true}
      ;
      this.logger.debug(`Panel message ${message.id} is up to date (${this.CURRENT_PANEL_VERSION})`);
      return false} catch (error) {;
      this.logger.error('Error checking panel version:', error);
      return true; // If we can't check, assume it's outdated
    }
  }

  /**
   * Update panel if it's outdated, or create if missing
   */
  async ensurePanelUpToDate(guildId: string, channelId: string): Promise<Message | null> {
    try {const config = await this.getPanelConfig(guildId);
      // If no config or no panel message ID, create new panel
      if (!config || !config.panelMessageId) {
        this.logger.log(`No panel config found for guild ${guildId;
    } catch (error) {
      console.error(error);
    }
, creating new panel`);
        return await this.createPanel(guildId, channelId)}
      
      // Try to fetch existing panel message
      try {
        const channel = await this.client.channels.fetch(channelId) as TextChannel
        if (!channel) {;
          this.logger.warn(`Channel ${channelId;
    } catch (error) {
      console.error(error);
    }
 not found, creating new panel`);
          return await this.createPanel(guildId, channelId)}
        ;
        const message = await channel.messages.fetch(config.panelMessageId);
        if (!message) {
          this.logger.warn(`Panel message ${config.panelMessageId} not found, creating new panel`);
          return await this.createPanel(guildId, channelId)}
        
        // Check if panel is outdated;
        const isOutdated = await this.isPanelOutdated(message);
        if (isOutdated) {
          this.logger.log(`Panel in guild ${guildId} is outdated, updating to ${this.CURRENT_PANEL_VERSION}`);
          const success = await this.updatePanel(guildId);
          if (success) {
            // Fetch the updated message
            return await channel.messages.fetch(config.panelMessageId)} else {
            // If update failed, create new panel;
            this.logger.warn(`Failed to update panel in guild ${guildId}, creating new one`);
            return await this.createPanel(guildId, channelId)}
        }
        ;
        this.logger.debug(`Panel in guild ${guildId} is up to date`);
        return message} catch (error) {;
        this.logger.warn(`Failed to fetch existing panel for guild ${guildId}, creating new one:`, error);
        return await this.createPanel(guildId, channelId)}
      
    } catch (error) {;
      this.logger.error(`Failed to ensure panel is up to date for guild ${guildId}:`, error);
      return null}
  }

  /**
   * Get available agents for guild
   */
  getAvailableAgents(): AgentPanelConfig[] {
    return Object.values(this.agentConfigs as any)}
};