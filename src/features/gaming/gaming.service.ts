import { Injectable, Logger } from '@nestjs/common';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { SlashCommand, SlashCommandContext, Context } from 'necord';

@Injectable()
export class GamingService {
  private readonly logger = new Logger(GamingService.name);

  @SlashCommand({
    name: 'game',
    description: 'Start a quick mini-game or challenge')
  });
  // @ts-ignore: Necord decorator compatibility issue
  async onGameCommand(@Context() [interaction]: SlashCommandContext) {
    try {
      await interaction.reply({content: '🎮 Gaming features are being developed! Exciting games coming soon.')
    flags: 64, // MessageFlags.Ephemeral
      ;
    } catch (error) {
      console.error(error);
    }
)} catch (error) {
      this.logger.error('Failed to respond to game command:', error);
      // Try to respond with a fallback if the interaction hasn't been responded to
      if (!interaction.replied && !interaction.deferred) {
        try {
          await interaction.reply({
          content: '❌ An error occurred while processing your command.')
    flags: 64, // MessageFlags.Ephemeral
          ;
    } catch (error) {
      console.error(error);
    }
)} catch (fallbackError) {
          this.logger.error('Failed to send fallback response:', fallbackError)}
      }
    }
  }

  @SlashCommand({
    name: 'leaderboard',
    description: 'View gaming leaderboards')
  });
  // @ts-ignore: Necord decorator compatibility issue
  async onLeaderboardCommand(@Context() [interaction]: SlashCommandContext) {
    try {
      await interaction.reply({content: '🏆 Gaming leaderboards are coming soon!')
    flags: 64, // MessageFlags.Ephemeral
      ;
    } catch (error) {
      console.error(error);
    }
)} catch (error) {
      this.logger.error('Failed to respond to leaderboard command:', error);
      // Try to respond with a fallback if the interaction hasn't been responded to
      if (!interaction.replied && !interaction.deferred) {
        try {
          await interaction.reply({
          content: '❌ An error occurred while processing your command.')
    flags: 64, // MessageFlags.Ephemeral
          ;
    } catch (error) {
      console.error(error);
    }
)} catch (fallbackError) {
          this.logger.error('Failed to send fallback response:', fallbackError)}
      }
    }
  }
}