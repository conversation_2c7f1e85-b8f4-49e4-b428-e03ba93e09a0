import { Logger } from '@nestjs/common';
import type {
  BaseFeatureService,
  FeatureStatus,
  FeatureConfig,
  FeatureLifecycle,
  FeatureServiceMetrics,
  FeatureServiceHealth,
  FeatureServiceEvent
} from '../types';
import { GuildMember } from 'discord.js';

/**
 * Abstract base class for all feature services
 * Provides common functionality and type safety
 */
export abstract class AbstractFeatureService implements BaseFeatureService, FeatureLifecycle {
  protected readonly logger: Logger;
  protected _status: FeatureStatus = FeatureStatus.DISABLED;
  protected _metrics: FeatureServiceMetrics = {
    commandsExecuted: 0,
    reactionsProcessed: 0,
    messagesHandled: 0,
    errorsEncountered: 0,
    averageResponseTime: 0,
    uptime: 0,
    lastActivity: undefined
  };
  
  private _startTime: Date = new Date();
  private _responseTimesSum: number = 0;
  private _responseTimesCount: number = 0;

  constructor(
    public readonly name: string,
    public readonly version: string = '1.0.0',
    public readonly enabled: boolean = true
  ) {
    this.logger = new Logger(`${this.constructor.name}`);
  }

  getStatus(): FeatureStatus {
    return this._status;
  }

  abstract getConfig(): FeatureConfig;

  async initialize?(): Promise<void> {
    this._status = FeatureStatus.INITIALIZING;
    this.logger.log(`Initializing ${this.name} service...`);
    
    try {
      await this.onEnable();
      this._status = FeatureStatus.ACTIVE;
      this.logger.log(`${this.name} service initialized successfully`);
    } catch (error) {
      this._status = FeatureStatus.ERROR;
      this.logger.error(`Failed to initialize ${this.name} service:`, error);
      throw error;
    }
  }

  async shutdown?(): Promise<void> {
    this.logger.log(`Shutting down ${this.name} service...`);
    
    try {
      await this.onDisable();
      this._status = FeatureStatus.DISABLED;
      this.logger.log(`${this.name} service shut down successfully`);
    } catch (error) {
      this._status = FeatureStatus.ERROR;
      this.logger.error(`Failed to shut down ${this.name} service:`, error);
      throw error;
    }
  }

  // Lifecycle methods - must be implemented by concrete classes
  abstract onEnable(): Promise<void>;
  abstract onDisable(): Promise<void>;
  abstract onGuildJoin(guildId: string): Promise<void>;
  abstract onGuildLeave(guildId: string): Promise<void>;
  abstract onConfigUpdate(config: FeatureConfig): Promise<void>;
  abstract onMemberJoin(member: GuildMember): Promise<void>;
  abstract onMemberLeave(member: GuildMember): Promise<void>;

  // Metrics and monitoring
  getMetrics(): FeatureServiceMetrics {
    return {
      ...this._metrics,
      uptime: Date.now() - this._startTime.getTime(),
      averageResponseTime: this._responseTimesCount > 0 
        ? this._responseTimesSum / this._responseTimesCount 
        : 0
    };
  }

  getHealth(): FeatureServiceHealth {
    return {
      status: this._status,
      metrics: this.getMetrics(),
      lastHealthCheck: new Date(),
      errors: this._status === FeatureStatus.ERROR ? ['Service encountered an error'] : undefined,
      warnings: undefined
    };
  }

  // Protected utility methods for concrete classes
  protected recordCommandExecution(): void {
    this._metrics.commandsExecuted++;
    this._metrics.lastActivity = new Date();
  }

  protected recordReactionProcessed(): void {
    this._metrics.reactionsProcessed++;
    this._metrics.lastActivity = new Date();
  }

  protected recordMessageHandled(): void {
    this._metrics.messagesHandled++;
    this._metrics.lastActivity = new Date();
  }

  protected recordError(error?: Error): void {
    this._metrics.errorsEncountered++;
    if (error) {
      this.logger.error(`Error in ${this.name} service:`, error);
    }
  }

  protected recordResponseTime(timeMs: number): void {
    this._responseTimesSum += timeMs;
    this._responseTimesCount++;
  }

  protected async executeWithMetrics<T>(
    operation: () => Promise<T>,
    operationType: 'command' | 'reaction' | 'message' | 'other' = 'other'
  ): Promise<T> {
    const startTime = Date.now();
    
    try {
      const result = await operation();
      const endTime = Date.now();
      
      this.recordResponseTime(endTime - startTime);
      
      switch (operationType) {
        case 'command':
          this.recordCommandExecution();
          break;
        case 'reaction':
          this.recordReactionProcessed();
          break;
        case 'message':
          this.recordMessageHandled();
          break;
      }
      
      return result;
    } catch (error) {
      this.recordError(error as Error);
      throw error;
    }
  }

  protected createEvent(
    type: string,
    data?: unknown,
    guildId?: string,
    userId?: string
  ): FeatureServiceEvent {
    return {
      type,
      serviceName: this.name,
      data,
      timestamp: new Date(),
      guildId,
      userId
    };
  }

  protected validateConfig(config: FeatureConfig): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (typeof config.enabled !== 'boolean') {
      errors.push('enabled must be a boolean');
    }

    if (typeof config.version !== 'string' || !config.version.trim()) {
      errors.push('version must be a non-empty string');
    }

    if (config.settings && typeof config.settings !== 'object') {
      errors.push('settings must be an object');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  protected logInfo(message: string, ...args: any[]): void {
    this.logger.log(message, ...args);
  }

  protected logWarn(message: string, ...args: any[]): void {
    this.logger.warn(message, ...args);
  }

  protected logError(message: string, error?: Error, ...args: any[]): void {
    this.logger.error(message, error?.stack, ...args);
  }

  protected logDebug(message: string, ...args: any[]): void {
    this.logger.debug(message, ...args);
  }
}

/**
 * Feature service registry implementation
 */
export class FeatureServiceRegistry {
  private readonly services = new Map<string, BaseFeatureService>();
  private readonly logger = new Logger(FeatureServiceRegistry.name);

  register(name: string, service: BaseFeatureService): void {
    if (this.services.has(name)) {
      throw new Error(`Feature service '${name}' is already registered`);
    }
    
    this.services.set(name, service);
    this.logger.log(`Registered feature service: ${name}`);
  }

  unregister(name: string): void {
    if (!this.services.has(name)) {
      throw new Error(`Feature service '${name}' is not registered`);
    }
    
    this.services.delete(name);
    this.logger.log(`Unregistered feature service: ${name}`);
  }

  get(name: string): BaseFeatureService | undefined {
    return this.services.get(name);
  }

  getAll(): Map<string, BaseFeatureService> {
    return new Map(this.services);
  }

  getEnabled(): Map<string, BaseFeatureService> {
    const enabled = new Map<string, BaseFeatureService>();
    
    for (const [name, service] of this.services) {
      if (service.enabled && service.getStatus() === FeatureStatus.ACTIVE) {
        enabled.set(name, service);
      }
    }
    
    return enabled;
  }

  getByCategory(category: string): Map<string, BaseFeatureService> {
    const categoryServices = new Map<string, BaseFeatureService>();
    
    for (const [name, service] of this.services) {
      const config = service.getConfig();
      if (config.settings?.category === category) {
        categoryServices.set(name, service);
      }
    }
    
    return categoryServices;
  }

  async initialize(): Promise<void> {
    this.logger.log('Initializing all feature services...');
    
    const initPromises: Promise<void>[] = [];
    
    for (const [name, service] of this.services) {
      if (service.initialize) {
        initPromises.push(
          service.initialize().catch(error => {
            this.logger.error(`Failed to initialize service ${name}:`, error);
            throw error;
          })
        );
      }
    }
    
    await Promise.all(initPromises);
    this.logger.log('All feature services initialized');
  }

  async shutdown(): Promise<void> {
    this.logger.log('Shutting down all feature services...');
    
    const shutdownPromises: Promise<void>[] = [];
    
    for (const [name, service] of this.services) {
      if (service.shutdown) {
        shutdownPromises.push(
          service.shutdown().catch(error => {
            this.logger.error(`Failed to shutdown service ${name}:`, error);
          })
        );
      }
    }
    
    await Promise.allSettled(shutdownPromises);
    this.logger.log('All feature services shut down');
  }
}
