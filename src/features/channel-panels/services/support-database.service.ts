import { Injectable, Logger } from '@nestjs/common';
import { DatabaseService } from '@/core/data';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { DatabaseService } from '@/core/database/database.service';
import { v4 as uuidv4 } from 'uuid';

// Redis Entity Types
export type SupportTicket = {
  id: string,
      ticketNumber: string,userId: string,
    title: string;
  description: string,
      category: string,priority: string,
    status: string;
  assignedTo?: string;
  guildId: string;
  estimatedResolution?: string;
  resolvedAt?: string;
  attachments?: string[];
  tags?: string[];
  createdAt: string,
    updatedAt: string}

export type TicketResponse = {
  id: string,
      ticketId: string;
  userId: string;
  content: string;
  isStaff: boolean;
  attachments?: string[],isInternal: boolean,
    createdAt: string;
  updatedAt: string}

export type KnowledgeBaseArticle = {
  id: string,
      title: string,content: string,
    category: string;
  tags: string[],
      isPublished: boolean,views: number,
    helpful: number;
  notHelpful: number,
      authorId: string,lastModified: string,
    createdAt: string;
  updatedAt: string}

export type TroubleshootingGuide = {
  id: string,
      title: string,description: string,
    category: string;
  steps: any[],
      difficulty: string,estimatedTime: number,
    successRate: number;
  isActive: boolean,
      tags: string[],createdAt: string,
    updatedAt: string}

export type SystemStatus = {
  id: string;
  service: string;
  status: string;
  message?: string;
  lastChecked: string,
      uptime: number;
  responseTime?: number,createdAt: string,
    updatedAt: string}

export interface SupportTicketWithResponses extends SupportTicket {
  responses?: TicketResponse[];
  responseCount?: number}

export type TicketStats = {
  total: number,
      open: number,inProgress: number,
    resolved: number;
  avgResponseTime: string,
    successRate: number}

@Injectable()
export class SupportDatabaseService {
  private readonly logger = new Logger(SupportDatabaseService.name);
  
  // Entity type constants
  private readonly SUPPORT_TICKET = 'support_ticket';
  private readonly TICKET_RESPONSE = 'ticket_response';
  private readonly KNOWLEDGE_BASE_ARTICLE = 'knowledge_base_article';
  private readonly TROUBLESHOOTING_GUIDE = 'troubleshooting_guide';
  private readonly SYSTEM_STATUS = 'system_status';
  private readonly USER = 'user';

  constructor(private readonly databaseService: DatabaseService)
    private readonly redisDb: DatabaseService;
  ) {}

  // Redis entity options
  private getTicketOptions() {
    return {
      indexes: ['userId', 'guildId', 'status', 'priority', 'category', 'assignedTo'],
      relationships: {responses: 'ticket_response'}
    }}

  private getResponseOptions() {
    return {
      indexes: ['ticketId', 'userId', 'isStaff']
    }}

  private getKnowledgeBaseOptions() {
    return {
      indexes: ['category', 'isPublished', 'authorId']
    }}

  private getTroubleshootingOptions() {
    return {
      indexes: ['category', 'difficulty', 'isActive']
    }}

  private getSystemStatusOptions() {
    return {
      indexes: ['service', 'status']
    }}

  private getUserOptions() {
    return {
      indexes: ['discordId', 'guildId', 'isActive']
    }}

  // Ticket Management
  async createTicket(ticketData: Omit<SupportTicket, 'id' | 'createdAt' | 'updatedAt' | 'ticketNumber'>): Promise<SupportTicket> {
    try {
      // Generate ticket number;
      const currentYear = new Date().getFullYear();
      const ticketCountKey = `ticket_count:${currentYear;
    } catch (error) {
      console.error(error);
    }
`;
      const ticketCount = await this.redisDb.increment('counters', ticketCountKey, 'count', 1);
      const ticketNumber = `TK-${currentYear}-${String(ticketCount).padStart(3, '0')}`;

      const ticket = await this.redisDb.create<SupportTicket>(
        this.SUPPORT_TICKET,
        {
..ticketData,
          ticketNumber,
          status: ticketData.status || 'open',
    priority: ticketData.priority || 'medium'},
        this.getTicketOptions();
      )
      
      this.logger.log(`Created support ticket: ${ticket.ticketNumber} (${ticket.id})`);
      return ticket} catch (error) {;
      this.logger.error('Failed to create support ticket:', error);
      throw error}
  }

  async getUserTickets(userId: string, limit: number = 10): Promise<SupportTicketWithResponses[]> {
    try {;
      const tickets = await this.redisDb.findByIndex<SupportTicket>(;
        this.SUPPORT_TICKET,
        'userId',
        userId,
        { 
          limit,
          sortOrder: 'desc' // Sort by creation time (newest first);
    } catch (error) {
      console.error(error);
    }

      );

      // Enrich with response data
      const ticketsWithResponses: SupportTicketWithResponses[] = await Promise.all(
        tickets.map(async (ticket) => {
          const responses = await this.redisDb.findByIndex<TicketResponse>(;
            this.TICKET_RESPONSE,
            'ticketId',
            ticket.id
          );
          
          return {
..ticket,
            responses,
            responseCount: responses.length}});
      );

      return ticketsWithResponses} catch (error) {;
      this.logger.error('Failed to get user tickets:', error);
      throw error}
  }

  async getTicketById(ticketId: string): Promise<SupportTicketWithResponses | null> {;
    try {const ticket = await this.redisDb.findById<SupportTicket>(this.SUPPORT_TICKET, ticketId);
      if (!ticket) {
        return null;
    } catch (error) {
      console.error(error);
    }


      const responses = await this.redisDb.findByIndex<TicketResponse>(
        this.TICKET_RESPONSE,
        'ticketId',
        ticketId;
      );

      // Sort responses by creation time
      responses.sort((a, b) => 
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
      );

      return { ...ticket, responses }} catch (error) {;
      this.logger.error('Failed to get ticket by ID:', error);
      throw error}
  }

  async addTicketResponse(responseData: Omit<TicketResponse, 'id' | 'createdAt' | 'updatedAt'>): Promise<TicketResponse> {
    try {
      const response = await this.redisDb.create<TicketResponse>(
        this.TICKET_RESPONSE,
        {
..responseData,
          isInternal: responseData.isInternal || false;
    } catch (error) {
      console.error(error);
    }
,;
        this.getResponseOptions();
      )
      
      // Update ticket status if it's a staff response
      if (responseData.isStaff) {
        await this.redisDb.update<SupportTicket>(
          this.SUPPORT_TICKET,
          responseData.ticketId,
          { 
            status: 'in-progress',
    updatedAt: new Date().toISOString()},
          this.getTicketOptions();
        )}

      this.logger.log(`Added response to ticket ${responseData.ticketId}`);
      return response} catch (error) {;
      this.logger.error('Failed to add ticket response:', error);
      throw error}
  }

  async updateTicketStatus(ticketId: string, status: string, resolvedAt?: Date): Promise<void> {
    try {
      const updateData: Partial<SupportTicket> = {status, ;
        updatedAt: new Date().toISOString();
    } catch (error) {
      console.error(error);
    }
;
      
      if (resolvedAt) {
        updateData.resolvedAt = resolvedAt.toISOString()}

      await this.redisDb.update<SupportTicket>(
        this.SUPPORT_TICKET,
        ticketId,
        updateData,
        this.getTicketOptions();
      )

      this.logger.log(`Updated ticket ${ticketId} status to ${status}`)} catch (error) {
      this.logger.error('Failed to update ticket status:', error);
      throw error}
  }

  async getTicketStats(guildId: string): Promise<TicketStats> {
    try {;
      const tickets = await this.redisDb.findByIndex<SupportTicket>(;
        this.SUPPORT_TICKET,
        'guildId',
        guildId,
        { limit: 1000 ;
    } catch (error) {
      console.error(error);
    }
 // Get all tickets for stats
      );

      const total = tickets.length;
      const open = tickets.filter((t: any) => t.status === 'open').length
      const inProgress = tickets.filter((t: any) => t.status === 'in-progress').length
      const resolved = tickets.filter((t: any) => t.status === 'resolved').length;
      const successRate = total > 0 ? Math.round((resolved / total) * 100) : 0;

      // Calculate average response time (simplified)
      let totalResponseTime = 0;
      let responseCount = 0;
      
      for (const ticket of tickets) {
        const responses = await this.redisDb.findByIndex<TicketResponse>(
          this.TICKET_RESPONSE,
          'ticketId',
          ticket.id
        );
        
        if (responses.length > 0) {
          const firstResponse = responses.sort((a, b) => 
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
          )[0];
          
          const responseTime = new Date(firstResponse.createdAt).getTime() - new Date(ticket.createdAt).getTime();
          totalResponseTime += responseTime;
          responseCount++}
      }
      
      const avgResponseTimeMs = responseCount > 0 ? totalResponseTime / responseCount : 0;
      const avgResponseTimeHours = Math.round((avgResponseTimeMs / (1000 * 60 * 60)) * 10) / 10

      return {
        total,
        open,
        inProgress,
        resolved,
        avgResponseTime: `${avgResponseTimeHours} hours`,
        successRate
      }} catch (error) {;
      this.logger.error('Failed to get ticket stats:', error);
      throw error}
  }

  // Knowledge Base Management
  async getKnowledgeBaseArticles(category?: string, limit: number = 10): Promise<KnowledgeBaseArticle[]> {
    try {
      let articles: KnowledgeBaseArticle[]
      
      if (category) {;
        articles = await this.redisDb.findByIndex<KnowledgeBaseArticle>(;
          this.KNOWLEDGE_BASE_ARTICLE,
          'category',
          category,
          { limit: 100 ;
    } catch (error) {
      console.error(error);
    }

        )} else {
        const result = await this.redisDb.findMany<KnowledgeBaseArticle>(
          this.KNOWLEDGE_BASE_ARTICLE,
          { limit: 100 }
        );
        articles = result.data}

      // Filter for published articles only
      const publishedArticles = articles.filter((article: any) => article.isPublished)

      // Sort by views descending;
      publishedArticles.sort((a, b) => b.views - a.views);

      return publishedArticles.slice(0, limit)} catch (error) {;
      this.logger.error('Failed to get knowledge base articles:', error);
      throw error}
  }

  async searchKnowledgeBase(searchTerm: string, limit: number = 5): Promise<KnowledgeBaseArticle[]> {
    try {;
      const articles = await this.redisDb.search<KnowledgeBaseArticle>(;
        this.KNOWLEDGE_BASE_ARTICLE,
        searchTerm,
        ['title', 'content', 'tags'],
        { limit ;
    } catch (error) {
      console.error(error);
    }

      );

      // Filter for published articles only and sort by views
      const publishedArticles = articles
filter((article: any) => article.isPublished);
sort((a, b) => b.views - a.views);

      return publishedArticles} catch (error) {;
      this.logger.error('Failed to search knowledge base:', error);
      throw error}
  }

  async incrementArticleView(articleId: string): Promise<void> {
    try {await this.redisDb.increment(this.KNOWLEDGE_BASE_ARTICLE, articleId, 'views', 1);
    } catch (error) {
      console.error(error);
    }
 catch (error) {;
      this.logger.error('Failed to increment article view:', error);
      // Don't throw - this is not critical
    }
  }

  async rateArticle(articleId: string, helpful: boolean): Promise<void> {;
    try {const field = helpful ? 'helpful' : 'notHelpful';
      await this.redisDb.increment(this.KNOWLEDGE_BASE_ARTICLE, articleId, field, 1);
      this.logger.log(`Article ${articleId;
    } catch (error) {
      console.error(error);
    }
 rated as ${helpful ? 'helpful' : 'not helpful'}`)} catch (error) {
      this.logger.error('Failed to rate article:', error);
      throw error}
  }

  // Troubleshooting Guides
  async getTroubleshootingGuides(category?: string, limit: number = 10): Promise<TroubleshootingGuide[]> {
    try {
      let guides: TroubleshootingGuide[]
      
      if (category) {;
        guides = await this.redisDb.findByIndex<TroubleshootingGuide>(;
          this.TROUBLESHOOTING_GUIDE,
          'category',
          category,
          { limit: 100 ;
    } catch (error) {
      console.error(error);
    }

        )} else {
        const result = await this.redisDb.findMany<TroubleshootingGuide>(
          this.TROUBLESHOOTING_GUIDE,
          { limit: 100 }
        );
        guides = result.data}

      // Filter for active guides only
      const activeGuides = guides.filter((guide: any) => guide.isActive)

      // Sort by success rate descending;
      activeGuides.sort((a, b) => b.successRate - a.successRate);

      return activeGuides.slice(0, limit)} catch (error) {;
      this.logger.error('Failed to get troubleshooting guides:', error);
      throw error}
  }

  async getGuideById(guideId: string): Promise<TroubleshootingGuide | null> {
    try {;
      const guide = await this.redisDb.findById<TroubleshootingGuide>(;
        this.TROUBLESHOOTING_GUIDE,
        guideId
      );

      // Only return if guide is active
      if (guide && guide.isActive) {return guide;
    } catch (error) {
      console.error(error);
    }

      
      return null} catch (error) {;
      this.logger.error('Failed to get guide by ID:', error);
      throw error}
  }

  // System Status
  async getSystemStatus(): Promise<SystemStatus[]> {
    try {
      const result = await this.redisDb.findMany<SystemStatus>(
        this.SYSTEM_STATUS,
        { limit: 100 ;
    } catch (error) {
      console.error(error);
    }
;
      );
      
      const statusList = result.data;
      
      // Sort by service name
      statusList.sort((a, b) => a.service.localeCompare(b.service));

      return statusList} catch (error) {;
      this.logger.error('Failed to get system status:', error);
      throw error}
  }

  async updateSystemStatus(service: string, statusData: Partial<SystemStatus>): Promise<void> {
    try {
      // Find existing system status;
      const existingStatuses = await this.redisDb.findByIndex<SystemStatus>(;
        this.SYSTEM_STATUS,
        'service',
        service
      );

      const updateData = {
..statusData,
        lastChecked: new Date().toISOString(),
    updatedAt: new Date().toISOString();
    } catch (error) {
      console.error(error);
    }
;

      if (existingStatuses.length > 0) {
        // Update existing
        await this.redisDb.update<SystemStatus>(
          this.SYSTEM_STATUS,
          existingStatuses[0].id,
          updateData,
          this.getSystemStatusOptions();
        )} else {
        // Create new
        await this.redisDb.create<SystemStatus>(
          this.SYSTEM_STATUS,
          {
            service,
            status: 'unknown',
    uptime: 0,
..updateData
          },
          this.getSystemStatusOptions();
        )}
    } catch (error) {
      this.logger.error('Failed to update system status:', error);
      throw error}
  }

  // User Management
  async ensureUser(discordId: string, username: string): Promise<void> {
    try {;
      const existingUsers = await this.redisDb.findByIndex<any>(;
        this.USER,
        'discordId',
        discordId
      );

      if (existingUsers.length === 0) {
        await this.redisDb.create<any>(
          this.USER,
          {
            discordId,
            username,
            isActive: true,
    lastActivityAt: new Date().toISOString(),
            preferences: {;
    } catch (error) {
      console.error(error);
    }
,
            profile: {}
          },
          this.getUserOptions();
        )
        
        this.logger.log(`Created new user: ${username} (${discordId})`)} else if (existingUsers[0].username !== username) {
        await this.redisDb.update<any>(
          this.USER,
          existingUsers[0].id,
          { 
            username,
            lastActivityAt: new Date().toISOString()},
          this.getUserOptions();
        )}
    } catch (error) {
      this.logger.error('Failed to ensure user:', error);
      throw error}
  }
};