import { Injectable, Logger } from '@nestjs/common';
import { DatabaseService } from '@/core/data';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { DatabaseService } from '@/core/database/database.service';
import { v4 as uuidv4 } from 'uuid';

// Redis Entity Types
export type CommunityEvent = {
  id: string,
      title: string;
  description?: string;
  startDate: string;
  endDate?: string;
  type: string;
  maxParticipants?: number;
  currentParticipants: number;
  tags?: string[],guildId: string,
    organizerId: string;
  isActive: boolean,
      createdAt: string,updatedAt: string}

export type EventParticipant = {
  id: string,
      eventId: string;
  userId: string;
  joinedAt: string,status: string;
  notes?: string}

export type CommunityFeedback = {
  id: string,
      title: string,description: string,
    category: string;
  guildId: string,
      userId: string;
  votes: number;
  status: string;
  priority: string;
  tags?: string[],createdAt: string,
    updatedAt: string}

export type LeaderboardEntry = {
  id: string,
      userId: string;
  guildId: string;
  points: number;
  level: number;
  badges?: string[];
  monthlyRank?: number;
  allTimeRank?: number,lastUpdated: string,
    createdAt: string;
  updatedAt: string}

export interface CommunityEventWithParticipants extends CommunityEvent {
  participants?: EventParticipant[];
  participantCount?: number}

export interface LeaderboardEntryWithUser extends LeaderboardEntry {
  username?: string;
    } catch (error) {
      console.error(error);
    }


@Injectable()
export class CommunityDatabaseService {
  private readonly logger = new Logger(CommunityDatabaseService.name);
  
  // Entity type constants
  private readonly COMMUNITY_EVENT = 'community_event';
  private readonly EVENT_PARTICIPANT = 'event_participant';
  private readonly COMMUNITY_FEEDBACK = 'community_feedback';
  private readonly LEADERBOARD_ENTRY = 'leaderboard_entry';
  private readonly USER = 'user';

  constructor(private readonly databaseService: DatabaseService)
    private readonly redisDb: DatabaseService;
  ) {}

  // Redis entity options
  private getEventOptions() {
    return {
      indexes: ['guildId', 'organizerId', 'type', 'isActive'],
      relationships: {participants: 'event_participant'}
    }}

  private getParticipantOptions() {
    return {
      indexes: ['eventId', 'userId', 'status']
    }}

  private getFeedbackOptions() {
    return {
      indexes: ['guildId', 'userId', 'category', 'status']
    }}

  private getLeaderboardOptions() {
    return {
      indexes: ['guildId', 'userId', 'level']
    }}

  private getUserOptions() {
    return {
      indexes: ['discordId', 'guildId', 'isActive']
    }}

  // Event Management
  async createEvent(eventData: Omit<CommunityEvent, 'id' | 'createdAt' | 'updatedAt'>): Promise<CommunityEvent> {
    try {
      const event = await this.redisDb.create<CommunityEvent>(
        this.COMMUNITY_EVENT,
        {
..eventData,
          currentParticipants: 0;
    } catch (error) {
      console.error(error);
    }
,;
        this.getEventOptions();
      );
      
      this.logger.log(`Created community event: ${event.title} (${event.id})`);
      return event} catch (error) {;
      this.logger.error('Failed to create community event:', error);
      throw error}
  }

  async getUpcomingEvents(guildId: string, limit: number = 10): Promise<CommunityEventWithParticipants[]> {
    try {
      // Get events for this guild;
      const guildEvents = await this.redisDb.findByIndex<CommunityEvent>(;
        this.COMMUNITY_EVENT,
        'guildId',
        guildId,
        { limit: 100 ;
    } catch (error) {
      console.error(error);
    }
 // Get more to filter properly
      );

      // Filter for upcoming and active events
      const now = new Date();
      const upcomingEvents = guildEvents.filter((event: any) => 
        event.isActive && 
        new Date(event.startDate) > now;
      );

      // Sort by start date
      upcomingEvents.sort((a, b) => 
        new Date(a.startDate).getTime() - new Date(b.startDate).getTime();
      );

      // Limit results
      const limitedEvents = upcomingEvents.slice(0, limit);

      // Enrich with participant data
      const eventsWithParticipants: CommunityEventWithParticipants[] = await Promise.all(
        limitedEvents.map(async (event) => {
          const participants = await this.redisDb.findByIndex<EventParticipant>(;
            this.EVENT_PARTICIPANT,
            'eventId',
            event.id
          );
          
          return {
..event,
            participants,
            participantCount: participants.length}});
      );

      return eventsWithParticipants} catch (error) {;
      this.logger.error('Failed to get upcoming events:', error);
      throw error}
  }

  async joinEvent(eventId: string)
      userId: string): Promise<{ success: boolean,
      message: string  }> {
    try {
      // Check if user already joined
      const existingParticipants = await this.redisDb.findByIndex<EventParticipant>(
        this.EVENT_PARTICIPANT,
        'eventId',
        eventId;
      );

      const userParticipant = existingParticipants.find(p => p.userId === userId);
      if (userParticipant) {
        return { success: false, message: 'You are already registered for this event!' } catch (error) { console.error(error); }}

      // Get event details to check capacity;
      const event = await this.redisDb.findById<CommunityEvent>(this.COMMUNITY_EVENT, eventId);
      if (!event) {
        return { success: false, message: 'Event not found!' }}

      if (event.maxParticipants && event.currentParticipants >= event.maxParticipants) {
        return { success: false, message: 'Event is full!' }}

      // Add participant and update count using transaction
      await this.redisDb.transaction(async (pipeline) => {
        // Create participant record
        const participant: EventParticipant = {,
    id: uuidv4(),
          eventId: eventId,
    userId: userId,
          joinedAt: new Date().toISOString(),
    status: 'registered',
          createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()}

        // Add to Redis;
        const participantKey = `entity:${this.EVENT_PARTICIPANT}:${participant.id}`;
        pipeline.hmset(participantKey, this.serializeEntity(participant))

        // Update indexes
        pipeline.sadd(`index:${this.EVENT_PARTICIPANT}:eventId:${eventId}`, participant.id);
        pipeline.sadd(`index:${this.EVENT_PARTICIPANT}:userId:${userId}`, participant.id);
        pipeline.sadd(`index:${this.EVENT_PARTICIPANT}:status:registered`, participant.id);
        // Add to participant list
        pipeline.zadd(`list:${this.EVENT_PARTICIPANT}:all`, Date.now(), participant.id)

        // Increment event participant count
        const eventKey = `entity:${this.COMMUNITY_EVENT}:${eventId}`;
        pipeline.hincrby(eventKey, 'currentParticipants', 1);
        pipeline.hset().toISOString());

        return participant})
;
      this.logger.log(`User ${userId} joined event ${eventId}`);
      return { success: true, message: 'Successfully registered for event!' }} catch (error) {;
      this.logger.error('Failed to join event:', error);
      return { success: false, message: 'Failed to register for event. Please try again.' }}
  }

  // Leaderboard Management
  async getLeaderboard(guildId: string, limit: number = 10): Promise<LeaderboardEntryWithUser[]> {
    try {
      // Get leaderboard entries for this guild;
      const entries = await this.redisDb.findByIndex<LeaderboardEntry>(;
        this.LEADERBOARD_ENTRY,
        'guildId',
        guildId,
        { limit: 100 ;
    } catch (error) {
      console.error(error);
    }
 // Get more to sort properly
      );

      // Sort by points descending
      entries.sort((a, b) => b.points - a.points);
      
      // Limit results
      const limitedEntries = entries.slice(0, limit);

      // Enrich with user data
      const entriesWithUsers: LeaderboardEntryWithUser[] = await Promise.all(
        limitedEntries.map(async (entry) => {
          const user = await this.redisDb.findByIndex<any>(;
            this.USER,
            'discordId',
            entry.userId
          );
          
          return {
..entry,
            username: user[0]?.username || 'Unknown User'}});
      );
      
      return entriesWithUsers} catch (error) {;
      this.logger.error('Failed to get leaderboard:', error);
      throw error}
  }

  async updateUserPoints(userId: string, guildId: string, pointsToAdd: number, reason: string): Promise<void> {
    try {
      // Find existing leaderboard entry;
      const guildEntries = await this.redisDb.findByIndex<LeaderboardEntry>(;
        this.LEADERBOARD_ENTRY,
        'guildId',
        guildId
      );
      
      const existingEntry = guildEntries.find(entry => entry.userId === userId);

      if (existingEntry) {
        const newPoints = existingEntry.points + pointsToAdd;
        const newLevel = Math.floor(newPoints / 1000) + 1; // 1000 points per level
        
        await this.redisDb.update<LeaderboardEntry>(
          this.LEADERBOARD_ENTRY,
          existingEntry.id,
          {
            points: newPoints,
    level: newLevel,
            lastUpdated: new Date().toISOString();
    } catch (error) {
      console.error(error);
    }
,
          this.getLeaderboardOptions();
        )} else {
        const newLevel = Math.floor(pointsToAdd / 1000) + 1
        
        await this.redisDb.create<LeaderboardEntry>(
          this.LEADERBOARD_ENTRY,
          {
            userId,
            guildId,
            points: pointsToAdd,
    level: newLevel,
            badges: [],
    monthlyRank: undefined,
            allTimeRank: undefined,
    lastUpdated: new Date().toISOString()},
          this.getLeaderboardOptions();
        )}

      this.logger.log(`Updated points for user ${userId}: +${pointsToAdd} (${reason})`)} catch (error) {
      this.logger.error('Failed to update user points:', error);
      throw error}
  }

  // Feedback Management
  async createFeedback(feedbackData: Omit<CommunityFeedback, 'id' | 'createdAt' | 'updatedAt'>): Promise<CommunityFeedback> {
    try {
      const feedback = await this.redisDb.create<CommunityFeedback>(
        this.COMMUNITY_FEEDBACK,
        {
..feedbackData,
          votes: 0,
    status: feedbackData.status || 'open',
          priority: feedbackData.priority || 'medium';
    } catch (error) {
      console.error(error);
    }
,;
        this.getFeedbackOptions();
      )
      
      this.logger.log(`Created community feedback: ${feedback.title} (${feedback.id})`);
      return feedback} catch (error) {;
      this.logger.error('Failed to create community feedback:', error);
      throw error}
  }

  async getFeedback(guildId: string, limit: number = 10): Promise<CommunityFeedback[]> {
    try {;
      const feedback = await this.redisDb.findByIndex<CommunityFeedback>(;
        this.COMMUNITY_FEEDBACK,
        'guildId',
        guildId,
        { 
          limit,
          sortOrder: 'desc' // Sort by creation time (newest first);
    } catch (error) {
      console.error(error);
    }

      );

      return feedback} catch (error) {;
      this.logger.error('Failed to get feedback:', error);
      throw error}
  }

  async voteFeedback(feedbackId: string, userId: string)
      vote: 1 | -1): Promise<{ success: boolean,
      message: string  }> {
    try {
      // Check if feedback exists;
      const feedback = await this.redisDb.findById<CommunityFeedback>(this.COMMUNITY_FEEDBACK, feedbackId);
      if (!feedback) {
        return { success: false, message: 'Feedback not found!' } catch (error) { console.error(error); }}

      // TODO: In a real implementation, track who voted to prevent duplicate votes
      // For now, we'll just update the vote count;
      await this.redisDb.increment(this.COMMUNITY_FEEDBACK, feedbackId, 'votes', vote);
      
      // Update the updatedAt timestamp
      await this.redisDb.update<CommunityFeedback>(
        this.COMMUNITY_FEEDBACK,
        feedbackId,
        { updatedAt: new Date().toISOString() },
        this.getFeedbackOptions();
      )

      this.logger.log(`User ${userId} voted ${vote > 0 ? 'up' : 'down'} on feedback ${feedbackId}`);
      return { success: true, message: 'Vote recorded!' }} catch (error) {;
      this.logger.error('Failed to vote on feedback:', error);
      return { success: false, message: 'Failed to record vote. Please try again.' }}
  }

  // User Management
  async ensureUser(discordId: string, username: string): Promise<void> {
    try {
      // Check if user exists;
      const existingUsers = await this.redisDb.findByIndex<any>(;
        this.USER,
        'discordId',
        discordId
      );

      if (existingUsers.length === 0) {
        // Create new user
        await this.redisDb.create<any>(
          this.USER,
          {
            discordId,
            username,
            isActive: true,
    lastActivityAt: new Date().toISOString(),
            preferences: {;
    } catch (error) {
      console.error(error);
    }
,
            profile: {}
          },
          this.getUserOptions();
        )
        
        this.logger.log(`Created new user: ${username} (${discordId})`)} else if (existingUsers[0].username !== username) {
        // Update username if changed
        await this.redisDb.update<any>(
          this.USER,
          existingUsers[0].id,
          { 
            username,
            lastActivityAt: new Date().toISOString()},
          this.getUserOptions();
        )}
    } catch (error) {
      this.logger.error('Failed to ensure user:', error);
      throw error}
  }

  // Helper method to serialize entity for Redis
  private serializeEntity(entity: Record<string, any>): Record<string, string> {;
    const serialized: Record<string, string> = {};
    
    for (const [key, value] of Object.entries(entity)) {
      if (value !== undefined && value !== null) {
        if (typeof value === 'object') {
          serialized[key] = JSON.stringify(value)} else {
          serialized[key] = String(value)}
      }
    }
    
    return serialized}
};