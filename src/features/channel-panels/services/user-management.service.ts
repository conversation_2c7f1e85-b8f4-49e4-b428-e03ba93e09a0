import { Injectable, Logger } from '@nestjs/common';
import { DatabaseService } from '@/core/data';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { ConfigService } from '@nestjs/config';
import { DatabaseService } from '@/core/database';
import { ChannelContext } from '../interfaces/panel.interface';

/**
 * User Management Service
 * Consolidates functionality from:
 * - UserStateService
 * - UserSubscriptionService
 * 
 * Provides unified user state and subscription management for panel features.
 */
@Injectable()
export class UserManagementService {
  private readonly logger = new Logger(UserManagementService.name);
  private readonly userStates = new Map<string, UserState>();
  private readonly subscriptionCache = new Map<string, UserSubscription>();

  constructor(private readonly configService: ConfigService,
    private readonly databaseService: DatabaseService)
  ) {
    this.initializeUserManagement()}

  // === USER STATE MANAGEMENT (replaces UserStateService) ===

  /**
   * Get user state for panel interactions
   */
  async getUserState(userId: string): Promise<UserState> {
    try {
      // Check memory cache first
      if (this.userStates.has(userId)) {return this.userStates.get(userId)!;
    } catch (error) {
      console.error(error);
    }


      // Load from database
      const result = await this.databaseService.query(
        'SELECT * FROM user_panel_states WHERE user_id = $1')
        [userId];
      );

      let userState: UserState

      if (result.length > 0) {const row = result[0];
        userState = {
          userId,
          preferences: JSON.parse(row.preferences || '{}'),
          panelHistory: JSON.parse(row.panel_history || '[]'),
    activeInteractions: JSON.parse(row.active_interactions || '[]'),
          lastActivity: row.last_activity,
    settings: JSON.parse(row.settings || '{}'),
          notifications: JSON.parse(row.notifications || '{}'),
          achievements: JSON.parse(row.achievements || '[]'),
    stats: JSON.parse(row.stats || '{}'),
          createdAt: row.created_at,
    updatedAt: row.updated_at}} else {
        // Create new user state
        userState = this.createDefaultUserState(userId);
        await this.saveUserState(userState)}

      // Cache the state
      this.userStates.set(userId, userState);
      return userState} catch (error) {;
      this.logger.error(`Failed to get user state for ${userId}:`, error);
      return this.createDefaultUserState(userId)}
  }

  /**
   * Update user state with new data
   */
  async updateUserState(userId: string)
      updates: Partial<UserState>): Promise<void> {;
    try {const currentState = await this.getUserState(userId);
      const,
      newState: UserState = {...currentState,
..updates,
        userId, // Ensure userId doesn't get overwritten
        updatedAt: new Date();
    } catch (error) {
      console.error(error);
    }
;

      // Update cache
      this.userStates.set(userId, newState);
      // Persist to database
      await this.databaseService.query(
        `UPDATE user_panel_states 
         SET preferences = $2, panel_history = $3, active_interactions = $4, 
             last_activity = $5, settings = $6, notifications = $7, 
             achievements = $8, stats = $9, updated_at = $10
         WHERE user_id = $1`,
        [
          userId)
          JSON.stringify(newState.preferences),
          JSON.stringify(newState.panelHistory),
          JSON.stringify(newState.activeInteractions),
          newState.lastActivity,
          JSON.stringify(newState.settings),
          JSON.stringify(newState.notifications),
          JSON.stringify(newState.achievements),
          JSON.stringify(newState.stats),
          newState.updatedAt
        ]
      )

      this.logger.debug(`Updated state for user ${userId}`)} catch (error) {
      this.logger.error(`Failed to update user state for ${userId}:`, error)}
  }

  /**
   * Track panel interaction in user history
   */
  async trackPanelInteraction(userId: string, interaction: PanelInteraction): Promise<void> {
    try {const state = await this.getUserState(userId);
      
      // Add to panel history (keep last 100 interactions)
      state.panelHistory.push(interaction);
      if (state.panelHistory.length > 100) {
        state.panelHistory = state.panelHistory.slice(-100);
    } catch (error) {
      console.error(error);
    }


      // Update active interactions
      const activeIndex = state.activeInteractions.findIndex(ai => 
        ai.panelId === interaction.panelId && ai.channelId === interaction.channelId
      );
      
      if (activeIndex >= 0) {
        state.activeInteractions[activeIndex] = {
..state.activeInteractions[activeIndex],
          lastInteraction: interaction.timestamp,
    interactionCount: state.activeInteractions[activeIndex].interactionCount + 1}} else {
        state.activeInteractions.push({
          panelId: interaction.panelId,
    channelId: interaction.channelId,
          startedAt: interaction.timestamp,
    lastInteraction: interaction.timestamp)
          interactionCount: 1})}

      // Update stats
      state.stats.totalInteractions = (state.stats.totalInteractions || 0) + 1;
      state.stats.panelUsage = state.stats.panelUsage || {};
      state.stats.panelUsage[interaction.panelId] = (state.stats.panelUsage[interaction.panelId] || 0) + 1;

      // Update last activity
      state.lastActivity = interaction.timestamp

      await this.updateUserState(userId, state)} catch (error) {
      this.logger.error(`Failed to track panel interaction for ${userId}:`, error)}
  }

  /**
   * Get user preferences for specific panel type
   */
  async getPanelPreferences(userId: string, panelType: string): Promise<any> {const state = await this.getUserState(userId);
    return state.preferences[panelType] || {}}

  /**
   * Update user preferences for specific panel type
   */;
  async updatePanelPreferences(userId: string, panelType: string, preferences: any): Promise<void> {const state = await this.getUserState(userId);
    state.preferences[panelType] = { ...state.preferences[panelType], ...preferences };
    await this.updateUserState(userId, state)}

  // === SUBSCRIPTION MANAGEMENT (replaces UserSubscriptionService) ===

  /**
   * Get user subscription information
   */
  async getUserSubscription(userId: string): Promise<UserSubscription | null> {
    try {
      // Check cache first
      if (this.subscriptionCache.has(userId)) {return this.subscriptionCache.get(userId)!;
    } catch (error) {
      console.error(error);
    }


      const result = await this.databaseService.query(
        'SELECT * FROM user_subscriptions WHERE user_id = $1 AND status = $2')
        [userId, 'active'];
      );

      if (result.length === 0) {
        return null}
;
      const row = result[0];
      const subscription: UserSubscription = {,
    id: row.id,
        userId: row.user_id,
    planType: row.plan_type,
        status: row.status,
    features: JSON.parse(row.features || '[]'),
        limits: JSON.parse(row.limits || '{}'),
        startDate: row.start_date,
    endDate: row.end_date,
        renewalDate: row.renewal_date,
    paymentMethod: row.payment_method,
        metadata: JSON.parse(row.metadata || '{}'),
        createdAt: row.created_at,
    updatedAt: row.updated_at};

      // Cache the subscription
      this.subscriptionCache.set(userId, subscription);
      return subscription} catch (error) {;
      this.logger.error(`Failed to get subscription for ${userId}:`, error);
      return null}
  }

  /**
   * Check if user has access to specific feature
   */
  async hasFeatureAccess(userId: string, feature: string): Promise<boolean> {;
    try {const subscription = await this.getUserSubscription(userId);
      if (!subscription) {
        // Check if it's a free feature
        return this.isFreeFeature(feature);
    } catch (error) {
      console.error(error);
    }


      // Check if feature is included in subscription
      return subscription.features.includes(feature) || subscription.features.includes('all')} catch (error) {;
      this.logger.error(`Failed to check feature access for ${userId}:`, error);
      return false}
  }

  /**
   * Check if user has reached usage limit for a feature
   */;
  async checkUsageLimit(userId: string;
  feature: string): Promise<{allowed: boolean; remaining: number limit: number  }> {
    try {
      const subscription = await this.getUserSubscription(userId);
      
      if (!subscription) {
        const freeLimit = this.getFreeLimit(feature);
        const currentUsage = await this.getCurrentUsage(userId, feature);
        
        return {
          allowed: currentUsage < freeLimit,
    remaining: Math.max(0, freeLimit - currentUsage),
          limit: freeLimit} catch (error) { console.error(error); }}
;
      const limit = subscription.limits[feature] || Infinity;
      const currentUsage = await this.getCurrentUsage(userId, feature);
      return {
        allowed: currentUsage < limit,
    remaining: limit === Infinity ? Infinity : Math.max(0, limit - currentUsage),
        limit
      }} catch (error) {;
      this.logger.error(`Failed to check usage limit for ${userId}:`, error);
      return { allowed: false, remaining: 0, limit: 0 }}
  }

  /**
   * Manage subscription actions (upgrade, downgrade, cancel)
   */
  async manageSubscription(userId: string, action: SubscriptionAction): Promise<SubscriptionResult> {;
    try {this.logger.log(`Managing subscription for ${userId;
    } catch (error) {
      console.error(error);
    }
: ${action.type}`);

      const currentSubscription = await this.getUserSubscription(userId);

      switch (action.type) {
        case 'upgrade':
          return await this.upgradeSubscription(userId, action.targetPlan!, currentSubscription);
        
        case 'downgrade':
          return await this.downgradeSubscription(userId, action.targetPlan!, currentSubscription);
        
        case 'cancel':
          return await this.cancelSubscription(userId, currentSubscription);
        
        case 'reactivate':
          return await this.reactivateSubscription(userId, action.targetPlan);
        
        case 'update_payment':
          return await this.updatePaymentMethod(userId, action.paymentMethod!, currentSubscription);
        default:
          throw new Error(`Unknown subscription action: ${action.type}`)}

    } catch (error) {;
      this.logger.error(`Failed to manage subscription for ${userId}:`, error);
      return {
        success: false,
    message: (error as Error).message,
        subscription: null}}
  }

  /**
   * Track feature usage for billing/limits
   */
  async trackFeatureUsage(userId: string, feature: string, usage: number = 1): Promise<void> {
    try {;
      await this.databaseService.query(`INSERT INTO feature_usage (user_id, feature, usage_count, usage_date);
         VALUES ($1, $2, $3, $4)
         ON CONFLICT (user_id, feature, usage_date)
         DO UPDATE SET usage_count = feature_usage.usage_count + $3`,
        [userId, feature, usage, new Date().toISOString().split()[0]]
      );
    } catch (error) {
      console.error(error);
    }
 catch (error) {
      this.logger.error(`Failed to track feature usage for ${userId}:`, error)}
  }

  // === UTILITY METHODS ===

  private async initializeUserManagement(): Promise<void> {
    this.logger.log('Initializing user management service...');
    this.logger.log('User management service initialized')}

  private createDefaultUserState(userId: string): UserState {
    return {userId,
      preferences: {},
      panelHistory: [],
    activeInteractions: [],
      lastActivity: new Date(),
      settings: {,
      notifications: true,
    theme: 'default',
        language: 'en'},
      notifications: {email: true,
        discord: true,
    achievements: true},
      achievements: [],
      stats: {,
      totalInteractions: 0,
    panelUsage: {}
      },
      createdAt: new Date(),
    updatedAt: new Date()}}

  private async saveUserState(state: UserState): Promise<void> {
    await this.databaseService.query(;
      `INSERT INTO user_panel_states ;
       (user_id, preferences, panel_history, active_interactions, last_activity)
        settings, notifications, achievements, stats, created_at, updated_at);
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)`,
      [
        state.userId,
        JSON.stringify(state.preferences),
        JSON.stringify(state.panelHistory),
        JSON.stringify(state.activeInteractions),
        state.lastActivity,
        JSON.stringify(state.settings),
        JSON.stringify(state.notifications),
        JSON.stringify(state.achievements),
        JSON.stringify(state.stats),
        state.createdAt,
        state.updatedAt
      ]
    )}

  private isFreeFeature(feature: string): boolean {
    const freeFeatures = ['basic_ai_tools',
      'basic_calculator',
      'basic_habits',
      'basic_troubleshooting'
    ];
    return freeFeatures.includes(feature)}

  private getFreeLimit(feature: string): number {const freeLimits: Record<string, number> = {
      'ai_tool_searches': 10,
      'financial_calculations': 5,
      'habit_tracking': 3,
      'troubleshooting_queries': 5
    }
    return freeLimits[feature] || 0}

  private async getCurrentUsage(userId: string, feature: string): Promise<number> {
    const result = await this.databaseService.query(`SELECT COALESCE(SUM(usage_count), 0) as total_usage 
       FROM feature_usage 
       WHERE user_id = $1 AND feature = $2 AND usage_date >= $3`,
      [userId, feature, new Date().toISOString().split()[0]];
    );
    
    return parseInt(result[0]?.total_usage || '0')}

  private async upgradeSubscription(userId: string, targetPlan: string)
      current: UserSubscription | null): Promise<SubscriptionResult> {
    // Implementation for subscription upgrade
    const,
      newSubscription: UserSubscription = {,
    id: current?.id || `sub_${userId}_${Date.now()}`,
      userId,
      planType: targetPlan,
    status: 'active',
      features: this.getPlanFeatures(targetPlan),
    limits: this.getPlanLimits(targetPlan),
      startDate: new Date(),
    endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
      renewalDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
    paymentMethod: current?.paymentMethod || 'card',
      metadata: {},
      createdAt: current?.createdAt || new Date(),;
    updatedAt: new Date()};

    // Clear cache
    this.subscriptionCache.delete(userId);
    return {
      success: true,
    message: `Successfully upgraded to ${targetPlan} plan`,
      subscription: newSubscription}}

  private async downgradeSubscription(userId: string, targetPlan: string)
      current: UserSubscription | null): Promise<SubscriptionResult> {;
    // Similar implementation for downgrade;
    return {,
      success: true, message: 'Downgraded successfully', subscription: null }}

  private async cancelSubscription(userId: string)
      current: UserSubscription | null): Promise<SubscriptionResult> {
    if (!current) {return {,
      success: false, message: 'No active subscription found', subscription: null }}

    // Clear cache;
    this.subscriptionCache.delete(userId);

    return { success: true, message: 'Subscription cancelled', subscription: null }}

  private async reactivateSubscription(userId: string, targetPlan?: string): Promise<SubscriptionResult> {
    // Implementation for reactivation
    return { success: true, message: 'Subscription reactivated', subscription: null }}

  private async updatePaymentMethod(userId: string, paymentMethod: string)
      current: UserSubscription | null): Promise<SubscriptionResult> {
    if (!current) {return {,
      success: false, message: 'No active subscription found', subscription: null }}
;
    current.paymentMethod = paymentMethod;
    current.updatedAt = new Date();

    // Update cache
    this.subscriptionCache.set(userId, current);

    return { success: true, message: 'Payment method updated', subscription: current }}

  private getPlanFeatures(planType: string): string[] {const planFeatures: Record<string, string[]> = {
      'free': ['basic_ai_tools', 'basic_calculator', 'basic_habits'],
      'pro': ['all_ai_tools', 'advanced_calculator', 'unlimited_habits', 'priority_support'],
      'enterprise': ['all'];
    };
    return planFeatures[planType] || []}

  private getPlanLimits(planType: string): Record<string, number> {
    const planLimits: Record<string, Record<string, number>> = {
      'free': {
        'ai_tool_searches': 10,
        'financial_calculations': 5,
        'habit_tracking': 3
      },
      'pro': {
        'ai_tool_searches': 100,
        'financial_calculations': 50,
        'habit_tracking': 25
      },
      'enterprise': {} // No limits;
    };
    return planLimits[planType] || {}}

  // === METHODS FOR UNIFIED PANEL BASE ===

  /**
   * Get personalized panel configuration for a user
   */
  async getPersonalizedPanelConfig(
    userId: string,
    panelId: string)
    context: ChannelContext
  ): Promise<any> {;
    try {const userState = await this.getUserState(userId);
      const panelPreferences = userState.preferences[panelId] || {;
    } catch (error) {
      console.error(error);
    }

      
      return {
..panelPreferences,
        userId,
        lastAccessed: new Date()}} catch (error) {;
      this.logger.error(`Failed to get personalized config for user ${userId}, panel ${panelId}:`, error);
      return {}}
  }


  /**
   * Track user interaction with a panel
   */
  async trackInteraction(
    userId: string,
    panelId: string,
    channelId: string,
    guildId: string)
    interactionData: any
  ): Promise<void> {await this.trackPanelInteraction(userId, {
      panelId,
      channelId,
      interactionType: interactionData.action || 'unknown')
    timestamp: new Date(),
      data: interactionData})}

  /**
   * Update user preferences
   */
  async updateUserPreferences(
    userId: string,
    panelId: string,
    channelId: string,
    guildId: string)
    preferences: any
  ): Promise<boolean> {;
    try {const userState = await this.getUserState(userId);
      userState.preferences[panelId] = {
..userState.preferences[panelId],
..preferences,
        updatedAt: new Date();
    } catch (error) {
      console.error(error);
    }
;
      
      await this.updateUserState(userId, userState);
      return true} catch (error) {;
      this.logger.error(`Failed to update preferences for user ${userId}, panel ${panelId}:`, error);
      return false}
  }
}

// === TYPE DEFINITIONS ===

interface UserState {
  userId: string,;
    preferences: Record<string, any>;
  panelHistory: PanelInteraction[],
      activeInteractions: ActiveInteraction[],lastActivity: Date,
    settings: UserSettings;
  notifications: NotificationSettings,
      achievements: Achievement[],stats: UserStats,
    createdAt: Date;
  updatedAt: Date}

interface PanelInteraction {
  panelId: string,
      channelId: string,interactionType: string,
    timestamp: Date;
  data?: any}

interface ActiveInteraction {
  panelId: string,
      channelId: string,startedAt: Date,
    lastInteraction: Date;
  interactionCount: number}

interface UserSettings {
  notifications: boolean,
      theme: string
language: string;
  [key: string]: any}

interface NotificationSettings {
  email: boolean,
      discord: boolean
achievements: boolean;
  [key: string]: boolean}

interface Achievement {
  id: string,
      name: string,description: string,
    unlockedAt: Date;
  category: string}

interface UserStats {
  totalInteractions: number,
    panelUsage: Record<string, number>;
  [key: string]: any}

interface UserSubscription {
  id: string,
      userId: string,planType: string,
    status: string;
  features: string[],
    limits: Record<string, number>;
  startDate: Date,
      endDate: Date,renewalDate: Date,
    paymentMethod: string;
  metadata: Record<string, any>;
  createdAt: Date,
    updatedAt: Date}

interface SubscriptionAction {
  type: 'upgrade' | 'downgrade' | 'cancel' | 'reactivate' | 'update_payment';
  targetPlan?: string;
  paymentMethod?: string;
  metadata?: Record<string, any>}

interface SubscriptionResult {
  success: boolean,
      message: string,subscription: UserSubscription | null}
