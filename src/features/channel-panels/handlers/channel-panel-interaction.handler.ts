import { Injectable, Logger } from '@nestjs/common';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { On, Context } from 'necord';
import { Interaction, ButtonInteraction, StringSelectMenuInteraction, InteractionType, ComponentType } from 'discord.js';
import { EnhancedChannelPanelOrchestratorService } from '../enhanced-channel-panel-orchestrator.service';
@Injectable()
export class ChannelPanelInteractionHandler {
  private readonly logger = new Logger(ChannelPanelInteractionHandler.name);

  constructor(private readonly orchestrator: EnhancedChannelPanelOrchestratorService)
    ) {}

  @On('interactionCreate');
  async handleChannelPanelInteraction(@Context() [interaction]: [Interaction]): Promise<void> {
    // Validate interaction object exists
    if (!interaction) {
      this.logger.warn('No interaction object received');
      return}

    // Validate it's a proper Discord interaction object
    if (typeof interaction !== 'object' || !('type' in interaction)) {
      this.logger.warn('Object is not a Discord interaction', {
        type: typeof interaction)
    constructor: (interaction as any)?.constructor?.name,
        hasType: 'type' in interaction});
      return}

    // Log interaction details for debugging (only if needed)
    this.logger.debug('Processing interaction', {
      type: interaction.type)
    customId: 'customId' in interaction ? interaction.customId : 'N/A'});

    // Use InteractionType enum for more reliable type checking
    const isButtonInteraction = interaction.type === InteractionType.MessageComponent && 
                                'componentType' in interaction && 
                                interaction.componentType === ComponentType.Button;
    
    const isSelectMenuInteraction = interaction.type === InteractionType.MessageComponent && 
                                   'componentType' in interaction && 
                                   interaction.componentType === ComponentType.StringSelect;

    // Only handle button and select menu interactions
    if (!isButtonInteraction && !isSelectMenuInteraction) {
      return}

    // Only handle interactions for our panel system
    if (!interaction.customId || !this.isChannelPanelInteraction(interaction.customId)) {
      return}

    try {
      this.logger.debug(`Processing channel panel interaction: ${interaction.customId;
    } catch (error) {
      console.error(error);
    }
`);
      
      // Safe type casting based on our validation above
      const componentInteraction = interaction as ButtonInteraction | StringSelectMenuInteraction;
      await this.orchestrator.handlePanelInteraction(componentInteraction)} catch (error) {
      this.logger.error('Failed to handle channel panel interaction:', error);
      
      // Let the orchestrator handle all error responses to avoid duplicate replies
      // The orchestrator's error handling is more robust and includes timeout checks
    }
  }

  /**
   * Determine if this interaction belongs to our channel panel system
   */
  private isChannelPanelInteraction(customId: string): boolean {
    // Our panels use specific prefixes for their interactions
    const channelPanelPrefixes = [
      // Announcement panel interactions;
      'announcement_',
      
      // Community panel interactions
      'welcome_',
      'intro_',
      'media_',
      'premium_',
      'community_',
      
      // AI Mastery panel interactions
      'tools_',
      'tutorials_',
      'news_',
      'coding_',
      'automation_',
      'ai_',
      
      // Wealth Creation panel interactions
      'money_strategies_',
      'success_',
      'entrepreneur_',
      'subscriptions_',
      'wealth_',
      
      // Personal Growth panel interactions
      'mindset_',
      'goals_',
      'productivity_',
      'growth_',
      
      // Technical Support panel interactions
      'support_',
      'help_',
      'tech_',
      'faq_',
      'setup_',
      'ticket_',
      'status_',
      'diagnostics_',
      'category_',
      
      // Networking & Business panel interactions
      'business_',
      'war_',
      'network_',
      
      // Gaming Entertainment panel interactions
      'gaming_',
      'tournament_',
      'leaderboard_',
      'stream_',
      
      // Trading Markets panel interactions
      'trading_',
      'market_',
      'portfolio_',
      'stock_',
      'crypto_',
      
      // Creative Showcase panel interactions
      'creative_',
      'art_',
      'showcase_',
      'gallery_',
      'contest_',
      
      // Educational Resources panel interactions
      'educational_',
      'course_',
      'learn_',
      'tutorial_',
      'resource_',
    ];

    return channelPanelPrefixes.some(prefix => customId.startsWith(prefix))}
};