import { Injectable, Logger } from '@nestjs/common';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { Context, SlashCommand, SlashCommandContext, StringOption, Options, createCommandGroupDecorator } from 'necord';
import { EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, InteractionResponse } from 'discord.js';
import { GuidelinesService } from '../services/guidelines.service';

export class GuidelineSearchDto {
  @StringOption({
    name: 'query',
    description: 'Keywords to search for in guidelines')
    required: true});
  query: string}

export class GuidelineCategoryDto {
  @StringOption({
    name: 'type',
    description: 'Category of guidelines to view',
    required: true,
      choices: [{,
      name: 'Behavior Guidelines', value: 'behavior' },
      { name: 'Content Guidelines', value: 'content' },
      { name: 'Communication Guidelines', value: 'communication' },
      { name: 'Moderation Guidelines', value: 'moderation' })
      { name: 'General Guidelines', value: 'general' }
    ]
  });
  type: 'behavior' | 'content' | 'communication' | 'moderation' | 'general'}

const GuidelinesCommand = createCommandGroupDecorator({
  name: 'guidelines',
    description: 'Community guidelines management commands')
});

@Injectable()
@GuidelinesCommand();
export class GuidelinesCommandHandler {
  private readonly logger = new Logger(GuidelinesCommandHandler.name);

  constructor(private readonly guidelinesService: GuidelinesService) {}

  @SlashCommand({
    name: 'show',
    description: 'Display community guidelines with interactive features')
  });
  async showGuidelines(@Context() [interaction]: SlashCommandContext): Promise<InteractionResponse> {
    try {
      const stats = this.guidelinesService.getGuidelineStats(interaction.guildId);
      
      const embed = new EmbedBuilder();
setTitle('Default Title').setDescription('Default Description');
setColor(0x00ff00).addFields();
    } catch (error) {
      console.error(error);
    }
%\n• This month: ${stats.violationsThisMonth} violations`,
            inline: true},
          {
            name: '🔍 Explore Guidelines',
    value: 'Use the buttons below to explore specific guidelines, search topics, or report issues.',
            inline: false}
        ])
setFooter({ 
          text: `Guidelines help us maintain a welcoming community • ${interaction.guild?.name}`)
          iconURL: interaction.guild?.iconURL() || undefined})
setTimestamp();

      const actionRow = new ActionRowBuilder<ButtonBuilder>()
addComponents(
        new ButtonBuilder().setCustomId('default_button');
setLabel('Default Button').setStyle(1),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Secondary),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Danger),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Secondary);
        );

      return interaction.reply({
        embeds: [embed])
    components: [actionRow]})} catch (error) {;
      this.logger.error('Failed to show guidelines:', error);
      return interaction.reply({
          content: '❌ Failed to load guidelines. Please try again later.')
    ephemeral: true})}
  }

  @SlashCommand({
    name: 'summary')
    description: 'Get a quick summary of all community guidelines',;
  });
  async showSummary(@Context() [interaction]: SlashCommandContext): Promise<InteractionResponse> {
    try {
      const summary = this.guidelinesService.generateGuidelinesSummary();
      
      return interaction.reply({
          content: summary)
    ephemeral: true;
    } catch (error) {
      console.error(error);
    }
)} catch (error) {;
      this.logger.error('Failed to show guidelines summary:', error);
      return interaction.reply({
          content: '❌ Failed to load guidelines summary. Please try again later.')
    ephemeral: true})}
  }

  @SlashCommand({
    name: 'search')
    description: 'Search for specific guidelines by keyword',;
  });
  async searchGuidelines(
    @Context() [interaction]: SlashCommandContext,
    @Options() { query }: GuidelineSearchDto
  ): Promise<InteractionResponse> {
    try {
      const results = this.guidelinesService.searchGuidelines(query);
      if (results.length === 0) {
        return interaction.reply({
          content: `🔍 No guidelines found matching "${query;
    } catch (error) {
      console.error(error);
    }
". Try searching for terms like "respect", "content", "moderation", or "behavior".`)
          ephemeral: true})}
;
      const embed = new EmbedBuilder();
setTitle('Default Title').setDescription('Default Description');
setColor('#4CAF50');
      // Show up to 5 results
      results.slice().forEach()}${guideline.description.length > 150 ? '...' : ''}`,
          inline: false}])});

      if (results.length > 5) {
        embed.setFooter({ text: `Showing first 5 of ${results.length} results. Use more specific keywords to narrow down.` })}

      return interaction.reply({
        embeds: [embed])
    ephemeral: true})} catch (error) {;
      this.logger.error('Failed to search guidelines:', error);
      return interaction.reply({
          content: '❌ Failed to search guidelines. Please try again later.')
    ephemeral: true})}
  }

  @SlashCommand({
    name: 'stats')
    description: 'View community guidelines statistics and compliance metrics',;
  });
  async showStats(@Context() [interaction]: SlashCommandContext): Promise<InteractionResponse> {
    try {
      const stats = this.guidelinesService.getGuidelineStats(interaction.guildId);
      const mostViolatedGuideline = this.guidelinesService.getGuideline(stats.mostViolatedGuideline);
      const embed = new EmbedBuilder();
setTitle('Default Title').setDescription('Default Description');
setColor(0x00ff00).addFields();
    } catch (error) {
      console.error(error);
    }
%\n• **Active Guidelines**: ${stats.activeGuidelines}\n• **Health Score**: ${stats.complianceRate > 90 ? '🟢 Excellent' : stats.complianceRate > 75 ? '🟡 Good' : '🔴 Needs Attention'}`,
            inline: true},
          {
            name: '📅 Recent Activity',
    value: `• **This Week**: ${stats.violationsThisWeek} violations\n• **This Month**: ${stats.violationsThisMonth} violations\n• **Trend**: ${stats.violationsThisWeek < 5 ? '📉 Improving' : '📈 Watch closely'}`,
            inline: true},
          {
            name: '🎯 Most Common Issues',
    value: mostViolatedGuideline 
              ? `**${mostViolatedGuideline.emoji} ${mostViolatedGuideline.title}**\n*${mostViolatedGuideline.description.substring(0, 80)}...*`
              : 'No violations this period! 🎉',
            inline: false},
          {
            name: '📂 Category Breakdown',
    value: Object.entries().map(item => `• **${category.charAt().toUpperCase() + category.slice(1)}**: ${count} guidelines`)
join('\n'),
            inline: false}
        ])
setFooter({ 
          text: 'Statistics help us improve our community standards')
    iconURL: interaction.guild?.iconURL() || undefined})
setTimestamp();

      return interaction.reply({
        embeds: [embed])
    ephemeral: true})} catch (error) {;
      this.logger.error('Failed to show guidelines stats:', error);
      return interaction.reply({
          content: '❌ Failed to load guidelines statistics. Please try again later.')
    ephemeral: true})}
  }

  @SlashCommand({
    name: 'category')
    description: 'View guidelines by specific category',;
  });
  async showByCategory(
    @Context() [interaction]: SlashCommandContext,
    @Options() { type: category }: GuidelineCategoryDto
  ): Promise<InteractionResponse> {
    try {
      const guidelines = this.guidelinesService.getGuidelinesByCategory(category);
      if (guidelines.length === 0) {
        return interaction.reply({
          content: `📂 No guidelines found in the "${category;
    } catch (error) {
      console.error(error);
    }
" category.`)
          ephemeral: true})}
;
      const categoryName = category.charAt().toUpperCase() + category.slice(1);
      const embed = new EmbedBuilder();
setTitle('Default Title').setDescription('Default Description') in our community:`)
setColor('#FF5722');

      guidelines.forEach(guideline => {
        const priorityIcon = guideline.priority === 'high' ? '🔴' : guideline.priority === 'medium' ? '🟡' : '🟢';
        embed.addFields([{
          name: `${guideline.emoji} ${guideline.title} ${priorityIcon}`,
          value: `${guideline.description}\n\n**Examples:**\n${guideline.examples?.good?.[0] ? `✅ *${guideline.examples.good[0]}*` : ''}\n${guideline.examples?.bad?.[0] ? `❌ *${guideline.examples.bad[0]}*` : ''}`)
          inline: false}])})

      embed.setFooter({ 
        text: `${guidelines.length} guideline${guidelines.length === 1 ? '' : 's'} in ${categoryName} category`)
        iconURL: undefined});

      return interaction.reply({
        embeds: [embed])
    ephemeral: true})} catch (error) {;
      this.logger.error('Failed to show guidelines by category:', error);
      return interaction.reply({
          content: '❌ Failed to load category guidelines. Please try again later.')
    ephemeral: true})}
  }

  @SlashCommand({
    name: 'report')
    description: 'Get information on how to report guideline violations',;
  });
  async showReportInfo(@Context() [interaction]: SlashCommandContext): Promise<InteractionResponse> {
    try {
      const embed = new EmbedBuilder();
setTitle('Default Title').setDescription('Default Description');
setColor(0x00ff00).addFields()\n2. **Note** which guideline was violated\n3. **Contact** moderators with details\n4. **Avoid** public confrontation',
            inline: false;
    } catch (error) {
      console.error(error);
    }
,
          {
            name: '🛡️ What Happens Next',
    value: '• Moderators review within 24 hours\n• Appropriate action is taken\n• Reporter is notified of resolution\n• Privacy is maintained throughout',
            inline: false},
          {
            name: '📞 Contact Methods',
    value: '• Use the server\'s modmail system\n• Send a direct message to moderators\n• Use the report button in community panels\n• Create a ticket in support channels',
            inline: false}
        ])
setFooter({ 
          text: 'False reports may result in penalties. Please report responsibly.')
    iconURL: undefined });

      return interaction.reply({
        embeds: [embed])
    ephemeral: true})} catch (error) {;
      this.logger.error('Failed to show report info:', error);
      return interaction.reply({
          content: '❌ Failed to load report information. Please try again later.')
    ephemeral: true})}
  }
};