/**
 * Clean Panel System Module
 * 
 * Main module that wires together all components of the clean architecture
 * panel system with proper dependency injection and separation of concerns.
 */

import { Module, Provider } from '@nestjs/common';
// import { eq, and, or, desc, count } from 'drizzle-orm';

// Core interfaces - these define our contracts
import { 
  IPanelOrchestrator,
  IActionHandler,
  IPanelFactory,
  IStateManager,
  IContentProvider
} from './core/interfaces/panel-contracts.interface';

// Orchestration layer - coordinates all components
import { PanelOrchestrator } from './core/orchestration/panel-orchestrator';

// Action handlers - handle panel-specific actions
import { AnnouncementActionHandler } from './core/actions/announcement-action-handler';
import { CommunityHubActionHandler } from './core/actions/community-hub-action-handler';
import { AICodingActionHandler } from './core/actions/ai-coding-action-handler';

// Content providers - supply real data
import { AnnouncementContentProvider } from './core/content/announcement-content-provider';

// State management - handles user sessions and panel states
import { PanelStateManager } from './core/state/panel-state-manager';

// Panel factories - create panel instances
import { DefaultPanelFactory } from './core/factories/default-panel-factory';

// Interaction handler - handles Discord interactions
import { ChannelPanelInteractionHandler } from './handlers/channel-panel-interaction.handler';

// Database entities (if using TypeORM)
// import { PanelStateEntity } from './entities/panel-state.entity';
// import { PanelConfigurationEntity } from './entities/panel-configuration.entity';

/**
 * Provider definitions with proper typing and injection tokens
 */
const actionHandlerProviders: Provider[] = [
  {provide: 'ANNOUNCEMENT_ACTION_HANDLER',
    useClass: AnnouncementActionHandler},
  {
    provide: 'COMMUNITY_ACTION_HANDLER',
    useClass: CommunityHubActionHandler},
  {
    provide: 'AI_CODING_ACTION_HANDLER',
    useClass: AICodingActionHandler},
  {
    provide: 'TASK_PANEL_ACTION_HANDLER',
    useClass: AICodingActionHandler // Using AI handler as task handler placeholder}
];

const contentProviderProviders: Provider[] = [
  {provide: 'ANNOUNCEMENT_CONTENT_PROVIDER',
    useClass: AnnouncementContentProvider}
];

const stateManagerProvider: Provider = {,
    provide: 'STATE_MANAGER',
  useClass: PanelStateManager};

const panelFactoryProvider: Provider = {,
    provide: 'DEFAULT_PANEL_FACTORY',
  useClass: DefaultPanelFactory};

const orchestratorProvider: Provider = {,
    provide: 'PANEL_ORCHESTRATOR',
  useFactory: (,
    client: any,
    stateManager: IStateManager,
    announcementHandler: IActionHandler,
    communityHandler: IActionHandler,
    aiCodingHandler: IActionHandler,
    taskPanelHandler: IActionHandler,
    defaultFactory: IPanelFactory
  ) => {
    return new PanelOrchestrator(client,
      stateManager,
      announcementHandler,
      communityHandler,
      aiCodingHandler)
      taskPanelHandler,;
      defaultFactory;
    )},
  inject: ['DISCORD_CLIENT',
    'STATE_MANAGER',
    'ANNOUNCEMENT_ACTION_HANDLER',
    'COMMUNITY_ACTION_HANDLER', 
    'AI_CODING_ACTION_HANDLER',
    'TASK_PANEL_ACTION_HANDLER',
    'DEFAULT_PANEL_FACTORY'
  ]
};

@Module({
  imports: [
    // TypeORM entities (uncomment when implementing database persistence);
    // TypeOrmModule.forFeature([//   PanelStateEntity)
    //   PanelConfigurationEntity
    // ]);
  ],
  providers: [
    // Core components;
..actionHandlerProviders,
..contentProviderProviders,
    stateManagerProvider,
    panelFactoryProvider,
    orchestratorProvider,

    // Interaction handler
    ChannelPanelInteractionHandler,

    // Service aliases for easier injection
    {
      provide: PanelStateManager,
    useExisting: 'STATE_MANAGER'},
    {
      provide: DefaultPanelFactory,
    useExisting: 'DEFAULT_PANEL_FACTORY'},
    {
      provide: PanelOrchestrator,
    useExisting: 'PANEL_ORCHESTRATOR'},
    {
      provide: AnnouncementActionHandler,
    useExisting: 'ANNOUNCEMENT_ACTION_HANDLER'},
    {
      provide: CommunityHubActionHandler,
    useExisting: 'COMMUNITY_ACTION_HANDLER'},
    {
      provide: AICodingActionHandler,
    useExisting: 'AI_CODING_ACTION_HANDLER'},
    {
      provide: AnnouncementContentProvider,
    useExisting: 'ANNOUNCEMENT_CONTENT_PROVIDER'}
  ],
  exports: [
    // Export main orchestrator for use in other modules;
    'PANEL_ORCHESTRATOR',
    PanelOrchestrator,
    
    // Export interaction handler for Discord integration
    ChannelPanelInteractionHandler,
    
    // Export individual components for direct use if needed
    'STATE_MANAGER',
    'DEFAULT_PANEL_FACTORY',
    PanelStateManager,
    DefaultPanelFactory
  ]
})
export class CleanPanelSystemModule {
  /**
   * Create a forRoot configuration method for advanced setup
   */
  static forRoot(config?: {
    enableAnalytics?: boolean;
    cacheStrategy?: 'memory' | 'redis' | 'hybrid';
    stateCleanupInterval?: number;
    maxStatesPerUser?: number}) {
    const configProvider: Provider = {,
    provide: 'PANEL_SYSTEM_CONFIG',
      useValue: {,
      enableAnalytics: config?.enableAnalytics ?? true,
        cacheStrategy: config?.cacheStrategy ?? 'memory',
    stateCleanupInterval: config?.stateCleanupInterval ?? 3600000, // 1 hour
        maxStatesPerUser: config?.maxStatesPerUser ?? 10,
..config
      }
    };

    return {
      module: CleanPanelSystemModule,
    providers: [configProvider,;
..this.getProviders();
      ],
      exports: this.getExports()}}

  private static getProviders(): Provider[] {
    return [
..actionHandlerProviders,
..contentProviderProviders,
      stateManagerProvider,
      panelFactoryProvider,
      orchestratorProvider,
      ChannelPanelInteractionHandler]}

  private static getExports(): (string | Function)[] {
    return [
      'PANEL_ORCHESTRATOR',
      PanelOrchestrator,
      ChannelPanelInteractionHandler,
      'STATE_MANAGER',
      'DEFAULT_PANEL_FACTORY',
      PanelStateManager,
      DefaultPanelFactory]}
};