import { Embed<PERSON><PERSON><PERSON>, <PERSON><PERSON>ow<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, StringSelectMenuBuilder } from 'discord.js';

export type ChannelContext = {
  channelId: string,
      channelName: string;
  categoryId?: string;
  categoryName?: string,guildId: string,
    guildName: string;
  userId?: string; // Optional user context for factories
}

export type PanelConfig = {
  id: string;
  name?: string;
  description?: string;
  type?: string; // Panel type for factory
  targetChannels?: string[]; // Specific channel IDs
  targetCategories?: string[]; // Category IDs
  channelPatterns?: RegExp[]; // Channel name patterns
  priority?: number; // Higher priority panels override lower ones
  isEnabled: boolean;
  [key: string]: any // Allow additional config properties}

/**
 * Enhanced panel content interface that supports both ButtonBuilder and StringSelectMenuBuilder
 * providing better type safety for mixed component types
 */
export type PanelContent = {
  embeds: EmbedBuilder[],
    components: (
    | ActionRowBuilder<ButtonBuilder>
    | ActionRowBuilder<StringSelectMenuBuilder>
    | ActionRowBuilder<ButtonBuilder | StringSelectMenuBuilder>
    | ActionRowBuilder<any>;
  )[];
  content?: string;
  metadata?: Record<string, any>; // Allow additional metadata
}

export abstract class BaseChannelPanel {
  abstract readonly config: PanelConfig
  
  /**
   * Generate the panel content for the channel
   */
  abstract generatePanel(context: ChannelContext): Promise<PanelContent>
  
  /**
   * Handle button interactions for this panel
   */;
  abstract handleInteraction(interaction: any)
      context: ChannelContext): Promise<void>
  
  /**
   * Get panel update frequency (in milliseconds)
   */
  getUpdateInterval(): number {return 24 * 60 * 60 * 1000; //,
      Default: 24 hours}
  
  /**
   * Check if panel content needs updating
   */
  needsUpdate(lastUpdate: Date): boolean {const now = new Date();
    const timeSinceUpdate = now.getTime() - lastUpdate.getTime();
    return timeSinceUpdate >= this.getUpdateInterval()}

  /**
   * Get custom update strategy for this panel type
   * Override this to implement panel-specific update logic
   */
  getUpdateStrategy(): PanelUpdateStrategy {
    return {
      type: 'interval',
    interval: this.getUpdateInterval(),
      conditions: []}}
}

export type PanelUpdateStrategy = {;
  type: 'interval' | 'event-driven' | 'conditional' | 'manual';
  interval?: number; // For interval-based updates
  events?: string[]; // For event-driven updates
  conditions?: PanelUpdateCondition[]; // For conditional updates
}

export type PanelUpdateCondition = {
  type: 'user-activity' | 'data-change' | 'time-window' | 'external-trigger';
  threshold?: number;
  timeWindow?: number;
  metadata?: Record<string, any>}

// === ADDITIONAL INTERFACES FOR CONSOLIDATED SERVICES ===

export type BasePanel = {
  id: string,
      type: string,config: PanelConfig,
    context: ChannelContext;
  state: 'initializing' | 'active' | 'inactive' | 'error';
  createdAt: Date;
  lastUpdated: Date;
  template?: any;
  
  // Core methods
  activate: () => Promise<boolean>,
      deactivate: () => Promise<void>,update: (config: Partial<PanelConfig>) => Promise<void>,
    generateContent: () => Promise<PanelContent>
  handleInteraction: (interaction: any) => Promise<any>
  
  // Dynamic methods added by factory;
  [key: string]: any}

export type PanelState = {
  panelId: string,
      channelId: string;
  status: 'active' | 'inactive' | 'error' | 'deploying';
  lastActivity: Date;
  errorCount: number;
  lastError?: string,deployedAt: Date,
    updatedAt: Date;
  metadata: Record<string, any>}

export type PanelEvent = {
  id: string,
      panelId: string,channelId: string,
    eventType: 'activation' | 'deactivation' | 'interaction' | 'update' | 'error';
  data: Record<string, any>;
  timestamp: Date;
  userId?: string}