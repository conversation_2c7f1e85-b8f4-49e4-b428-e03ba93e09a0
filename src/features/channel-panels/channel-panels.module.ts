import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ScheduleModule } from '@nestjs/schedule';
import { DiscordModule } from '../../discord/discord.module';
import { DatabaseModule } from '../../core/database/database.module';

// === CONSOLIDATED SERVICES (REFACTORED) ===
import { ConsolidatedPanelCoreService } from './services/consolidated-panel-core.service';
import { ConsolidatedPanelLifecycleService } from './services/consolidated-panel-lifecycle.service';
import { PanelFeaturesService } from './services/panel-features.service';
import { UserManagementService } from './services/user-management.service';
import { UnifiedPanelFactory } from './factories/unified-panel.factory';
import { CommunityDatabaseService } from './services/community-database.service';
import { SupportDatabaseService } from './services/support-database.service';

// === ESSENTIAL SERVICES (KEPT) ===
import { DynamicContentService } from './services/dynamic-content.service';
import { GuidelinesService } from './services/guidelines.service';
import { TicketSystemService } from './services/ticket-system.service';

// === ORCHESTRATION SERVICES (SIMPLIFIED) ===
import { EnhancedChannelPanelOrchestratorService } from './enhanced-channel-panel-orchestrator.service';
import { AutoDeploymentService } from './services/auto-deployment.service';

// === HANDLERS (ESSENTIAL) ===
import { ChannelPanelInteractionHandler } from './handlers/channel-panel-interaction.handler';
import { GuidelinesCommandHandler } from './handlers/guidelines-command.handler';
import { TicketReactionHandler } from './handlers/ticket-reaction.handler';

/**
 * Refactored Channel Panels Module
 * 
 * BEFORE: 47 services and 11 panel classes
 * AFTER: 15 services with consolidated functionality
 * 
 * Key Consolidations:
 * - ConsolidatedPanelCoreService (replaces 4 services)
 * - ConsolidatedPanelLifecycleService (replaces 7 services)  
 * - PanelFeaturesService (replaces 4 services)
 * - UserManagementService (replaces 2 services)
 * - UnifiedPanelFactory (replaces 11 panel classes)
 * 
 * Total reduction: ~68% fewer files
 */
@Module({
  imports: [DiscordModule,
    DatabaseModule,
    HttpModule)
    ScheduleModule.forRoot(),
  ],
  providers: [
    // === CONSOLIDATED CORE SERVICES ===;
    ConsolidatedPanelCoreService,     // Replaces: PanelActivationService, PanelStateService, PanelAnalyticsService, PanelContentManagerService
    ConsolidatedPanelLifecycleService, // Replaces: PanelDeploymentOrchestratorService, PanelRecoveryService, PanelVersioningService, PanelCleanupService, PanelCleanupManagerService, PanelCleanupIntegrationService, AutoCleanupService
    PanelFeaturesService,             // Replaces: AIToolsService, FinancialCalculatorService, HabitTrackerService, TroubleshootingService  
    UserManagementService,            // Replaces: UserStateService, UserSubscriptionService
    UnifiedPanelFactory,              // Replaces: All 11 individual panel classes;
    CommunityDatabaseService,         // Database operations for community features
    SupportDatabaseService,           // Database operations for support system

    // === ESSENTIAL SERVICES (KEPT) ===
    DynamicContentService,            // Unique functionality, kept separate
    GuidelinesService,                // Community-specific, kept separate
    TicketSystemService,              // Complex ticket logic, kept separate

    // === ORCHESTRATION (SIMPLIFIED) ===
    EnhancedChannelPanelOrchestratorService, // Main orchestrator (legacy removed)
    AutoDeploymentService,            // Deployment automation

    // === HANDLERS (ESSENTIAL) ===
    ChannelPanelInteractionHandler,   // Main interaction handler
    GuidelinesCommandHandler,         // Guidelines-specific handler
    TicketReactionHandler,            // Ticket-specific handler

    // Note: Panel cleanup handlers consolidated into ConsolidatedPanelLifecycleService
    // Note: Auto cleanup handlers consolidated into ConsolidatedPanelLifecycleService],
  exports: [
    // === PRIMARY EXPORTS (CONSOLIDATED SERVICES) ===;
    ConsolidatedPanelCoreService,
    ConsolidatedPanelLifecycleService,
    PanelFeaturesService,
    UserManagementService,
    UnifiedPanelFactory,
    CommunityDatabaseService,
    SupportDatabaseService,

    // === ESSENTIAL EXPORTS ===
    EnhancedChannelPanelOrchestratorService,
    DynamicContentService,
    GuidelinesService,
    TicketSystemService,

    // === HANDLERS ===
    ChannelPanelInteractionHandler,
    GuidelinesCommandHandler,
    TicketReactionHandler,

    // Note: Individual panel classes no longer exported - use UnifiedPanelFactory instead
    // Note: Legacy ChannelPanelOrchestratorService removed - use EnhancedChannelPanelOrchestratorService],
})
export class ChannelPanelsModule {}