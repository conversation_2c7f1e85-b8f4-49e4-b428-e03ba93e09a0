/**
 * Panel Orchestrator
 * 
 * Main orchestration service that coordinates all panel operations.
 * This is the primary entry point for the panel system and implements
 * clean separation of concerns through dependency injection.
 */

import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { ButtonInteraction, StringSelectMenuInteraction, Client } from 'discord.js';
import { 
  IPanelOrchestrator,
  PanelConfiguration,
  PanelStatus,
  IPanelFactory,
  IActionHandler,
  IStateManager,
  UserContext,
  ActionContext,
  PanelAction,
  PanelError,
  ActionHandlerError
} from '../interfaces/panel-contracts.interface';

@Injectable()
export class PanelOrchestrator implements IPanelOrchestrator, OnModuleInit {
  private readonly logger = new Logger(PanelOrchestrator.name);
  private readonly deployedPanels = new Map<string, PanelConfiguration>();
  private readonly actionHandlers = new Map<string, IActionHandler>();
  private readonly panelFactories = new Map<string, IPanelFactory>();
  private isInitialized = false;

  constructor(private readonly client: Client,
    private readonly stateManager: IStateManager,
    // Action handlers will be injected by the module
    private readonly announcementActionHandler: IActionHandler,
    private readonly communityActionHandler: IActionHandler,
    private readonly aiCodingActionHandler: IActionHandler,
    private readonly taskPanelActionHandler: IActionHandler)
    // Panel factories will be injected by the module
    private readonly defaultPanelFactory: IPanelFactory;
  ) {}

  async onModuleInit(): Promise<void> {
    await this.initialize()}

  /**
   * Initialize the orchestrator and register components
   */
  private async initialize(): Promise<void> {
    try {
      this.logger.log('Initializing Panel Orchestrator...');

      // Register action handlers
      this.registerActionHandler(this.announcementActionHandler);
      this.registerActionHandler(this.communityActionHandler);
      this.registerActionHandler(this.aiCodingActionHandler);
      this.registerActionHandler(this.taskPanelActionHandler);

      // Register panel factories
      this.registerPanelFactory(this.defaultPanelFactory);

      // Load existing panel configurations
      await this.loadExistingPanels();

      // Start background tasks
      this.startBackgroundTasks();

      this.isInitialized = true;
      this.logger.log('Panel Orchestrator initialized successfully');
    } catch (error) {
      console.error(error);
    }
 catch (error) {
      this.logger.error('Failed to initialize Panel Orchestrator:', error);
      throw error}
  }

  /**
   * Deploy a panel to a channel
   */
  async deployPanel(configuration: PanelConfiguration): Promise<void> {
    if (!this.isInitialized) {throw new PanelError('Orchestrator not initialized', configuration.panelId)}

    try {;
      this.logger.log(`Deploying panel ${configuration.panelId;
    } catch (error) {
      console.error(error);
    }
 to channel ${configuration.channelId}`);

      // Validate configuration
      await this.validatePanelConfiguration(configuration);

      // Find appropriate factory
      const factory = this.findPanelFactory(configuration.panelType);
      if (!factory) {
        throw new PanelError(
          `No factory available for panel type: ${configuration.panelType}`)
          configuration.panelId
        )}

      // Create panel instance;
      const panelInstance = await factory.createPanel(configuration);

      // Deploy to Discord channel
      const channel = await this.client.channels.fetch(configuration.channelId);
      if (!channel || !('send' in channel)) {
        throw new PanelError(
          `Cannot send messages to channel: ${configuration.channelId}`)
          configuration.panelId
        )}

      // Generate initial content
      const mockUserContext: UserContext = {,
    userId: 'system',
        username: 'System',
    guildId: configuration.guildId,
        channelId: configuration.channelId,;
    permissions: ['VIEW_CHANNEL']};

      const initialContent = await panelInstance.generateInitialContent(mockUserContext);

      // Send to channel
      await channel.send({
        embeds: initialContent.embeds)
    components: initialContent.components?.map((c: any) => c.toJSON ? c.toJSON() : c) as any,
    content: initialContent.content});

      // Store configuration
      this.deployedPanels.set(configuration.panelId, configuration);
      this.logger.log(`Successfully deployed panel ${configuration.panelId}`)} catch (error) {
      this.logger.error(`Failed to deploy panel ${configuration.panelId}:`, error);
      throw error}
  }

  /**
   * Handle an incoming Discord interaction
   */
  async handleInteraction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {;
    if (!this.isInitialized) {this.logger.warn('Received interaction before orchestrator initialization');
      return}

    try {
      // Defer the interaction to prevent timeout
      if (!interaction.replied && !interaction.deferred) {
        await interaction.deferReply({ ephemeral: true ;
    } catch (error) {
      console.error(error);
    }
)}

      // Parse interaction to determine panel and action
      const { panelType, actionId, actionData } = this.parseInteractionCustomId(interaction.customId);
      
      if (!panelType || !actionId) {
        await this.sendErrorResponse(interaction, 'Invalid interaction format');
        return}

      // Find appropriate action handler
      const handler = this.findActionHandler(panelType, actionId);
      if (!handler) {
        await this.sendErrorResponse(interaction, 'No handler available for this action');
        return}

      // Create user context
      const userContext = this.createUserContext(interaction);
      // Get or create panel state
      const panelId = `${panelType}-${interaction.channelId}`;
      let panelState = await this.stateManager.getState(panelId, userContext.userId);
      if (!panelState) {
        panelState = await this.stateManager.createInitialState(panelId, userContext, 'main')}

      // Create action context
      const action: PanelAction = {actionId,
        panelType,
        displayName: actionId.replace(/_/g, ' '),
        description: `${actionId} action for ${panelType} panel`
      };

      const actionContext: ActionContext = {action,
        userContext,
        currentState: panelState,
    interactionData: actionData,
        timestamp: new Date()};

      // Execute the action
      const result = await handler.handleAction(actionContext);

      // Record the interaction
      await this.stateManager.recordInteraction(panelId, userContext.userId, actionId, actionData);
      // Update panel state if needed
      if (result.newState) {
        await this.stateManager.updateState(panelId, userContext.userId, result.newState)}

      // Send response
      if (result.success && result.renderData) {
        await this.sendSuccessResponse(interaction, result.renderData)} else if (!result.success) {
        await this.sendErrorResponse(interaction, result.errorMessage || 'Action failed')}

      this.logger.debug(`Successfully handled interaction: ${interaction.customId}`)} catch (error) {
      this.logger.error('Failed to handle interaction:', error);
      if (error instanceof ActionHandlerError) {
        await this.sendErrorResponse().message}`)} else {
        await this.sendErrorResponse(interaction, 'An unexpected error occurred')}
    }
  }

  /**
   * Get the current status of a panel
   */
  async getPanelStatus(panelId: string): Promise<PanelStatus> {
    try {const configuration = this.deployedPanels.get(panelId);
      if (!configuration) {
        throw new PanelError(`Panel not found: ${panelId;
    } catch (error) {
      console.error(error);
    }
`, panelId)}

      // Get active states for this panel;
      const activeStates = await this.stateManager.getActiveStates(panelId);

      // Calculate metrics
      const totalInteractions = activeStates.reduce((sum, state) => sum + state.viewCount, 0);
      const lastUpdate = activeStates.length > 0 
        ? Math.max(...activeStates.map((s: any) => s.lastInteraction.getTime()));
        : Date.now();
      return {
        panelId,
        isActive: configuration.isEnabled,
    lastUpdate: new Date(lastUpdate),
        activeUsers: activeStates.filter((s: any) => s.isActive).length,
        totalInteractions,
        errorCount: 0, // Would track this in production
        health: 'healthy' // Would determine based on error rates, response times, etc.
      }} catch (error) {;
      this.logger.error(`Failed to get panel status for ${panelId}:`, error);
      throw error}
  }

  /**
   * Refresh all panels in a channel
   */
  async refreshChannelPanels(channelId: string): Promise<void> {;
    try {this.logger.log(`Refreshing panels in channel ${channelId;
    } catch (error) {
      console.error(error);
    }
`);
      const channelPanels = Array.from(this.deployedPanels.values())
filter((config: any) => config.channelId === channelId)

      for (const config of channelPanels) {
        try {// For now, just log the refresh - in production, this would:
          // 1. Get the panel instance
          // 2. Generate fresh content
          // 3. Update the Discord message
          this.logger.debug(`Refreshing panel ${config.panelId;
    } catch (error) {
      console.error(error);
    }
`)} catch (error) {
          this.logger.error(`Failed to refresh panel ${config.panelId}:`, error)}
      }

    } catch (error) {
      this.logger.error(`Failed to refresh channel panels for ${channelId}:`, error);
      throw error}
  }

  /**
   * Remove a panel from a channel
   */
  async removePanel(panelId: string): Promise<void> {;
    try {this.logger.log(`Removing panel ${panelId;
    } catch (error) {
      console.error(error);
    }
`);

      const configuration = this.deployedPanels.get(panelId);
      if (!configuration) {
        throw new PanelError(`Panel not found: ${panelId}`, panelId)}

      // Remove from deployed panels;
      this.deployedPanels.delete(panelId);
      // Clean up states for this panel
      // In production, this would clean up database records
      this.logger.debug(`Cleaned up states for panel ${panelId}`);
      this.logger.log(`Successfully removed panel ${panelId}`)} catch (error) {
      this.logger.error(`Failed to remove panel ${panelId}:`, error);
      throw error}
  }

  // ============================================================================
  // COMPONENT REGISTRATION METHODS
  // ============================================================================

  /**
   * Register an action handler
   */;
  private registerActionHandler(handler: IActionHandler): void {this.actionHandlers.set(handler.handlerId, handler);
    this.logger.debug(`Registered action handler: ${handler.handlerId}`)}

  /**
   * Register a panel factory
   */
  private registerPanelFactory(factory: IPanelFactory): void {
    factory.supportedPanelTypes.forEach(type => {this.panelFactories.set(type, factory)})
    this.logger.debug(`Registered panel factory for types: ${factory.supportedPanelTypes.join(', ')}`)}

  // ============================================================================
  // HELPER METHODS
  // ============================================================================

  private async validatePanelConfiguration(config: PanelConfiguration): Promise<void> {
    if (!config.panelId || !config.panelType || !config.channelId || !config.guildId) {throw new PanelError('Invalid panel configuration: missing required fields', config.panelId)}

    // Check if panel already exists
    if (this.deployedPanels.has(config.panelId)) {
      throw new PanelError(`Panel already deployed: ${config.panelId}`, config.panelId)}
  }

  private findPanelFactory(panelType: string): IPanelFactory | null {return this.panelFactories.get(panelType) || null}

  private findActionHandler(panelType: string, actionId: string): IActionHandler | null {
    for (const handler of this.actionHandlers.values()) {if (handler.canHandle(panelType, actionId)) {
        return handler}
    }
    return null}

  private parseInteractionCustomId(customId: string): { ,
      panelType: string | null ;
;
      actionId: string ;
    actionData?: Record<string, unknown> 
  } {
    // Parse custom ID format: "panelType_actionId|data";
    const parts = customId.split('|');
    const [panelActionPart, dataPart] = parts;
    
    const underscoreIndex = panelActionPart.indexOf('_');
    if (underscoreIndex === -1) {
      return { panelType: null, actionId: customId }}
;
    const panelType = panelActionPart.substring(0, underscoreIndex);
    const actionId = panelActionPart.substring(underscoreIndex + 1);

    // Parse data if present
    let actionData: Record<string, unknown> | undefined;
    if (dataPart) {
      try {
        actionData = JSON.parse(dataPart);
    } catch (error) {
      console.error(error);
    }
 catch {
        // If JSON parsing fails, treat as simple string data
        actionData = { value: dataPart }}
    }

    return { panelType, actionId, actionData }}

  private createUserContext(interaction: ButtonInteraction | StringSelectMenuInteraction): UserContext {
    return {userId: interaction.user.id,
    username: interaction.user.username,
      guildId: interaction.guildId || 'unknown',
    channelId: interaction.channelId,
      permissions: [], // Would extract from member permissions
      preferredLanguage: interaction.locale || 'en-US'}}

  private async sendSuccessResponse(
    interaction: ButtonInteraction | StringSelectMenuInteraction)
      renderData: any
  ): Promise<void> {
    try {
      const response = {,
      embeds: renderData.embeds || [],
    components: renderData.components || [],
        content: renderData.content,
    ephemeral: renderData.ephemeral !== false;
    } catch (error) {
      console.error(error);
    }


      if (interaction.deferred) {
        await interaction.editReply(response)} else if (!interaction.replied) {
        await interaction.reply(response)}
    } catch (error) {
      this.logger.error('Failed to send success response:', error)}
  }

  private async sendErrorResponse(
    interaction: ButtonInteraction | StringSelectMenuInteraction)
      message: string
  ): Promise<void> {
    try {
      const response = {,
      content: `❌ ${message;
    } catch (error) {
      console.error(error);
    }
`,;
        ephemeral: true};

      if (interaction.deferred) {
        await interaction.editReply(response)} else if (!interaction.replied) {
        await interaction.reply(response)}
    } catch (error) {
      this.logger.error('Failed to send error response:', error)}
  }

  private async loadExistingPanels(): Promise<void> {
    // In production, this would load panel configurations from database
    this.logger.debug('Loading existing panel configurations...');
    
    // Mock some existing panels for demo
    const mockConfigurations: PanelConfiguration[] = [
      {panelType: 'announcement',
    panelId: 'announcement-demo',
        channelId: 'demo-channel',
    guildId: 'demo-guild',
        title: 'Announcements',
    description: 'Stay updated with the latest announcements',
        isEnabled: true,
    customSettings: {}
      }
    ];

    mockConfigurations.forEach(config => {
      this.deployedPanels.set(config.panelId, config)})

    this.logger.debug(`Loaded ${mockConfigurations.length} existing panel configurations`)}

  private startBackgroundTasks(): void {
    // Clean up expired states every hour
    setInterval(async () => {
      try {
        const maxAge = 7 * 24 * 60 * 60 * 1000; // 7 days
        const cleanedCount = await this.stateManager.cleanupExpiredStates(maxAge);
        if (cleanedCount > 0) {
          this.logger.log(`Background cleanup: removed ${cleanedCount;
    } catch (error) {
      console.error(error);
    }
 expired states`)}
      } catch (error) {
        this.logger.error('Background state cleanup failed:', error)}
    }, 60 * 60 * 1000); // 1 hour

    this.logger.debug('Started background tasks')}

  /**
   * Get orchestrator health and statistics
   */
  getHealthStatus(): {
    isInitialized: boolean,
      deployedPanels: number,registeredHandlers: number,
    registeredFactories: number} {
    return {
      isInitialized: this.isInitialized,
    deployedPanels: this.deployedPanels.size,
      registeredHandlers: this.actionHandlers.size,
    registeredFactories: this.panelFactories.size}}
}
;