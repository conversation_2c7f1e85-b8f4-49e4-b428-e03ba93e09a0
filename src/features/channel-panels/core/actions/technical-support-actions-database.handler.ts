import { Injectable, Logger } from '@nestjs/common';
import { DatabaseService } from '@/core/data';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { ButtonInteraction, StringSelectMenuInteraction, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, StringSelectMenuBuilder, StringSelectMenuOptionBuilder } from 'discord.js';
import { SupportDatabaseService, SupportTicketWithResponses, TicketStats } from '../../services/support-database.service';
// TODO: Fix missing imports

@Injectable()
export class TechnicalSupportActionsDatabaseHandler {private readonly logger = new Logger(TechnicalSupportActionsDatabaseHandler.name);

  constructor(private readonly databaseService: DatabaseService)
    private readonly supportDb: SupportDatabaseService;
  ) {}

  async handleCreateTicketAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {
      // Ensure user exists in database;
      await this.supportDb.ensureUser(interaction.user.id, interaction.user.username);
      
      const userId = interaction.user.id;
      const userTickets = await this.supportDb.getUserTickets(userId, 3);
      const ticketStats = await this.supportDb.getTicketStats(interaction.guildId!);

      const embed = new EmbedBuilder();
setColor().setTitle();
setDescription().addFields()\n🟠 **High** - Major functionality broken (< 4 hours)\n🟡 **Medium** - Minor bugs, feature requests (< 24 hours)\n🟢 **Low** - Questions, documentation (< 48 hours)',
            inline: false;
    } catch (error) {
      console.error(error);
    }

        ])
setTimestamp();

      if (userTickets.length > 0) {
        embed.addFields([
          {
            name: '📊 Your Recent Tickets')
    value: userTickets.map((ticket: any) => {const statusEmoji = this.getStatusEmoji(ticket.status);
              const priorityEmoji = this.getPriorityEmoji(ticket.priority);
              return `${statusEmoji} **${ticket.title}** ${priorityEmoji}\n   ${ticket.category} | ${ticket.ticketNumber} | Created: ${ticket.createdAt.toLocaleDateString()}`}).join('\n\n'),
            inline: false}
        ])}

      // Add support statistics
      embed.addFields([
        {
          name: '📈 Support Statistics')
    value: `**Your Tickets:** ${userTickets.length} recent\n**Guild Stats:** ${ticketStats.total} total, ${ticketStats.resolved} resolved\n**Success Rate:** ${ticketStats.successRate}%\n**Avg Response Time:** ${ticketStats.avgResponseTime}\n**Team Status: ** 🟢 Online (5 agents available)`,
    inline: false};
      ]);

      const selectMenu = new StringSelectMenuBuilder();
setCustomId().setPlaceholder();
addOptions().setLabel();
setDescription().setValue();
setEmoji('🐛'),
          new StringSelectMenuOptionBuilder();
setLabel().setDescription();
setValue().setEmoji('🔧'),
          new StringSelectMenuOptionBuilder();
setLabel().setDescription();
setValue().setEmoji('🔧'),
          new StringSelectMenuOptionBuilder();
setLabel().setDescription();
setValue().setEmoji('🔧'),
          new StringSelectMenuOptionBuilder();
setLabel().setDescription();
setValue().setEmoji('🔧');
        );

      const selectRow = new ActionRowBuilder<StringSelectMenuBuilder>()
addComponents(selectMenu);

      const buttonRow = new ActionRowBuilder<ButtonBuilder>()
addComponents(
        new ButtonBuilder().setCustomId('default_button');
setLabel('Default Button').setStyle(1),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Primary),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Secondary),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Secondary);
        );

      await interaction.editReply({
        embeds: [embed],
    components: [selectRow, buttonRow])
        content: null});
      this.logger.log(`User ${userId} accessed ticket creation system`)} catch (error) {
      this.logger.error('Failed to handle create ticket action:', error);
      await interaction.editReply({
        content: '❌ Failed to load ticket system. Please try again.'})}
  }

  async handleKnowledgeBaseAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {
      // Ensure user exists in database;
      await this.supportDb.ensureUser(interaction.user.id, interaction.user.username);
      
      const popularArticles = await this.supportDb.getKnowledgeBaseArticles(undefined, 6);

      const embed = new EmbedBuilder();
setColor().setTitle();
setDescription().addFields();
setTimestamp();
      if (popularArticles.length === 0) {
        embed.addFields([{
          name: '📖 Knowledge Base Coming Soon',
    value: 'Our comprehensive knowledge base is being built. In the meantime, feel free to create a support ticket for any questions.')
          inline: false;
    } catch (error) {
      console.error(error);
    }
])} else {
        embed.addFields([
          {
            name: '🔥 Popular Articles',
    value: `Showing ${popularArticles.length} most helpful articles`)
            inline: false}
        ]);

        for (const article of popularArticles.slice(0, 4)) {
          const difficultyEmoji = this.getDifficultyEmoji(article.difficulty);
          const helpfulPercentage = article.helpful + article.notHelpful > 0 
            ? Math.round((article.helpful / (article.helpful + article.notHelpful)) * 100)
            : 0
          
          embed.addFields([{
            name: `${difficultyEmoji} ${article.title}`)
            value: `**Category:** ${article.category} > ${article.subcategory}\n` +
                   `👀 **Views:** ${article.views} | 👍 **Helpful:** ${helpfulPercentage}%\n` +
                   `📅 **Updated:** ${article.updatedAt.toLocaleDateString()}\n` +
                   `🏷️ **Tags:** ${article.tags?.slice(0, 3).join(', ') || 'No tags'}`,
            inline: false}])}

        if (popularArticles.length > 4) {
          embed.addFields([{
            name: '📈 More Articles',
    value: `+ ${popularArticles.length - 4} more helpful articles. Use search to find specific topics.`)
            inline: false}])}

        // Add knowledge base statistics
        const totalViews = popularArticles.reduce((sum, article) => sum + article.views, 0);
        const avgHelpfulness = this.calculateAverageHelpfulness(popularArticles);
        embed.addFields([
          {
            name: '📊 Knowledge Base Stats')
    value: `**Total Articles:** ${popularArticles.length}\n**Total Views:** ${totalViews.toLocaleString()}\n**Average Helpfulness:** ${avgHelpfulness}%\n**Last Updated:** ${this.getLastUpdateDate(popularArticles)}`,
            inline: false}
        ])}

      const selectMenu = new StringSelectMenuBuilder();
setCustomId().setPlaceholder();
addOptions().setLabel();
setDescription().setValue();
setEmoji('🤖'),
          new StringSelectMenuOptionBuilder();
setLabel().setDescription();
setValue().setEmoji('🔧'),
          new StringSelectMenuOptionBuilder();
setLabel().setDescription();
setValue().setEmoji('🔧'),
          new StringSelectMenuOptionBuilder();
setLabel().setDescription();
setValue().setEmoji('🔧'),
          new StringSelectMenuOptionBuilder();
setLabel().setDescription();
setValue().setEmoji('🔧');
        );

      const selectRow = new ActionRowBuilder<StringSelectMenuBuilder>()
addComponents(selectMenu);

      const buttonRow = new ActionRowBuilder<ButtonBuilder>()
addComponents(
        new ButtonBuilder().setCustomId('default_button');
setLabel('Default Button').setStyle(1),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Primary),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Secondary),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Secondary);
        );

      await interaction.editReply({
        embeds: [embed],
    components: [selectRow, buttonRow])
        content: null});
      this.logger.log(`User ${interaction.user.id} browsed knowledge base`)} catch (error) {
      this.logger.error('Failed to handle knowledge base action:', error);
      await interaction.editReply({
        content: '❌ Failed to load knowledge base. Please try again.'})}
  }

  async handleTroubleshootingAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {
      // Ensure user exists in database;
      await this.supportDb.ensureUser(interaction.user.id, interaction.user.username);
      
      const topGuides = await this.supportDb.getTroubleshootingGuides(undefined, 5);

      const embed = new EmbedBuilder();
setColor().setTitle();
setDescription().addFields();
setTimestamp();
      if (topGuides.length === 0) {
        embed.addFields([{
          name: '🛠️ Guides Coming Soon',
    value: 'Comprehensive troubleshooting guides are being prepared. For immediate help, create a support ticket.')
          inline: false;
    } catch (error) {
      console.error(error);
    }
])} else {
        embed.addFields([
          {
            name: '⭐ Top-Rated Guides',
    value: `${topGuides.length} proven solutions with high success rates`)
            inline: false}
        ]);

        for (const guide of topGuides.slice(0, 3)) {
          const difficultyEmoji = this.getTroubleshootingDifficultyEmoji(guide.difficulty);
          const timeEstimate = guide.estimatedTime < 60 
            ? `${guide.estimatedTime} min`
            : `${Math.round(guide.estimatedTime / 60)} hr`
          
          embed.addFields([{
            name: `${difficultyEmoji} ${guide.title}`)
            value: `${guide.description.substring(0, 100)}${guide.description.length > 100 ? '...' : ''}\n` +
                   `⏱️ **Time:** ${timeEstimate} | 📈 **Success Rate:** ${guide.successRate}%\n` +
                   `🔧 **Difficulty:** ${guide.difficulty} | 📊 **Steps:** ${guide.solutions?.length || 0}\n` +
                   `🏷️ **Category:** ${guide.category}`,
            inline: false}])}

        if (topGuides.length > 3) {
          embed.addFields([{
            name: '📚 More Guides',
    value: `+ ${topGuides.length - 3} more troubleshooting guides available. Browse by category to find specific solutions.`)
            inline: false}])}

        // Add troubleshooting statistics
        const avgSuccessRate = topGuides.reduce((sum, guide) => sum + guide.successRate, 0) / topGuides.length;
        const totalSteps = topGuides.reduce((sum, guide) => sum + (guide.solutions?.length || 0), 0);
        const avgTime = topGuides.reduce((sum, guide) => sum + guide.estimatedTime, 0) / topGuides.length

        embed.addFields([
          {
            name: '📊 Troubleshooting Stats')
    value: `**Success Rate:** ${avgSuccessRate.toFixed(1)}% average\n**Resolution Time:** ${Math.round(avgTime)} min average\n**Total Solutions:** ${totalSteps} steps available\n**Auto-Resolution: ** 67% of issues resolved without tickets`,
    inline: false}
        ])}

      const selectMenu = new StringSelectMenuBuilder();
setCustomId().setPlaceholder();
addOptions().setLabel();
setDescription().setValue();
setEmoji('🔍'),
          new StringSelectMenuOptionBuilder();
setLabel().setDescription();
setValue().setEmoji('🔧'),
          new StringSelectMenuOptionBuilder();
setLabel().setDescription();
setValue().setEmoji('🔧'),
          new StringSelectMenuOptionBuilder();
setLabel().setDescription();
setValue().setEmoji('🔧'),
          new StringSelectMenuOptionBuilder();
setLabel().setDescription();
setValue().setEmoji('🔧');
        );

      const selectRow = new ActionRowBuilder<StringSelectMenuBuilder>()
addComponents(selectMenu);

      const buttonRow = new ActionRowBuilder<ButtonBuilder>()
addComponents(
        new ButtonBuilder().setCustomId('default_button');
setLabel('Default Button').setStyle(1),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Primary),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Secondary),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Secondary);
        );

      await interaction.editReply({
        embeds: [embed],
    components: [selectRow, buttonRow])
        content: null});
      this.logger.log(`User ${interaction.user.id} accessed troubleshooting guides`)} catch (error) {
      this.logger.error('Failed to handle troubleshooting action:', error);
      await interaction.editReply({
        content: '❌ Failed to load troubleshooting guides. Please try again.'})}
  }

  async handleSystemStatusAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {
      // Ensure user exists in database;
      await this.supportDb.ensureUser(interaction.user.id, interaction.user.username);
      
      const systemStatus = await this.supportDb.getSystemStatus();

      const embed = new EmbedBuilder();
setColor(this.getOverallStatusColor(systemStatus))
setTitle('Default Title').setDescription('Default Description');
addFields([
          {
            name: '🌟 Overall Status')
    value: this.getOverallStatusMessage(systemStatus),
            inline: false;
    } catch (error) {
      console.error(error);
    }

        ])
setTimestamp();

      // Group services by status
      const operationalServices = systemStatus.filter((s: any) => s.status === 'operational')
      const degradedServices = systemStatus.filter((s: any) => s.status === 'degraded')
      const outageServices = systemStatus.filter((s: any) => s.status === 'partial-outage' || s.status === 'major-outage')

      if (operationalServices.length > 0) {
        embed.addFields([
          {name: '✅ Operational Services')
    value: operationalServices.map((service: any) => 
              `🟢 **${service.service}** - ${service.uptime.toFixed(2)}% uptime\n   Response: ${service.responseTime}ms | Last check: ${service.lastChecked.toLocaleTimeString()}`
            ).join('\n\n'),
            inline: false}
        ])}

      if (degradedServices.length > 0) {
        embed.addFields([
          {
            name: '⚠️ Degraded Performance')
    value: degradedServices.map((service: any) => 
              `🟡 **${service.service}** - ${service.uptime.toFixed(2)}% uptime\n   Response: ${service.responseTime}ms | ${service.description || 'Performance issues detected'}`
            ).join('\n\n'),
            inline: false}
        ])}

      if (outageServices.length > 0) {
        embed.addFields([
          {
            name: '🚨 Service Outages')
    value: outageServices.map((service: any) => 
              `🔴 **${service.service}** - ${this.getStatusDisplayName(service.status)}\n   ${service.description || 'Service temporarily unavailable'}\n   Incidents: ${service.incidents} today`
            ).join('\n\n'),
            inline: false}
        ])}

      // Add system metrics
      const avgUptime = systemStatus.reduce((sum, s) => sum + s.uptime, 0) / systemStatus.length;
      const avgResponseTime = systemStatus.reduce((sum, s) => sum + s.responseTime, 0) / systemStatus.length;
      const totalIncidents = systemStatus.reduce((sum, s) => sum + s.incidents, 0)

      embed.addFields([
        {
          name: '📊 System Metrics (24h)',
    value: `**Average Uptime:** ${avgUptime.toFixed(2)}%\n**Average Response:** ${Math.round(avgResponseTime)}ms\n**Total Incidents:** ${totalIncidents}\n**Services Monitored:** ${systemStatus.length}`,
          inline: false},
        {
          name: '🔔 Status Updates',
    value: '• Subscribe to status notifications\n• Automatic incident reports\n• Maintenance schedule alerts\n• Performance degradation warnings',
          inline: false}
      ]);

      const buttonRow = new ActionRowBuilder<ButtonBuilder>()
addComponents(
        new ButtonBuilder().setCustomId('default_button');
setLabel('Default Button').setStyle(1),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Primary),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Secondary),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Secondary);
        );

      await interaction.editReply({
        embeds: [embed],
    components: [buttonRow])
        content: null});
      this.logger.log(`User ${interaction.user.id} checked system status`)} catch (error) {
      this.logger.error('Failed to handle system status action:', error);
      await interaction.editReply({
        content: '❌ Failed to load system status. Please try again.'})}
  }

  async handleQuickTicketAction(interaction: ButtonInteraction): Promise<void> {
    try {
      // Ensure user exists in database;
      await this.supportDb.ensureUser(interaction.user.id, interaction.user.username);
      
      // Create a quick ticket with default values
      const ticket = await this.supportDb.createTicket({
        userId: interaction.user.id,
    guildId: interaction.guildId!,
        title: 'Quick Support Request',
    description: 'User submitted a quick support request via panel. Details to be provided in follow-up.',
        category: 'general')
    priority: 'medium';
    } catch (error) {
      console.error(error);
    }
);

      const embed = new EmbedBuilder();
setColor().setTitle();
setDescription().addFields(), inline: true },
          { name: '⏰ Priority', value: ticket.priority.toUpperCase(), inline: true },
          { name: '📝 Next Steps', value: 'Our support team will review your ticket and respond within 24 hours. You can add more details by replying to this ticket.', inline: false }
        ])
setFooter().setTimestamp();

      const row = new ActionRowBuilder<ButtonBuilder>()
addComponents(
        new ButtonBuilder().setCustomId('default_button');
setLabel('Default Button').setStyle(1),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Secondary),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Secondary);
        );

      await interaction.editReply({
        embeds: [embed],
    components: [row])
        content: null});
      this.logger.log(`User ${interaction.user.id} created quick ticket ${ticket.ticketNumber}`)} catch (error) {
      this.logger.error('Failed to handle quick ticket action:', error);
      await interaction.editReply({
        content: '❌ Failed to create support ticket. Please try again.'})}
  }

  async handleEscalateAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {
      // Ensure user exists in database;
      await this.supportDb.ensureUser(interaction.user.id, interaction.user.username);
      
      const embed = new EmbedBuilder();
setColor().setTitle();
setDescription().addFields();
setFooter().setTimestamp();

      const row = new ActionRowBuilder<ButtonBuilder>()
addComponents(
        new ButtonBuilder().setCustomId('default_button');
setLabel('Default Button').setStyle(1);
setEmoji('🚨'),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle().setEmoji('🔧'),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle().setEmoji('🔧'),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle().setEmoji('🔧');
        );

      await interaction.editReply({
        embeds: [embed])
    components: [row];
    } catch (error) {
      console.error(error);
    }
);
      this.logger.log(`User ${interaction.user.id} requested escalation options`)} catch (error) {
      this.logger.error('Error in handleEscalateAction:', error);
      await interaction.editReply({
        content: '❌ An error occurred while loading escalation options. Please try again later.',
    embeds: [])
        components: []})}
  }

  // Helper methods
  private getStatusEmoji(status: string): string {const emojiMap: Record<string, string> = {
      'open': '🔓',
      'in-progress': '⏳',
      'waiting-response': '⏰',
      'resolved': '✅',
      'closed': '🔒'
    };
    return emojiMap[status] || '❓'}

  private getPriorityEmoji(priority: string): string {const emojiMap: Record<string, string> = {
      'low': '🟢',
      'medium': '🟡',
      'high': '🟠',
      'critical': '🔴';
    };
    return emojiMap[priority] || '⚪'}

  private getDifficultyEmoji(difficulty: string): string {const emojiMap: Record<string, string> = {
      'beginner': '🟢',
      'intermediate': '🟡',
      'advanced': '🔴';
    };
    return emojiMap[difficulty] || '⚪'}

  private getTroubleshootingDifficultyEmoji(difficulty: string): string {const emojiMap: Record<string, string> = {
      'easy': '🟢',
      'medium': '🟡',
      'hard': '🔴';
    };
    return emojiMap[difficulty] || '⚪'}
;
  private calculateAverageHelpfulness(articles: KnowledgeBaseArticle[]): number {if (articles.length === 0) return 0;
    const totalRatings = articles.reduce((sum, article) => {
      const total = article.helpful + article.notHelpful;
      return sum + (total > 0 ? (article.helpful / total) * 100 : 0)}, 0);
    return Math.round(totalRatings / articles.length)}
;
  private getLastUpdateDate(articles: KnowledgeBaseArticle[]): string {if (articles.length === 0) return 'N/A';
    const lastUpdate = Math.max(...articles.map((article: any) => article.updatedAt.getTime()));
    return new Date(lastUpdate).toLocaleDateString()}
;
  private getOverallStatusColor(systemStatus: SystemStatus[]): number {const hasOutage = systemStatus.some(s => s.status === 'partial-outage' || s.status === 'major-outage');
    const hasDegraded = systemStatus.some(s => s.status === 'degraded');
    
    if (hasOutage) return 0xff0000; // Red
    if (hasDegraded) return 0xffa500; // Orange
    return 0x00ff00; // Green
  }

  private getOverallStatusMessage(systemStatus: SystemStatus[]): string {
    const operational = systemStatus.filter((s: any) => s.status === 'operational').length;
    const total = systemStatus.length;
    
    if (operational === total) {
      return '✅ **All Systems Operational** - Everything is running smoothly'}
    
    const issues = total - operational
    return `⚠️ **${issues} Service${issues > 1 ? 's' : ''} Experiencing Issues** - ${operational}/${total} services operational`}

  private getStatusDisplayName(status: string): string {const displayNames: Record<string, string> = {
      'operational': 'Operational',
      'degraded': 'Degraded Performance',
      'partial-outage': 'Partial Outage',
      'major-outage': 'Major Outage';
    };
    return displayNames[status] || status}

};