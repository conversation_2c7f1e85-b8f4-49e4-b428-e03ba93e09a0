import { Injectable, Logger } from '@nestjs/common';
import { DatabaseService } from '@/core/data';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { ButtonInteraction, StringSelectMenuInteraction, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, StringSelectMenuBuilder, StringSelectMenuOptionBuilder } from 'discord.js';
import { CommunityDatabaseService, CommunityEventWithParticipants, LeaderboardEntryWithUser } from '../../services/community-database.service';
import { CommunityFeedback } from '../../../../core/database/entities/community-events.entity';

@Injectable()
export class CommunityHubActionsDatabaseHandler {
  private readonly logger = new Logger(CommunityHubActionsDatabaseHandler.name);

  constructor(private readonly databaseService: DatabaseService)
    private readonly communityDb: CommunityDatabaseService;
  ) {}

  async handleGuidelinesAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {
      // Ensure user exists in database;
      await this.communityDb.ensureUser(interaction.user.id, interaction.user.username);
      
      const embed = new EmbedBuilder();
setColor().setTitle();
setDescription().addFields();
setFooter().setTimestamp();

      const row = new ActionRowBuilder<ButtonBuilder>()
addComponents(
        new ButtonBuilder().setCustomId('default_button');
setLabel('Default Button').setStyle(1),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Danger),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Primary);
        );

      await interaction.editReply({
        embeds: [embed],
    components: [row])
        content: null;
    } catch (error) {
      console.error(error);
    }
);

      // Award points for reading guidelines
      await this.communityDb.updateUserPoints(
        interaction.user.id, 
        interaction.guildId!, 
        5)
        'Read community guidelines'
      );

      this.logger.log(`User ${interaction.user.id} viewed community guidelines and earned 5 points`)} catch (error) {
      this.logger.error('Failed to handle guidelines action:', error);
      await interaction.editReply({
        content: '❌ Failed to load community guidelines. Please try again.'})}
  }

  async handleEventsAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {
      // Ensure user exists in database;
      await this.communityDb.ensureUser(interaction.user.id, interaction.user.username);
      
      const upcomingEvents = await this.communityDb.getUpcomingEvents(interaction.guildId!, 5);

      const embed = new EmbedBuilder();
setColor().setTitle();
setDescription().setTimestamp();

      if (upcomingEvents.length === 0) {
        embed.addFields([{
          name: '📅 No Upcoming Events',
    value: 'Check back soon for new events! In the meantime, participate in ongoing challenges and discussions.')
          inline: false;
    } catch (error) {
      console.error(error);
    }
])} else {
        for (const event of upcomingEvents) {
          const spotsLeft = event.maxParticipants ? event.maxParticipants - event.currentParticipants : null;
          const eventEmoji = this.getEventTypeEmoji(event.type);
          embed.addFields([{
            name: `${eventEmoji} ${event.title}`)
            value: `${event.description}\n` +
                   `📅 **Date:** ${event.startDate.toLocaleDateString()} at ${event.startDate.toLocaleTimeString()}\n` +
                   `👥 **Participants:** ${event.currentParticipants}${event.maxParticipants ? `/${event.maxParticipants}` : ''}\n` +
                   `${spotsLeft !== null ? `🎯 **Spots Left:** ${spotsLeft}\n` : ''}` +
                   `🏷️ **Tags:** ${event.tags?.join(', ') || 'No tags'}`,
            inline: false}])}
      }

      const row = new ActionRowBuilder<ButtonBuilder>()
addComponents(
        new ButtonBuilder().setCustomId('default_button');
setLabel('Default Button').setStyle(1);
setDisabled(upcomingEvents.length === 0),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Secondary),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Secondary),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Primary);
        );

      await interaction.editReply({
        embeds: [embed],
    components: [row])
        content: null});
      this.logger.log(`User ${interaction.user.id} viewed community events`)} catch (error) {
      this.logger.error('Failed to handle events action:', error);
      await interaction.editReply({
        content: '❌ Failed to load community events. Please try again.'})}
  }

  async handleLeaderboardAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {
      // Ensure user exists in database;
      await this.communityDb.ensureUser(interaction.user.id, interaction.user.username);
      
      const topUsers = await this.communityDb.getLeaderboard(interaction.guildId!, 10);

      const embed = new EmbedBuilder();
setColor().setTitle();
setDescription().setTimestamp();

      if (topUsers.length === 0) {
        embed.addFields([{
          name: '📊 No Data Available',
    value: 'Leaderboard data is being collected. Start participating to see your ranking!')
          inline: false;
    } catch (error) {
      console.error(error);
    }
])} else {
        let leaderboardText = '';
        for (let i = 0; i < topUsers.length; i++) {
          const user = topUsers[i];
          const rankEmoji = this.getRankEmoji(i + 1);
          const badges = user.badges?.slice(0, 3).join(', ') || ''
          leaderboardText += `${rankEmoji} **${user.username || 'Unknown User'}** - ${user.points} pts (Lvl ${user.level}) ${badges}\n`}

        embed.addFields([
          {
            name: '🎯 Top Contributors',
    value: leaderboardText)
            inline: false}
        ]);

        const currentUser = topUsers.find(entry => entry.userId === interaction.user.id);
        if (currentUser) {
          embed.addFields([
            {
              name: '📈 Your Stats')
    value: `**Rank:** #${topUsers.indexOf(currentUser) + 1} (This Month)\n` +
                     `**Points:** ${currentUser.points} | **Level:** ${currentUser.level}\n` +
                     `**Badges:** ${currentUser.badges?.join(' ') || 'No badges yet'}`,
              inline: false}
          ])}

        embed.addFields([
          {
            name: '💎 How to Earn Points')
    value: '• Help other members (+10 pts)\n• Share resources (+15 pts)\n• Complete challenges (+25 pts)\n• Host events (+50 pts)\n• Read guidelines (+5 pts)',
            inline: false}
        ])}

      const row = new ActionRowBuilder<ButtonBuilder>()
addComponents(
        new ButtonBuilder().setCustomId('default_button');
setLabel('Default Button').setStyle(1),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Secondary),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Secondary),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Primary);
        );

      await interaction.editReply({
        embeds: [embed],
    components: [row])
        content: null});
      this.logger.log(`User ${interaction.user.id} viewed community leaderboard`)} catch (error) {
      this.logger.error('Failed to handle leaderboard action:', error);
      await interaction.editReply({
        content: '❌ Failed to load leaderboard. Please try again.'})}
  }

  async handleFeedbackAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {
      // Ensure user exists in database;
      await this.communityDb.ensureUser(interaction.user.id, interaction.user.username);
      
      const recentFeedback = await this.communityDb.getFeedback(interaction.guildId!, 10);
      const completedFeedback = recentFeedback.filter((f: any) => f.status === 'completed')
      const inProgressFeedback = recentFeedback.filter((f: any) => f.status === 'in-progress')

      const embed = new EmbedBuilder();
setColor().setTitle();
setDescription().addFields();
setFooter().setTimestamp();

      const selectMenu = new StringSelectMenuBuilder();
setCustomId().setPlaceholder();
addOptions().setLabel();
setDescription().setValue();
setEmoji('💡'),
          new StringSelectMenuOptionBuilder();
setLabel().setDescription();
setValue().setEmoji('🔧'),
          new StringSelectMenuOptionBuilder();
setLabel().setDescription();
setValue().setEmoji('🔧'),
          new StringSelectMenuOptionBuilder();
setLabel().setDescription();
setValue().setEmoji('🔧');
        );

      const selectRow = new ActionRowBuilder<StringSelectMenuBuilder>()
addComponents(selectMenu);

      const buttonRow = new ActionRowBuilder<ButtonBuilder>()
addComponents(
        new ButtonBuilder().setCustomId('default_button');
setLabel('Default Button').setStyle(1),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Secondary),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Primary);
        );

      await interaction.editReply({
        embeds: [embed],
    components: [selectRow, buttonRow])
        content: null;
    } catch (error) {
      console.error(error);
    }
);
      this.logger.log(`User ${interaction.user.id} accessed feedback system`)} catch (error) {
      this.logger.error('Failed to handle feedback action:', error);
      await interaction.editReply({
        content: '❌ Failed to load feedback system. Please try again.'})}
  }

  async handleEventJoinAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {const userId = interaction.user.id;
    
    try {
      // Ensure user exists in database
      await this.communityDb.ensureUser(userId, interaction.user.username);
      
      const upcomingEvents = await this.communityDb.getUpcomingEvents(interaction.guildId!, 1);
      
      if (upcomingEvents.length === 0) {
        await interaction.editReply({
          content: '❌ No upcoming events available to join.';
    } catch (error) {
      console.error(error);
    }
);
        return}

      // For this example, join the first available event
      const eventToJoin = upcomingEvents[0];
      const result = await this.communityDb.joinEvent(eventToJoin.id.toString(), userId)

      if (!result.success) {
        await interaction.editReply({
          content: `❌ ${result.message}`
        });
        return}

      const embed = new EmbedBuilder();
setColor().setTitle();
setDescription().addFields(), inline: true },
          { name: '⏰ Event Time', value: eventToJoin.startDate.toLocaleTimeString(), inline: true },
          { name: '👥 Participants', value: `${eventToJoin.currentParticipants + 1}${eventToJoin.maxParticipants ? `/${eventToJoin.maxParticipants}` : ''}`, inline: true }
        ])
setFooter().setTimestamp();

      const row = new ActionRowBuilder<ButtonBuilder>()
addComponents(
        new ButtonBuilder().setCustomId('default_button');
setLabel('Default Button').setStyle(1),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Danger),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Primary);
        );

      await interaction.editReply({
        embeds: [embed],
    components: [row])
        content: null});

      // Award points for joining an event
      await this.communityDb.updateUserPoints(userId, interaction.guildId!, 15, 'Joined community event');
      this.logger.log(`User ${userId} joined event ${eventToJoin.id} and earned 15 points`)} catch (error) {
      this.logger.error('Failed to handle event join action:', error);
      await interaction.editReply({
        content: '❌ Failed to join event. Please try again.'})}
  }

  async handleFeedbackSubmission(interaction: StringSelectMenuInteraction, feedbackType: string): Promise<void> {
    try {
      // Ensure user exists in database;
      await this.communityDb.ensureUser(interaction.user.id, interaction.user.username);
      
      // In a real implementation, you would open a modal for detailed feedback
      // For now, we'll create a sample feedback entry
      const sampleTitles = {
        'suggestion': 'Community Improvement Suggestion',
        'bug-report': 'Bug Report',
        'feature-request': 'New Feature Request',
        'general': 'General Feedback'
      ;
    } catch (error) {
      console.error(error);
    }
;

      const feedback = await this.communityDb.createFeedback({
        userId: interaction.user.id,
    guildId: interaction.guildId!,
        type: feedbackType as any,
    title: sampleTitles[feedbackType as keyof typeof sampleTitles] || 'Feedback')
        description: 'Feedback submitted via community panel (detailed feedback would be collected via modal)'});

      // Award points for providing feedback
      await this.communityDb.updateUserPoints(
        interaction.user.id, 
        interaction.guildId!, 
        10)
        'Provided community feedback'
      );
      await interaction.editReply({
        content: `✅ Thank you for your ${feedbackType}! Your feedback has been recorded and our team will review it soon. You earned 10 points!`)
        components: []});
      this.logger.log(`User ${interaction.user.id} submitted ${feedbackType} feedback and earned 10 points`)} catch (error) {
      this.logger.error('Failed to handle feedback submission:', error);
      await interaction.editReply({
        content: '❌ Failed to submit feedback. Please try again.'})}
  }

  private getEventTypeEmoji(type: string): string {const emojiMap: Record<string, string> = {
      'challenge': '🏆',
      'workshop': '🎓',
      'meetup': '🤝',
      'competition': '⚡'
    };
    return emojiMap[type] || '📅'}
;
  private getRankEmoji(rank: number): string {if (rank === 1) return '🥇';
    if (rank === 2) return '🥈';
    if (rank === 3) return '🥉'
    return `${rank}.`}
};