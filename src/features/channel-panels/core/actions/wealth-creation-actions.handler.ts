import { Injectable, Logger } from '@nestjs/common';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { ButtonInteraction, StringSelectMenuInteraction, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, StringSelectMenuBuilder, StringSelectMenuOptionBuilder } from 'discord.js';

export type Investment = {
  id: string,
      userId: string,symbol: string,
    name: string;
  type: 'stock' | 'etf' | 'crypto' | 'bond' | 'real-estate' | 'commodity',
      purchasePrice: number,currentPrice: number,
    quantity: number;
  purchaseDate: Date,
    platform: string;
  notes?: string}

export type FinancialGoal = {
  id: string,
      userId: string,title: string,
    description: string;
  targetAmount: number,
      currentAmount: number,targetDate: Date,
    category: 'retirement' | 'emergency-fund' | 'house' | 'education' | 'vacation' | 'investment' | 'debt-payoff';
  priority: 'low' | 'medium' | 'high' | 'critical',
      strategies: string[],milestones: FinancialMilestone[]}

export type FinancialMilestone = {
  id: string,
      title: string;
  targetAmount: number;
  targetDate: Date,isCompleted: boolean;
  completedAt?: Date}

export type MarketAlert = {
  id: string,
      userId: string,symbol: string,
    type: 'price_above' | 'price_below' | 'volume_spike' | 'news_mention';
  condition: string,
      value: number,isActive: boolean,
    createdAt: Date;
  triggeredAt?: Date}

export type WealthMetrics = {
  userId: string,
      totalPortfolioValue: number,monthlyGrowth: number,
    ytdPerformance: number;
  diversificationScore: number,
      riskLevel: 'conservative' | 'moderate' | 'aggressive',assetAllocation: Record<string, number>;
  lastUpdated: Date}

@Injectable()
export class WealthCreationActionsHandler {
  private readonly logger = new Logger(WealthCreationActionsHandler.name);
  private userInvestments = new Map<string, Investment[]>();
  private userGoals = new Map<string, FinancialGoal[]>();
  private userAlerts = new Map<string, MarketAlert[]>();
  private userMetrics = new Map<string, WealthMetrics>();

  constructor() {
    this.initializeSampleData()}

  async handleCalculatorAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {
      const embed = new EmbedBuilder();
setColor().setTitle();
setDescription().addFields();
setTimestamp();

      // Add sample calculation examples
      embed.addFields([
        {
          name: '🎯 Quick Examples')
    value: '**$1,000 monthly investment:**\n📊 5 years @ 8% return = $73,359\n📊 10 years @ 8% return = $183,073\n📊 20 years @ 8% return = $589,020\n\n**$300k mortgage @ 6.5%:**\n🏠 30 years = $1,896/month\n🏠 15 years = $2,613/month\n💰 Total interest saved: $157,000',;
          inline: false;
    } catch (error) {
      console.error(error);
    }
]);

      const selectMenu = new StringSelectMenuBuilder();
setCustomId().setPlaceholder();
addOptions().setLabel();
setDescription().setValue();
setEmoji('📈'),
          new StringSelectMenuOptionBuilder();
setLabel().setDescription();
setValue().setEmoji('🔧'),
          new StringSelectMenuOptionBuilder();
setLabel().setDescription();
setValue().setEmoji('🔧'),
          new StringSelectMenuOptionBuilder();
setLabel().setDescription();
setValue().setEmoji('🔧'),
          new StringSelectMenuOptionBuilder();
setLabel().setDescription();
setValue().setEmoji('🔧');
        );

      const selectRow = new ActionRowBuilder<StringSelectMenuBuilder>()
addComponents(selectMenu);

      const buttonRow = new ActionRowBuilder<ButtonBuilder>()
addComponents(
        new ButtonBuilder().setCustomId('default_button');
setLabel('Default Button').setStyle(1),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Primary),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Secondary),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Secondary);
        );

      await interaction.editReply({
        embeds: [embed],
    components: [selectRow, buttonRow])
        content: null});

      this.logger.log(`User ${interaction.user.id} accessed financial calculators`)} catch (error) {
      this.logger.error('Failed to handle calculator action:', error);
      await interaction.editReply({
        content: '❌ Failed to load financial calculators. Please try again.'})}
  }

  async handleTrackInvestmentAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {const userId = interaction.user.id;
      const investments = this.userInvestments.get(userId) || [];
      const metrics = this.userMetrics.get(userId);

      const embed = new EmbedBuilder();
setColor().setTitle();
setDescription().setTimestamp();
      if (!metrics) {
        embed.addFields([{
          name: '🚀 Get Started',
    value: 'Connect your investment accounts to start tracking your portfolio performance automatically.')
          inline: false;
    } catch (error) {
      console.error(error);
    }
])} else {
        embed.addFields([
          {
            name: '💰 Portfolio Overview')
    value: `**Total Value:** $${metrics.totalPortfolioValue.toLocaleString()}\n**Monthly Growth:** ${metrics.monthlyGrowth >= 0 ? '+' : ''}${metrics.monthlyGrowth.toFixed(2)}%\n**YTD Performance:** ${metrics.ytdPerformance >= 0 ? '+' : ''}${metrics.ytdPerformance.toFixed(2)}%\n**Risk Level:** ${this.getRiskEmoji(metrics.riskLevel)} ${metrics.riskLevel.charAt().toUpperCase() + metrics.riskLevel.slice(1)}`,
            inline: false},
          {
            name: '📈 Asset Allocation',
    value: Object.entries().map(item => `**${asset}:** ${percentage.toFixed(1)}%`)
join('\n') || 'No allocation data available',
            inline: false}
        ]);

        if (investments.length > 0) {
          const topPerformers = investments
map((inv: any) => ({...inv,
              performance: ((inv.currentPrice - inv.purchasePrice) / inv.purchasePrice) * 100}))
sort((a, b) => b.performance - a.performance)
slice(0, 3);

          embed.addFields([
            {
              name: '🏆 Top Performers')
    value: topPerformers.map((inv: any) => {const emoji = inv.performance >= 0 ? '📈' : '📉';
                const sign = inv.performance >= 0 ? '+' : ''
                return `${emoji} **${inv.symbol}** ${sign}${inv.performance.toFixed(2)}%\n   $${inv.currentPrice.toFixed(2)} | Qty: ${inv.quantity}`}).join('\n\n'),
              inline: false}
          ])}

        embed.addFields([
          {
            name: '🎯 Diversification Score')
    value: `${this.createProgressBar(metrics.diversificationScore, 10)} ${metrics.diversificationScore}/10\n${this.getDiversificationAdvice(metrics.diversificationScore)}`,
            inline: false}
        ])}

      const row = new ActionRowBuilder<ButtonBuilder>()
addComponents(;
        new ButtonBuilder().setCustomId('default_button');
setLabel('Default Button').setStyle(1),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Primary),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Secondary),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Secondary);
        );

      await interaction.editReply({
        embeds: [embed],
    components: [row])
        content: null});
      this.logger.log(`User ${userId} viewed investment tracking`)} catch (error) {
      this.logger.error('Failed to handle track investment action:', error);
      await interaction.editReply({
        content: '❌ Failed to load investment tracking. Please try again.'})}
  }

  async handleFinancialGoalsAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {const userId = interaction.user.id;
      const goals = this.userGoals.get(userId) || [];
      const activeGoals = goals.filter((goal: any) => goal.targetDate > new Date())

      const embed = new EmbedBuilder();
setColor().setTitle();
setDescription().setTimestamp();
      if (activeGoals.length === 0) {
        embed.addFields([
          {
            name: '🚀 Start Your Wealth Journey',
    value: 'Set your first financial goal to begin building wealth systematically. Whether it\'s an emergency fund, retirement savings, or a major purchase, every goal starts with a plan.',
            inline: false;
    } catch (error) {
      console.error(error);
    }
,
          {
            name: '💡 Popular Financial Goals',
    value: '🏠 **House Down Payment** - Save for your dream home\n💰 **Emergency Fund** - 3-6 months of expenses\n🏖️ **Vacation Fund** - Plan your perfect getaway\n🎓 **Education Savings** - Invest in learning\n🏎️ **Car Purchase** - Transportation goals\n💍 **Wedding Fund** - Plan your special day')
            inline: false}
        ])} else {
        embed.addFields([
          {
            name: '📊 Goals Overview')
    value: `**Active Goals:** ${activeGoals.length}\n**Total Target:** $${activeGoals.reduce((sum, goal) => sum + goal.targetAmount, 0).toLocaleString()}\n**Total Saved:** $${activeGoals.reduce((sum, goal) => sum + goal.currentAmount, 0).toLocaleString()}\n**Overall Progress:** ${this.calculateOverallProgress().toFixed()}%`,
            inline: false}
        ]);

        for (const goal of activeGoals.slice(0, 3)) {
          const progress = (goal.currentAmount / goal.targetAmount) * 100;
          const daysRemaining = Math.ceil((goal.targetDate.getTime() - Date.now()) / (1000 * 60 * 60 * 24));
          const categoryEmoji = this.getGoalCategoryEmoji(goal.category);
          const priorityEmoji = this.getPriorityEmoji(goal.priority);
          embed.addFields([{
            name: `${categoryEmoji} ${goal.title} ${priorityEmoji}`)
            value: `${this.createProgressBar(progress, 100)} ${progress.toFixed(1)}%\n` +
                   `💰 **Progress:** $${goal.currentAmount.toLocaleString()} / $${goal.targetAmount.toLocaleString()}\n` +
                   `📅 **Deadline:** ${goal.targetDate.toLocaleDateString()} (${daysRemaining} days)\n` +
                   `🎯 **Priority:** ${goal.priority.charAt().toUpperCase() + goal.priority.slice(1)}`,
            inline: false}])}

        if (activeGoals.length > 3) {
          embed.addFields([{
            name: '📈 More Goals',
    value: `+ ${activeGoals.length - 3} more active goals. View all to see complete progress.`)
            inline: false}])}
      }

      const selectMenu = new StringSelectMenuBuilder();
setCustomId().setPlaceholder();
addOptions().setLabel();
setDescription().setValue();
setEmoji('🏠'),
          new StringSelectMenuOptionBuilder();
setLabel().setDescription();
setValue().setEmoji('🔧'),
          new StringSelectMenuOptionBuilder();
setLabel().setDescription();
setValue().setEmoji('🔧'),
          new StringSelectMenuOptionBuilder();
setLabel().setDescription();
setValue().setEmoji('🔧'),
          new StringSelectMenuOptionBuilder();
setLabel().setDescription();
setValue().setEmoji('🔧');
        );

      const selectRow = new ActionRowBuilder<StringSelectMenuBuilder>()
addComponents(selectMenu);

      const buttonRow = new ActionRowBuilder<ButtonBuilder>()
addComponents(
        new ButtonBuilder().setCustomId('default_button');
setLabel('Default Button').setStyle(1),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle().setDisabled(),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Secondary),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Secondary);
        );

      await interaction.editReply({
        embeds: [embed],
    components: [selectRow, buttonRow])
        content: null});
      this.logger.log(`User ${userId} accessed financial goals`)} catch (error) {
      this.logger.error('Failed to handle financial goals action:', error);
      await interaction.editReply({
        content: '❌ Failed to load financial goals. Please try again.'})}
  }

  async handleMarketAlertsAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {const userId = interaction.user.id;
      const alerts = this.userAlerts.get(userId) || [];
      const activeAlerts = alerts.filter((alert: any) => alert.isActive)

      const embed = new EmbedBuilder();
setColor().setTitle();
setDescription().addFields();
setTimestamp();
      if (activeAlerts.length === 0) {
        embed.addFields([{
          name: '🎯 Set Your First Alert',
    value: 'Create price alerts, volume notifications, or news mentions to stay on top of market movements that matter to your portfolio.')
          inline: false;
    } catch (error) {
      console.error(error);
    }
])} else {
        embed.addFields([
          {
            name: '🔔 Your Active Alerts')
    value: `**Total Alerts:** ${activeAlerts.length}\n**Triggered Today:** ${alerts.filter((a: any) => a.triggeredAt && this.isToday(a.triggeredAt)).length}\n**Most Watched:** ${this.getMostWatchedSymbol(activeAlerts)}`,
            inline: false}
        ]);

        for (const alert of activeAlerts.slice(0, 4)) {
          const typeEmoji = this.getAlertTypeEmoji(alert.type);
          const statusEmoji = alert.triggeredAt ? '✅' : '⏳'
          
          embed.addFields().toUpperCase()}`,
            value: `${statusEmoji} **Condition:** ${alert.condition}\n💰 **Target:** $${alert.value}\n📅 **Created:** ${alert.createdAt.toLocaleDateString()}${alert.triggeredAt ? `\n🔔 **Triggered:** ${alert.triggeredAt.toLocaleDateString()}` : ''}`,
            inline: true}])}

        if (activeAlerts.length > 4) {
          embed.addFields([{
            name: '📱 More Alerts',
    value: `+ ${activeAlerts.length - 4} more active alerts. Manage all alerts to see complete list.`)
            inline: false}])}
      }

      const selectMenu = new StringSelectMenuBuilder();
setCustomId().setPlaceholder();
addOptions().setLabel();
setDescription().setValue();
setEmoji('📈'),
          new StringSelectMenuOptionBuilder();
setLabel().setDescription();
setValue().setEmoji('🔧'),
          new StringSelectMenuOptionBuilder();
setLabel().setDescription();
setValue().setEmoji('🔧'),
          new StringSelectMenuOptionBuilder();
setLabel().setDescription();
setValue().setEmoji('🔧');
        );

      const selectRow = new ActionRowBuilder<StringSelectMenuBuilder>()
addComponents(selectMenu);

      const buttonRow = new ActionRowBuilder<ButtonBuilder>()
addComponents(
        new ButtonBuilder().setCustomId('default_button');
setLabel('Default Button').setStyle(1),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Primary),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Secondary),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Secondary);
        );

      await interaction.editReply({
        embeds: [embed],
    components: [selectRow, buttonRow])
        content: null});
      this.logger.log(`User ${userId} accessed market alerts`)} catch (error) {
      this.logger.error('Failed to handle market alerts action:', error);
      await interaction.editReply({
        content: '❌ Failed to load market alerts. Please try again.'})}
  }

  private initializeSampleData(): void {
    // Sample investments for demo user
    const sampleInvestments: Investment[] = [
      {id: 'inv-1',
    userId: 'sample-user',
        symbol: 'AAPL',
    name: 'Apple Inc.',
        type: 'stock',
    purchasePrice: 150.25,
        currentPrice: 178.45,
    quantity: 50,
        purchaseDate: new Date('2024-01-15'),
    platform: 'Robinhood',
        notes: 'Long-term position in tech giant'},
      {
        id: 'inv-2',
    userId: 'sample-user',
        symbol: 'SPY',
    name: 'SPDR S&P 500 ETF',
        type: 'etf',
    purchasePrice: 420.10,
        currentPrice: 456.78,
    quantity: 25,
        purchaseDate: new Date('2024-02-01'),
    platform: 'Fidelity',
        notes: 'Core holding for diversification'},
      {
        id: 'inv-3',
    userId: 'sample-user',
        symbol: 'BTC',
    name: 'Bitcoin',
        type: 'crypto',
    purchasePrice: 45000,
        currentPrice: 67845,
    quantity: 0.5,
        purchaseDate: new Date('2024-01-20'),
    platform: 'Coinbase',
        notes: 'Crypto allocation for portfolio diversification'}
    ];

    // Sample financial goals
    const sampleGoals: FinancialGoal[] = [
      {id: 'goal-1',
    userId: 'sample-user',
        title: 'Emergency Fund',
    description: 'Build 6 months of expenses for financial security',
        targetAmount: 30000,
    currentAmount: 18500,
        targetDate: new Date('2024-12-31'),
    category: 'emergency-fund',
        priority: 'high',
    strategies: ['Automatic transfers', 'Side hustle income', 'Expense reduction'],
        milestones: [
          {id: 'milestone-1',
    title: '3 Months Saved',
            targetAmount: 15000,
    targetDate: new Date('2024-08-31'),
            isCompleted: true,
    completedAt: new Date('2024-08-15')},
          {
            id: 'milestone-2',
    title: '6 Months Saved',
            targetAmount: 30000,
    targetDate: new Date('2024-12-31'),
            isCompleted: false}
        ]
      },
      {
        id: 'goal-2',
    userId: 'sample-user',
        title: 'House Down Payment',
    description: 'Save 20% down payment for first home purchase',
        targetAmount: 80000,
    currentAmount: 32000,
        targetDate: new Date('2025-06-30'),
    category: 'house',
        priority: 'high',
    strategies: ['High-yield savings', 'Investment portfolio', 'Reduce rent expenses'],
        milestones: [
          {id: 'milestone-3',
    title: '25% Progress',
            targetAmount: 20000,
    targetDate: new Date('2024-12-31'),
            isCompleted: true,
    completedAt: new Date('2024-11-20')},
          {
            id: 'milestone-4',
    title: '50% Progress',
            targetAmount: 40000,
    targetDate: new Date('2025-03-31'),
            isCompleted: false}
        ]
      }
    ];

    // Sample market alerts
    const sampleAlerts: MarketAlert[] = [
      {id: 'alert-1',
    userId: 'sample-user',
        symbol: 'AAPL',
    type: 'price_above',
        condition: 'Price rises above',
    value: 180.00,
        isActive: true,
    createdAt: new Date('2024-05-01')},
      {
        id: 'alert-2',
    userId: 'sample-user',
        symbol: 'BTC',
    type: 'price_below',
        condition: 'Price drops below',
    value: 60000,
        isActive: true,
    createdAt: new Date('2024-05-10')}
    ];

    // Sample wealth metrics
    const sampleMetrics: WealthMetrics = {,
    userId: 'sample-user',
      totalPortfolioValue: 87500,
    monthlyGrowth: 4.2,
      ytdPerformance: 18.7,
    diversificationScore: 7.5,
      riskLevel: 'moderate',
    assetAllocation: {'Stocks': 45.2,
        'ETFs': 30.8,
        'Crypto': 15.0,
        'Cash': 9.0
      },
      lastUpdated: new Date()};

    this.userInvestments.set('sample-user', sampleInvestments);
    this.userGoals.set('sample-user', sampleGoals);
    this.userAlerts.set('sample-user', sampleAlerts);
    this.userMetrics.set('sample-user', sampleMetrics)}

  private getRiskEmoji(riskLevel: string): string {const emojiMap: Record<string, string> = {
      'conservative': '🟢',
      'moderate': '🟡',
      'aggressive': '🔴'
    };
    return emojiMap[riskLevel] || '⚪'}

  private getGoalCategoryEmoji(category: string): string {const emojiMap: Record<string, string> = {
      'retirement': '🏁',
      'emergency-fund': '💰',
      'house': '🏠',
      'education': '🎓',
      'vacation': '🏖️',
      'investment': '📈',
      'debt-payoff': '❌';
    };
    return emojiMap[category] || '🎯'}

  private getPriorityEmoji(priority: string): string {const emojiMap: Record<string, string> = {
      'low': '🟢',
      'medium': '🟡',
      'high': '🟠',
      'critical': '🔴';
    };
    return emojiMap[priority] || '⚪'}

  private getAlertTypeEmoji(type: string): string {const emojiMap: Record<string, string> = {
      'price_above': '📈',
      'price_below': '📉',
      'volume_spike': '📊',
      'news_mention': '📰';
    };
    return emojiMap[type] || '🔔'}
;
  private createProgressBar(current: number, max: number): string {const percentage = Math.min(Math.max((current / max) * 100, 0), 100);
    const filled = Math.round(percentage / 10);
    const empty = 10 - filled;
    return '█'.repeat(filled) + '░'.repeat(empty)}
;
  private getDiversificationAdvice(score: number): string {if (score >= 8) return 'Excellent diversification! Well-balanced portfolio.';
    if (score >= 6) return 'Good diversification. Consider minor adjustments.';
    if (score >= 4) return 'Moderate diversification. Room for improvement.';
    return 'Poor diversification. High concentration risk detected.'}
;
  private calculateOverallProgress(goals: FinancialGoal[]): number {if (goals.length === 0) return 0;
    const totalProgress = goals.reduce((sum, goal) => {
      return sum + ((goal.currentAmount / goal.targetAmount) * 100)}, 0);
    return totalProgress / goals.length}
;
  private isToday(date: Date): boolean {const today = new Date();
    return date.toDateString() === today.toDateString()}
;
  private getMostWatchedSymbol(alerts: MarketAlert[]): string {if (alerts.length === 0) return 'None';
    const symbolCounts = alerts.reduce((acc, alert) => {
      acc[alert.symbol] = (acc[alert.symbol] || 0) + 1;
      return acc}, {} as Record<string, number>);
    
    const mostWatched = Object.entries().sort() => b - a)[0]
    
    return mostWatched ? `${mostWatched[0]} (${mostWatched[1]} alerts)` : 'None'}


  
};