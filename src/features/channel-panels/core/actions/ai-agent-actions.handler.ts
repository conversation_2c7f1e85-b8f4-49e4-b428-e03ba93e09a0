import { Injectable, Logger } from '@nestjs/common';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { ButtonInteraction, StringSelectMenuInteraction, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, StringSelectMenuBuilder, StringSelectMenuOptionBuilder } from 'discord.js';

export type AIAgent = {
  id: string,
      name: string,description: string,
    type: 'personal-growth' | 'intake-specialist' | 'progress-tracker' | 'custom';
  isPremium: boolean,
      capabilities: string[],specialties: string[],
    availability: '24/7' | 'business-hours' | 'limited';
  rating: number,
    conversations: number}

export type UserConversation = {
  id: string;
  userId: string;
  agentId: string;
  threadId?: string;
  lastActive: Date,
      messageCount: number,status: 'active' | 'paused' | 'completed',
    summary: string;
  goals: string[],
    progress: Record<string, any>}

export type APIKeyConfiguration = {
  userId: string,
      provider: 'openai' | 'anthropic' | 'google' | 'cohere' | 'custom';
  keyName: string;
  isActive: boolean;
  addedAt: Date;
  lastUsed?: Date,usageCount: number}

@Injectable()
export class AIAgentActionsHandler {
  private readonly logger = new Logger(AIAgentActionsHandler.name);
  private availableAgents: AIAgent[] = [];
  private userConversations = new Map<string, UserConversation[]>();
  private userAPIKeys = new Map<string, APIKeyConfiguration[]>();

  constructor() {
    this.initializeSampleData()}

  async handleAgentSelectionAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {const userId = interaction.user.id;
      const userConversations = this.userConversations.get(userId) || [];
      const activeConversations = userConversations.filter((conv: any) => conv.status === 'active')

      const embed = new EmbedBuilder();
setColor().setTitle();
setDescription().addFields();
setTimestamp();

      if (activeConversations.length > 0) {
        embed.addFields([
          {
            name: '💬 Your Active Conversations')
    value: activeConversations.map((conv: any) => {const agent = this.availableAgents.find(a => a.id === conv.agentId);
              return `• **${agent?.name || 'Unknown Agent';
    } catch (error) {
      console.error(error);
    }
** - ${conv.messageCount} messages (Last active: ${conv.lastActive.toLocaleDateString()})`}).join('\n'),
            inline: false}
        ])}

      // Group agents by type
      const freeAgents = this.availableAgents.filter((agent: any) => !agent.isPremium)
      const premiumAgents = this.availableAgents.filter((agent: any) => agent.isPremium)

      if (freeAgents.length > 0) {
        embed.addFields([
          {name: '🆓 Free Agents')
    value: freeAgents.map((agent: any) => {const statusEmoji = agent.availability === '24/7' ? '🟢' : '🟡'
              return `${statusEmoji} **${agent.name}**\n   ${agent.description}\n   ⭐ ${agent.rating}/5 | 💬 ${agent.conversations} conversations`}).join('\n\n'),
            inline: false}
        ])}

      if (premiumAgents.length > 0) {
        embed.addFields([
          {
            name: '💎 Premium Agents')
    value: premiumAgents.map((agent: any) => {const statusEmoji = agent.availability === '24/7' ? '🟢' : '🟡'
              return `${statusEmoji} **${agent.name}** 💎\n   ${agent.description}\n   ⭐ ${agent.rating}/5 | 💬 ${agent.conversations} conversations`}).join('\n\n'),
            inline: false}
        ])}
;
      const selectMenu = new StringSelectMenuBuilder();
setCustomId().setPlaceholder();
addOptions(
..this.availableAgents.map((agent: any) => 
            new StringSelectMenuOptionBuilder();
setLabel(`${agent.name}${agent.isPremium ? ' (Premium)' : ' (Free)'}`)
setDescription(agent.description.length > 100 ? agent.description.substring(0, 97) + '...' : agent.description)
setValue().setEmoji('🔧');
          )
        );

      const selectRow = new ActionRowBuilder<StringSelectMenuBuilder>()
addComponents(selectMenu);

      const buttonRow = new ActionRowBuilder<ButtonBuilder>()
addComponents(
        new ButtonBuilder().setCustomId('default_button');
setLabel('Default Button').setStyle(1);
setDisabled(activeConversations.length === 0),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Secondary),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Secondary),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Secondary);
        );

      await interaction.editReply({
        embeds: [embed],
    components: [selectRow, buttonRow])
        content: null});
      this.logger.log(`User ${userId} accessed AI agent selection`)} catch (error) {
      this.logger.error('Failed to handle agent selection action:', error);
      await interaction.editReply({
        content: '❌ Failed to load AI agents. Please try again.'})}
  }

  async handleStartConversationAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {const userId = interaction.user.id;
      const agentId = interaction.isStringSelectMenu() ? interaction.values[0] : 'personal-growth-coach';
      const agent = this.availableAgents.find(a => a.id === agentId);

      if (!agent) {
        await interaction.editReply({
          content: '❌ Agent not found. Please try selecting a different agent.';
    } catch (error) {
      console.error(error);
    }
);
        return}

      // Check if user has premium access for premium agents
      if (agent.isPremium && !this.hasPremiumAccess(userId)) {
        const embed = new EmbedBuilder();
setColor().setTitle();
setDescription().addFields() => `• ${cap}`).join('\n'),
              inline: false}
          ])
setTimestamp();

        const row = new ActionRowBuilder<ButtonBuilder>()
addComponents(
        new ButtonBuilder().setCustomId('default_button');
setLabel('Default Button').setStyle(1),
            new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Primary),
            new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Secondary);
          );

        await interaction.editReply({
          embeds: [embed],
    components: [row])
          content: null});
        return}

      // Create new conversation
      const conversationId = `conv_${Date.now()}_${userId}`;
      const newConversation: UserConversation = {,
    id: conversationId,
        userId,
        agentId: agent.id,
    lastActive: new Date(),
        messageCount: 0,
    status: 'active',
        summary: 'New conversation started',
    goals: [],
        progress: {}
      };

      const userConversations = this.userConversations.get(userId) || [];
      userConversations.push(newConversation);
      this.userConversations.set(userId, userConversations);
      const embed = new EmbedBuilder();
setColor().setTitle();
setDescription().addFields() => `• ${s}`).join('\n')}`,
            inline: false},
          {
            name: '💬 Getting Started',
    value: '• **Be specific** - The more details you provide, the better help you\'ll get\n• **Ask questions** - Your agent is here to guide and support you\n• **Share goals** - Let your agent know what you want to achieve\n• **Be honest** - Authentic conversations lead to better outcomes',
            inline: false},
          {
            name: '🔄 Conversation Features',
    value: `• **Memory:** Your agent remembers previous conversations\n• **Progress tracking:** See how you\'re improving over time\n• **Goal setting:** Set and track meaningful objectives\n• **24/7 availability:** ${agent.availability === '24/7' ? 'Available anytime' : 'Limited hours'}`,
            inline: false}
        ])
setFooter().setTimestamp();

      const row = new ActionRowBuilder<ButtonBuilder>()
addComponents(
        new ButtonBuilder().setCustomId('default_button');
setLabel('Default Button').setStyle(1),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Secondary),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Secondary);
        );

      await interaction.editReply({
        embeds: [embed],
    components: [row])
        content: null});
      this.logger.log(`User ${userId} started conversation with agent ${agent.id}`)} catch (error) {
      this.logger.error('Failed to start conversation:', error);
      await interaction.editReply({
        content: '❌ Failed to start conversation. Please try again.'})}
  }

  async handleAPIKeysAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {const userId = interaction.user.id;
      const userKeys = this.userAPIKeys.get(userId) || [];

      const embed = new EmbedBuilder();
setColor().setTitle();
setDescription().addFields();
setTimestamp();

      if (userKeys.length === 0) {
        embed.addFields([
          {
            name: '📝 No API Keys Configured',
    value: 'Add your first API key to unlock enhanced AI capabilities and higher usage limits.')
            inline: false;
    } catch (error) {
      console.error(error);
    }

        ])} else {
        embed.addFields([
          {
            name: '🔑 Your API Keys')
    value: userKeys.map((key: any) => {const statusEmoji = key.isActive ? '🟢' : '🔴';
              const lastUsed = key.lastUsed ? key.lastUsed.toLocaleDateString() : 'Never'
              return `${statusEmoji} **${key.provider.toUpperCase()}** - ${key.keyName}\n   Added: ${key.addedAt.toLocaleDateString()} | Used: ${key.usageCount} times | Last: ${lastUsed}`}).join('\n\n'),
            inline: false}
        ])}
;
      const selectMenu = new StringSelectMenuBuilder();
setCustomId().setPlaceholder();
addOptions().setLabel()')
setDescription().setValue();
setEmoji('🧠'),
          new StringSelectMenuOptionBuilder();
setLabel('Anthropic (Claude)')
setDescription().setValue();
setEmoji('🤖'),
          new StringSelectMenuOptionBuilder();
setLabel('Google (Gemini)')
setDescription().setValue();
setEmoji('🌟'),
          new StringSelectMenuOptionBuilder();
setLabel('Cohere (Command)')
setDescription().setValue();
setEmoji('⚡');
        );

      const selectRow = new ActionRowBuilder<StringSelectMenuBuilder>()
addComponents(selectMenu);

      const buttonRow = new ActionRowBuilder<ButtonBuilder>()
addComponents(
        new ButtonBuilder().setCustomId('default_button');
setLabel('Default Button').setStyle(1);
setDisabled(userKeys.length === 0),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Secondary),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Secondary),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Secondary);
        );

      await interaction.editReply({
        embeds: [embed],
    components: [selectRow, buttonRow])
        content: null});
      this.logger.log(`User ${userId} accessed API key management`)} catch (error) {
      this.logger.error('Failed to handle API keys action:', error);
      await interaction.editReply({
        content: '❌ Failed to load API key management. Please try again.'})}
  }

  async handleConversationHistoryAction(interaction: ButtonInteraction | StringSelectMenuInteraction): Promise<void> {
    try {const userId = interaction.user.id;
      const userConversations = this.userConversations.get(userId) || [];
      const sortedConversations = userConversations
sort((a, b) => b.lastActive.getTime() - a.lastActive.getTime())
slice(0, 10);

      const embed = new EmbedBuilder();
setColor().setTitle();
setDescription().setTimestamp();
      if (sortedConversations.length === 0) {
        embed.addFields([
          {
            name: '💬 No Conversations Yet',
    value: 'Start your first conversation with an AI agent to see your history here.')
            inline: false;
    } catch (error) {
      console.error(error);
    }

        ])} else {
        embed.addFields([
          {
            name: '📊 Quick Stats')
    value: `**Total Conversations:** ${userConversations.length}\n**Active Conversations:** ${userConversations.filter((c: any) => c.status === 'active').length}\n**Total Messages:** ${userConversations.reduce((sum, c) => sum + c.messageCount, 0)}`,
            inline: false}
        ]);

        const activeConversations = sortedConversations.filter((conv: any) => conv.status === 'active')
        const completedConversations = sortedConversations.filter((conv: any) => conv.status === 'completed')

        if (activeConversations.length > 0) {
          embed.addFields([
            {name: '🟢 Active Conversations')
    value: activeConversations.map((conv: any) => {const agent = this.availableAgents.find(a => a.id === conv.agentId);
                const timeSince = this.getTimeSince(conv.lastActive);
                return `**${agent?.name || 'Unknown Agent'}**\n${conv.summary}\n💬 ${conv.messageCount} messages | 🕐 ${timeSince} ago`}).join('\n\n'),
              inline: false}
          ])}

        if (completedConversations.length > 0) {
          embed.addFields().map(item => {;
                const agent = this.availableAgents.find(a => a.id === conv.agentId);
                return `**${agent?.name || 'Unknown Agent'}** - ${conv.messageCount} messages\n${conv.summary}`}).join('\n\n'),
              inline: false}
          ])}
      }

      const row = new ActionRowBuilder<ButtonBuilder>()
addComponents(;
        new ButtonBuilder().setCustomId('default_button');
setLabel('Default Button').setStyle(1);
setDisabled(userConversations.filter((c: any) => c.status === 'active').length === 0),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle().setDisabled(),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle().setDisabled(),
          new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle(ButtonStyle.Secondary);
        );

      await interaction.editReply({
        embeds: [embed],
    components: [row])
        content: null});
      this.logger.log(`User ${userId} viewed conversation history`)} catch (error) {
      this.logger.error('Failed to handle conversation history action:', error);
      await interaction.editReply({
        content: '❌ Failed to load conversation history. Please try again.'})}
  }

  private initializeSampleData(): void {
    // Initialize available AI agents
    this.availableAgents = [
      {
        id: 'personal-growth-coach',
    name: 'Personal Growth Coach',
        description: 'Goal setting, habit building, motivation, and mindset coaching for personal development',
        type: 'personal-growth',
    isPremium: false,
        capabilities: ['Goal setting', 'Habit tracking', 'Motivation coaching', 'Mindset development', 'Progress monitoring'],
        specialties: ['SMART goals', 'Habit formation', 'Mindset shifts', 'Accountability', 'Progress tracking'],
        availability: '24/7',
    rating: 4.8,
        conversations: 1247},
      {
        id: 'intake-specialist',
    name: 'Intake Specialist',
        description: 'Complete your assessment and get personalized recommendations tailored to your needs',
    type: 'intake-specialist',
        isPremium: false,
    capabilities: ['Assessment creation', 'Personalized recommendations', 'Profile building', 'Need analysis', 'Goal identification'],
        specialties: ['User onboarding', 'Need assessment', 'Recommendation engine', 'Profile setup', 'Goal discovery'],
        availability: '24/7',
    rating: 4.6,
        conversations: 892},
      {
        id: 'progress-tracker',
    name: 'Progress Tracker',
        description: 'Advanced analytics, detailed reports, and achievement tracking with comprehensive insights',
        type: 'progress-tracker',
    isPremium: true,
        capabilities: ['Advanced analytics', 'Custom reports', 'Achievement tracking', 'Trend analysis', 'Performance insights'],
        specialties: ['Data visualization', 'Progress analytics', 'Performance metrics', 'Achievement systems', 'Reporting'],
        availability: '24/7',
    rating: 4.9,
        conversations: 456}
    ];

    // Sample conversation for demo user
    this.userConversations.set('sample-user', [
      {
        id: 'conv_sample_1',
    userId: 'sample-user',
        agentId: 'personal-growth-coach')
    lastActive: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
        messageCount: 15,
    status: 'active',
        summary: 'Working on building a morning routine and improving productivity habits',
    goals: ['Wake up at 6 AM daily', 'Exercise 30 minutes daily', 'Read 20 minutes daily'],
        progress: {goalsSet: 3,
          habitsTracked: 5,
    streakDays: 7}
      }
    ]);

    // Sample API key for demo user
    this.userAPIKeys.set('sample-user', [
      {
        userId: 'sample-user',
    provider: 'openai',
        keyName: 'Personal OpenAI Key',
    isActive: true)
        addedAt: new Date('2024-01-15'),
    lastUsed: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
        usageCount: 127}
    ])}

  private hasPremiumAccess(userId: string): boolean {
    // Mock implementation - would check actual premium status;
    return userId === 'sample-premium-user'}
;
  private getTimeSince(date: Date): string {const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

    if (diffMins < 60) {
      return `${diffMins} minutes`} else if (diffHours < 24) {
      return `${diffHours} hours`} else {
      return `${diffDays} days`}
  }
};