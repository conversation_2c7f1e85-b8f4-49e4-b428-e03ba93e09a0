import { Injectable, Logger } from '@nestjs/common';
import { DatabaseService } from '@/core/data';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { CacheService } from '@/core/database';
import { WhopService } from '../../../core/services/whop.service';
import { CommunityTier, UserMembership, TierLevel, TierFeatures, TierLimits } from '../entities/tier-system.entity';

export type TierConfiguration = {
  level: TierLevel,
      name: string,description: string,
    monthlyPrice: number;
  yearlyPrice?: number;
  features: TierFeatures,
      limits: TierLimits,discordRoleIds: string[],
    accessibleChannels: string[]}

@Injectable()
export class TierManagementService {
  private readonly logger = new Logger(TierManagementService.name);

  constructor(private readonly databaseService: DatabaseService,
    private readonly redisDatabaseService: CacheService,
    private readonly whopService: WhopService)
  ) {}

  async initializeDefaultTiers(guildId: string): Promise<void> {
    const defaultTiers: TierConfiguration[] = [
      {level: 'free',
    name: 'Free Community',
        description: 'Basic access to community resources and introductory AI guidance',
    monthlyPrice: 0,
      features: {,
      aiAgentAccess: true,
          aiToolRecommendations: false,
    productivityAutomation: false,
          personalAiAssistant: false,
    entrepreneurshipGuidance: false,
          businessStrategyAccess: false,
    moneyMakingResources: false,
          investmentAdvice: false,
    devRequestsPerMonth: 1,
          priorityMatching: false,
    projectTrackingAccess: false,
          escrowPayments: false,
    developerNetworkAccess: false,
          escrowProtection: false,
    directDeveloperContact: false,
          premiumSupport: false,
    exclusiveContent: false,
          customIntegrations: false,
    mentalHealthSupport: false,
          personalCoaching: false,
    goalTrackingSystem: false,
          communityEvents: true,
    oneOnOneConsultation: false,
          customSolutions: false,
    whiteGloveService: false,
          dedicatedSupport: false,
    networkingConnections: true,
        },
        limits: {monthlyDevRequests: 1,
          aiAgentQueries: 10,
    personalCoachingSessions: 0,
          projectTrackingSlots: 1,
    maxSimultaneousProjects: 1,
          maxTeamMembers: 1,
    storageLimit: 1,
          apiCallsPerMonth: 100,
    communityEventsPerMonth: 2,
          networkingConnections: 5,
        },
        discordRoleIds: [],
    accessibleChannels: ['general-chat', 'introductions', 'ai-basics'],
      },
      {
        level: 'ai_explorer',
    name: 'AI Explorer',
        description: 'Enhanced AI mastery tools and productivity automation guidance',
    monthlyPrice: 29.99,
        yearlyPrice: 299.99,
      features: {,
      aiAgentAccess: true,
    aiToolRecommendations: true,
          productivityAutomation: true,
    personalAiAssistant: true,
          entrepreneurshipGuidance: false,
    businessStrategyAccess: false,
          moneyMakingResources: true,
    investmentAdvice: false,
          devRequestsPerMonth: 2,
    priorityMatching: false,
          projectTrackingAccess: true,
    escrowPayments: false,
          developerNetworkAccess: false,
    escrowProtection: false,
          directDeveloperContact: false,
    premiumSupport: false,
          exclusiveContent: false,
    customIntegrations: false,
          mentalHealthSupport: true,
    personalCoaching: false,
          goalTrackingSystem: true,
    communityEvents: true,
          oneOnOneConsultation: false,
    customSolutions: false,
          whiteGloveService: false,
    dedicatedSupport: false,
          networkingConnections: true,
        },
        limits: {monthlyDevRequests: 2,
          aiAgentQueries: 100,
    personalCoachingSessions: 0,
          projectTrackingSlots: 3,
    maxSimultaneousProjects: 2,
          maxTeamMembers: 3,
    storageLimit: 5,
          apiCallsPerMonth: 500,
    communityEventsPerMonth: 4,
          networkingConnections: 15,
        },
        discordRoleIds: [],
    accessibleChannels: ['ai-mastery', 'productivity-tools', 'automation-tips'],
      },
      {
        level: 'wealth_builder',
    name: 'Wealth Builder',
        description: 'Comprehensive wealth creation strategies and business development resources',
    monthlyPrice: 49.99,
        yearlyPrice: 499.99,
      features: {,
      aiAgentAccess: true,
    aiToolRecommendations: true,
          productivityAutomation: true,
    personalAiAssistant: true,
          entrepreneurshipGuidance: true,
    businessStrategyAccess: true,
          moneyMakingResources: true,
    investmentAdvice: true,
          devRequestsPerMonth: 3,
    priorityMatching: true,
          projectTrackingAccess: true,
    escrowPayments: true,
          developerNetworkAccess: false,
    escrowProtection: true,
          directDeveloperContact: true,
    premiumSupport: false,
          exclusiveContent: false,
    customIntegrations: false,
          mentalHealthSupport: true,
    personalCoaching: true,
          goalTrackingSystem: true,
    communityEvents: true,
          oneOnOneConsultation: false,
    customSolutions: false,
          whiteGloveService: false,
    dedicatedSupport: false,
          networkingConnections: true,
        },
        limits: {monthlyDevRequests: 3,
          aiAgentQueries: 250,
    personalCoachingSessions: 2,
          projectTrackingSlots: 5,
    maxSimultaneousProjects: 3,
          maxTeamMembers: 5,
    storageLimit: 10,
          apiCallsPerMonth: 1000,
    communityEventsPerMonth: 6,
          networkingConnections: 30,
        },
        discordRoleIds: [],
    accessibleChannels: ['wealth-strategies', 'business-development', 'investment-talks'],
      },
      {
        level: 'dev_premium',
    name: 'Dev Premium',
        description: 'Full access to developer network and premium project matching',
    monthlyPrice: 99.99,
        yearlyPrice: 999.99,
      features: {,
      aiAgentAccess: true,
    aiToolRecommendations: true,
          productivityAutomation: true,
    personalAiAssistant: true,
          entrepreneurshipGuidance: true,
    businessStrategyAccess: true,
          moneyMakingResources: true,
    investmentAdvice: true,
          devRequestsPerMonth: 10,
    priorityMatching: true,
          projectTrackingAccess: true,
    escrowPayments: true,
          developerNetworkAccess: true,
    escrowProtection: true,
          directDeveloperContact: true,
    premiumSupport: true,
          exclusiveContent: true,
    customIntegrations: false,
          mentalHealthSupport: true,
    personalCoaching: true,
          goalTrackingSystem: true,
    communityEvents: true,
          oneOnOneConsultation: true,
    customSolutions: false,
          whiteGloveService: false,
    dedicatedSupport: true,
          networkingConnections: true,
        },
        limits: {monthlyDevRequests: 10,
          aiAgentQueries: 500,
    personalCoachingSessions: 4,
          projectTrackingSlots: 15,
    maxSimultaneousProjects: 10,
          maxTeamMembers: 15,
    storageLimit: 50,
          apiCallsPerMonth: 5000,
    communityEventsPerMonth: 10,
          networkingConnections: 100,
        },
        discordRoleIds: [],
    accessibleChannels: ['dev-premium', 'priority-matching', 'exclusive-networking'],
      },
      {
        level: 'enterprise',
    name: 'Enterprise',
        description: 'White-glove service with custom solutions and dedicated support',
    monthlyPrice: 299.99,
        yearlyPrice: 2999.99,
      features: {,
      aiAgentAccess: true,
    aiToolRecommendations: true,
          productivityAutomation: true,
    personalAiAssistant: true,
          entrepreneurshipGuidance: true,
    businessStrategyAccess: true,
          moneyMakingResources: true,
    investmentAdvice: true,
          devRequestsPerMonth: -1, // Unlimited
          priorityMatching: true,
    projectTrackingAccess: true,
          escrowPayments: true,
    developerNetworkAccess: true,
          escrowProtection: true,
    directDeveloperContact: true,
          premiumSupport: true,
    exclusiveContent: true,
          customIntegrations: true,
    mentalHealthSupport: true,
          personalCoaching: true,
    goalTrackingSystem: true,
          communityEvents: true,
    oneOnOneConsultation: true,
          customSolutions: true,
    whiteGloveService: true,
          dedicatedSupport: true,
    networkingConnections: true,
        },
        limits: {monthlyDevRequests: -1, // Unlimited
          aiAgentQueries: -1, // Unlimited
          personalCoachingSessions: -1, // Unlimited
          projectTrackingSlots: -1,
    maxSimultaneousProjects: -1,
          maxTeamMembers: -1,
    storageLimit: -1,
          apiCallsPerMonth: -1,
    communityEventsPerMonth: -1, // Unlimited
          networkingConnections: -1, // Unlimited
        },
        discordRoleIds: [],
    accessibleChannels: ['enterprise-lounge', 'custom-solutions', 'vip-support'],
      },
    ];

    for (const tierConfig of defaultTiers) {
      await this.createOrUpdateTier(guildId, tierConfig)}

    this.logger.log(`Initialized ${defaultTiers.length} default tiers for guild ${guildId}`)}

  async createOrUpdateTier(guildId: string, config: TierConfiguration): Promise<CommunityTier> {
    // This would use proper database operations in a real implementation
    // For now, creating a mock tier object
    const tier: CommunityTier = {,
    id: `tier_${config.level}_${guildId}`,
      guildId,
      level: config.level,
    name: config.name,
      description: config.description,
    monthlyPrice: config.monthlyPrice,
      yearlyPrice: config.yearlyPrice || 0,
    features: config.features,
      limits: config.limits,
    discordRoleIds: config.discordRoleIds,
      accessibleChannels: config.accessibleChannels,
    restrictedChannels: [],
      isActive: true,
    sortOrder: this.getTierSortOrder(config.level),
      createdAt: new Date(),
    updatedAt: new Date(),
    }

    this.logger.log(`Created/updated tier: ${config.name} for guild ${guildId}`);
    return tier}

  async getUserMembership(userId: string, guildId: string): Promise<UserMembership | null> {
    try {// In a real implementation, this would query the database
      // For now, return a mock membership based on Whop integration;
      ;
      let tierLevel: TierLevel = 'free'
      let whopSubscriptionId: string | undefined

      try {
        // Try to get tier from Whop service;
        const whopTier = await this.whopService.getUserTier(userId);
        tierLevel = this.mapWhopTierToLocal(whopTier);
        whopSubscriptionId = await this.whopService.getUserSubscriptionId(userId) || undefined;
    } catch (error) {
      console.error(error);
    }
 catch (error) {
        this.logger.warn(`Could not get Whop tier for user ${userId}, defaulting to free tier`)}

      const membership: UserMembership = {,
    id: `membership_${userId}_${guildId}`,
        userId,
        guildId,
        tierId: `tier_${tierLevel}_${guildId}`,
        whopSubscriptionId,
        subscriptionStartDate: new Date(),
    subscriptionEndDate: whopSubscriptionId ? new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) : undefined,
        status: 'active',
      usageStats: {,
      monthlyDevRequests: 0,
    aiAgentQueries: 0,
          personalCoachingSessions: 0,
    lastResetDate: new Date().toISOString(),
        },
        createdAt: new Date(),
    updatedAt: new Date(),
      }

      return membership} catch (error) {;
      this.logger.error(`Failed to get user membership for ${userId}:`, error);
      return null}
  }

  async updateUserUsage(userId: string, guildId: string, usageType: keyof UserMembership['usageStats'], increment: number = 1): Promise<boolean> {;
    try {const membership = await this.getUserMembership(userId, guildId);
      if (!membership) return false;

      // Check if usage is within limits
      const tier = await this.getTierByLevel(guildId, this.extractTierLevelFromId(membership.tierId));
      if (!tier) return false;

      const currentUsage = membership.usageStats[usageType] as number;
      const limit = tier.limits[usageType as keyof TierLimits] as number

      if (limit !== -1 && currentUsage + increment > limit) {
        this.logger.warn(`User ${userId;
    } catch (error) {
      console.error(error);
    }
 would exceed ${usageType} limit: ${currentUsage + increment} > ${limit}`);
        return false}

      // In real implementation, update database
      (membership.usageStats as any)[usageType] = currentUsage + increment
      ;
      this.logger.log(`Updated ${usageType} usage for user ${userId}: ${currentUsage} -> ${currentUsage + increment}`);
      return true} catch (error) {;
      this.logger.error(`Failed to update usage for user ${userId}:`, error);
      return false}
  }

  async getUserTierFeatures(userId: string, guildId: string): Promise<TierFeatures | null> {;
    try {const membership = await this.getUserMembership(userId, guildId);
      if (!membership) return null;

      const tierLevel = this.extractTierLevelFromId(membership.tierId);
      const tier = await this.getTierByLevel(guildId, tierLevel);
      return tier?.features || null;
    } catch (error) {
      console.error(error);
    }
 catch (error) {;
      this.logger.error(`Failed to get user tier features for ${userId}:`, error);
      return null}
  }

  async canUserAccessFeature(userId: string, guildId: string, feature: keyof TierFeatures): Promise<boolean> {;
    try {const features = await this.getUserTierFeatures(userId, guildId);
      return features?.[feature] === true || false;
    } catch (error) {
      console.error(error);
    }
 catch (error) {;
      this.logger.error(`Failed to check feature access for user ${userId}:`, error);
      return false}
  }

  async syncUserWithWhop(userId: string, guildId: string): Promise<void> {
    try {;
      // Get current Whop subscription status;
      const whopTier = await this.whopService.getUserTier(userId);
      const subscriptionId = await this.whopService.getUserSubscriptionId(userId);
      
      // Update local membership record
      const localTier = this.mapWhopTierToLocal(whopTier);
      this.logger.log(`Synced user ${userId;
    } catch (error) {
      console.error(error);
    }
 with Whop: ${whopTier} -> ${localTier}`);
      // In real implementation, update database record
      // await this.updateUserMembership(userId, guildId, { tier: localTier, whopSubscriptionId: subscriptionId })} catch (error) {
      this.logger.error(`Failed to sync user ${userId} with Whop:`, error);
      throw error}
  }

  private async getTierByLevel(guildId: string, level: TierLevel): Promise<CommunityTier | null> {// Mock implementation - in real app, query database
    const mockTier: CommunityTier = {,
    id: `tier_${level}_${guildId}`,
      guildId,
      level,
      name: this.getTierName(level),
    description: `${level} tier description`,
      monthlyPrice: this.getTierPrice(level),
    features: this.getDefaultFeatures(level),
      limits: this.getDefaultLimits(level),
    discordRoleIds: [],
      accessibleChannels: [],
    restrictedChannels: [],
      isActive: true,
    sortOrder: this.getTierSortOrder(level),
      createdAt: new Date(),
    updatedAt: new Date(),;
    };

    return mockTier}

  private mapWhopTierToLocal(whopTier: string): TierLevel {const tierMap: Record<string, TierLevel> = {
      'basic': 'free',
      'premium': 'ai_explorer',
      'enterprise': 'enterprise',
      'ai_mastery': 'ai_explorer',
      'wealth_creation': 'wealth_builder',
      'dev_premium': 'dev_premium',;
    };

    return tierMap[whopTier.toLowerCase()] || 'free'}
;
  private extractTierLevelFromId(tierId: string): TierLevel {const parts = tierId.split('_');
    return (parts[1] as TierLevel) || 'free'}
;
  private getTierSortOrder(level: TierLevel): number {const order = { free: 1, ai_explorer: 2, wealth_builder: 3, dev_premium: 4, enterprise: 5 };
    return order[level] || 1}

  private getTierName(level: TierLevel): string {
    const names = {free: 'Free Community',
    ai_explorer: 'AI Explorer',
      wealth_builder: 'Wealth Builder',
    dev_premium: 'Dev Premium',
      enterprise: 'Enterprise',;
    };
    return names[level]}
;
  private getTierPrice(level: TierLevel): number {const prices = { free: 0, ai_explorer: 29.99, wealth_builder: 49.99, dev_premium: 99.99, enterprise: 299.99 };
    return prices[level]}

  private getDefaultFeatures(level: TierLevel): TierFeatures {;
    // Return appropriate features based on tier level;
    const baseFeatures: TierFeatures = {,
    aiAgentAccess: false,
      aiToolRecommendations: false,
    productivityAutomation: false,
      personalAiAssistant: false,
    entrepreneurshipGuidance: false,
      businessStrategyAccess: false,
    moneyMakingResources: false,
      investmentAdvice: false,
    devRequestsPerMonth: 0,
      priorityMatching: false,
    escrowProtection: false,
      escrowPayments: false,
    directDeveloperContact: false,
      developerNetworkAccess: false,
    projectTrackingAccess: false,
      premiumSupport: false,
    exclusiveContent: false,
      customIntegrations: false,
    mentalHealthSupport: false,
      personalCoaching: false,
    goalTrackingSystem: false,
      communityEvents: false,
    networkingConnections: false,
      oneOnOneConsultation: false,
    customSolutions: false,
      whiteGloveService: false,
    dedicatedSupport: false,
    };

    // Progressively enable features based on tier
    if (level === 'free') {
      baseFeatures.aiAgentAccess = true;
      baseFeatures.communityEvents = true;
      baseFeatures.devRequestsPerMonth = 1} else if (level === 'ai_explorer') {
      Object.assign(baseFeatures, {
        aiAgentAccess: true,
    aiToolRecommendations: true,
        productivityAutomation: true,
    personalAiAssistant: true,
        moneyMakingResources: true,
    projectTrackingAccess: true,
        mentalHealthSupport: true,
    goalTrackingSystem: true,
        communityEvents: true,
    devRequestsPerMonth: 2)
      })}
    // Add more tier-specific features...

    return baseFeatures}

  private getDefaultLimits(level: TierLevel): TierLimits {const limitsMap: Record<TierLevel, TierLimits> = {
      free: { ,
    monthlyDevRequests: 1, 
        aiAgentQueries: 10,
    personalCoachingSessions: 0, 
        projectTrackingSlots: 1,
    maxSimultaneousProjects: 1,
        maxTeamMembers: 1,
    storageLimit: 1,
        apiCallsPerMonth: 100,
    communityEventsPerMonth: 2, 
        networkingConnections: 5 },
      ai_explorer: { ,
    monthlyDevRequests: 2, 
        aiAgentQueries: 100,
    personalCoachingSessions: 0, 
        projectTrackingSlots: 3,
    maxSimultaneousProjects: 2,
        maxTeamMembers: 3,
    storageLimit: 5,
        apiCallsPerMonth: 500,
    communityEventsPerMonth: 4, 
        networkingConnections: 15 },
      wealth_builder: { ,
    monthlyDevRequests: 3, 
        aiAgentQueries: 250,
    personalCoachingSessions: 2, 
        projectTrackingSlots: 5,
    maxSimultaneousProjects: 3,
        maxTeamMembers: 5,
    storageLimit: 10,
        apiCallsPerMonth: 1000,
    communityEventsPerMonth: 6, 
        networkingConnections: 30 },
      dev_premium: { ,
    monthlyDevRequests: 10, 
        aiAgentQueries: 500,
    personalCoachingSessions: 4, 
        projectTrackingSlots: 15,
    maxSimultaneousProjects: 10,
        maxTeamMembers: 15,
    storageLimit: 50,
        apiCallsPerMonth: 5000,
    communityEventsPerMonth: 10, 
        networkingConnections: 100 },
      enterprise: { ,
    monthlyDevRequests: -1, 
        aiAgentQueries: -1,
    personalCoachingSessions: -1, 
        projectTrackingSlots: -1,
    maxSimultaneousProjects: -1,
        maxTeamMembers: -1,
    storageLimit: -1,
        apiCallsPerMonth: -1,
    communityEventsPerMonth: -1, 
        networkingConnections: -1 },;
    };

    return limitsMap[level]}
}


  private getDefaultLimits(tier: any): any { return {}}
;