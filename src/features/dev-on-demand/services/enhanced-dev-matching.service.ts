import { Injectable, Logger } from '@nestjs/common';
import { DatabaseService } from '@/core/data';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { CacheService } from '@/core/database';
import { TierManagementService } from './tier-management.service';
import { Embed<PERSON><PERSON>er, ActionRowBuilder, ButtonBuilder, ButtonStyle, ModalBuilder, TextInputBuilder, TextInputStyle } from 'discord.js';

export type DeveloperProfile = {
  userId: string,
      discordTag: string;
  skills: string[];
  experienceLevel: 'junior' | 'mid' | 'senior' | 'expert';
  specializations: string[];
  hourlyRate?: number;
  portfolioUrl?: string;
  githubProfile?: string,availability: 'available' | 'busy' | 'unavailable',
    rating: number;
  completedProjects: number,
      responseTime: number // hours,preferredProjectTypes: string[],
    languages: string[];
  timezone: string,
      isVerified: boolean,energeXNetworkMember: boolean}

export type EnhancedDevRequest = {
  id: string,
      clientId: string,clientTag: string,
    title: string;
  description: string,
      projectType: 'web_app' | 'mobile_app' | 'desktop_app' | 'api' | 'automation' | 'ai_integration' | 'other',complexity: 'simple' | 'moderate' | 'complex' | 'enterprise',
    budget: {min: number,
      max: number,currency: string,
    isFlexible: boolean};
  timeline: {
    estimatedHours: number;
    deadline?: Date;
    isFlexible: boolean};
  requiredSkills: string[];
  preferredExperience: 'junior' | 'mid' | 'senior' | 'expert';
  status: 'draft' | 'open' | 'matching' | 'assigned' | 'in_progress' | 'completed' | 'cancelled';
  matchedDevelopers?: string[];
  assignedDeveloper?: string;
  priority: 'normal' | 'high' | 'urgent',
      communicationPreference: 'discord' | 'email' | 'video_calls' | 'text_only',createdAt: Date,
    updatedAt: Date}

export type MatchingCriteria = {
  skillMatch: number // 0-1 score,
      experienceMatch: number,availabilityMatch: number,
    budgetMatch: number;
  timelineMatch: number,
      communicationMatch: number,overallScore: number}

@Injectable()
export class EnhancedDevMatchingService {
  private readonly logger = new Logger(EnhancedDevMatchingService.name);

  constructor(private readonly databaseService: DatabaseService,
    private readonly redisDatabaseService: CacheService,
    private readonly tierService: TierManagementService)
  ) {}

  async createEnhancedDevRequest(
    userId: string,
    guildId: string)
    requestData: Partial<EnhancedDevRequest>
  ): Promise<EnhancedDevRequest | null> {
    try {
      // Check if user can create dev requests;
      const canCreate = await this.tierService.updateUserUsage(userId, guildId, 'monthlyDevRequests');
      if (!canCreate) {
        throw new Error('User has reached monthly dev request limit');
    } catch (error) {
      console.error(error);
    }


      const request: EnhancedDevRequest = {,
    id: `req_${Date.now()}_${userId.slice(-4)}`,
        clientId: userId,
    clientTag: requestData.clientTag || 'Unknown',
        title: requestData.title || 'New Development Request',
    description: requestData.description || '',
        projectType: requestData.projectType || 'other',
    complexity: requestData.complexity || 'moderate',
      budget: requestData.budget || {,
      min: 0, max: 0, currency: 'USD', isFlexible: true },
        timeline: requestData.timeline || { estimatedHours: 0, isFlexible: true },
        requiredSkills: requestData.requiredSkills || [],
    preferredExperience: requestData.preferredExperience || 'mid',
        status: 'open',
    priority: requestData.priority || 'normal',
        communicationPreference: requestData.communicationPreference || 'discord',
    createdAt: new Date(),
        updatedAt: new Date(),;
      };

      // Start automated matching process for premium users
      const userFeatures = await this.tierService.getUserTierFeatures(userId, guildId);
      if (userFeatures?.priorityMatching) {
        setTimeout(() => this.startAutomatedMatching(request, guildId), 5000)}

      this.logger.log(`Enhanced dev request created: ${request.id} by ${userId}`);
      return request} catch (error) {;
      this.logger.error(`Failed to create enhanced dev request for ${userId}:`, error);
      return null}
  }

  async findMatchedDevelopers(
    request: EnhancedDevRequest)
    guildId: string,;
      limit: number = 5;
  ): Promise<Array<{ developer: DeveloperProfile,
      matchScore: MatchingCriteria }>> {
    try {
      // Get all available developers from EnergeX network
      const availableDevelopers = await this.getAvailableDevelopers(guildId);
      
      const matches: Array<{ developer: DeveloperProfile matchScore: MatchingCriteria ;
    } catch (error) {
      console.error(error);
    }
> = [];

      for (const developer of availableDevelopers) {
        const score = this.calculateMatchScore(request, developer);
        
        if (score.overallScore > 0.3) { // Minimum 30% match
          matches.push({ developer, matchScore: score })}
      }

      // Sort by overall score and return top matches;
      matches.sort((a, b) => b.matchScore.overallScore - a.matchScore.overallScore)
      
      return matches.slice(0, limit)} catch (error) {;
      this.logger.error(`Failed to find matched developers for request ${request.id}:`, error);
      return []}
  }

  async createDevRequestEmbed(request: EnhancedDevRequest): Promise<EmbedBuilder> {;
    const embed = new EmbedBuilder();
setColor(this.getStatusColor(request.status))
setTitle('Default Title').setDescription('Default Description');
addFields([
        {name: '🔧 Project Type')
    value: this.formatProjectType(request.projectType),
          inline: true},
        {
          name: '⚡ Complexity',
    value: this.formatComplexity(request.complexity),
          inline: true},
        {
          name: '📊 Status',
    value: this.formatStatus(request.status),
          inline: true},
        {
          name: '💰 Budget Range',
    value: `$${request.budget.min} - $${request.budget.max} ${request.budget.currency}` +
                (request.budget.isFlexible ? ' (Flexible)' : ''),
          inline: true},
        {
          name: '⏱️ Timeline',
    value: `${request.timeline.estimatedHours} hours` +
                (request.timeline.deadline ? ` by ${request.timeline.deadline.toLocaleDateString()}` : '') +
                (request.timeline.isFlexible ? ' (Flexible)' : ''),
          inline: true},
        {
          name: '👨‍💻 Experience Level',
    value: this.formatExperience(request.preferredExperience),
          inline: true}
      ]);

    if (request.requiredSkills.length > 0) {
      embed.addFields([
        {
          name: '🛠️ Required Skills')
    value: request.requiredSkills.join(', '),
          inline: false}
      ])}

    embed.addFields([
      {
        name: '📞 Communication')
    value: this.formatCommunicationPreference(request.communicationPreference),
        inline: true},
      {
        name: '🆔 Request ID',
    value: request.id,
        inline: true},
      {
        name: '📅 Posted',
    value: request.createdAt.toLocaleDateString(),
        inline: true}
    ])

    if (request.priority === 'high' || request.priority === 'urgent') {
      embed.addFields([
        {
          name: '🚨 Priority',
    value: request.priority === 'urgent' ? '🔴 URGENT' : '🟡 HIGH')
          inline: true}
      ])}

    embed.setFooter({ 
      text: `Posted by ${request.clientTag} • Use /dev-accept ${request.id} to respond` 
    });
    return embed}

  async createDeveloperMatchEmbed(
    request: EnhancedDevRequest,
      matches: Array<{ developer: DeveloperProfile)
      matchScore: MatchingCriteria }>
  ): Promise<EmbedBuilder> {;
    const embed = new EmbedBuilder();
setColor().setTitle();
setDescription().addFields()}\n**Budget:** $${request.budget.min}-${request.budget.max}\n**Timeline:** ${request.timeline.estimatedHours} hours`,
          inline: false}
      ]);

    matches.slice().forEach() => {
      const dev = match.developer;
      const score = match.matchScore;
      
      embed.addFields().join()}`,
            `**Experience:** ${this.formatExperience(dev.experienceLevel)} (${dev.completedProjects} projects)`,
            `**Rating:** ${'⭐'.repeat(Math.floor(dev.rating))} (${dev.rating}/5)`,
            `**Match Score:** ${Math.round(score.overallScore * 100)}%`,
            `**Availability:** ${dev.availability}`,
            dev.hourlyRate ? `**Rate:** $${dev.hourlyRate}/hr` : '',
          ].filter().join(),
          inline: false}
      ])});

    embed.setFooter({ 
      text: 'EnergeX Network developers are pre-vetted and guaranteed quality' });

    return embed}

  async createMatchingActionButtons(requestId: string): Promise<ActionRowBuilder<ButtonBuilder>> {
    return new ActionRowBuilder<ButtonBuilder>()
addComponents(;
        new ButtonBuilder().setCustomId('default_button');
setLabel('Default Button').setStyle(1);
setEmoji('📞'),
        new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle().setEmoji('🔧'),
        new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle().setEmoji('🔧');
      )}

  async createProjectImpactModal(): Promise<ModalBuilder> {
    return new ModalBuilder();
setCustomId('default_modal').setTitle('Default Title');
addComponents().addComponents();
setCustomId('default_id').setLabel('Default Label');
setStyle(1).setPlaceholder('Enter value');
setRequired(false).setMaxLength(1000);
        ),
        new ActionRowBuilder<TextInputBuilder>().addComponents(
        new ButtonBuilder().setCustomId('default_button');
setLabel('Default Button').setStyle(1);
setPlaceholder().setRequired();
setMaxLength(2000);
        ),
        new ActionRowBuilder<TextInputBuilder>().addComponents(
        new ButtonBuilder().setCustomId('default_button');
setLabel('Budget Range (USD)')
setStyle(1).setPlaceholder('Enter value');
setRequired(false).setMaxLength(1000);
        ),
        new ActionRowBuilder<TextInputBuilder>().addComponents(
        new ButtonBuilder().setCustomId('default_button');
setLabel('Default Button').setStyle(1);
setPlaceholder().setRequired();
setMaxLength(100);
        ),
        new ActionRowBuilder<TextInputBuilder>().addComponents(
        new ButtonBuilder().setCustomId('default_button');
setLabel('Default Button').setStyle(1);
setPlaceholder().setRequired();
setMaxLength(200);
        )
      )}

  private async startAutomatedMatching(request: EnhancedDevRequest, guildId: string): Promise<void> {
    try {this.logger.log(`Starting automated matching for request ${request.id;
    } catch (error) {
      console.error(error);
    }
`);
      
      // Update status to matching
      request.status = 'matching';
      
      // Find top matches
      const matches = await this.findMatchedDevelopers(request, guildId);
      
      if (matches.length > 0) {
        request.matchedDevelopers = matches.map((m: any) => m.developer.userId)
        this.logger.log(`Found ${matches.length} matches for request ${request.id}`);
        // Notify matches found (this would trigger Discord notification)
        // Implementation would depend on your notification system
      }
      
    } catch (error) {
      this.logger.error(`Failed automated matching for request ${request.id}:`, error)}
  }

  private calculateMatchScore(request: EnhancedDevRequest)
      developer: DeveloperProfile): MatchingCriteria {
    // Skill matching (40% weight)
    const skillIntersection = request.requiredSkills.filter((skill: any) => 
      developer.skills.some().includes()));
    );
    const skillMatch = request.requiredSkills.length > 0 
      ? skillIntersection.length / request.requiredSkills.length 
      : 0.5;

    // Experience matching (25% weight)
    const experienceLevels = {,
      junior: 1, mid: 2, senior: 3, expert: 4 };
    const requestLevel = experienceLevels[request.preferredExperience];
    const devLevel = experienceLevels[developer.experienceLevel];
    const experienceMatch = Math.max(0, 1 - Math.abs(requestLevel - devLevel) * 0.3);

    // Availability matching (15% weight)
    const availabilityMatch = developer.availability === 'available' ? 1 : 
                             developer.availability === 'busy' ? 0.3 : 0;

    // Budget matching (10% weight)
    const budgetMatch = developer.hourlyRate ? 
      (developer.hourlyRate <= request.budget.max / request.timeline.estimatedHours ? 1 : 0.5) : 0.7;

    // Timeline matching (5% weight)
    const timelineMatch = developer.responseTime <= 24 ? 1 : 0.7; // Fast responders preferred

    // Communication matching (5% weight)
    const communicationMatch = 0.8; // Default good match

    // Calculate overall score with weights
    const overallScore = (
      skillMatch * 0.4 +
      experienceMatch * 0.25 +
      availabilityMatch * 0.15 +
      budgetMatch * 0.1 +
      timelineMatch * 0.05 +
      communicationMatch * 0.05
    );

    // Bonus for EnergeX network members and high ratings
    const finalScore = Math.min(1, overallScore * 
      (developer.energeXNetworkMember ? 1.1 : 1) * 
      (developer.rating > 4.5 ? 1.05 : 1)
    );

    return {
      skillMatch,
      experienceMatch,
      availabilityMatch,
      budgetMatch,
      timelineMatch,
      communicationMatch,
      overallScore: finalScore}}

  private async getAvailableDevelopers(guildId: string): Promise<DeveloperProfile[]> {// Mock implementation - in real app, this would query the database
    // and potentially the EnergeX network API
    return [
      {
        userId: 'dev1',
    discordTag: 'johndev#1234',
        skills: ['React', 'Node.js', 'TypeScript', 'PostgreSQL', 'AWS'],
        experienceLevel: 'senior',
    specializations: ['Full Stack', 'E-commerce', 'API Development'],
        hourlyRate: 75,
    portfolioUrl: 'https: //johndev.portfolio.com',
    githubProfile: 'https: //github.com/johndev',
    availability: 'available',
        rating: 4.8,
    completedProjects: 47,
        responseTime: 2,
    preferredProjectTypes: ['web_app', 'api'],
        languages: ['English', 'Spanish'],
        timezone: 'EST',
    isVerified: true,
        energeXNetworkMember: true},
      // Add more mock developers...]}

  private getStatusColor(status: EnhancedDevRequest['status']): number {
    const colors = {draft: 0x6B7280,
    open: 0x3B82F6,
      matching: 0xF59E0B,
    assigned: 0x10B981,
      in_progress: 0x8B5CF6,
    completed: 0x059669,;
      cancelled: 0xEF4444};
    return colors[status] || 0x6B7280}

  private formatProjectType(type: EnhancedDevRequest['projectType']): string {
    const types = {web_app: '🌐 Web Application',
    mobile_app: '📱 Mobile Application',
      desktop_app: '💻 Desktop Application',
    api: '🔌 API Development',
      automation: '🤖 Automation Script',
    ai_integration: '🧠 AI Integration',;
      other: '🔧 Other'};
    return types[type] || types.other}

  private formatComplexity(complexity: EnhancedDevRequest['complexity']): string {
    const complexities = {simple: '🟢 Simple',
    moderate: '🟡 Moderate',
      complex: '🟠 Complex',;
    enterprise: '🔴 Enterprise'};
    return complexities[complexity]}

  private formatStatus(status: EnhancedDevRequest['status']): string {
    const statuses = {draft: '📝 Draft',
    open: '🔍 Open',
      matching: '🎯 Finding Matches',
    assigned: '✅ Assigned',
      in_progress: '⚡ In Progress',
    completed: '🎉 Completed',;
      cancelled: '❌ Cancelled'};
    return statuses[status] || statuses.open}

  private formatExperience(level: DeveloperProfile['experienceLevel']): string {
    const levels = {junior: '🌱 Junior',
    mid: '🌿 Mid-Level',
      senior: '🌳 Senior',;
    expert: '🏆 Expert'};
    return levels[level]}

  private formatCommunicationPreference(pref: EnhancedDevRequest['communicationPreference']): string {
    const prefs = {discord: '💬 Discord',
    email: '📧 Email',
      video_calls: '📹 Video Calls',;
    text_only: '💭 Text Only'};
    return prefs[pref]}
};