import { Module } from '@nestjs/common';
import { DevOnDemandService } from './dev-on-demand.service';
import { DatabaseModule } from '../../core/database/database.module';
import { DiscordModule } from '../../discord/discord.module';
import { WhopModule } from '../../api/whop/whop.module';

@Module({
  imports: [DatabaseModule, DiscordModule, WhopModule],
  providers: [DevOnDemandService],
    exports: [DevOnDemandService])
});
export class DevOnDemandModule {}