// TypeScript types and interfaces for tier system

export type TierLevel = 'free' | 'ai_explorer' | 'wealth_builder' | 'dev_premium' | 'enterprise';

export type TierFeatures = {
  // AI Mastery Features
  aiAgentAccess: boolean,
      aiToolRecommendations: boolean,productivityAutomation: boolean,
    personalAiAssistant: boolean
  
  // Wealth Creation Features;
  entrepreneurshipGuidance: boolean,
      businessStrategyAccess: boolean,moneyMakingResources: boolean,
    investmentAdvice: boolean
  
  // Dev On Demand Features;
  devRequestsPerMonth: number,
      priorityMatching: boolean,escrowProtection: boolean,
    escrowPayments: boolean;
  directDeveloperContact: boolean,
      developerNetworkAccess: boolean
  projectTrackingAccess: boolean
  
  // Premium Features,premiumSupport: boolean,
    exclusiveContent: boolean
  customIntegrations: boolean
  
  // Community Features;
  communityEvents: boolean,
      mentalHealthSupport: boolean,personalCoaching: boolean,
    goalTrackingSystem: boolean;
  networkingConnections: boolean,
      oneOnOneConsultation: boolean,customSolutions: boolean,
    whiteGloveService: boolean;
  dedicatedSupport: boolean}

export type TierLimits = {
  // Monthly usage limits
  monthlyDevRequests: number,
      aiAgentQueries: number,personalCoachingSessions: number,
    projectTrackingSlots: number
  
  // Feature limits;
  maxSimultaneousProjects: number,
      maxTeamMembers: number,storageLimit: number // in GB,
    apiCallsPerMonth: number
  
  // Community limits;
  communityEventsPerMonth: number,
    networkingConnections: number}

export type CommunityTier = {
  id: string,
      guildId: string,level: TierLevel,
    name: string;
  description: string,
      monthlyPrice: number;
  yearlyPrice?: number,features: TierFeatures,
    limits: TierLimits;
  discordRoleIds: string[],
      accessibleChannels: string[],restrictedChannels: string[],
    isActive: boolean
  sortOrder: number;
  customConfig?: Record<string, any>;
  createdAt: Date,
    updatedAt: Date}

export type UserMembership = {
  id: string,
      userId: string,guildId: string,
    tierId: string;
  whopSubscriptionId?: string;
  subscriptionStartDate?: Date;
  subscriptionEndDate?: Date;
  status: 'active' | 'cancelled' | 'expired' | 'suspended',
      usageStats: {,
      monthlyDevRequests: number,
    aiAgentQueries: number;
    personalCoachingSessions: number,
    lastResetDate: string};
  paymentHistory?: Array<{
    date: string,
      amount: number,currency: string,
    transactionId: string;
    status: string}>;
  createdAt: Date,
    updatedAt: Date}