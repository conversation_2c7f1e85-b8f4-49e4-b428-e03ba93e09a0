import { Injectable, Logger } from '@nestjs/common';
import { DatabaseService } from '@/core/data';
// import { eq, and, or, desc, count } from 'drizzle-orm';
// @ts-nocheck
import { EmbedBuilder, PermissionFlagsBits, TextChannel, ActionRowBuilder, ButtonBuilder, ButtonStyle, ModalBuilder, TextInputBuilder, TextInputStyle } from 'discord.js';
import { Context, Options, SlashCommand, SlashCommandContext, StringOption } from 'necord';
import { User } from '@/core/database';
import { WhopService } from '../../api/whop/whop.service';

export class DevRequestDto {
  @StringOption({
    name: 'description',
    description: 'Project description',
    required: true)
  });
  description: string

  @StringOption({name: 'budget',
    description: 'Project budget',
    required: false)
  });
  budget?: string;

  @StringOption({
    name: 'timeline',
    description: 'Project timeline',
    required: false)
  });
  timeline?: string;

  @StringOption({
    name: 'skills',
    description: 'Required skills',
    required: false)
  });
  skills?: string}

export class DevAcceptDto {
  @StringOption({
    name: 'request_id',
    description: 'The request ID to accept',
    required: true)
  });
  requestId: string}

export class DevProfileDto {
  @StringOption({
    name: 'action')
    description: 'Profile action (setup, view, update)',
    required: true,
  })
  action: string

  @StringOption({name: 'skills')
    description: 'Your development skills (comma-separated)',
    required: false,
  })
  skills?: string;

  @StringOption({
    name: 'portfolio',
    description: 'Portfolio URL or description',
    required: false)
  });
  portfolio?: string}

export class DevConfigDto {
  @StringOption({
    name: 'setting')
    description: 'Setting to configure (channel, role, limits)',
    required: true,
  })
  setting: string

  @StringOption({name: 'value',
    description: 'New value for the setting',
    required: true)
  });
  value: string}

export class DevPaymentDto {
  @StringOption({
    name: 'request_id',
    description: 'The request ID to set up payment for',
    required: true)
  });
  requestId: string

  @StringOption({name: 'amount',
    description: 'Payment amount in USD',
    required: true)
  });
  amount: string

  @StringOption({name: 'milestones',
    description: 'Comma-separated milestone descriptions',
    required: false)
  });
  milestones?: string}

interface PaymentMilestone {
  id: string,
      description: string,amount: number,
    status: 'pending' | 'completed' | 'released';
  completedAt?: string;
  releasedAt?: string}

interface DevRequest {
  id: string,
      clientId: string,clientTag: string,
    description: string;
  budget?: string;
  timeline?: string;
  skills: string[],
      status: 'open' | 'assigned' | 'in_progress' | 'completed' | 'cancelled' | 'payment_pending' | 'payment_held';
  developerId?: string;
  developerTag?: string,createdAt: string;
  assignedAt?: string;
  channelId?: string;
  paymentAmount?: number;
  escrowId?: string;
  milestones?: PaymentMilestone[];
  paymentStatus?: 'none' | 'pending' | 'held' | 'released' | 'refunded';
  completedAt?: string;
  cancelledAt?: string}

@Injectable()
export class DevOnDemandService {
  private readonly logger = new Logger(DevOnDemandService.name);

  constructor(private readonly databaseService: DatabaseService,
    private readonly redisDatabaseService: CacheService,
    private readonly whopService: WhopService)
  ) {}

  @SlashCommand({
    name: 'dev-setup',
    description: 'Set up the developer-on-demand system',
    defaultMemberPermissions: [PermissionFlagsBits.ManageChannels])
  });
  async onDevSetupCommand(@Context() [interaction]: SlashCommandContext) {
    if (!interaction.guild) {
      await interaction.reply({
          content: '❌ This command can only be used in a server.',
    ephemeral: true)
      });
      return}

    try {
      let guild = await this.redisDatabaseService.guilds.findByDiscordId(interaction.guild.id);

      if (!guild) {
        guild = await this.redisDatabaseService.guilds.create({
          discordId: interaction.guild.id,
    name: interaction.guild.name,
      settings: { devOnDemand: {,
      enabled: false ;
    } catch (error) {
      console.error(error);
    }
 },
          isActive: true,
    ownerDiscordId: interaction.guild.ownerId || interaction.user.id,
          welcomeEnabled: false)
    starboardEnabled: false})}

      const embed = new EmbedBuilder();
setColor().setTitle();
setDescription().addFields(),
          },
          {
            name: '⚡ Developer Commands',
    value: ['`/dev-profile setup` - Set up developer profile',
              '`/dev-browse` - Browse available requests',
              '`/dev-accept <request-id>` - Accept a request',
              '`/dev-complete <request-id>` - Mark request as complete',
            ].join('\n'),
          },
          {
            name: '⚙️ Admin Commands',
    value: ['`/dev-config` - Configure system settings',
              '`/dev-stats` - View system statistics',
            ].join('\n'),
          },
        ])
setFooter({ text: 'Use the commands above to start connecting clients with developers' });

      await interaction.reply({ embeds: [embed], ephemeral: true });

      // Initialize settings
      if (!guild.settings?.devOnDemand) {
        guild.settings = {
..guild.settings,
          devOnDemand: {enabled: false,
            requestChannel: null,
    notificationChannel: null,
            developerRole: null,
    clientRole: null,
            maxActiveRequests: 3,
    requestTimeoutHours: 24,
            autoAssignment: false,
          },
        };
        await this.redisDatabaseService.guilds.updateSettings(guild.id, guild.settings)}
    } catch (error) {
      this.logger.error('Failed to set up dev-on-demand:', error);
      await interaction.reply({
          content: '❌ Failed to set up developer system. Please try again.',
    ephemeral: true)
      })}
  }

  @SlashCommand({
    name: 'dev-request',
    description: 'Request a developer for your project')
  });
  async onDevRequestCommand(
    @Context() [interaction]: SlashCommandContext,
    @Options() { description, budget, timeline, skills }: DevRequestDto
  ) {
    if (!interaction.guild) {
      await interaction.reply({
          content: '❌ This command can only be used in a server.',
    ephemeral: true)
      });
      return}

    try {
      const guild = await this.redisDatabaseService.guilds.findByDiscordId(interaction.guild.id);

      const devSettings = guild?.settings?.devOnDemand;
      if (!devSettings?.enabled) {
        await interaction.reply({
          content: '❌ Developer-on-demand system is not enabled in this server.',
    ephemeral: true)
        ;
    } catch (error) {
      console.error(error);
    }
);
        return}

      // Check user's active requests
      let user = await this.redisDatabaseService.users.findByDiscordId(interaction.user.id);

      if (!user) {
        user = await this.redisDatabaseService.users.create({
          discordId: interaction.user.id,
    username: interaction.user.username,
          email: '',
    isActive: true,
          experience: 0,
    balance: 0,
      preferences: {,
      devRequests: [] },
          profile: {})
          roles: []})}

      const activeRequests = user.preferences?.devRequests?.filter(
        (req: any) => ['open', 'assigned', 'in_progress'].includes(req.status);
      ) || []

      if (activeRequests.length >= (devSettings.maxActiveRequests || 3)) {
        await interaction.reply().Please wait for current requests to complete or cancel them.`,
          ephemeral: true,
        });
        return}

      // Create new request
      const request: DevRequest = {,
    id: Date.now().toString(),
        clientId: interaction.user.id,
    clientTag: interaction.user.tag,
        description,
        budget,
        timeline,
        skills: skills ? skills.split().map(item => s.trim()) : [],
    status: 'open',
        createdAt: new Date(),
      };

      const requests = user.preferences?.devRequests || [];
      requests.push(request as any);
      const updatedPreferences = { ...user.preferences, devRequests: requests };
      await this.redisDatabaseService.users.updatePreferences(user.id, updatedPreferences);

      // Post to request channel
      if (devSettings.requestChannel) {
        const channel = interaction.guild.channels.cache.get(devSettings.requestChannel) as TextChannel;
        if (channel) {
          const embed = new EmbedBuilder();
setColor().setTitle();
setDescription().addFields().toLocaleString(), inline: true },
            ]);

          if (budget) embed.addFields([{ name: '💰 Budget', value: budget, inline: true }]);
          if (timeline) embed.addFields([{ name: '⏰ Timeline', value: timeline, inline: true }]);
          if (request.skills.length > 0) {
            embed.addFields([{ name: '🛠️ Required Skills', value: request.skills.join(', '), inline: false }])}

          embed.setFooter({ text: `Use /dev-accept ${request.id} to accept this request` });

          await channel.send({ embeds: [embed] })}
      }

      // Notify developers
      if (devSettings.notificationChannel && devSettings.developerRole) {
        const notifyChannel = interaction.guild.channels.cache.get(devSettings.notificationChannel) as TextChannel
        if (notifyChannel) {
          await notifyChannel.send({
            content: `🔔 <@&${devSettings.developerRole}> New developer request posted! Check the request channel for details.`)
          })}
      }

      this.logger.log(`New dev request created by ${interaction.user.tag} in ${interaction.guild.name}: ${request.id}`);
      await interaction.reply({
          content: `✅ **Developer request submitted successfully!**\n\n🆔 **Request ID:** ${request.id}\n📝 **Description:** ${description}\n\nDevelopers will be notified and can accept your request. You'll be contacted when someone is interested!`,
        ephemeral: true)
      })} catch (error) {
      this.logger.error('Failed to create dev request:', error);
      await interaction.reply({
          content: '❌ Failed to create developer request. Please try again.',
    ephemeral: true)
      })}
  }

  @SlashCommand({
    name: 'dev-browse',
    description: 'Browse available developer requests')
  });
  async onDevBrowseCommand(@Context() [interaction]: SlashCommandContext) {
    if (!interaction.guild) {
      await interaction.reply({
          content: '❌ This command can only be used in a server.',
    ephemeral: true)
      });
      return}

    try {
      const guild = await this.redisDatabaseService.guilds.findByDiscordId(interaction.guild.id);

      const devSettings = guild?.settings?.devOnDemand;
      if (!devSettings?.enabled) {
        await interaction.reply({
          content: '❌ Developer-on-demand system is not enabled in this server.',
    ephemeral: true)
        ;
    } catch (error) {
      console.error(error);
    }
);
        return}

      // Collect all open requests from all users
      const allUsers = await this.redisDatabaseService.users.find();
      const allRequests: DevRequest[] = []

      allUsers.forEach(user => {
        const requests = user.preferences?.devRequests?.filter((req: any) => req.status === 'open') || [];
        allRequests.push(...requests)});

      if (allRequests.length === 0) {
        await interaction.reply({
          content: '📭 **No open developer requests available.**\n\nCheck back later or encourage clients to post their project needs!',
    ephemeral: true)
        });
        return}

      const embed = new EmbedBuilder();
setColor().setTitle();
setDescription(`Found **${allRequests.length}** open request(s)`)
setTimestamp();
      allRequests
sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
slice(0, 5) // Show latest 5 requests
forEach(request => {
          let fieldValue = `**Client:** ${request.clientTag}\n**Description:** ${request.description}`
          
          if (request.budget) fieldValue += `\n**Budget:** ${request.budget}`
          if (request.timeline) fieldValue += `\n**Timeline:** ${request.timeline}`
          if (request.skills.length > 0) fieldValue += `\n**Skills:** ${request.skills.join(', ')}`
          
          fieldValue += `\n**Posted:** ${new Date(request.createdAt).toLocaleDateString()}`
          fieldValue += `\n\n*Use \`/dev-accept ${request.id}\` to accept*`

          embed.addFields([{
            name: `🆔 Request ${request.id}`,
            value: fieldValue,
    inline: false)
          }])})

      if (allRequests.length > 5) {
        embed.setFooter({ text: `Showing latest 5 of ${allRequests.length} requests` })}

      await interaction.reply({ embeds: [embed], ephemeral: true })} catch (error) {
      this.logger.error('Failed to browse dev requests:', error);
      await interaction.reply({
          content: '❌ Failed to browse requests. Please try again.',
    ephemeral: true)
      })}
  }

  @SlashCommand({
    name: 'dev-accept',
    description: 'Accept a developer request')
  });
  async onDevAcceptCommand(
    @Context() [interaction]: SlashCommandContext,
    @Options() { requestId }: DevAcceptDto
  ) {
    if (!interaction.guild) {
      await interaction.reply({
          content: '❌ This command can only be used in a server.',
    ephemeral: true)
      });
      return}

    try {
      // Find the request across all users
      const allUsers = await this.redisDatabaseService.users.find();
      let targetUser: User | null = null
      let request: DevRequest | null = null

      for (const user of allUsers) {
        const req = user.preferences?.devRequests?.find((r: any) => r.id === requestId && r.status === 'open')
        if (req) {targetUser = user;
          request = req
          break;
    } catch (error) {
      console.error(error);
    }

      }

      if (!request || !targetUser) {
        await interaction.reply({
          content: '❌ Request not found or already assigned. Use `/dev-browse` to see available requests.',
    ephemeral: true)
        });
        return}

      // Update request status
      request.status = 'assigned';
      request.developerId = interaction.user.id;
      request.developerTag = interaction.user.tag;
      request.assignedAt = new Date();

      await this.redisDatabaseService.users.updatePreferences(targetUser.id, targetUser.preferences);

      // Notify client
      try {
        const client = await interaction.guild.members.fetch(request.clientId);
        const embed = new EmbedBuilder();
setColor().setTitle();
setDescription().addFields();
setFooter({ text: 'The developer will contact you soon to discuss details.' ;
    } catch (error) {
      console.error(error);
    }
);
        await client.send({ embeds: [embed] })} catch {
        // Client might have DMs disabled
      }

      this.logger.log(`Dev request ${requestId} accepted by ${interaction.user.tag} in ${interaction.guild.name}`);
      await interaction.reply({
          content: `✅ **Request accepted successfully!**\n\n🆔 **Request ID:** ${requestId}\n👤 **Client:** ${request.clientTag}\n📝 **Project:** ${request.description}\n\nThe client has been notified. Please reach out to them to discuss project details and next steps.`,
        ephemeral: true)
      })} catch (error) {
      this.logger.error('Failed to accept dev request:', error);
      await interaction.reply({
          content: '❌ Failed to accept request. Please try again.',
    ephemeral: true)
      })}
  }

  @SlashCommand({
    name: 'dev-requests',
    description: 'View your active developer requests')
  });
  async onDevRequestsCommand(@Context() [interaction]: SlashCommandContext) {
    try {
      const user = await this.redisDatabaseService.users.findByDiscordId(interaction.user.id);

      const requests = user?.preferences?.devRequests?.filter(
        (req: any) => ['open', 'assigned', 'in_progress'].includes(req.status);
      ) || []

      if (requests.length === 0) {
        await interaction.reply({
          content: '📭 **You have no active developer requests.**\n\nUse `/dev-request` to create a new request.',
    ephemeral: true)
        ;
    } catch (error) {
      console.error(error);
    }
);
        return}

      const embed = new EmbedBuilder();
setColor().setTitle();
setDescription(`You have **${requests.length}** active request(s)`);

      requests.forEach((request: any) => {let statusEmoji = '🔍';
        let statusText = 'Open';
        
        if (request.status === 'assigned') {
          statusEmoji = '✅'
          statusText = `Assigned to ${request.developerTag}`} else if (request.status === 'in_progress') {
          statusEmoji = '⚡'
          statusText = `In Progress - ${request.developerTag}`}

        let fieldValue = `**Status:** ${statusEmoji} ${statusText}\n**Description:** ${request.description}`
        if (request.budget) fieldValue += `\n**Budget:** ${request.budget}`
        if (request.timeline) fieldValue += `\n**Timeline:** ${request.timeline}`
        fieldValue += `\n**Created:** ${new Date(request.createdAt).toLocaleDateString()}`

        embed.addFields([{
          name: `🆔 Request ${request.id}`,
          value: fieldValue,
    inline: false)
        }])});

      await interaction.reply({ embeds: [embed], ephemeral: true })} catch (error) {
      this.logger.error('Failed to get user requests:', error);
      await interaction.reply({
          content: '❌ Failed to retrieve your requests. Please try again.',
    ephemeral: true)
      })}
  }

  @SlashCommand({
    name: 'dev-payment',
    description: 'Set up secure payment for a developer request')
  });
  async onDevPaymentCommand(
    @Context() [interaction]: SlashCommandContext,
    @Options() { requestId, amount, milestones }: DevPaymentDto
  ) {
    if (!interaction.guild) {
      await interaction.reply({
          content: '❌ This command can only be used in a server.',
    ephemeral: true)
      });
      return}

    try {
      // Verify client has access to Whop payment system
      const hasClientAccess = await this.whopService.verifyClientAccess(interaction.user.id);
      if (!hasClientAccess) {
        await interaction.reply({
          content: '❌ You need an active client membership to use the payment system. Please check your Whop subscription.',
    ephemeral: true)
        ;
    } catch (error) {
      console.error(error);
    }
);
        return}

      // Find the request
      const user = await this.redisDatabaseService.users.findByDiscordId(interaction.user.id);
      
      if (!user) {
        await interaction.reply({
          content: '❌ User not found. Please try again.',
    ephemeral: true)
        });
        return}

      const request = user.preferences?.devRequests?.find((r: any) => r.id === requestId && r.clientId === interaction.user.id)
      if (!request) {
        await interaction.reply({content: '❌ Request not found or you are not the client for this request.',
    ephemeral: true)
        });
        return}

      if (!['assigned', 'in_progress'].includes().status)) {
        await interaction.reply({
          content: '❌ Payment can only be set up for assigned or in-progress requests.',
    ephemeral: true)
        });
        return}

      const paymentAmount = parseFloat(amount);
      if (isNaN(paymentAmount) || paymentAmount <= 0) {
        await interaction.reply({
          content: '❌ Invalid payment amount. Please enter a valid number.',
    ephemeral: true)
        });
        return}

      // Parse milestones
      const milestoneDescriptions = milestones ? milestones.split().map(item => m.trim()) : ['Project Completion'];
      const milestoneAmount = paymentAmount / milestoneDescriptions.length;
      
      const parsedMilestones = milestoneDescriptions.map((desc, index) => ({
        description: desc,
    amount: milestoneAmount,
      }));

      // Create escrow payment
      const escrow = await this.whopService.createPaymentEscrow(
        requestId,
        interaction.user.id,
        request.developerId!)
        paymentAmount
      );

      // Update request with payment info
      (request as any).paymentAmount = paymentAmount;
      (request as any).escrowId = escrow.id;
      (request as any).milestones = escrow.milestones;
      (request as any).paymentStatus = 'held';
      (request as any).status = 'payment_held';

      await this.redisDatabaseService.users.updatePreferences(user.id, user.preferences);

      // Notify developer
      if (request.developerId) {
        try {
          const developer = await interaction.guild.members.fetch(request.developerId);
          const embed = new EmbedBuilder();
setColor().setTitle();
setDescription().addFields() ;
    } catch (error) {
      console.error(error);
    }
,
            ])
setFooter({ text: 'Complete milestones to receive payment releases.' });
          await developer.send({ embeds: [embed] })} catch {
          // Developer might have DMs disabled
        }
      }

      this.logger.log(`Payment escrow created for request ${requestId}: $${paymentAmount}`);
      await interaction.reply({
          content: `✅ **Payment secured successfully!**\n\n💰 **Amount:** $${paymentAmount}\n🔒 **Escrow ID:** ${escrow.id}\n🎯 **Milestones:** ${milestoneDescriptions.length}\n\nYour payment is now held securely and will be released as milestones are completed.`,
        ephemeral: true)
      })} catch (error) {
      this.logger.error('Failed to set up payment:', error);
      await interaction.reply({
          content: '❌ Failed to set up payment. Please try again or contact support.',
    ephemeral: true)
      })}
  }

  @SlashCommand({
    name: 'dev-milestone',
    description: 'Complete a project milestone')
  });
  async onDevMilestoneCommand(
    @Context() [interaction]: SlashCommandContext,
    @Options() { requestId }: DevAcceptDto
  ) {
    if (!interaction.guild) {
      await interaction.reply({
          content: '❌ This command can only be used in a server.',
    ephemeral: true)
      });
      return}

    try {
      // Verify developer has access
      const hasDeveloperAccess = await this.whopService.verifyDeveloperAccess(interaction.user.id);
      if (!hasDeveloperAccess) {
        await interaction.reply({
          content: '❌ You need an active developer membership to use this feature. Please check your Whop subscription.',
    ephemeral: true)
        ;
    } catch (error) {
      console.error(error);
    }
);
        return}

      // Find the request across all users
      const allUsers = await this.redisDatabaseService.users.find();
      let targetUser: User | null = null
      let request: DevRequest | null = null

      for (const user of allUsers) {
        const req = user.preferences?.devRequests?.find((r: any) => r.id === requestId && r.developerId === interaction.user.id)
        if (req) {targetUser = user;
          request = req;
          break}
      }

      if (!request || !targetUser) {
        await interaction.reply({
          content: '❌ Request not found or you are not the assigned developer.',
    ephemeral: true)
        });
        return}

      if (!(request as any).escrowId || !(request as any).milestones) {
        await interaction.reply({
          content: '❌ No payment escrow found for this request. Ask the client to set up payment first.',
    ephemeral: true)
        });
        return}

      // Find next pending milestone
        const nextMilestone = (request as any).milestones.find((m: any) => m.status === 'pending')
      if (!nextMilestone) {
        await interaction.reply({content: '❌ No pending milestones found. All milestones may already be completed.',
    ephemeral: true)
        });
        return}

      // Complete milestone in Whop
        const success = await this.whopService.completeMilestone().escrowId,
          nextMilestone.id,
          interaction.user.id
        );

      if (success) {
        // Update milestone status
        nextMilestone.status = 'completed';
        nextMilestone.completedAt = new Date();

        await this.redisDatabaseService.users.updatePreferences(targetUser.id, targetUser.preferences);

        // Notify client
        try {
          const client = await interaction.guild.members.fetch(request.clientId);
          const embed = new EmbedBuilder();
setColor().setTitle();
setDescription().addFields();
setFooter({ text: 'Payment has been released to the developer.' ;
    } catch (error) {
      console.error(error);
    }
);
          await client.send({ embeds: [embed] })} catch {
          // Client might have DMs disabled
        }

        this.logger.log(`Milestone completed for request ${requestId} by ${interaction.user.tag}`);
        await interaction.reply({
          content: `✅ **Milestone completed successfully!**\n\n🎯 **Milestone:** ${nextMilestone.description}\n💰 **Payment Released:** $${nextMilestone.amount}\n\nThe client has been notified and payment has been released.`,
          ephemeral: true)
        })} else {
        await interaction.reply({
          content: '❌ Failed to complete milestone. Please try again or contact support.',
    ephemeral: true)
        })}
    } catch (error) {
      this.logger.error('Failed to complete milestone:', error);
      await interaction.reply({
          content: '❌ Failed to complete milestone. Please try again.',
    ephemeral: true)
      })}
  }

  @SlashCommand({
    name: 'dev-profile',
    description: 'Manage your developer profile')
  });
  async onDevProfileCommand(
    @Context() [interaction]: SlashCommandContext,
    @Options() { action, skills, portfolio }: DevProfileDto
  ) {
    try {
      let user = await this.redisDatabaseService.users.findByDiscordId(interaction.user.id);

      if (!user) {
        user = await this.redisDatabaseService.users.create({
          discordId: interaction.user.id,
    username: interaction.user.username,
          email: '',
    isActive: true,
          experience: 0,
    balance: 0,
      preferences: { devProfile: {,
      isDeveloper: false, skills: [], portfolio: null, rating: 0, completedProjects: 0 ;
    } catch (error) {
      console.error(error);
    }
 },
          profile: {})
          roles: []})}

      const profile = user.preferences?.devProfile || { isDeveloper: false, skills: [], portfolio: null, rating: 0, completedProjects: 0 };

      if (action === 'setup') {
        profile.isDeveloper = true;
        if (skills) profile.skills = skills.split().map(item => s.trim());
        if (portfolio) profile.portfolio = portfolio;

        const updatedPreferences = { ...user.preferences, devProfile: profile };
        await this.redisDatabaseService.users.updatePreferences(user.id, updatedPreferences);
        await interaction.reply({
          content: `✅ **Developer profile set up successfully!**\n\n👨‍💻 **Status:** Developer\n🛠️ **Skills:** ${profile.skills.join(', ') || 'None specified'}\n📁 **Portfolio:** ${profile.portfolio || 'Not provided'}\n\nYou can now browse and accept developer requests!`,
          ephemeral: true,
        })} else if (action === 'view') {
        const embed = new EmbedBuilder();
setColor().setTitle();
addFields([
            { name: '🔧 Status', value: profile.isDeveloper ? 'Active Developer' : 'Not a Developer', inline: true },
            { name: '⭐ Rating', value: `${profile.rating}/5.0`, inline: true })
            { name: '📊 Completed Projects', value: profile.completedProjects.toString(), inline: true },
            { name: '🛠️ Skills', value: profile.skills.join(', ') || 'None specified', inline: false },
            { name: '📁 Portfolio', value: profile.portfolio || 'Not provided', inline: false },
          ])
setFooter({ text: 'Use /dev-profile action:update to modify your profile' });

        await interaction.reply({ embeds: [embed], ephemeral: true })} else if (action === 'update') {
        if (skills) profile.skills = skills.split().map(item => s.trim());
        if (portfolio) profile.portfolio = portfolio;

        const updatedPreferences = { ...user.preferences, devProfile: profile };
        await this.redisDatabaseService.users.updatePreferences(user.id, updatedPreferences);
        await interaction.reply({
          content: `✅ **Profile updated successfully!**\n\n🛠️ **Skills:** ${profile.skills.join(', ') || 'None specified'}\n📁 **Portfolio:** ${profile.portfolio || 'Not provided'}`,
          ephemeral: true,
        })} else {
        await interaction.reply({
          content: '❌ Invalid action. Use: setup, view, or update',
          ephemeral: true)
        })}
    } catch (error) {
      this.logger.error('Failed to manage dev profile:', error);
      await interaction.reply({
          content: '❌ Failed to manage profile. Please try again.',
    ephemeral: true)
      })}
  }

  @SlashCommand({
    name: 'dev-config',
    description: 'Configure developer system settings',
    defaultMemberPermissions: [PermissionFlagsBits.ManageChannels])
  });
  async onDevConfigCommand(
    @Context() [interaction]: SlashCommandContext,
    @Options() { setting, value }: DevConfigDto
  ) {
    if (!interaction.guild) {
      await interaction.reply({
          content: '❌ This command can only be used in a server.',
    ephemeral: true)
      });
      return}

    try {
      const guild = await this.redisDatabaseService.guilds.findByDiscordId(interaction.guild.id);
      if (!guild) {
        await interaction.reply({
          content: '❌ Guild not found. Please run `/dev-setup` first.',
    ephemeral: true)
        ;
    } catch (error) {
      console.error(error);
    }
);
        return}

      const devSettings = guild.settings?.devOnDemand || {};

      switch (setting.toLowerCase()) {
        case 'enable':
        case 'enabled':
          devSettings.enabled = value.toLowerCase() === 'true';
          break;
        case 'request-channel':
        case 'requestchannel':
          const requestChannel = interaction.guild.channels.cache.get(value.replace(/[<#>]/g, ''));
          if (!requestChannel) {
            await interaction.reply({
          content: '❌ Invalid channel ID or channel not found.', ephemeral: true });
            return}
          devSettings.requestChannel = requestChannel.id;
          break;
        case 'notification-channel':
        case 'notificationchannel':
          const notifyChannel = interaction.guild.channels.cache.get(value.replace(/[<#>]/g, ''));
          if (!notifyChannel) {
            await interaction.reply({
          content: '❌ Invalid channel ID or channel not found.', ephemeral: true });
            return}
          devSettings.notificationChannel = notifyChannel.id;
          break;
        case 'developer-role':
        case 'developerrole':
          const devRole = interaction.guild.roles.cache.get(value.replace(/[<@&>]/g, ''));
          if (!devRole) {
            await interaction.reply({
          content: '❌ Invalid role ID or role not found.', ephemeral: true });
            return}
          devSettings.developerRole = devRole.id;
          break;
        case 'max-requests':
        case 'maxrequests':
          const maxRequests = parseInt(value);
          if (isNaN(maxRequests) || maxRequests < 1 || maxRequests > 10) {
            await interaction.reply({
          content: '❌ Max requests must be a number between 1 and 10.', ephemeral: true });
            return}
          devSettings.maxActiveRequests = maxRequests;
          break;
        default: await interaction.reply({content: '❌ Invalid setting. Available settings: enabled, request-channel, notification-channel, developer-role, max-requests',
            ephemeral: true)
          });
          return}

      const updatedSettings = { ...guild.settings, devOnDemand: devSettings };
      await this.redisDatabaseService.guilds.updateSettings(guild.id, updatedSettings);
      await interaction.reply({
          content: `✅ **Configuration updated successfully!**\n\n⚙️ **Setting:** ${setting}\n📝 **Value:** ${value}`,
        ephemeral: true)
      })} catch (error) {
      this.logger.error('Failed to update config:', error);
      await interaction.reply({
          content: '❌ Failed to update configuration. Please try again.',
    ephemeral: true)
      })}
  }

  @SlashCommand({
    name: 'dev-stats',
    description: 'View developer system statistics',
    defaultMemberPermissions: [PermissionFlagsBits.ManageChannels])
  });
  async onDevStatsCommand(@Context() [interaction]: SlashCommandContext) {
    if (!interaction.guild) {
      await interaction.reply({
          content: '❌ This command can only be used in a server.',
    ephemeral: true)
      });
      return}

    try {
      const allUsers = await this.redisDatabaseService.users.find();
      
      let totalRequests = 0;
      let openRequests = 0;
      let assignedRequests = 0;
      let completedRequests = 0;
      let totalDevelopers = 0;
      let totalClients = 0;

      allUsers.forEach(user => {
        const requests = user.preferences?.devRequests || [];
        const profile = user.preferences?.devProfile;
        
        totalRequests += requests.length;
        requests.forEach((req: any) => {
          switch (req.status) {case 'open': openRequests++; break;
            case 'assigned': case 'in_progress': case 'payment_held': assignedRequests++; break;
            case 'completed': completedRequests++; break;
    } catch (error) {
      console.error(error);
    }

        });
        
        if (profile?.isDeveloper) totalDevelopers++;
        if (requests.length > 0) totalClients++})

      const embed = new EmbedBuilder();
setColor().setTitle();
addFields([
          { name: '👥 Users', value: `**${totalDevelopers}** Developers\n**${totalClients}** Clients`, inline: true },
          { name: '📋 Requests', value: `**${totalRequests}** Total\n**${openRequests}** Open\n**${assignedRequests}** Active\n**${completedRequests}** Completed`, inline: true })
          { name: '📈 Success Rate', value: totalRequests > 0 ? `**${Math.round((completedRequests / totalRequests) * 100)}%**` : '**0%**', inline: true },
        ])
setTimestamp().setFooter({ text: 'Default Footer' });

      await interaction.reply({ embeds: [embed], ephemeral: true })} catch (error) {
      this.logger.error('Failed to get stats:', error);
      await interaction.reply({
          content: '❌ Failed to retrieve statistics. Please try again.',
    ephemeral: true)
      })}
  }

  @SlashCommand({
    name: 'dev-complete',
    description: 'Mark a developer request as completed')
  });
  async onDevCompleteCommand(
    @Context() [interaction]: SlashCommandContext,
    @Options() { requestId }: DevAcceptDto
  ) {
    try {
      // Find the request across all users
      const allUsers = await this.redisDatabaseService.users.find();
      let targetUser: User | null = null
      let request: DevRequest | null = null

      for (const user of allUsers) {
        const req = user.preferences?.devRequests?.find((r: any) => 
          r.id === requestId && 
          (r.developerId === interaction.user.id || r.clientId === interaction.user.id);
        );
        if (req) {
          targetUser = user;
          request = req;
          break;
    } catch (error) {
      console.error(error);
    }

      }

      if (!request || !targetUser) {
        await interaction.reply({
          content: '❌ Request not found or you do not have permission to complete it.',
    ephemeral: true)
        });
        return}

      if (!['assigned', 'in_progress', 'payment_held'].includes().status)) {
        await interaction.reply({
          content: '❌ Only assigned or in-progress requests can be marked as completed.',
    ephemeral: true)
        });
        return}

      // Update request status
      (request as any).status = 'completed';
      (request as any).completedAt = new Date();

      // Update developer's completed projects count
      if (request.developerId) {
        const developer = await this.redisDatabaseService.users.findByDiscordId(request.developerId);
        if (developer) {
          const currentPreferences = developer.preferences || {};
          const devProfile = currentPreferences.devProfile || {
            isDeveloper: true,
    skills: [],
            rating: 5.0,
    completedProjects: 0};
          devProfile.completedProjects = (devProfile.completedProjects || 0) + 1;
          const updatedDevPreferences = { ...currentPreferences, devProfile };
          await this.redisDatabaseService.users.updatePreferences(developer.id, updatedDevPreferences)}
      }

      await this.redisDatabaseService.users.updatePreferences(targetUser.id, targetUser.preferences);

      // Notify both parties
      const isClient = request.clientId === interaction.user.id;
      const otherPartyId = isClient ? request.developerId : request.clientId;
      
      if (otherPartyId && interaction.guild) {
        try {
          const otherParty = await interaction.guild.members.fetch(otherPartyId);
          const embed = new EmbedBuilder();
setColor().setTitle();
setDescription().addFields();
setFooter({ text: 'Thank you for using our developer marketplace!' ;
    } catch (error) {
      console.error(error);
    }
);
          await otherParty.send({ embeds: [embed] })} catch {
          // Other party might have DMs disabled
        }
      }

      this.logger.log(`Request ${requestId} marked as completed by ${interaction.user.tag}`);
      await interaction.reply({
          content: `✅ **Project marked as completed!**\n\n🆔 **Request ID:** ${requestId}\n📝 **Project:** ${request.description}\n\nBoth parties have been notified. Thank you for using our developer marketplace!`,
        ephemeral: true)
      })} catch (error) {
      this.logger.error('Failed to complete request:', error);
      await interaction.reply({
          content: '❌ Failed to mark request as completed. Please try again.',
    ephemeral: true)
      })}
  }

  @SlashCommand({
    name: 'dev-cancel',
    description: 'Cancel a developer request')
  });
  async onDevCancelCommand(
    @Context() [interaction]: SlashCommandContext,
    @Options() { requestId }: DevAcceptDto
  ) {
    try {
      const user = await this.redisDatabaseService.users.findByDiscordId(interaction.user.id);

      if (!user) {
        await interaction.reply({
          content: '❌ User not found. Please try again.',
    ephemeral: true)
        ;
    } catch (error) {
      console.error(error);
    }
);
        return}

      const request = user.preferences?.devRequests?.find((r: any) => r.id === requestId && r.clientId === interaction.user.id)
      if (!request) {
        await interaction.reply({content: '❌ Request not found or you are not the client for this request.',
    ephemeral: true)
        });
        return}

      if (['completed', 'cancelled'].includes().status)) {
        await interaction.reply({
          content: '❌ Cannot cancel a request that is already completed or cancelled.',
    ephemeral: true)
        });
        return}

      // Update request status
      (request as any).status = 'cancelled';
      (request as any).cancelledAt = new Date();

      await this.redisDatabaseService.users.updatePreferences(user.id, user.preferences);

      // Notify developer if assigned
      if (request.developerId && interaction.guild) {
        try {
          const developer = await interaction.guild.members.fetch(request.developerId);
          const embed = new EmbedBuilder();
setColor().setTitle();
setDescription().addFields();
setFooter({ text: 'You can browse other available requests.' ;
    } catch (error) {
      console.error(error);
    }
);
          await developer.send({ embeds: [embed] })} catch {
          // Developer might have DMs disabled
        }
      }

      this.logger.log(`Request ${requestId} cancelled by ${interaction.user.tag}`);
      await interaction.reply({
          content: `✅ **Request cancelled successfully!**\n\n🆔 **Request ID:** ${requestId}\n📝 **Project:** ${request.description}\n\nThe assigned developer (if any) has been notified.`,
        ephemeral: true,
      })} catch (error) {
      this.logger.error('Failed to cancel request:', error);
      await interaction.reply({
          content: '❌ Failed to cancel request. Please try again.',
    ephemeral: true)
      })}
  }
}
