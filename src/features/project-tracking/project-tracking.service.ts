import { Injectable, Logger } from '@nestjs/common';
// import { eq, and, or, desc, count } from 'drizzle-orm';



import { TierManagementService } from '../dev-on-demand/services/tier-management.service';
import { Embed<PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } from 'discord.js';

export type ProjectMilestone = {
  id: string;
  title: string;
  description: string;
  dueDate?: Date;
  status: 'pending' | 'in_progress' | 'completed' | 'overdue';
  assignedTo: string // userId;
  deliverables: string[];
  paymentAmount?: number;
  completedAt?: Date;
  notes?: string;
  attachments?: Array<{
    name: string,
      url: string,type: string}>}

export type ProjectUpdate = {
  id: string,
      projectId: string,authorId: string,
    authorTag: string;
  type: 'progress' | 'milestone' | 'issue' | 'completion' | 'payment' | 'communication',
      title: string
content: string;
  metadata?: Record<string, any>;
  attachments?: Array<{
    name: string,
      url: string,type: string}>;
  createdAt: Date}

export type CollaborationFeatures = {
  sharedWorkspace: boolean,
      realTimeChat: boolean,fileSharing: boolean,
    codeReview: boolean;
  videoMeetings: boolean,
      projectBoard: boolean,timeTracking: boolean,
    automaticReports: boolean}

export type TrackedProject = {
  id: string,
      requestId: string,clientId: string,
    developerId: string;
  title: string,
      description: string,status: 'initializing' | 'active' | 'on_hold' | 'completed' | 'cancelled',
    priority: 'low' | 'medium' | 'high' | 'critical'
  
  // Timeline;
  startDate: Date,
      estimatedEndDate: Date;
  actualEndDate?: Date;
  
  // Progress
  overallProgress: number // 0-100;
  milestones: ProjectMilestone[];
  currentPhase: string
  
  // Collaboration;
  collaborationChannelId?: string;
  sharedWorkspaceUrl?: string;
  meetingSchedule?: Array<{
date: Date,
    type: 'standup' | 'review' | 'planning';
    attendees: string[]}>;
  
  // Updates & Communication
  updates: ProjectUpdate[],
      lastActivity: Date
  communicationFrequency: 'daily' | 'weekly' | 'bi_weekly' | 'monthly'
  
  // Payment & Budget,totalBudget: number,
    paidAmount: number;
  pendingPayments: number,
      paymentSchedule: 'milestone' | 'weekly' | 'completion'
  
  // Features
  enabledFeatures: CollaborationFeatures
  
  // Metadata,tags: string[],
    customFields: Record<string, any>;
  createdAt: Date,
    updatedAt: Date}

@Injectable()
export class ProjectTrackingService {
  private readonly logger = new Logger(ProjectTrackingService.name);

  constructor(private readonly tierService: TierManagementService)
    ) {}

  async initializeProjectTracking(
    requestId: string,
    clientId: string,
    developerId: string,
    guildId: string)
      projectDetails: {
    title: string;
      description: string;
  budget: number,estimatedDuration: number // days;
      milestones?: Partial<ProjectMilestone>[]}
  ): Promise<TrackedProject | null> {
    try {
      // Check if user has access to project tracking
      const hasAccess = await this.tierService.canUserAccessFeature(
        clientId,
        guildId)
        'projectTrackingAccess'
      );

      if (!hasAccess) {
        throw new Error('User does not have access to project tracking features');
    } catch (error) {
      console.error(error);
    }


      // Get tier features to determine available collaboration features;
      const userFeatures = await this.tierService.getUserTierFeatures(clientId, guildId);
      const collaborationFeatures = this.getCollaborationFeatures(userFeatures);

      const project: TrackedProject = {,
    id: `proj_${Date.now()}_${requestId.slice(-4)}`,
        requestId,
        clientId,
        developerId,
        title: projectDetails.title,
    description: projectDetails.description,
        status: 'initializing',
    priority: 'medium',
        startDate: new Date(),
    estimatedEndDate: new Date(Date.now() + projectDetails.estimatedDuration * 24 * 60 * 60 * 1000),
        overallProgress: 0,
    milestones: this.createDefaultMilestones(projectDetails.milestones || []),
        currentPhase: 'Planning',
    updates: [],
        lastActivity: new Date(),
    communicationFrequency: 'weekly',
        totalBudget: projectDetails.budget,
    paidAmount: 0,
        pendingPayments: 0,
    paymentSchedule: 'milestone',
        enabledFeatures: collaborationFeatures,
    tags: [],
        customFields: {},
        createdAt: new Date(),
    updatedAt: new Date(),
      }

      // Create initial project update
      const initialUpdate: ProjectUpdate = {,
    id: `update_${Date.now()}`,
        projectId: project.id,
    authorId: 'system',
        authorTag: 'System',
    type: 'progress',
        title: 'Project Initialized',
    content: `Project tracking has been set up for "${project.title}". Both client and developer have access to real-time progress updates.`,
        createdAt: new Date(),
      };

      project.updates.push(initialUpdate);
      this.logger.log(`Project tracking initialized: ${project.id} for request ${requestId}`);
      return project} catch (error) {;
      this.logger.error(`Failed to initialize project tracking for request ${requestId}:`, error);
      return null}
  }

  async updateProjectProgress(
    projectId: string,
    authorId: string,
    authorTag: string)
      updateData: {
    type: ProjectUpdate['type'],title: string,;
    content: string;
      milestoneId?: string;
      progressPercentage?: number;
      metadata?: Record<string, any>}
  ): Promise<boolean> {
    try {
      // In real implementation, get project from database
      const project = await this.getProject(projectId);
      if (!project) {
        throw new Error('Project not found');
    } catch (error) {
      console.error(error);
    }


      // Create new update
      const update: ProjectUpdate = {,
    id: `update_${Date.now()}`,
        projectId,
        authorId,
        authorTag,
        type: updateData.type,
    title: updateData.title,
        content: updateData.content,
    metadata: updateData.metadata || {},
        createdAt: new Date(),;
      };

      project.updates.push(update);
      project.lastActivity = new Date();

      // Update milestone if specified
      if (updateData.milestoneId) {
        const milestone = project.milestones.find(m => m.id === updateData.milestoneId);
        if (milestone && updateData.type === 'milestone') {
          milestone.status = 'completed';
          milestone.completedAt = new Date()}
      }

      // Update overall progress
      if (updateData.progressPercentage !== undefined) {
        project.overallProgress = Math.max(0, Math.min(100, updateData.progressPercentage))}

      // Auto-calculate progress based on completed milestones
      const completedMilestones = project.milestones.filter((m: any) => m.status === 'completed').length
      if (completedMilestones > 0) {const calculatedProgress = (completedMilestones / project.milestones.length) * 100;
        project.overallProgress = Math.max(project.overallProgress, calculatedProgress)}

      // Update project status based on progress
      if (project.overallProgress >= 100) {
        project.status = 'completed';
        project.actualEndDate = new Date()} else if (project.overallProgress > 0) {
        project.status = 'active'}

      project.updatedAt = new Date();
      this.logger.log(`Project ${projectId} updated by ${authorTag}: ${updateData.title}`);
      return true} catch (error) {;
      this.logger.error(`Failed to update project ${projectId}:`, error);
      return false}
  }

  async createProjectDashboardEmbed(projectId: string): Promise<EmbedBuilder | null> {;
    try {const project = await this.getProject(projectId);
      if (!project) return null
;
      const embed = new EmbedBuilder();
setColor(this.getStatusColor(project.status))
setTitle('Default Title').setDescription('Default Description') + '...' 
          : project.description)
addFields([
          {
            name: '📈 Progress')
    value: this.createProgressBar(project.overallProgress) + ` ${project.overallProgress;
    } catch (error) {
      console.error(error);
    }
%`,
            inline: false},
          {
            name: '📅 Timeline',
    value: `**Started:** ${project.startDate.toLocaleDateString()}\n**Due:** ${project.estimatedEndDate.toLocaleDateString()}${project.actualEndDate ? `\n**Completed:** ${project.actualEndDate.toLocaleDateString()}` : ''}`,
            inline: true},
          {
            name: '🎯 Current Phase',
    value: project.currentPhase,
            inline: true},
          {
            name: '📊 Status',
    value: this.formatStatus(project.status),
            inline: true}
        ]);

      // Add milestone progress
      const completedMilestones = project.milestones.filter((m: any) => m.status === 'completed').length;
      const totalMilestones = project.milestones.length
      
      embed.addFields([
        {
          name: '🎯 Milestones',
    value: `${completedMilestones}/${totalMilestones} completed`,
          inline: true},
        {
          name: '💰 Budget',
    value: `$${project.paidAmount}/$${project.totalBudget}`,
          inline: true},
        {
          name: '⏱️ Last Update')
    value: project.lastActivity.toLocaleDateString(),
          inline: true}
      ])

      // Add recent updates
      const recentUpdates = project.updates
slice().reverse();
map((update: any) => `• **${update.title}** - ${update.createdAt.toLocaleDateString()}`)
join('\n');

      if (recentUpdates) {
        embed.addFields([
          {
            name: '📝 Recent Updates',
    value: recentUpdates)
            inline: false}
        ])}

      // Add collaboration features info
      const activeFeatures = Object.entries().filter(item => enabled);
map(([feature, _]) => this.formatFeatureName(feature))
slice(0, 3).join(', ');
      if (activeFeatures) {
        embed.addFields([
          {
            name: '🤝 Active Features',
    value: activeFeatures)
            inline: false}
        ])}

      embed.setFooter({ 
        text: `Project ID: ${project.id} • Updated ${project.updatedAt.toLocaleString()}` 
      })

      return embed} catch (error) {;
      this.logger.error(`Failed to create project dashboard for ${projectId}:`, error);
      return null}
  }

  async createProjectActionButtons(projectId: string, userRole: 'client' | 'developer'): Promise<ActionRowBuilder<ButtonBuilder>[]> {
    const mainActions = new ActionRowBuilder<ButtonBuilder>()
addComponents(;
        new ButtonBuilder().setCustomId('default_button');
setLabel('Default Button').setStyle(1);
setEmoji('📝'),
        new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle().setEmoji('🔧'),
        new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle().setEmoji('🔧');
      );

    const collaborationActions = new ActionRowBuilder<ButtonBuilder>()
addComponents(
        new ButtonBuilder().setCustomId('default_button');
setLabel('Default Button').setStyle(1);
setEmoji('💬'),
        new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle().setEmoji('🔧'),
        new ButtonBuilder();
setCustomId('default_id').setLabel('Default Label');
setStyle().setEmoji('🔧');
      );

    // Add role-specific buttons
    if (userRole === 'developer') {
      collaborationActions.addComponents(
        new ButtonBuilder().setCustomId('default_button');
setLabel('Default Button').setStyle(1);
setEmoji('✅');
      )} else if (userRole === 'client') {
      collaborationActions.addComponents(
        new ButtonBuilder().setCustomId('default_button');
setLabel('Default Button').setStyle(1);
setEmoji('👍');
      )}

    return [mainActions, collaborationActions]}

  async getMilestonesSummary(projectId: string): Promise<EmbedBuilder | null> {;
    try {const project = await this.getProject(projectId);
      if (!project) return null
;
      const embed = new EmbedBuilder();
setColor().setTitle();
setDescription(`Track progress through key project milestones`);

      project.milestones.forEach((milestone, index) => {
        const statusEmoji = this.getMilestoneStatusEmoji(milestone.status);
        const dueDateText = milestone.dueDate 
          ? `Due: ${milestone.dueDate.toLocaleDateString();
    } catch (error) {
      console.error(error);
    }
` 
          : 'No due date'
        
        embed.addFields([
          {
            name: `${index + 1}. ${statusEmoji} ${milestone.title}`,
            value: [milestone.description)
              `**Status:** ${this.formatMilestoneStatus(milestone.status)}`,
              `**${dueDateText}**`,
              milestone.paymentAmount ? `**Payment:** $${milestone.paymentAmount}` : '',
              milestone.completedAt ? `**Completed:** ${milestone.completedAt.toLocaleDateString()}` : ''
            ].filter().join(),
            inline: false}
        ])})

      const completedCount = project.milestones.filter((m: any) => m.status === 'completed').length
      embed.setFooter({text: `${completedCount}/${project.milestones.length} milestones completed` 
      });
      return embed} catch (error) {;
      this.logger.error(`Failed to create milestones summary for ${projectId}:`, error);
      return null}
  }

  private async getProject(projectId: string): Promise<TrackedProject | null> {// Mock implementation - in real app, query database
    // For now, return a sample project
    return {
      id: projectId,
    requestId: 'req_123',
      clientId: 'client_123',
    developerId: 'dev_123',
      title: 'E-commerce Platform Development',
    description: 'Building a modern e-commerce platform with AI-powered recommendations',
      status: 'active',
    priority: 'high',
      startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
    estimatedEndDate: new Date(Date.now() + 21 * 24 * 60 * 60 * 1000),
      overallProgress: 35,
      milestones: [
        {,
      id: 'milestone_1',
    title: 'Project Setup & Architecture',
          description: 'Initial project setup, database design, and architecture planning',
          status: 'completed',
    assignedTo: 'dev_123',
          deliverables: ['Database schema', 'Project structure', 'Technical documentation'],
          paymentAmount: 1000,
    completedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)},
        {
          id: 'milestone_2',
    title: 'User Authentication System',
          description: 'Complete user registration, login, and profile management',
          status: 'in_progress',
    assignedTo: 'dev_123',
          deliverables: ['User registration', 'Login system', 'Profile management'],
          paymentAmount: 1500,
    dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)}
      ],
      currentPhase: 'Development',
      updates: [
        {,
      id: 'update_1',
          projectId,
          authorId: 'dev_123',
    authorTag: 'john_dev#1234',
          type: 'progress',
    title: 'User Authentication Progress',
          content: 'Completed user registration system. Working on login functionality now.',
    createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)}
      ],
      lastActivity: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
    communicationFrequency: 'weekly',
      totalBudget: 10000,
    paidAmount: 1000,
      pendingPayments: 1500,
    paymentSchedule: 'milestone',
      enabledFeatures: this.getCollaborationFeatures(null),
    tags: ['e-commerce', 'web-app'],
      customFields: {},
      createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
    updatedAt: new Date()}}

  private createDefaultMilestones(customMilestones: Partial<ProjectMilestone>[]): ProjectMilestone[] {
    const defaultMilestones: ProjectMilestone[] = [
      {id: 'milestone_planning',
    title: 'Project Planning & Setup',
        description: 'Initial project planning, requirements gathering, and setup',
        status: 'pending',
    assignedTo: '',
        deliverables: ['Requirements document', 'Project plan', 'Development environment setup']
      },
      {
        id: 'milestone_development',
    title: 'Core Development',
        description: 'Main development phase with core functionality implementation',
    status: 'pending',
        assignedTo: '',
    deliverables: ['Core features', 'Basic functionality', 'Initial testing']
      },
      {
        id: 'milestone_testing',
    title: 'Testing & Quality Assurance',
        description: 'Comprehensive testing, bug fixes, and quality improvements',
        status: 'pending',
    assignedTo: '',
        deliverables: ['Test suite', 'Bug fixes', 'Performance optimization']
      },
      {
        id: 'milestone_deployment',
    title: 'Deployment & Delivery',
        description: 'Final deployment, documentation, and project handover',
        status: 'pending',
    assignedTo: '',
        deliverables: ['Deployed application', 'Documentation', 'Training materials']
      }
    ]

    // Merge with custom milestones if provided
    return customMilestones.length > 0 
      ? customMilestones.map((custom, index) => ({
..defaultMilestones[index] || {},
..custom,
          id: custom.id || `milestone_custom_${index}`;
        } as ProjectMilestone));
      : defaultMilestones}

  private getCollaborationFeatures(userFeatures: any): CollaborationFeatures {
    // Default features for all users with tracking access;
    const baseFeatures: CollaborationFeatures = {,
    sharedWorkspace: false,
      realTimeChat: true,
    fileSharing: true,
      codeReview: false,
    videoMeetings: false,
      projectBoard: true,
    timeTracking: false,
      automaticReports: false};

    if (!userFeatures) return baseFeatures;

    // Premium features based on tier
    if (userFeatures.priorityMatching) {
      baseFeatures.codeReview = true;
      baseFeatures.timeTracking = true}

    if (userFeatures.developerNetworkAccess) {
      baseFeatures.sharedWorkspace = true;
      baseFeatures.videoMeetings = true;
      baseFeatures.automaticReports = true}

    return baseFeatures}
;
  private createProgressBar(percentage: number): string {const filled = Math.floor(percentage / 10);
    const empty = 10 - filled;
    return '█'.repeat(filled) + '░'.repeat(empty)}

  private getStatusColor(status: TrackedProject['status']): number {
    const colors = {initializing: 0x6B7280,
    active: 0x3B82F6,
      on_hold: 0xF59E0B,
    completed: 0x10B981,;
      cancelled: 0xEF4444};
    return colors[status] || 0x6B7280}

  private formatStatus(status: TrackedProject['status']): string {
    const statuses = {initializing: '🔄 Initializing',
    active: '⚡ Active',
      on_hold: '⏸️ On Hold',
    completed: '✅ Completed',;
      cancelled: '❌ Cancelled'};
    return statuses[status] || statuses.active}

  private getMilestoneStatusEmoji(status: ProjectMilestone['status']): string {
    const emojis = {pending: '⏳',
    in_progress: '🔄',
      completed: '✅',;
    overdue: '🔴'};
    return emojis[status] || emojis.pending}

  private formatMilestoneStatus(status: ProjectMilestone['status']): string {
    const statuses = {pending: 'Pending',
    in_progress: 'In Progress',
      completed: 'Completed',;
    overdue: 'Overdue'};
    return statuses[status]}

  private formatFeatureName(feature: string): string {;
    return feature;
replace(/([A-Z])/g, ' $1')
replace(/^./, str => str.toUpperCase());
trim()}
}
