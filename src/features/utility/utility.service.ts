import { Injectable, Logger } from '@nestjs/common';
import { Guild as DiscordGuild, GuildMember, User } from 'discord.js';
import { Context, Options, SlashCommand, SlashCommandContext, UserOption } from 'necord';
import {
    FeatureConfig,
    FeatureStatus,
    UtilityService as IUtilityService,
    ServerInfo,
    UserInfo
} from '../types/feature-services.interface';

class UserInfoDto {
  @UserOption({ 
    name: 'user',
    description: 'Select a user to view their profile info (leave empty for yourself)', 
    required: false 
  })
  user?: User;
}

@Injectable()
export class UtilityService implements IUtilityService {
  private readonly logger = new Logger(UtilityService.name);
  
  readonly name = 'utility';
  readonly version = '1.0.0';
  readonly enabled = true;

  getStatus(): FeatureStatus {
    return FeatureStatus.ACTIVE;
  }

  getConfig(): FeatureConfig {
    return {
      enabled: true,
      version: this.version,
      settings: {
        pingEnabled: true,
        serverInfoEnabled: true,
        userInfoEnabled: true,
        helpEnabled: true
      }
    };
  }

  @SlashCommand({
    name: 'ping',
    description: '🏓 Check bot response time and latency'
  })
  async onPingCommand(@Context() [interaction]: SlashCommandContext): Promise<void> {
    const sent = Date.now();
    await interaction.reply('🏓 Pong!');
    const received = Date.now();
    
    await interaction.editReply(`🏓 Pong! Latency: ${received - sent}ms`);
  }

  @SlashCommand({
    name: 'serverinfo',
    description: '📊 Display detailed information about this Discord server'
  })
  async onServerInfoCommand(@Context() [interaction]: SlashCommandContext): Promise<void> {
    if (!interaction.guild) {
      await interaction.reply({
        content: '❌ This command can only be used in a server.',
        ephemeral: true
      });
      return;
    }

    const serverInfo = await this.getServerInfo(interaction.guild);
    
    await interaction.reply({
      content: `📊 **Server Information**\n\n🏷️ Name: **${serverInfo.name}**\n👥 Members: **${serverInfo.memberCount}**\n📅 Created: **${serverInfo.createdAt.toDateString()}**\n👑 Owner: **<@${serverInfo.ownerId}>**`,
      ephemeral: true
    });
  }

  @SlashCommand({
    name: 'userinfo',
    description: '👤 View detailed profile information for yourself or another user'
  })
  async onUserInfoCommand(@Context() [interaction]: SlashCommandContext, @Options() options?: UserInfoDto): Promise<void> {
    const targetUser = options?.user || interaction.user;
    const member = interaction.guild?.members.cache.get(targetUser.id);
    const userInfo = await this.getUserInfo(targetUser, member);

    const content = `👤 **${targetUser === interaction.user ? 'Your' : `${targetUser.tag}'s`} Information**\n\n🏷️ Username: **${userInfo.tag}**\n🆔 ID: **${userInfo.id}**\n📅 Created: **${userInfo.createdAt.toDateString()}**${userInfo.joinedAt ? `\n🎯 Joined: **${userInfo.joinedAt.toDateString()}**\n🎭 Roles: **${userInfo.roles?.length || 0}**` : ''}`;

    await interaction.reply({ content, ephemeral: true });
    this.logger.log(`Userinfo: ${targetUser.tag} requested by ${interaction.user.tag}`);
  }

  @SlashCommand({
    name: 'help',
    description: '❓ Show all available bot commands and how to use them'
  })
  async onHelpCommand(@Context() [interaction]: SlashCommandContext): Promise<void> {
    await interaction.reply({
      content: `🤖 **EnergeX Bot Help**\n\n**AI Agents:**\n• \`/coach\` - Connect with your personal growth coach\n• \`/intake\` - Complete your intake assessment\n• \`/progress\` - Check your progress and goals\n\n**Utility:**\n• \`/ping\` - Check bot latency\n• \`/serverinfo\` - Server information\n• \`/userinfo\` - User information\n• \`/level\` - Check your level and XP\n• \`/balance\` - Check your coin balance\n\n**More features coming soon!** 🚀`,
      ephemeral: true
    });
  }

  async getServerInfo(guild: DiscordGuild): Promise<ServerInfo> {
    return {
      name: guild.name,
      memberCount: guild.memberCount,
      createdAt: guild.createdAt,
      ownerId: guild.ownerId,
      features: guild.features ? Array.from(guild.features) : [],
      boostLevel: guild.premiumTier,
      boostCount: guild.premiumSubscriptionCount || 0
    };
  }

  async getUserInfo(user: User, member?: GuildMember): Promise<UserInfo> {
    return {
      id: user.id,
      tag: user.tag,
      username: user.username,
      discriminator: user.discriminator,
      avatarUrl: user.displayAvatarURL(),
      createdAt: user.createdAt,
      joinedAt: member?.joinedAt || undefined,
      roles: member ? member.roles.cache.map(role => role.name).filter(name => name !== '@everyone') : undefined,
      permissions: member ? member.permissions.toArray() : undefined
    };
  }
}