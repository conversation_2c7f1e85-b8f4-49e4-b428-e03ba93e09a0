/**
 * Comprehensive channel management types for Discord guild operations
 * Provides type safety for channel operations, permissions, and organization
 */

import type { 
  Snowflake, 
  ChannelType,
  PermissionsString,
  VideoQualityMode,
  OverwriteType,
  ThreadAutoArchiveDuration,
  ForumLayoutType,
  SortOrderType 
} from 'discord.js';
import type { 
  EnhancedDiscordChannel,
  DiscordPermissionOverwrite,
  DiscordForumTag,
  DiscordDefaultReaction,
  DiscordThreadMetadata 
} from './discord.types';

// ====== CHANNEL CORE TYPES ======

/**
 * Enhanced channel information with additional metadata
 */
export interface ChannelInfo extends EnhancedDiscordChannel {
  guildId: Snowflake;
  createdAt: Date;
  
  // Activity statistics
  activity: ChannelActivityStats;
  
  // Permission analysis
  permissions: ChannelPermissionAnalysis;
  
  // Organization info
  organization: ChannelOrganizationInfo;
  
  // Settings and configuration
  settings: ChannelSettings;
  
  // Audit information
  lastModified: Date;
  modifiedBy: Snowflake | null;
  changeHistory: ChannelChange[];
}

/**
 * Channel activity statistics
 */
export interface ChannelActivityStats {
  messageCount: {
    total: number;
    today: number;
    week: number;
    month: number;
  };
  
  memberActivity: {
    activeToday: number;
    activeWeek: number;
    activeMonth: number;
    topPosters: Array<{
      userId: Snowflake;
      messageCount: number;
    }>;
  };
  
  voiceActivity?: {
    sessionCount: number;
    totalMinutes: number;
    averageSession: number;
    peakConcurrent: number;
    activeUsers: number;
  };
  
  threadActivity?: {
    activeThreads: number;
    archivedThreads: number;
    totalThreads: number;
    messageCount: number;
  };
  
  // Time-based activity patterns
  activityByHour: number[];
  activityByDay: number[];
  
  // Recent activity metrics
  lastMessage: Date | null;
  lastActivity: Date | null;
  quietPeriod: number; // Hours since last activity
}

/**
 * Channel permission analysis
 */
export interface ChannelPermissionAnalysis {
  overrides: EnhancedPermissionOverwrite[];
  effectivePermissions: Record<Snowflake, ChannelPermissionSet>; // User/Role ID -> permissions
  publicAccess: boolean;
  memberAccess: number; // Number of members with access
  roleAccess: Snowflake[]; // Roles with explicit access
  
  // Permission issues
  conflicts: PermissionConflict[];
  orphanedOverrides: Snowflake[]; // Overrides for non-existent users/roles
  redundantOverrides: Snowflake[]; // Overrides that don't change anything
  
  // Security analysis
  security: ChannelSecurityAnalysis;
}

/**
 * Enhanced permission override
 */
export interface EnhancedPermissionOverwrite extends DiscordPermissionOverwrite {
  targetName: string;
  targetType: 'user' | 'role';
  isOrphaned: boolean;
  isRedundant: boolean;
  effectivePermissions: PermissionsString[];
  conflictsWith: Snowflake[];
}

/**
 * Channel permission set for a user/role
 */
export interface ChannelPermissionSet {
  allow: PermissionsString[];
  deny: PermissionsString[];
  inherited: PermissionsString[];
  effective: PermissionsString[];
  source: 'role' | 'user' | 'everyone' | 'category';
}

/**
 * Permission conflict information
 */
export interface PermissionConflict {
  type: 'role_user' | 'role_role' | 'category_channel';
  targetId: Snowflake;
  conflictingId: Snowflake;
  permission: PermissionsString;
  resolution: 'allow' | 'deny' | 'unknown';
  severity: 'low' | 'medium' | 'high';
}

/**
 * Channel security analysis
 */
export interface ChannelSecurityAnalysis {
  visibilityLevel: ChannelVisibilityLevel;
  sensitivePermissions: PermissionsString[];
  publiclyVisible: boolean;
  everyoneCanSend: boolean;
  adminOnlyAccess: boolean;
  risks: ChannelSecurityRisk[];
  recommendations: string[];
}

/**
 * Channel visibility levels
 */
export enum ChannelVisibilityLevel {
  PUBLIC = 'public',           // Everyone can see and participate
  MEMBERS_ONLY = 'members',    // Only guild members
  ROLE_RESTRICTED = 'role',    // Specific roles only
  PRIVATE = 'private',         // Specific users/roles only
  HIDDEN = 'hidden',           // Not visible to most members
  ADMIN_ONLY = 'admin'         // Administrators only
}

/**
 * Channel security risks
 */
export interface ChannelSecurityRisk {
  type: ChannelRiskType;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  mitigation: string;
  affectedUsers: number;
}

/**
 * Channel risk types
 */
export enum ChannelRiskType {
  OPEN_TO_EVERYONE = 'open_to_everyone',
  ADMIN_PERMISSIONS = 'admin_permissions',
  WEBHOOK_ACCESS = 'webhook_access',
  MENTION_EVERYONE = 'mention_everyone',
  EXTERNAL_LINKS = 'external_links',
  FILE_UPLOADS = 'file_uploads',
  VOICE_PRIORITY = 'voice_priority'
}

/**
 * Channel organization information
 */
export interface ChannelOrganizationInfo {
  categoryId: Snowflake | null;
  categoryName: string | null;
  position: number;
  siblingChannels: Snowflake[];
  childChannels: Snowflake[]; // For categories
  
  // Organization metadata
  purpose: ChannelPurpose;
  tags: string[];
  description: string | null;
  guidelines: string | null;
  
  // Auto-organization
  autoArchive: boolean;
  autoDelete: boolean;
  archiveAfterDays: number;
  deleteAfterDays: number;
}

/**
 * Channel purposes for organization
 */
export enum ChannelPurpose {
  GENERAL = 'general',
  ANNOUNCEMENTS = 'announcements',
  DISCUSSION = 'discussion',
  SUPPORT = 'support',
  GAMING = 'gaming',
  CREATIVE = 'creative',
  VOICE_CHAT = 'voice_chat',
  MUSIC = 'music',
  BOTS = 'bots',
  LOGS = 'logs',
  ARCHIVE = 'archive',
  TEMPORARY = 'temporary',
  CUSTOM = 'custom'
}

/**
 * Channel settings configuration
 */
export interface ChannelSettings {
  // Basic settings
  name: string;
  topic: string | null;
  nsfw: boolean;
  
  // Rate limiting
  rateLimitPerUser: number;
  
  // Voice settings (if applicable)
  bitrate?: number;
  userLimit?: number;
  rtcRegion?: string | null;
  videoQualityMode?: VideoQualityMode;
  
  // Thread settings (if applicable)
  defaultAutoArchiveDuration?: ThreadAutoArchiveDuration;
  defaultThreadRateLimitPerUser?: number;
  
  // Forum settings (if applicable)
  availableTags?: ForumTagInfo[];
  defaultReactionEmoji?: DiscordDefaultReaction | null;
  defaultSortOrder?: SortOrderType | null;
  defaultForumLayout?: ForumLayoutType;
  
  // Auto-moderation
  autoModEnabled: boolean;
  wordFilter: string[];
  linkFilter: boolean;
  inviteFilter: boolean;
  spamFilter: boolean;
  
  // Webhook settings
  webhooksEnabled: boolean;
  maxWebhooks: number;
  
  // Integration settings
  integrations: ChannelIntegration[];
}

/**
 * Forum tag information
 */
export interface ForumTagInfo extends DiscordForumTag {
  usage: {
    postCount: number;
    recentUsage: number;
    popularWith: Snowflake[]; // User IDs who frequently use this tag
  };
  settings: {
    required: boolean;
    staffOnly: boolean;
    autoApply: boolean;
    color?: number;
  };
}

/**
 * Channel integrations
 */
export interface ChannelIntegration {
  type: ChannelIntegrationType;
  name: string;
  enabled: boolean;
  config: Record<string, any>;
  permissions: PermissionsString[];
}

/**
 * Channel integration types
 */
export enum ChannelIntegrationType {
  WEBHOOK = 'webhook',
  BOT = 'bot',
  RSS = 'rss',
  YOUTUBE = 'youtube',
  TWITCH = 'twitch',
  GITHUB = 'github',
  CUSTOM = 'custom'
}

/**
 * Channel change history
 */
export interface ChannelChange {
  id: string;
  timestamp: Date;
  moderatorId: Snowflake;
  action: ChannelChangeAction;
  changes: Record<string, { old: any; new: any }>;
  reason?: string;
  automated: boolean;
}

/**
 * Channel change actions
 */
export enum ChannelChangeAction {
  CREATED = 'created',
  DELETED = 'deleted',
  NAME_CHANGED = 'name_changed',
  TOPIC_CHANGED = 'topic_changed',
  POSITION_CHANGED = 'position_changed',
  CATEGORY_CHANGED = 'category_changed',
  PERMISSIONS_CHANGED = 'permissions_changed',
  SETTINGS_CHANGED = 'settings_changed',
  NSFW_CHANGED = 'nsfw_changed',
  SLOWMODE_CHANGED = 'slowmode_changed',
  BITRATE_CHANGED = 'bitrate_changed',
  USER_LIMIT_CHANGED = 'user_limit_changed'
}

// ====== CHANNEL MANAGEMENT OPERATIONS ======

/**
 * Channel creation request
 */
export interface ChannelCreateRequest {
  name: string;
  type: ChannelType;
  topic?: string;
  position?: number;
  parentId?: Snowflake | null;
  permissionOverwrites?: CreatePermissionOverwrite[];
  nsfw?: boolean;
  bitrate?: number;
  userLimit?: number;
  rateLimitPerUser?: number;
  rtcRegion?: string | null;
  videoQualityMode?: VideoQualityMode;
  defaultAutoArchiveDuration?: ThreadAutoArchiveDuration;
  defaultThreadRateLimitPerUser?: number;
  availableTags?: ForumTagCreateRequest[];
  defaultReactionEmoji?: DiscordDefaultReaction;
  defaultSortOrder?: SortOrderType;
  defaultForumLayout?: ForumLayoutType;
  reason?: string;
  
  // Extended options
  purpose?: ChannelPurpose;
  tags?: string[];
  description?: string;
  guidelines?: string;
  templateId?: string;
  copyFromChannelId?: Snowflake;
}

/**
 * Permission overwrite creation data
 */
export interface CreatePermissionOverwrite {
  id: Snowflake;
  type: OverwriteType;
  allow?: PermissionsString[];
  deny?: PermissionsString[];
}

/**
 * Forum tag creation request
 */
export interface ForumTagCreateRequest {
  name: string;
  emojiId?: Snowflake | null;
  emojiName?: string | null;
  moderated?: boolean;
}

/**
 * Channel update request
 */
export interface ChannelUpdateRequest {
  name?: string;
  topic?: string | null;
  position?: number;
  parentId?: Snowflake | null;
  permissionOverwrites?: CreatePermissionOverwrite[];
  nsfw?: boolean;
  bitrate?: number;
  userLimit?: number;
  rateLimitPerUser?: number;
  rtcRegion?: string | null;
  videoQualityMode?: VideoQualityMode;
  defaultAutoArchiveDuration?: ThreadAutoArchiveDuration;
  defaultThreadRateLimitPerUser?: number;
  availableTags?: ForumTagCreateRequest[];
  defaultReactionEmoji?: DiscordDefaultReaction | null;
  defaultSortOrder?: SortOrderType | null;
  defaultForumLayout?: ForumLayoutType;
  reason?: string;
  
  // Extended options
  purpose?: ChannelPurpose;
  tags?: string[];
  description?: string;
  guidelines?: string;
}

/**
 * Channel deletion request
 */
export interface ChannelDeletionRequest {
  channelId: Snowflake;
  reason?: string;
  backup?: boolean; // Create backup before deletion
  notifyMembers?: boolean;
  transferContentTo?: Snowflake; // Channel to move content to
}

/**
 * Channel position update
 */
export interface ChannelPositionUpdate {
  channelId: Snowflake;
  position: number;
  parent?: Snowflake | null;
  lockPermissions?: boolean;
}

/**
 * Bulk channel position update
 */
export interface BulkChannelPositionUpdate {
  updates: ChannelPositionUpdate[];
  reason?: string;
}

// ====== CHANNEL TEMPLATES ======

/**
 * Channel template for quick creation
 */
export interface ChannelTemplate {
  id: string;
  name: string;
  description: string;
  category: ChannelTemplateCategory;
  channelType: ChannelType;
  template: Partial<ChannelCreateRequest>;
  permissions: ChannelPermissionTemplate[];
  tags: string[];
  usageCount: number;
  isDefault: boolean;
}

/**
 * Channel template categories
 */
export enum ChannelTemplateCategory {
  GENERAL = 'general',
  MODERATION = 'moderation',
  COMMUNITY = 'community',
  GAMING = 'gaming',
  CREATIVE = 'creative',
  SUPPORT = 'support',
  ARCHIVE = 'archive',
  VOICE = 'voice',
  CUSTOM = 'custom'
}

/**
 * Permission template for channels
 */
export interface ChannelPermissionTemplate {
  name: string;
  description: string;
  overrides: CreatePermissionOverwrite[];
  applyToNewChannels: boolean;
  channelTypes: ChannelType[];
}

// ====== CHANNEL SEARCH AND FILTERING ======

/**
 * Channel search options
 */
export interface ChannelSearchOptions {
  query?: string;
  types?: ChannelType[];
  categories?: Snowflake[];
  purposes?: ChannelPurpose[];
  hasActivity?: boolean;
  activityLevel?: 'low' | 'medium' | 'high';
  nsfw?: boolean;
  slowmode?: boolean;
  hasPermissionOverrides?: boolean;
  createdAfter?: Date;
  createdBefore?: Date;
  sortBy?: ChannelSortField;
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

/**
 * Channel sort fields
 */
export type ChannelSortField = 
  | 'name'
  | 'position'
  | 'createdAt'
  | 'messageCount'
  | 'memberCount'
  | 'activity'
  | 'type';

// ====== RESPONSE TYPES ======

/**
 * Channel list response
 */
export interface ChannelListResponse {
  channels: ChannelInfo[];
  total: number;
  page: number;
  pageSize: number;
  hasMore: boolean;
  filters: ChannelSearchOptions;
  stats: ChannelListStats;
}

/**
 * Channel list statistics
 */
export interface ChannelListStats {
  totalChannels: number;
  byType: Record<ChannelType, number>;
  byPurpose: Record<ChannelPurpose, number>;
  byVisibility: Record<ChannelVisibilityLevel, number>;
  activeChannels: number;
  inactiveChannels: number;
  publicChannels: number;
  privateChannels: number;
  categories: number;
}

/**
 * Channel operation result
 */
export interface ChannelOperationResult {
  success: boolean;
  channelId: Snowflake;
  action: string;
  changes: Record<string, { old: any; new: any }>;
  warnings: string[];
  errors: string[];
  affectedMembers?: number;
  timestamp: Date;
}

/**
 * Channel organization analysis
 */
export interface ChannelOrganizationAnalysis {
  categories: Array<{
    categoryId: Snowflake | null;
    categoryName: string | null;
    channelCount: number;
    channels: ChannelInfo[];
    capacity: number;
    recommendations: string[];
  }>;
  
  orphanedChannels: ChannelInfo[]; // Channels without categories
  emptyCategories: Snowflake[]; // Categories with no channels
  overcrowdedCategories: Snowflake[]; // Categories with too many channels
  
  issues: Array<{
    type: 'naming' | 'organization' | 'permissions' | 'activity';
    severity: 'low' | 'medium' | 'high';
    description: string;
    affectedChannels: Snowflake[];
    suggestion: string;
  }>;
  
  statistics: {
    totalChannels: number;
    categorizedChannels: number;
    averageChannelsPerCategory: number;
    activeChannels: number;
    inactiveChannels: number;
    duplicateNames: number;
  };
}

// ====== CHANNEL AUTOMATION TYPES ======

/**
 * Channel automation rule
 */
export interface ChannelAutomationRule {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  triggers: ChannelAutomationTrigger[];
  conditions: ChannelAutomationCondition[];
  actions: ChannelAutomationAction[];
  cooldown: number; // Minutes
  maxExecutions: number; // Per day
  createdBy: Snowflake;
  createdAt: Date;
  lastExecuted: Date | null;
  executionCount: number;
}

/**
 * Channel automation triggers
 */
export interface ChannelAutomationTrigger {
  type: ChannelTriggerType;
  parameters: Record<string, any>;
}

/**
 * Channel trigger types
 */
export enum ChannelTriggerType {
  INACTIVITY = 'inactivity',
  HIGH_ACTIVITY = 'high_activity',
  MEMBER_COUNT = 'member_count',
  MESSAGE_COUNT = 'message_count',
  TIME_BASED = 'time_based',
  KEYWORD_MENTION = 'keyword_mention',
  USER_JOIN = 'user_join',
  USER_LEAVE = 'user_leave'
}

/**
 * Channel automation conditions
 */
export interface ChannelAutomationCondition {
  type: 'channel_type' | 'has_permission' | 'member_count' | 'activity_level' | 'custom';
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains';
  value: any;
}

/**
 * Channel automation actions
 */
export interface ChannelAutomationAction {
  type: ChannelActionType;
  parameters: Record<string, any>;
  delay?: number; // Minutes to wait before executing
}

/**
 * Channel action types
 */
export enum ChannelActionType {
  ARCHIVE = 'archive',
  DELETE = 'delete',
  MOVE_CATEGORY = 'move_category',
  CHANGE_PERMISSIONS = 'change_permissions',
  SEND_MESSAGE = 'send_message',
  CREATE_THREAD = 'create_thread',
  NOTIFY_MODERATORS = 'notify_moderators',
  UPDATE_TOPIC = 'update_topic',
  ENABLE_SLOWMODE = 'enable_slowmode',
  DISABLE_SLOWMODE = 'disable_slowmode'
}

// ====== CHANNEL AUDIT TYPES ======

/**
 * Channel audit report
 */
export interface ChannelAuditReport {
  guildId: Snowflake;
  generatedAt: Date;
  period: {
    start: Date;
    end: Date;
  };
  
  summary: {
    totalChanges: number;
    channelsCreated: number;
    channelsDeleted: number;
    channelsModified: number;
    permissionChanges: number;
    categoryChanges: number;
  };
  
  changes: ChannelAuditEntry[];
  
  analysis: {
    mostActiveChannels: Array<{
      channelId: Snowflake;
      changes: number;
      activity: number;
    }>;
    
    topModifiers: Array<{
      userId: Snowflake;
      changes: number;
    }>;
    
    securityEvents: Array<{
      timestamp: Date;
      type: string;
      channelId: Snowflake;
      severity: 'low' | 'medium' | 'high';
      description: string;
    }>;
  };
}

/**
 * Channel audit entry
 */
export interface ChannelAuditEntry {
  id: string;
  timestamp: Date;
  action: ChannelChangeAction;
  channelId: Snowflake;
  channelName: string;
  channelType: ChannelType;
  moderatorId: Snowflake;
  moderatorName: string;
  changes: Record<string, { old: any; new: any }>;
  reason?: string;
  automated: boolean;
  riskLevel: 'low' | 'medium' | 'high';
}

// ====== UTILITY TYPES ======

/**
 * Channel comparison function
 */
export type ChannelComparator = (a: ChannelInfo, b: ChannelInfo) => number;

/**
 * Channel filter function
 */
export type ChannelFilter = (channel: ChannelInfo) => boolean;

/**
 * Channel transformer function
 */
export type ChannelTransformer<T> = (channel: ChannelInfo) => T;

/**
 * Permission calculator function
 */
export type ChannelPermissionCalculator = (
  channel: ChannelInfo,
  userId: Snowflake
) => ChannelPermissionSet;