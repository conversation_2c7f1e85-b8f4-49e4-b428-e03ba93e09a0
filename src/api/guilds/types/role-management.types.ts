/**
 * Comprehensive role management types for Discord guild operations
 * Provides type safety for role operations, permissions, and hierarchy management
 */

import type { 
  Snowflake, 
  PermissionsString,
  RoleFlags,
  ColorResolvable 
} from 'discord.js';
import type { 
  EnhancedDiscordRole,
  EnhancedDiscordMember,
  DiscordRoleTags 
} from './discord.types';

// ====== ROLE CORE TYPES ======

/**
 * Enhanced role information with additional metadata
 */
export interface RoleInfo extends EnhancedDiscordRole {
  guildId: Snowflake;
  createdAt: Date;
  memberCount: number;
  mentionCount: number; // How many times role was mentioned
  
  // Hierarchy information
  hierarchy: RoleHierarchyInfo;
  
  // Permission analysis
  permissions: RolePermissionInfo;
  
  // Usage statistics
  usage: RoleUsageStats;
  
  // Integration info
  integration: RoleIntegrationInfo | null;
  
  // Audit information
  lastModified: Date;
  modifiedBy: Snowflake | null;
  changeHistory: RoleChange[];
}

/**
 * Role hierarchy information
 */
export interface RoleHierarchyInfo {
  position: number;
  isHighest: boolean;
  isEveryone: boolean;
  canManage: boolean;
  managedBy: Snowflake[]; // Role IDs that can manage this role
  canManageRoles: Snowflake[]; // Role IDs this role can manage
  higherRoles: Snowflake[];
  lowerRoles: Snowflake[];
  conflictsWithBots: boolean;
  recommendedPosition?: number;
}

/**
 * Role permission analysis
 */
export interface RolePermissionInfo {
  permissions: PermissionsString[];
  dangerous: PermissionsString[];
  administrative: PermissionsString[];
  moderative: PermissionsString[];
  basic: PermissionsString[];
  level: RolePermissionLevel;
  risks: RolePermissionRisk[];
  recommendations: string[];
  allowsEverything: boolean;
  allowsNothing: boolean;
}

/**
 * Role permission levels
 */
export enum RolePermissionLevel {
  BASIC = 0,
  TRUSTED = 1,
  MODERATOR = 2,
  ADMIN = 3,
  DANGEROUS = 4
}

/**
 * Role permission risks
 */
export interface RolePermissionRisk {
  type: RoleRiskType;
  severity: RiskSeverity;
  permission: PermissionsString;
  description: string;
  mitigation: string;
}

/**
 * Role risk types
 */
export enum RoleRiskType {
  ADMINISTRATOR = 'administrator',
  MANAGE_GUILD = 'manage_guild',
  MANAGE_ROLES = 'manage_roles',
  MANAGE_CHANNELS = 'manage_channels',
  MANAGE_WEBHOOKS = 'manage_webhooks',
  MENTION_EVERYONE = 'mention_everyone',
  VIEW_AUDIT_LOG = 'view_audit_log',
  KICK_MEMBERS = 'kick_members',
  BAN_MEMBERS = 'ban_members',
  MANAGE_NICKNAMES = 'manage_nicknames'
}

/**
 * Risk severity levels
 */
export enum RiskSeverity {
  LOW = 1,
  MEDIUM = 2,
  HIGH = 3,
  CRITICAL = 4
}

/**
 * Role usage statistics
 */
export interface RoleUsageStats {
  assignedMembers: number;
  activeMembers: number; // Members online with this role
  recentAssignments: number; // Last 7 days
  recentRemovals: number; // Last 7 days
  averageTimeWithRole: number; // Milliseconds
  automatedAssignments: number;
  manualAssignments: number;
  topAssigners: Array<{
    userId: Snowflake;
    count: number;
  }>;
  usageByChannel: Record<Snowflake, number>;
  mentionFrequency: {
    daily: number;
    weekly: number;
    monthly: number;
  };
}

/**
 * Role integration information
 */
export interface RoleIntegrationInfo {
  type: RoleIntegrationType;
  integrationId: Snowflake;
  applicationId?: Snowflake;
  botId?: Snowflake;
  subscriptionId?: Snowflake;
  managed: boolean;
  canDelete: boolean;
  canModify: boolean;
  syncedAt: Date | null;
  source: string;
}

/**
 * Role integration types
 */
export enum RoleIntegrationType {
  BOT = 'bot',
  INTEGRATION = 'integration',
  SUBSCRIPTION = 'subscription',
  BOOST = 'boost',
  PREMIUM = 'premium',
  LINKED = 'linked',
  NONE = 'none'
}

/**
 * Role change history
 */
export interface RoleChange {
  id: string;
  timestamp: Date;
  moderatorId: Snowflake;
  action: RoleChangeAction;
  changes: Record<string, { old: any; new: any }>;
  reason?: string;
  automated: boolean;
}

/**
 * Role change actions
 */
export enum RoleChangeAction {
  CREATED = 'created',
  DELETED = 'deleted',
  NAME_CHANGED = 'name_changed',
  COLOR_CHANGED = 'color_changed',
  PERMISSIONS_CHANGED = 'permissions_changed',
  POSITION_CHANGED = 'position_changed',
  HOIST_CHANGED = 'hoist_changed',
  MENTIONABLE_CHANGED = 'mentionable_changed',
  ICON_CHANGED = 'icon_changed',
  EMOJI_CHANGED = 'emoji_changed'
}

// ====== ROLE MANAGEMENT OPERATIONS ======

/**
 * Role creation request
 */
export interface RoleCreateRequest {
  name: string;
  color?: ColorResolvable;
  permissions?: PermissionsString[];
  hoist?: boolean;
  mentionable?: boolean;
  icon?: string | null;
  unicodeEmoji?: string | null;
  position?: number;
  reason?: string;
  templateId?: string; // Use predefined template
  copyFromRoleId?: Snowflake; // Copy from existing role
}

/**
 * Role update request
 */
export interface RoleUpdateRequest {
  name?: string;
  color?: ColorResolvable;
  permissions?: PermissionsString[];
  hoist?: boolean;
  mentionable?: boolean;
  icon?: string | null;
  unicodeEmoji?: string | null;
  position?: number;
  reason?: string;
}

/**
 * Role deletion request
 */
export interface RoleDeletionRequest {
  roleId: Snowflake;
  reason?: string;
  transferMembersTo?: Snowflake; // Role ID to transfer members to
  notifyMembers?: boolean;
  backupRole?: boolean; // Create backup before deletion
}

/**
 * Role position update request
 */
export interface RolePositionUpdate {
  roleId: Snowflake;
  position: number;
}

/**
 * Bulk role position update
 */
export interface BulkRolePositionUpdate {
  updates: RolePositionUpdate[];
  reason?: string;
}

// ====== ROLE TEMPLATES ======

/**
 * Role template for quick creation
 */
export interface RoleTemplate {
  id: string;
  name: string;
  description: string;
  category: RoleTemplateCategory;
  template: {
    name: string;
    color: ColorResolvable;
    permissions: PermissionsString[];
    hoist: boolean;
    mentionable: boolean;
  };
  tags: string[];
  usageCount: number;
  isDefault: boolean;
}

/**
 * Role template categories
 */
export enum RoleTemplateCategory {
  ADMINISTRATIVE = 'administrative',
  MODERATION = 'moderation',
  COMMUNITY = 'community',
  GAMING = 'gaming',
  CREATIVE = 'creative',
  TECHNICAL = 'technical',
  SPECIAL = 'special',
  CUSTOM = 'custom'
}

/**
 * Predefined role templates
 */
export const ROLE_TEMPLATES: Record<string, RoleTemplate> = {
  ADMIN: {
    id: 'admin',
    name: 'Administrator',
    description: 'Full server administration permissions',
    category: RoleTemplateCategory.ADMINISTRATIVE,
    template: {
      name: 'Administrator',
      color: 0xFF0000,
      permissions: ['Administrator'],
      hoist: true,
      mentionable: false
    },
    tags: ['admin', 'management'],
    usageCount: 0,
    isDefault: true
  },
  MODERATOR: {
    id: 'moderator',
    name: 'Moderator',
    description: 'Standard moderation permissions',
    category: RoleTemplateCategory.MODERATION,
    template: {
      name: 'Moderator',
      color: 0x00FF00,
      permissions: [
        'ViewChannel',
        'SendMessages',
        'ManageMessages',
        'KickMembers',
        'BanMembers',
        'ManageNicknames',
        'MuteMembers',
        'DeafenMembers',
        'MoveMembers'
      ],
      hoist: true,
      mentionable: true
    },
    tags: ['moderation', 'staff'],
    usageCount: 0,
    isDefault: true
  },
  MEMBER: {
    id: 'member',
    name: 'Member',
    description: 'Standard member permissions',
    category: RoleTemplateCategory.COMMUNITY,
    template: {
      name: 'Member',
      color: 0x0099FF,
      permissions: [
        'ViewChannel',
        'SendMessages',
        'SendMessagesInThreads',
        'EmbedLinks',
        'AttachFiles',
        'ReadMessageHistory',
        'UseExternalEmojis',
        'UseExternalStickers',
        'AddReactions',
        'UseSlashCommands',
        'Connect',
        'Speak',
        'UseVAD'
      ],
      hoist: false,
      mentionable: false
    },
    tags: ['basic', 'default'],
    usageCount: 0,
    isDefault: true
  },
  BOT: {
    id: 'bot',
    name: 'Bot',
    description: 'Standard bot permissions',
    category: RoleTemplateCategory.TECHNICAL,
    template: {
      name: 'Bot',
      color: 0x7289DA,
      permissions: [
        'ViewChannel',
        'SendMessages',
        'EmbedLinks',
        'AttachFiles',
        'ReadMessageHistory',
        'UseExternalEmojis',
        'AddReactions',
        'UseSlashCommands',
        'Connect',
        'Speak',
        'ManageMessages'
      ],
      hoist: true,
      mentionable: false
    },
    tags: ['bot', 'automation'],
    usageCount: 0,
    isDefault: true
  }
};

// ====== ROLE ASSIGNMENT TYPES ======

/**
 * Role assignment configuration
 */
export interface RoleAssignmentConfig {
  roleId: Snowflake;
  autoAssign: RoleAutoAssignConfig | null;
  restrictions: RoleRestrictions;
  requirements: RoleRequirements;
  limits: RoleAssignmentLimits;
}

/**
 * Auto-assignment configuration
 */
export interface RoleAutoAssignConfig {
  enabled: boolean;
  triggers: RoleAutoAssignTrigger[];
  conditions: RoleAutoAssignCondition[];
  delay: number; // Milliseconds
  removeOnLeave: boolean;
  logChannel?: Snowflake;
}

/**
 * Auto-assignment triggers
 */
export enum RoleAutoAssignTrigger {
  JOIN_GUILD = 'join_guild',
  VERIFY_ACCOUNT = 'verify_account',
  BOOST_GUILD = 'boost_guild',
  REACTION_ADD = 'reaction_add',
  MESSAGE_COUNT = 'message_count',
  TIME_IN_GUILD = 'time_in_guild',
  LEVEL_UP = 'level_up',
  COMMAND_USAGE = 'command_usage',
  VOICE_TIME = 'voice_time'
}

/**
 * Auto-assignment conditions
 */
export interface RoleAutoAssignCondition {
  type: 'has_role' | 'lacks_role' | 'account_age' | 'member_count' | 'custom';
  value: any;
  operator: 'equals' | 'greater_than' | 'less_than' | 'contains' | 'not_contains';
}

/**
 * Role restrictions
 */
export interface RoleRestrictions {
  maxMembers?: number;
  requiresRole?: Snowflake; // Must have this role first
  conflictsWithRoles: Snowflake[]; // Cannot have these roles
  temporaryOnly: boolean;
  selfAssignable: boolean;
  requiresApproval: boolean;
  approvers: Snowflake[]; // User/Role IDs who can approve
}

/**
 * Role requirements
 */
export interface RoleRequirements {
  minimumAccountAge: number; // Days
  minimumGuildAge: number; // Days
  minimumLevel?: number;
  minimumMessages?: number;
  minimumVoiceTime?: number; // Minutes
  requiredChannels: Snowflake[]; // Must be active in these channels
  customChecks: RoleCustomCheck[];
}

/**
 * Custom role requirement check
 */
export interface RoleCustomCheck {
  id: string;
  name: string;
  description: string;
  handler: string; // Function name or endpoint
  parameters: Record<string, any>;
  required: boolean;
}

/**
 * Role assignment limits
 */
export interface RoleAssignmentLimits {
  maxRolesPerUser: number;
  maxDailyAssignments: number;
  maxHourlyAssignments: number;
  cooldownBetweenAssignments: number; // Minutes
  bypassLimitsRoles: Snowflake[]; // Roles that bypass limits
}

// ====== ROLE SEARCH AND FILTERING ======

/**
 * Role search options
 */
export interface RoleSearchOptions {
  query?: string;
  permissions?: PermissionsString[];
  excludePermissions?: PermissionsString[];
  color?: ColorResolvable;
  hoist?: boolean;
  mentionable?: boolean;
  managed?: boolean;
  memberCount?: {
    min?: number;
    max?: number;
  };
  hierarchy?: {
    above?: number;
    below?: number;
  };
  sortBy?: RoleSortField;
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

/**
 * Role sort fields
 */
export type RoleSortField = 
  | 'name'
  | 'position'
  | 'memberCount'
  | 'color'
  | 'createdAt'
  | 'permissions'
  | 'usage';

// ====== RESPONSE TYPES ======

/**
 * Role list response
 */
export interface RoleListResponse {
  roles: RoleInfo[];
  total: number;
  page: number;
  pageSize: number;
  hasMore: boolean;
  filters: RoleSearchOptions;
  stats: RoleListStats;
}

/**
 * Role list statistics
 */
export interface RoleListStats {
  totalRoles: number;
  managedRoles: number;
  hoistedRoles: number;
  mentionableRoles: number;
  emptyRoles: number;
  dangerousRoles: number;
  byPermissionLevel: Record<RolePermissionLevel, number>;
  byIntegrationType: Record<RoleIntegrationType, number>;
}

/**
 * Role operation result
 */
export interface RoleOperationResult {
  success: boolean;
  roleId: Snowflake;
  action: string;
  changes: Record<string, { old: any; new: any }>;
  warnings: string[];
  errors: string[];
  affectedMembers: number;
  timestamp: Date;
}

/**
 * Role hierarchy analysis result
 */
export interface RoleHierarchyAnalysis {
  roles: Array<{
    role: RoleInfo;
    canManage: boolean;
    managedBy: Snowflake[];
    canManageRoles: Snowflake[];
    conflicts: string[];
    recommendations: string[];
  }>;
  hierarchyIssues: Array<{
    type: 'conflict' | 'warning' | 'optimization';
    severity: RiskSeverity;
    description: string;
    affectedRoles: Snowflake[];
    suggestion: string;
  }>;
  statistics: {
    totalRoles: number;
    managedRoles: number;
    conflictingRoles: number;
    orphanedRoles: number;
    adminRoles: number;
    botRoles: number;
  };
}

// ====== ROLE SYNCHRONIZATION TYPES ======

/**
 * Role synchronization configuration
 */
export interface RoleSyncConfig {
  enabled: boolean;
  source: RoleSyncSource;
  mapping: RoleSyncMapping[];
  syncInterval: number; // Minutes
  onUserJoin: boolean;
  onUserLeave: boolean;
  removeUnmapped: boolean;
  logChannel?: Snowflake;
}

/**
 * Role sync sources
 */
export interface RoleSyncSource {
  type: 'webhook' | 'api' | 'database' | 'external';
  endpoint?: string;
  credentials?: Record<string, string>;
  query?: string;
}

/**
 * Role sync mapping
 */
export interface RoleSyncMapping {
  externalId: string;
  externalName: string;
  discordRoleId: Snowflake;
  conditions?: Record<string, any>;
  priority: number;
}

// ====== ROLE AUDIT TYPES ======

/**
 * Role audit report
 */
export interface RoleAuditReport {
  guildId: Snowflake;
  generatedAt: Date;
  period: {
    start: Date;
    end: Date;
  };
  
  summary: {
    totalChanges: number;
    rolesCreated: number;
    rolesDeleted: number;
    rolesModified: number;
    permissionChanges: number;
    memberAssignments: number;
    memberRemovals: number;
  };
  
  changes: RoleAuditEntry[];
  
  analysis: {
    topChangedRoles: Array<{
      roleId: Snowflake;
      changes: number;
    }>;
    
    topModifiers: Array<{
      userId: Snowflake;
      changes: number;
    }>;
    
    riskEvents: Array<{
      timestamp: Date;
      type: string;
      severity: RiskSeverity;
      description: string;
    }>;
  };
}

/**
 * Role audit entry
 */
export interface RoleAuditEntry {
  id: string;
  timestamp: Date;
  action: RoleChangeAction;
  roleId: Snowflake;
  roleName: string;
  moderatorId: Snowflake;
  moderatorName: string;
  changes: Record<string, { old: any; new: any }>;
  reason?: string;
  automated: boolean;
  riskLevel: RiskSeverity;
}

// ====== UTILITY TYPES ======

/**
 * Role comparison function
 */
export type RoleComparator = (a: RoleInfo, b: RoleInfo) => number;

/**
 * Role filter function
 */
export type RoleFilter = (role: RoleInfo) => boolean;

/**
 * Role transformer function
 */
export type RoleTransformer<T> = (role: RoleInfo) => T;

/**
 * Permission analyzer function
 */
export type PermissionAnalyzer = (permissions: PermissionsString[]) => RolePermissionInfo;