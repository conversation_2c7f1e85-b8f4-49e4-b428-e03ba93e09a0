/**
 * Comprehensive Discord.js type definitions for guild management
 * Provides complete type safety for Discord API interactions
 */

import type {
  Snowflake,
  PermissionsString,
  ChannelType,
  GuildFeature,
  VerificationLevel,
  ExplicitContentFilterLevel,
  DefaultMessageNotificationLevel,
  MFALevel,
  PremiumTier,
  SystemChannelFlagsString,
  GuildNSFWLevel,
  Locale,
  VideoQualityMode,
  OverwriteType,
  RoleFlags,
  UserFlags,
  ActivityType,
  PresenceStatus,
  APIRole,
  APIChannel,
  APIGuildMember,
  APIUser,
  APIGuild,
  RESTPostAPIChannelMessageJSONBody
} from 'discord.js';

// ====== CORE DISCORD TYPES ======

/**
 * Enhanced Discord Guild representation with full type safety
 */
export interface EnhancedDiscordGuild extends APIGuild {
  // Core guild properties
  id: Snowflake;
  name: string;
  icon?: string | null;
  iconURL?: (options?: { format?: 'webp' | 'png' | 'jpg' | 'jpeg' | 'gif'; size?: number; dynamic?: boolean }) => string | null;
  splash?: string | null;
  discoverySplash?: string | null;
  banner?: string | null;
  ownerId: Snowflake;
  
  // Guild settings
  region?: string;
  afkChannelId?: Snowflake | null;
  afkTimeout: number;
  widgetEnabled?: boolean;
  widgetChannelId?: Snowflake | null;
  verificationLevel: VerificationLevel;
  defaultMessageNotifications: DefaultMessageNotificationLevel;
  explicitContentFilter: ExplicitContentFilterLevel;
  mfaLevel: MFALevel;
  systemChannelId?: Snowflake | null;
  systemChannelFlags: SystemChannelFlagsString[];
  rulesChannelId?: Snowflake | null;
  maxPresences?: number | null;
  maxMembers?: number;
  vanityUrlCode?: string | null;
  description?: string | null;
  premiumTier: PremiumTier;
  premiumSubscriptionCount?: number;
  preferredLocale: Locale;
  publicUpdatesChannelId?: Snowflake | null;
  maxVideoChannelUsers?: number;
  nsfwLevel: GuildNSFWLevel;
  
  // Collections and features
  features: GuildFeature[];
  roles: EnhancedDiscordRole[];
  channels: EnhancedDiscordChannel[];
  members: EnhancedDiscordMember[];
  emojis: DiscordEmoji[];
  stickers: DiscordSticker[];
  
  // Computed properties
  memberCount: number;
  large: boolean;
  unavailable?: boolean;
  shardId?: number;
  
  // Application integration
  applicationId?: Snowflake | null;
  approximateMemberCount?: number;
  approximatePresenceCount?: number;
  
  // Premium features
  premiumProgressBarEnabled: boolean;
  
  // Safety and moderation
  safetyAlertsChannelId?: Snowflake | null;
}

/**
 * Enhanced Discord Channel with comprehensive typing
 */
export interface EnhancedDiscordChannel extends APIChannel {
  id: Snowflake;
  type: ChannelType;
  guildId?: Snowflake;
  position?: number;
  permissionOverwrites?: DiscordPermissionOverwrite[];
  name?: string;
  topic?: string | null;
  nsfw?: boolean;
  lastMessageId?: Snowflake | null;
  bitrate?: number;
  userLimit?: number;
  rateLimitPerUser?: number;
  recipients?: DiscordUser[];
  icon?: string | null;
  ownerId?: Snowflake;
  applicationId?: Snowflake;
  parentId?: Snowflake | null;
  lastPinTimestamp?: string | null;
  rtcRegion?: string | null;
  videoQualityMode?: VideoQualityMode;
  messageCount?: number;
  memberCount?: number;
  threadMetadata?: DiscordThreadMetadata;
  member?: DiscordThreadMember;
  defaultAutoArchiveDuration?: number;
  permissions?: string;
  flags?: number;
  totalMessageSent?: number;
  availableTags?: DiscordForumTag[];
  appliedTags?: Snowflake[];
  defaultReactionEmoji?: DiscordDefaultReaction | null;
  defaultThreadRateLimitPerUser?: number;
  defaultSortOrder?: number | null;
  defaultForumLayout?: number;
}

/**
 * Enhanced Discord Role with permission details
 */
export interface EnhancedDiscordRole extends APIRole {
  id: Snowflake;
  name: string;
  color: number;
  hoist: boolean;
  icon?: string | null;
  unicodeEmoji?: string | null;
  position: number;
  permissions: string;
  managed: boolean;
  mentionable: boolean;
  tags?: DiscordRoleTags;
  flags?: RoleFlags;
  
  // Computed properties
  hexColor: string;
  members?: EnhancedDiscordMember[];
  memberCount?: number;
  
  // Permission helpers
  hasPermission: (permission: PermissionsString) => boolean;
  permissionsList: PermissionsString[];
}

/**
 * Enhanced Discord Member with comprehensive user data
 */
export interface EnhancedDiscordMember extends APIGuildMember {
  user: EnhancedDiscordUser;
  nick?: string | null;
  avatar?: string | null;
  roles: Snowflake[];
  joinedAt: string;
  premiumSince?: string | null;
  deaf: boolean;
  mute: boolean;
  flags: number;
  pending?: boolean;
  permissions?: string;
  communicationDisabledUntil?: string | null;
  
  // Computed properties
  displayName: string;
  displayAvatarURL: (options?: { format?: string; size?: number; dynamic?: boolean }) => string;
  hoistedRole?: EnhancedDiscordRole;
  colorRole?: EnhancedDiscordRole;
  
  // Permission and role helpers
  hasPermission: (permission: PermissionsString, channel?: EnhancedDiscordChannel) => boolean;
  hasRole: (roleId: Snowflake) => boolean;
  rolesList: EnhancedDiscordRole[];
  
  // Status and activity
  presence?: DiscordPresence;
  voice?: DiscordVoiceState;
}

/**
 * Enhanced Discord User with profile information
 */
export interface EnhancedDiscordUser extends APIUser {
  id: Snowflake;
  username: string;
  discriminator: string;
  globalName?: string | null;
  avatar?: string | null;
  bot?: boolean;
  system?: boolean;
  mfaEnabled?: boolean;
  banner?: string | null;
  accentColor?: number | null;
  locale?: Locale;
  verified?: boolean;
  email?: string | null;
  flags?: UserFlags;
  premiumType?: number;
  publicFlags?: UserFlags;
  avatarDecorationData?: DiscordAvatarDecorationData | null;
  
  // Computed properties
  tag: string;
  avatarURL: (options?: { format?: string; size?: number; dynamic?: boolean }) => string | null;
  bannerURL: (options?: { format?: string; size?: number; dynamic?: boolean }) => string | null;
  displayAvatarURL: (options?: { format?: string; size?: number; dynamic?: boolean }) => string;
  
  // User state
  createdAt: Date;
  createdTimestamp: number;
}

// ====== SUPPORTING TYPES ======

/**
 * Discord Permission Overwrite with type safety
 */
export interface DiscordPermissionOverwrite {
  id: Snowflake;
  type: OverwriteType;
  allow: string;
  deny: string;
  
  // Computed properties
  allowList: PermissionsString[];
  denyList: PermissionsString[];
}

/**
 * Discord Role Tags for managed roles
 */
export interface DiscordRoleTags {
  botId?: Snowflake;
  integrationId?: Snowflake;
  premiumSubscriber?: null;
  subscriptionListingId?: Snowflake;
  availableForPurchase?: null;
  guildConnections?: null;
}

/**
 * Discord Thread Metadata
 */
export interface DiscordThreadMetadata {
  archived: boolean;
  autoArchiveDuration: number;
  archiveTimestamp: string;
  locked: boolean;
  invitable?: boolean;
  createTimestamp?: string | null;
}

/**
 * Discord Thread Member
 */
export interface DiscordThreadMember {
  id?: Snowflake;
  userId?: Snowflake;
  joinTimestamp: string;
  flags: number;
  member?: EnhancedDiscordMember;
}

/**
 * Discord Forum Tag
 */
export interface DiscordForumTag {
  id: Snowflake;
  name: string;
  moderated: boolean;
  emojiId?: Snowflake | null;
  emojiName?: string | null;
}

/**
 * Discord Default Reaction for forums
 */
export interface DiscordDefaultReaction {
  emojiId?: Snowflake | null;
  emojiName?: string | null;
}

/**
 * Discord Emoji
 */
export interface DiscordEmoji {
  id?: Snowflake | null;
  name?: string | null;
  roles?: Snowflake[];
  user?: EnhancedDiscordUser;
  requireColons?: boolean;
  managed?: boolean;
  animated?: boolean;
  available?: boolean;
}

/**
 * Discord Sticker
 */
export interface DiscordSticker {
  id: Snowflake;
  packId?: Snowflake;
  name: string;
  description?: string | null;
  tags: string;
  type: number;
  formatType: number;
  available?: boolean;
  guildId?: Snowflake;
  user?: EnhancedDiscordUser;
  sortValue?: number;
}

/**
 * Discord Voice State
 */
export interface DiscordVoiceState {
  guildId?: Snowflake;
  channelId?: Snowflake | null;
  userId: Snowflake;
  member?: EnhancedDiscordMember;
  sessionId: string;
  deaf: boolean;
  mute: boolean;
  selfDeaf: boolean;
  selfMute: boolean;
  selfStream?: boolean;
  selfVideo: boolean;
  suppress: boolean;
  requestToSpeakTimestamp?: string | null;
}

/**
 * Discord Presence
 */
export interface DiscordPresence {
  user: EnhancedDiscordUser;
  guildId?: Snowflake;
  status: PresenceStatus;
  activities: DiscordActivity[];
  clientStatus: DiscordClientStatus;
}

/**
 * Discord Activity
 */
export interface DiscordActivity {
  name: string;
  type: ActivityType;
  url?: string | null;
  createdTimestamp: number;
  timestamps?: DiscordActivityTimestamps;
  applicationId?: Snowflake;
  details?: string | null;
  state?: string | null;
  emoji?: DiscordActivityEmoji | null;
  party?: DiscordActivityParty;
  assets?: DiscordActivityAssets;
  secrets?: DiscordActivitySecrets;
  instance?: boolean;
  flags?: number;
  buttons?: DiscordActivityButton[];
}

/**
 * Discord Activity Timestamps
 */
export interface DiscordActivityTimestamps {
  start?: number;
  end?: number;
}

/**
 * Discord Activity Emoji
 */
export interface DiscordActivityEmoji {
  name: string;
  id?: Snowflake;
  animated?: boolean;
}

/**
 * Discord Activity Party
 */
export interface DiscordActivityParty {
  id?: string;
  size?: [number, number];
}

/**
 * Discord Activity Assets
 */
export interface DiscordActivityAssets {
  largeImage?: string;
  largeText?: string;
  smallImage?: string;
  smallText?: string;
}

/**
 * Discord Activity Secrets
 */
export interface DiscordActivitySecrets {
  join?: string;
  spectate?: string;
  match?: string;
}

/**
 * Discord Activity Button
 */
export interface DiscordActivityButton {
  label: string;
  url: string;
}

/**
 * Discord Client Status
 */
export interface DiscordClientStatus {
  desktop?: PresenceStatus;
  mobile?: PresenceStatus;
  web?: PresenceStatus;
}

/**
 * Discord Avatar Decoration Data
 */
export interface DiscordAvatarDecorationData {
  asset: string;
  skuId: Snowflake;
}

// ====== WEBHOOK AND INTEGRATION TYPES ======

/**
 * Discord Webhook
 */
export interface DiscordWebhook {
  id: Snowflake;
  type: number;
  guildId?: Snowflake | null;
  channelId: Snowflake | null;
  user?: EnhancedDiscordUser;
  name?: string | null;
  avatar?: string | null;
  token?: string;
  applicationId?: Snowflake | null;
  sourceGuild?: Partial<EnhancedDiscordGuild>;
  sourceChannel?: Partial<EnhancedDiscordChannel>;
  url?: string;
}

/**
 * Discord Integration
 */
export interface DiscordIntegration {
  id: Snowflake;
  name: string;
  type: string;
  enabled: boolean;
  syncing?: boolean;
  roleId?: Snowflake;
  enableEmoticons?: boolean;
  expireBehavior?: number;
  expireGracePeriod?: number;
  user?: EnhancedDiscordUser;
  account: DiscordIntegrationAccount;
  syncedAt?: string;
  subscriberCount?: number;
  revoked?: boolean;
  application?: DiscordIntegrationApplication;
  scopes?: string[];
}

/**
 * Discord Integration Account
 */
export interface DiscordIntegrationAccount {
  id: string;
  name: string;
}

/**
 * Discord Integration Application
 */
export interface DiscordIntegrationApplication {
  id: Snowflake;
  name: string;
  icon?: string | null;
  description: string;
  bot?: EnhancedDiscordUser;
}

// ====== AUDIT LOG TYPES ======

/**
 * Discord Audit Log
 */
export interface DiscordAuditLog {
  auditLogEntries: DiscordAuditLogEntry[];
  users: EnhancedDiscordUser[];
  integrations: DiscordIntegration[];
  webhooks: DiscordWebhook[];
  threads: EnhancedDiscordChannel[];
  guildScheduledEvents: DiscordGuildScheduledEvent[];
  autoModerationRules: DiscordAutoModerationRule[];
}

/**
 * Discord Audit Log Entry
 */
export interface DiscordAuditLogEntry {
  targetId?: string | null;
  changes?: DiscordAuditLogChange[];
  userId?: Snowflake | null;
  id: Snowflake;
  actionType: number;
  options?: DiscordAuditLogEntryOptions;
  reason?: string;
}

/**
 * Discord Audit Log Change
 */
export interface DiscordAuditLogChange {
  newValue?: any;
  oldValue?: any;
  key: string;
}

/**
 * Discord Audit Log Entry Options
 */
export interface DiscordAuditLogEntryOptions {
  deleteMemberDays?: string;
  membersRemoved?: string;
  channelId?: Snowflake;
  messageId?: Snowflake;
  count?: string;
  id?: Snowflake;
  type?: string;
  roleName?: string;
}

// ====== SCHEDULED EVENTS TYPES ======

/**
 * Discord Guild Scheduled Event
 */
export interface DiscordGuildScheduledEvent {
  id: Snowflake;
  guildId: Snowflake;
  channelId?: Snowflake | null;
  creatorId?: Snowflake | null;
  name: string;
  description?: string | null;
  scheduledStartTime: string;
  scheduledEndTime?: string | null;
  privacyLevel: number;
  status: number;
  entityType: number;
  entityId?: Snowflake | null;
  entityMetadata?: DiscordGuildScheduledEventEntityMetadata | null;
  creator?: EnhancedDiscordUser;
  userCount?: number;
  image?: string | null;
}

/**
 * Discord Guild Scheduled Event Entity Metadata
 */
export interface DiscordGuildScheduledEventEntityMetadata {
  location?: string;
}

// ====== AUTO MODERATION TYPES ======

/**
 * Discord Auto Moderation Rule
 */
export interface DiscordAutoModerationRule {
  id: Snowflake;
  guildId: Snowflake;
  name: string;
  creatorId: Snowflake;
  eventType: number;
  triggerType: number;
  triggerMetadata: DiscordAutoModerationTriggerMetadata;
  actions: DiscordAutoModerationAction[];
  enabled: boolean;
  exemptRoles: Snowflake[];
  exemptChannels: Snowflake[];
}

/**
 * Discord Auto Moderation Trigger Metadata
 */
export interface DiscordAutoModerationTriggerMetadata {
  keywordFilter?: string[];
  regexPatterns?: string[];
  presets?: number[];
  allowList?: string[];
  mentionTotalLimit?: number;
  mentionRaidProtectionEnabled?: boolean;
}

/**
 * Discord Auto Moderation Action
 */
export interface DiscordAutoModerationAction {
  type: number;
  metadata?: DiscordAutoModerationActionMetadata;
}

/**
 * Discord Auto Moderation Action Metadata
 */
export interface DiscordAutoModerationActionMetadata {
  channelId?: Snowflake;
  durationSeconds?: number;
  customMessage?: string;
}

// ====== INVITE TYPES ======

/**
 * Discord Invite
 */
export interface DiscordInvite {
  code: string;
  guild?: Partial<EnhancedDiscordGuild>;
  channel: Partial<EnhancedDiscordChannel> | null;
  inviter?: EnhancedDiscordUser;
  targetType?: number;
  targetUser?: EnhancedDiscordUser;
  targetApplication?: DiscordInviteApplication;
  approximatePresenceCount?: number;
  approximateMemberCount?: number;
  expiresAt?: string | null;
  stageInstance?: DiscordInviteStageInstance;
  guildScheduledEvent?: DiscordGuildScheduledEvent;
  
  // Metadata for existing invites
  uses?: number;
  maxUses?: number;
  maxAge?: number;
  temporary?: boolean;
  createdAt?: string;
}

/**
 * Discord Invite Application
 */
export interface DiscordInviteApplication {
  id: Snowflake;
  name: string;
  icon?: string | null;
  description: string;
  rpcOrigins?: string[];
  botPublic: boolean;
  botRequireCodeGrant: boolean;
  bot?: Partial<EnhancedDiscordUser>;
  termsOfServiceUrl?: string;
  privacyPolicyUrl?: string;
  customInstallUrl?: string;
  installParams?: DiscordInstallParams;
  tags?: string[];
}

/**
 * Discord Install Params
 */
export interface DiscordInstallParams {
  scopes: string[];
  permissions: string;
}

/**
 * Discord Invite Stage Instance
 */
export interface DiscordInviteStageInstance {
  members: Partial<EnhancedDiscordMember>[];
  participantCount: number;
  speakerCount: number;
  topic: string;
}

// ====== MESSAGE TYPES ======

/**
 * Enhanced Discord Message
 */
export interface EnhancedDiscordMessage {
  id: Snowflake;
  channelId: Snowflake;
  author: EnhancedDiscordUser;
  content: string;
  timestamp: string;
  editedTimestamp?: string | null;
  tts: boolean;
  mentionEveryone: boolean;
  mentions: EnhancedDiscordUser[];
  mentionRoles: Snowflake[];
  mentionChannels?: DiscordChannelMention[];
  attachments: DiscordAttachment[];
  embeds: DiscordEmbed[];
  reactions?: DiscordReaction[];
  nonce?: number | string;
  pinned: boolean;
  webhookId?: Snowflake;
  type: number;
  activity?: DiscordMessageActivity;
  application?: Partial<DiscordInviteApplication>;
  applicationId?: Snowflake;
  messageReference?: DiscordMessageReference;
  flags?: number;
  referencedMessage?: EnhancedDiscordMessage | null;
  interaction?: DiscordMessageInteraction;
  thread?: EnhancedDiscordChannel;
  components?: DiscordMessageComponent[];
  stickerItems?: DiscordStickerItem[];
  stickers?: DiscordSticker[];
  position?: number;
  roleSubscriptionData?: DiscordRoleSubscriptionData;
}

/**
 * Discord Channel Mention
 */
export interface DiscordChannelMention {
  id: Snowflake;
  guildId: Snowflake;
  type: ChannelType;
  name: string;
}

/**
 * Discord Attachment
 */
export interface DiscordAttachment {
  id: Snowflake;
  filename: string;
  description?: string;
  contentType?: string;
  size: number;
  url: string;
  proxyUrl: string;
  height?: number | null;
  width?: number | null;
  ephemeral?: boolean;
  duration_secs?: number;
  waveform?: string;
  flags?: number;
}

/**
 * Discord Embed
 */
export interface DiscordEmbed {
  title?: string;
  type?: string;
  description?: string;
  url?: string;
  timestamp?: string;
  color?: number;
  footer?: DiscordEmbedFooter;
  image?: DiscordEmbedImage;
  thumbnail?: DiscordEmbedThumbnail;
  video?: DiscordEmbedVideo;
  provider?: DiscordEmbedProvider;
  author?: DiscordEmbedAuthor;
  fields?: DiscordEmbedField[];
}

/**
 * Discord Embed Footer
 */
export interface DiscordEmbedFooter {
  text: string;
  iconUrl?: string;
  proxyIconUrl?: string;
}

/**
 * Discord Embed Image
 */
export interface DiscordEmbedImage {
  url: string;
  proxyUrl?: string;
  height?: number;
  width?: number;
}

/**
 * Discord Embed Thumbnail
 */
export interface DiscordEmbedThumbnail {
  url: string;
  proxyUrl?: string;
  height?: number;
  width?: number;
}

/**
 * Discord Embed Video
 */
export interface DiscordEmbedVideo {
  url?: string;
  proxyUrl?: string;
  height?: number;
  width?: number;
}

/**
 * Discord Embed Provider
 */
export interface DiscordEmbedProvider {
  name?: string;
  url?: string;
}

/**
 * Discord Embed Author
 */
export interface DiscordEmbedAuthor {
  name: string;
  url?: string;
  iconUrl?: string;
  proxyIconUrl?: string;
}

/**
 * Discord Embed Field
 */
export interface DiscordEmbedField {
  name: string;
  value: string;
  inline?: boolean;
}

/**
 * Discord Reaction
 */
export interface DiscordReaction {
  count: number;
  countDetails: DiscordReactionCountDetails;
  me: boolean;
  meBurst: boolean;
  emoji: Partial<DiscordEmoji>;
  burstColors: number[];
}

/**
 * Discord Reaction Count Details
 */
export interface DiscordReactionCountDetails {
  burst: number;
  normal: number;
}

/**
 * Discord Message Activity
 */
export interface DiscordMessageActivity {
  type: number;
  partyId?: string;
}

/**
 * Discord Message Reference
 */
export interface DiscordMessageReference {
  messageId?: Snowflake;
  channelId?: Snowflake;
  guildId?: Snowflake;
  failIfNotExists?: boolean;
}

/**
 * Discord Message Interaction
 */
export interface DiscordMessageInteraction {
  id: Snowflake;
  type: number;
  name: string;
  user: EnhancedDiscordUser;
  member?: Partial<EnhancedDiscordMember>;
}

/**
 * Discord Message Component
 */
export interface DiscordMessageComponent {
  type: number;
  customId?: string;
  disabled?: boolean;
  style?: number;
  label?: string;
  emoji?: Partial<DiscordEmoji>;
  url?: string;
  options?: DiscordSelectOption[];
  channelTypes?: ChannelType[];
  placeholder?: string;
  defaultValues?: DiscordDefaultValue[];
  minValues?: number;
  maxValues?: number;
  minLength?: number;
  maxLength?: number;
  required?: boolean;
  value?: string;
  components?: DiscordMessageComponent[];
}

/**
 * Discord Select Option
 */
export interface DiscordSelectOption {
  label: string;
  value: string;
  description?: string;
  emoji?: Partial<DiscordEmoji>;
  default?: boolean;
}

/**
 * Discord Default Value
 */
export interface DiscordDefaultValue {
  id: Snowflake;
  type: string;
}

/**
 * Discord Sticker Item
 */
export interface DiscordStickerItem {
  id: Snowflake;
  name: string;
  formatType: number;
}

/**
 * Discord Role Subscription Data
 */
export interface DiscordRoleSubscriptionData {
  roleSubscriptionListingId: Snowflake;
  tierName: string;
  totalMonthsSubscribed: number;
  isRenewal: boolean;
}

// ====== UTILITY TYPES ======

/**
 * Guild event types for event handling
 */
export type GuildEventType = 
  | 'guildCreate'
  | 'guildUpdate' 
  | 'guildDelete'
  | 'guildMemberAdd'
  | 'guildMemberRemove'
  | 'guildMemberUpdate'
  | 'guildBanAdd'
  | 'guildBanRemove'
  | 'channelCreate'
  | 'channelUpdate'
  | 'channelDelete'
  | 'roleCreate'
  | 'roleUpdate'
  | 'roleDelete'
  | 'emojiCreate'
  | 'emojiUpdate'
  | 'emojiDelete'
  | 'stickerCreate'
  | 'stickerUpdate'
  | 'stickerDelete';

/**
 * Permission level enum for easy checking
 */
export enum DiscordPermissionLevel {
  NONE = 0,
  VIEW_CHANNEL = 1,
  SEND_MESSAGES = 2,
  MANAGE_MESSAGES = 3,
  MANAGE_CHANNELS = 4,
  MANAGE_GUILD = 5,
  ADMINISTRATOR = 6
}

/**
 * Common permission sets for role validation
 */
export const DiscordPermissionSets = {
  BASIC_USER: ['VIEW_CHANNEL', 'SEND_MESSAGES', 'READ_MESSAGE_HISTORY'] as PermissionsString[],
  MODERATOR: ['VIEW_CHANNEL', 'SEND_MESSAGES', 'MANAGE_MESSAGES', 'KICK_MEMBERS', 'BAN_MEMBERS'] as PermissionsString[],
  ADMIN: ['ADMINISTRATOR'] as PermissionsString[],
  BOT_REQUIRED: ['VIEW_CHANNEL', 'SEND_MESSAGES', 'EMBED_LINKS', 'ATTACH_FILES', 'USE_EXTERNAL_EMOJIS'] as PermissionsString[]
} as const;

/**
 * Type guard functions for Discord entities
 */
export const DiscordTypeGuards = {
  isGuild: (obj: any): obj is EnhancedDiscordGuild => {
    return obj && typeof obj.id === 'string' && typeof obj.name === 'string' && Array.isArray(obj.features);
  },
  
  isChannel: (obj: any): obj is EnhancedDiscordChannel => {
    return obj && typeof obj.id === 'string' && typeof obj.type === 'number';
  },
  
  isRole: (obj: any): obj is EnhancedDiscordRole => {
    return obj && typeof obj.id === 'string' && typeof obj.name === 'string' && typeof obj.permissions === 'string';
  },
  
  isMember: (obj: any): obj is EnhancedDiscordMember => {
    return obj && obj.user && typeof obj.user.id === 'string' && Array.isArray(obj.roles);
  },
  
  isUser: (obj: any): obj is EnhancedDiscordUser => {
    return obj && typeof obj.id === 'string' && typeof obj.username === 'string';
  }
} as const;

/**
 * Channel type helpers
 */
export const DiscordChannelTypes = {
  GUILD_TEXT: 0,
  DM: 1,
  GUILD_VOICE: 2,
  GROUP_DM: 3,
  GUILD_CATEGORY: 4,
  GUILD_NEWS: 5,
  GUILD_STORE: 6,
  GUILD_NEWS_THREAD: 10,
  GUILD_PUBLIC_THREAD: 11,
  GUILD_PRIVATE_THREAD: 12,
  GUILD_STAGE_VOICE: 13,
  GUILD_DIRECTORY: 14,
  GUILD_FORUM: 15
} as const;

/**
 * Export all types for easy importing
 */
export * from 'discord.js';