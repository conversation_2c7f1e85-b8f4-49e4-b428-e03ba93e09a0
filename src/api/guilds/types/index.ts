/**
 * Comprehensive Discord Guild Management Types
 * 
 * This module exports all type definitions for Discord guild management,
 * providing complete type safety for guild operations, member management,
 * role assignments, channel configuration, and event handling.
 * 
 * <AUTHOR> Code Assistant
 * @version 1.0.0
 */

// ====== CORE DISCORD TYPES ======
export * from './discord.types';

// ====== MEMBER MANAGEMENT TYPES ======
export * from './member-management.types';

// ====== ROLE MANAGEMENT TYPES ======
export * from './role-management.types';

// ====== CHANNEL MANAGEMENT TYPES ======
export * from './channel-management.types';

// ====== GUILD EVENT TYPES ======
export * from './guild-events.types';

// ====== TYPE UTILITIES ======

/**
 * Utility type for extracting the data property from events
 */
export type ExtractEventData<T> = T extends { data: infer D } ? D : never;

/**
 * Utility type for creating partial updates
 */
export type PartialUpdate<T> = Partial<T> & {
  id: string;
  updatedAt?: Date;
  reason?: string;
};

/**
 * Utility type for paginated responses
 */
export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  hasMore: boolean;
  nextCursor?: string;
  prevCursor?: string;
}

/**
 * Utility type for operation results
 */
export interface OperationResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  errors?: string[];
  warnings?: string[];
  timestamp: Date;
}

/**
 * Utility type for bulk operations
 */
export interface BulkOperationResult<T = any> {
  totalProcessed: number;
  successful: OperationResult<T>[];
  failed: OperationResult<T>[];
  summary: {
    successCount: number;
    failureCount: number;
    warningCount: number;
  };
  completedAt: Date;
}

/**
 * Utility type for search/filter operations
 */
export interface SearchOptions {
  query?: string;
  filters?: Record<string, any>;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
  cursor?: string;
}

/**
 * Utility type for audit trails
 */
export interface AuditTrail {
  id: string;
  timestamp: Date;
  action: string;
  actor: {
    id: string;
    type: 'user' | 'bot' | 'system';
    name: string;
  };
  target: {
    id: string;
    type: string;
    name: string;
  };
  changes?: Record<string, {
    old: any;
    new: any;
  }>;
  metadata?: Record<string, any>;
  reason?: string;
}

/**
 * Utility type for configuration templates
 */
export interface ConfigurationTemplate<T> {
  id: string;
  name: string;
  description: string;
  category: string;
  template: T;
  tags: string[];
  isDefault: boolean;
  usageCount: number;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Utility type for validation results
 */
export interface ValidationResult<T = any> {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
  data?: T;
  sanitizedData?: T;
}

/**
 * Validation error type
 */
export interface ValidationError {
  field: string;
  code: string;
  message: string;
  value?: any;
  constraints?: Record<string, string>;
}

/**
 * Validation warning type
 */
export interface ValidationWarning {
  field: string;
  code: string;
  message: string;
  value?: any;
  severity: 'low' | 'medium' | 'high';
}

/**
 * Utility type for feature flags
 */
export interface FeatureFlag {
  name: string;
  enabled: boolean;
  rolloutPercentage?: number;
  conditions?: Record<string, any>;
  metadata?: Record<string, any>;
}

/**
 * Utility type for rate limiting
 */
export interface RateLimit {
  key: string;
  limit: number;
  windowMs: number;
  remaining: number;
  resetTime: Date;
}

/**
 * Utility type for caching
 */
export interface CacheOptions {
  ttl: number; // Time to live in milliseconds
  key?: string;
  tags?: string[];
  invalidateOn?: string[];
}

/**
 * Utility type for metrics and monitoring
 */
export interface Metrics {
  name: string;
  value: number;
  unit?: string;
  tags?: Record<string, string>;
  timestamp: Date;
}

// ====== COMMON CONSTANTS ======

/**
 * Discord API limits and constraints
 */
export const DISCORD_LIMITS = {
  GUILD: {
    NAME_MIN_LENGTH: 2,
    NAME_MAX_LENGTH: 100,
    DESCRIPTION_MAX_LENGTH: 120,
    MAX_ROLES: 250,
    MAX_CHANNELS: 500,
    MAX_EMOJIS: 50,
    MAX_STICKERS: 5,
    MAX_MEMBERS: 800000
  },
  ROLE: {
    NAME_MIN_LENGTH: 1,
    NAME_MAX_LENGTH: 100,
    MAX_POSITION: 250
  },
  CHANNEL: {
    NAME_MIN_LENGTH: 1,
    NAME_MAX_LENGTH: 100,
    TOPIC_MAX_LENGTH: 1024,
    SLOWMODE_MAX_SECONDS: 21600,
    MAX_THREADS: 1000
  },
  MEMBER: {
    NICKNAME_MAX_LENGTH: 32,
    MAX_ROLES: 250,
    TIMEOUT_MAX_DURATION: 2419200000 // 28 days in ms
  },
  MESSAGE: {
    CONTENT_MAX_LENGTH: 2000,
    EMBED_TOTAL_MAX_LENGTH: 6000,
    MAX_EMBEDS: 10,
    MAX_ATTACHMENTS: 10
  }
} as const;

/**
 * Permission level hierarchies
 */
export const PERMISSION_HIERARCHIES = {
  DANGER_LEVELS: [
    'Administrator',
    'ManageGuild',
    'ManageRoles',
    'ManageChannels',
    'ManageWebhooks',
    'BanMembers',
    'KickMembers',
    'ManageNicknames',
    'ViewAuditLog'
  ],
  MODERATION_PERMISSIONS: [
    'ManageMessages',
    'ModerateMembers',
    'MuteMembers',
    'DeafenMembers',
    'MoveMembers',
    'UseSlashCommands'
  ],
  BASIC_PERMISSIONS: [
    'ViewChannel',
    'SendMessages',
    'ReadMessageHistory',
    'AddReactions',
    'UseExternalEmojis',
    'Connect',
    'Speak'
  ]
} as const;

/**
 * Common regex patterns for validation
 */
export const VALIDATION_PATTERNS = {
  DISCORD_ID: /^\d{17,19}$/,
  DISCORD_TAG: /^.{1,32}#\d{4}$/,
  HEX_COLOR: /^#[0-9A-Fa-f]{6}$/,
  DISCORD_INVITE: /discord(?:\.gg|app\.com\/invite)\/([a-zA-Z0-9-]+)/,
  EMOJI_UNICODE: /^\p{Emoji}$/u,
  WEBHOOK_URL: /^https:\/\/discord(?:app)?\.com\/api\/webhooks\/\d+\/[\w-]+$/
} as const;

/**
 * Event type groupings for easier handling
 */
export const EVENT_GROUPS = {
  GUILD_LIFECYCLE: [
    'guild_create',
    'guild_update', 
    'guild_delete',
    'guild_unavailable',
    'guild_available'
  ],
  MEMBER_EVENTS: [
    'member_add',
    'member_remove',
    'member_update',
    'member_chunk'
  ],
  ROLE_EVENTS: [
    'role_create',
    'role_update',
    'role_delete'
  ],
  CHANNEL_EVENTS: [
    'channel_create',
    'channel_update',
    'channel_delete'
  ],
  MODERATION_EVENTS: [
    'ban_add',
    'ban_remove',
    'member_timeout',
    'member_timeout_remove'
  ]
} as const;

// ====== TYPE GUARDS ======

/**
 * Type guard functions for runtime type checking
 */
export const TypeGuards = {
  /**
   * Check if a value is a valid Discord snowflake
   */
  isSnowflake: (value: any): value is string => {
    return typeof value === 'string' && VALIDATION_PATTERNS.DISCORD_ID.test(value);
  },

  /**
   * Check if a value is a valid hex color
   */
  isHexColor: (value: any): value is string => {
    return typeof value === 'string' && VALIDATION_PATTERNS.HEX_COLOR.test(value);
  },

  /**
   * Check if an object has required properties
   */
  hasRequiredProperties: <T extends Record<string, any>>(
    obj: any,
    properties: (keyof T)[]
  ): obj is T => {
    return obj && typeof obj === 'object' && properties.every(prop => prop in obj);
  },

  /**
   * Check if a value is a valid permission string
   */
  isPermissionString: (value: any): value is PermissionsString => {
    const validPermissions = [
      'CreateInstantInvite', 'KickMembers', 'BanMembers', 'Administrator',
      'ManageChannels', 'ManageGuild', 'AddReactions', 'ViewAuditLog',
      'PrioritySpeaker', 'Stream', 'ViewChannel', 'SendMessages',
      'SendTTSMessages', 'ManageMessages', 'EmbedLinks', 'AttachFiles',
      'ReadMessageHistory', 'MentionEveryone', 'UseExternalEmojis',
      'ViewGuildInsights', 'Connect', 'Speak', 'MuteMembers', 'DeafenMembers',
      'MoveMembers', 'UseVAD', 'ChangeNickname', 'ManageNicknames',
      'ManageRoles', 'ManageWebhooks', 'ManageEmojisAndStickers',
      'UseApplicationCommands', 'RequestToSpeak', 'ManageEvents',
      'ManageThreads', 'CreatePublicThreads', 'CreatePrivateThreads',
      'UseExternalStickers', 'SendMessagesInThreads', 'UseEmbeddedActivities',
      'ModerateMembers', 'ViewCreatorMonetizationAnalytics', 'UseSoundboard',
      'UseExternalSounds', 'SendVoiceMessages'
    ];
    return typeof value === 'string' && validPermissions.includes(value);
  }
} as const;

// ====== HELPER FUNCTIONS ======

/**
 * Helper functions for common operations
 */
export const Helpers = {
  /**
   * Generate a unique ID for internal use
   */
  generateId: (): string => {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  },

  /**
   * Format a Discord timestamp
   */
  formatTimestamp: (date: Date, style: 't' | 'T' | 'd' | 'D' | 'f' | 'F' | 'R' = 'f'): string => {
    const timestamp = Math.floor(date.getTime() / 1000);
    return `<t:${timestamp}:${style}>`;
  },

  /**
   * Parse a Discord mention
   */
  parseMention: (mention: string): { type: 'user' | 'role' | 'channel'; id: string } | null => {
    const userMatch = mention.match(/^<@!?(\d+)>$/);
    if (userMatch) return { type: 'user', id: userMatch[1] };

    const roleMatch = mention.match(/^<@&(\d+)>$/);
    if (roleMatch) return { type: 'role', id: roleMatch[1] };

    const channelMatch = mention.match(/^<#(\d+)>$/);
    if (channelMatch) return { type: 'channel', id: channelMatch[1] };

    return null;
  },

  /**
   * Calculate permission level from permission array
   */
  calculatePermissionLevel: (permissions: PermissionsString[]): number => {
    if (permissions.includes('Administrator')) return 6;
    if (permissions.some(p => ['ManageGuild', 'ManageRoles', 'ManageChannels'].includes(p))) return 5;
    if (permissions.some(p => PERMISSION_HIERARCHIES.MODERATION_PERMISSIONS.includes(p))) return 3;
    if (permissions.some(p => PERMISSION_HIERARCHIES.BASIC_PERMISSIONS.includes(p))) return 1;
    return 0;
  },

  /**
   * Sanitize a string for Discord use
   */
  sanitizeForDiscord: (text: string): string => {
    return text
      .replace(/[@#`\\]/g, '\\$&') // Escape Discord markup
      .replace(/\n{3,}/g, '\n\n')  // Limit consecutive newlines
      .substring(0, 2000);         // Truncate to Discord limit
  },

  /**
   * Deep merge two objects
   */
  deepMerge: <T extends Record<string, any>>(target: T, source: Partial<T>): T => {
    const result = { ...target };
    
    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = Helpers.deepMerge(result[key] || {}, source[key]);
      } else {
        result[key] = source[key]!;
      }
    }
    
    return result;
  }
} as const;