/**
 * Comprehensive member management types for Discord guild operations
 * Provides type safety for member operations, role assignments, and permissions
 */

import type { 
  Snowflake, 
  PermissionsString, 
  PresenceStatus,
  ActivityType 
} from 'discord.js';
import type { 
  EnhancedDiscordMember, 
  EnhancedDiscordUser, 
  EnhancedDiscordRole,
  DiscordPresence,
  DiscordVoiceState 
} from './discord.types';

// ====== MEMBER MANAGEMENT CORE TYPES ======

/**
 * Member search and filtering options
 */
export interface MemberSearchOptions {
  query?: string;
  roles?: Snowflake[];
  excludeRoles?: Snowflake[];
  status?: PresenceStatus[];
  joinedAfter?: Date;
  joinedBefore?: Date;
  hasNickname?: boolean;
  isBot?: boolean;
  isPending?: boolean;
  isCommunicationDisabled?: boolean;
  limit?: number;
  offset?: number;
  sortBy?: MemberSortField;
  sortOrder?: 'asc' | 'desc';
}

/**
 * Fields available for sorting members
 */
export type MemberSortField = 
  | 'joinedAt'
  | 'username' 
  | 'nickname'
  | 'displayName'
  | 'status'
  | 'roleCount'
  | 'highestRole';

/**
 * Comprehensive member information with metadata
 */
export interface MemberInfo extends EnhancedDiscordMember {
  // Extended member properties
  guildId: Snowflake;
  displayName: string;
  displayColor: number;
  displayColorHex: string;
  displayAvatar: string;
  
  // Role information
  rolesList: MemberRoleInfo[];
  highestRole: MemberRoleInfo | null;
  colorRole: MemberRoleInfo | null;
  hoistedRole: MemberRoleInfo | null;
  
  // Permission information
  permissions: MemberPermissions;
  channelPermissions?: Record<Snowflake, MemberChannelPermissions>;
  
  // Activity and presence
  presence: MemberPresenceInfo | null;
  voice: MemberVoiceInfo | null;
  
  // Guild-specific metadata
  memberNumber: number; // Order they joined the guild
  timeInGuild: number; // Milliseconds since joining
  boostingSince: Date | null;
  isOwner: boolean;
  
  // Moderation information
  moderationFlags: MemberModerationFlags;
  warnings: MemberWarning[];
  infractions: MemberInfraction[];
  
  // Activity tracking
  lastSeen: Date | null;
  messageCount?: number;
  activityScore?: number;
}

/**
 * Simplified role information for member context
 */
export interface MemberRoleInfo {
  id: Snowflake;
  name: string;
  color: number;
  position: number;
  permissions: string;
  managed: boolean;
  mentionable: boolean;
  isEveryone: boolean;
  icon?: string | null;
  unicodeEmoji?: string | null;
}

/**
 * Member permission information
 */
export interface MemberPermissions {
  administrator: boolean;
  permissions: PermissionsString[];
  denied: PermissionsString[];
  level: PermissionLevel;
  canManageGuild: boolean;
  canManageChannels: boolean;
  canManageRoles: boolean;
  canModerateMembers: boolean;
  canViewAuditLog: boolean;
  canUseSlashCommands: boolean;
}

/**
 * Permission levels for easy categorization
 */
export enum PermissionLevel {
  MEMBER = 0,
  TRUSTED = 1,
  MODERATOR = 2,
  ADMIN = 3,
  OWNER = 4
}

/**
 * Channel-specific permissions for a member
 */
export interface MemberChannelPermissions {
  channelId: Snowflake;
  allow: PermissionsString[];
  deny: PermissionsString[];
  canView: boolean;
  canSend: boolean;
  canManage: boolean;
  inheritedFromRoles: Snowflake[];
}

/**
 * Member presence information
 */
export interface MemberPresenceInfo {
  status: PresenceStatus;
  activities: MemberActivity[];
  clientStatus: {
    desktop?: PresenceStatus;
    mobile?: PresenceStatus;
    web?: PresenceStatus;
  };
  lastOnline: Date | null;
}

/**
 * Member activity information
 */
export interface MemberActivity {
  name: string;
  type: ActivityType;
  details?: string | null;
  state?: string | null;
  url?: string | null;
  timestamps?: {
    start?: Date;
    end?: Date;
  };
  isCustomStatus: boolean;
}

/**
 * Member voice state information
 */
export interface MemberVoiceInfo {
  channelId: Snowflake | null;
  channelName: string | null;
  isConnected: boolean;
  isDeafened: boolean;
  isMuted: boolean;
  isSelfDeafened: boolean;
  isSelfMuted: boolean;
  isSpeaking: boolean;
  isStreaming: boolean;
  isVideo: boolean;
  joinedTimestamp: Date | null;
  sessionId: string;
}

/**
 * Member moderation flags
 */
export interface MemberModerationFlags {
  isBanned: boolean;
  isKicked: boolean;
  isMuted: boolean;
  isTimedOut: boolean;
  timeoutUntil: Date | null;
  hasWarnings: boolean;
  warningCount: number;
  infractions: number;
  trustLevel: MemberTrustLevel;
  autoModFlags: string[];
  joinedRecently: boolean; // Within last 7 days
  accountAge: number; // Days since Discord account creation
}

/**
 * Member trust levels for moderation
 */
export enum MemberTrustLevel {
  UNKNOWN = 0,
  NEW = 1,
  MEMBER = 2,
  TRUSTED = 3,
  VERIFIED = 4
}

/**
 * Member warning record
 */
export interface MemberWarning {
  id: string;
  guildId: Snowflake;
  userId: Snowflake;
  moderatorId: Snowflake;
  reason: string;
  severity: WarningSeverity;
  createdAt: Date;
  expiresAt: Date | null;
  isActive: boolean;
  evidence?: string[];
  autoGenerated: boolean;
}

/**
 * Warning severity levels
 */
export enum WarningSeverity {
  LOW = 1,
  MEDIUM = 2,
  HIGH = 3,
  SEVERE = 4
}

/**
 * Member infraction record
 */
export interface MemberInfraction {
  id: string;
  guildId: Snowflake;
  userId: Snowflake;
  moderatorId: Snowflake;
  type: InfractionType;
  reason: string;
  duration?: number; // Milliseconds
  createdAt: Date;
  expiresAt: Date | null;
  isActive: boolean;
  relatedMessageId?: Snowflake;
  relatedChannelId?: Snowflake;
  evidence?: InfractionEvidence[];
}

/**
 * Infraction types
 */
export enum InfractionType {
  WARNING = 'warning',
  MUTE = 'mute',
  TIMEOUT = 'timeout',
  KICK = 'kick',
  BAN = 'ban',
  SOFTBAN = 'softban'
}

/**
 * Evidence attached to infractions
 */
export interface InfractionEvidence {
  type: 'message' | 'image' | 'log' | 'report';
  data: string;
  description?: string;
  timestamp: Date;
}

// ====== ROLE MANAGEMENT TYPES ======

/**
 * Role assignment request
 */
export interface RoleAssignmentRequest {
  userId: Snowflake;
  roleIds: Snowflake[];
  reason?: string;
  temporary?: boolean;
  expiresAt?: Date;
  notifyUser?: boolean;
}

/**
 * Role removal request
 */
export interface RoleRemovalRequest {
  userId: Snowflake;
  roleIds: Snowflake[];
  reason?: string;
  notifyUser?: boolean;
}

/**
 * Bulk role operation
 */
export interface BulkRoleOperation {
  userIds: Snowflake[];
  addRoles?: Snowflake[];
  removeRoles?: Snowflake[];
  reason?: string;
  notifyUsers?: boolean;
}

/**
 * Role hierarchy information
 */
export interface RoleHierarchy {
  role: EnhancedDiscordRole;
  canAssign: boolean;
  canRemove: boolean;
  assignableBy: Snowflake[]; // Role IDs that can assign this role
  memberCount: number;
  isHigherThan: (otherRoleId: Snowflake) => boolean;
  isLowerThan: (otherRoleId: Snowflake) => boolean;
}

// ====== MEMBER OPERATIONS TYPES ======

/**
 * Member moderation action
 */
export interface MemberModerationAction {
  type: ModerationActionType;
  targetId: Snowflake;
  moderatorId: Snowflake;
  reason?: string;
  duration?: number;
  deleteMessages?: boolean;
  notifyUser?: boolean;
  evidence?: InfractionEvidence[];
}

/**
 * Moderation action types
 */
export enum ModerationActionType {
  WARN = 'warn',
  MUTE = 'mute',
  UNMUTE = 'unmute',
  TIMEOUT = 'timeout',
  REMOVE_TIMEOUT = 'remove_timeout',
  KICK = 'kick',
  BAN = 'ban',
  UNBAN = 'unban',
  SOFTBAN = 'softban'
}

/**
 * Member modification request
 */
export interface MemberModificationRequest {
  userId: Snowflake;
  nickname?: string | null;
  roles?: Snowflake[];
  mute?: boolean;
  deaf?: boolean;
  channelId?: Snowflake | null;
  communicationDisabledUntil?: Date | null;
  reason?: string;
}

/**
 * Member join information
 */
export interface MemberJoinInfo {
  user: EnhancedDiscordUser;
  joinedAt: Date;
  inviteCode?: string | null;
  inviterId?: Snowflake | null;
  accountAge: number; // Days
  isBot: boolean;
  hasAvatar: boolean;
  defaultRoles: Snowflake[];
  flags: string[]; // Security flags like 'new_account', 'no_avatar', etc.
}

/**
 * Member leave information
 */
export interface MemberLeaveInfo {
  user: EnhancedDiscordUser;
  leftAt: Date;
  reason: MemberLeaveReason;
  roles: Snowflake[];
  nickname: string | null;
  joinedAt: Date;
  timeInGuild: number;
  wasKicked: boolean;
  wasBanned: boolean;
  lastMessage?: Date | null;
}

/**
 * Reason for member leaving
 */
export enum MemberLeaveReason {
  LEFT = 'left',
  KICKED = 'kicked',
  BANNED = 'banned',
  UNKNOWN = 'unknown'
}

// ====== MEMBER STATISTICS TYPES ======

/**
 * Guild member statistics
 */
export interface GuildMemberStats {
  totalMembers: number;
  humanMembers: number;
  botMembers: number;
  onlineMembers: number;
  offlineMembers: number;
  pendingMembers: number;
  membersByStatus: Record<PresenceStatus, number>;
  membersByRole: Record<Snowflake, number>;
  averageJoinDate: Date;
  recentJoins: number; // Last 7 days
  recentLeaves: number; // Last 7 days
  memberRetention: {
    day1: number;
    day7: number;
    day30: number;
  };
  activityStats: MemberActivityStats;
}

/**
 * Member activity statistics
 */
export interface MemberActivityStats {
  activeToday: number;
  activeThisWeek: number;
  activeThisMonth: number;
  messagesPerDay: number;
  topActiveMembers: {
    userId: Snowflake;
    username: string;
    messageCount: number;
    activityScore: number;
  }[];
  channelActivity: Record<Snowflake, number>;
  peakActivity: {
    hour: number;
    day: number;
    count: number;
  };
}

// ====== MEMBER EVENTS TYPES ======

/**
 * Member event types
 */
export type MemberEventType = 
  | 'memberJoin'
  | 'memberLeave'
  | 'memberUpdate'
  | 'memberRoleAdd'
  | 'memberRoleRemove'
  | 'memberNicknameChange'
  | 'memberTimeout'
  | 'memberTimeoutRemove'
  | 'memberBan'
  | 'memberUnban'
  | 'memberWarning'
  | 'memberPresenceUpdate'
  | 'memberVoiceUpdate';

/**
 * Member event data
 */
export interface MemberEvent {
  type: MemberEventType;
  guildId: Snowflake;
  userId: Snowflake;
  timestamp: Date;
  data: Record<string, any>;
  moderatorId?: Snowflake;
  reason?: string;
}

// ====== RESPONSE TYPES ======

/**
 * Member list response with pagination
 */
export interface MemberListResponse {
  members: MemberInfo[];
  total: number;
  page: number;
  pageSize: number;
  hasMore: boolean;
  filters: MemberSearchOptions;
  stats: {
    totalMatching: number;
    byRole: Record<Snowflake, number>;
    byStatus: Record<PresenceStatus, number>;
  };
}

/**
 * Member operation result
 */
export interface MemberOperationResult {
  success: boolean;
  userId: Snowflake;
  action: string;
  changes: Record<string, { old: any; new: any }>;
  reason?: string;
  error?: string;
  timestamp: Date;
}

/**
 * Bulk operation result
 */
export interface BulkMemberOperationResult {
  totalProcessed: number;
  successful: MemberOperationResult[];
  failed: MemberOperationResult[];
  summary: {
    successCount: number;
    failureCount: number;
    errors: Record<string, number>;
  };
  completedAt: Date;
}

// ====== PERMISSION HELPERS ======

/**
 * Permission check result
 */
export interface PermissionCheckResult {
  hasPermission: boolean;
  reason?: string;
  requiredPermissions: PermissionsString[];
  currentPermissions: PermissionsString[];
  missingPermissions: PermissionsString[];
  deniedPermissions: PermissionsString[];
  source: 'role' | 'user' | 'channel' | 'category';
}

/**
 * Role permission analysis
 */
export interface RolePermissionAnalysis {
  role: EnhancedDiscordRole;
  permissions: PermissionsString[];
  dangerous: PermissionsString[];
  administrative: PermissionsString[];
  moderative: PermissionsString[];
  basic: PermissionsString[];
  memberCount: number;
  canAssignRoles: Snowflake[];
  risksAndWarnings: string[];
}

// ====== VALIDATION TYPES ======

/**
 * Member operation validation
 */
export interface MemberOperationValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  canProceed: boolean;
  requiresConfirmation: boolean;
  affectedMembers: number;
  estimatedTime?: number;
}

/**
 * Role hierarchy validation
 */
export interface RoleHierarchyValidation {
  isValid: boolean;
  canManage: boolean;
  higherRoles: Snowflake[];
  conflicts: string[];
  warnings: string[];
  recommendations: string[];
}

// ====== EXPORT HELPER TYPES ======

/**
 * Member export format
 */
export type MemberExportFormat = 'csv' | 'json' | 'xlsx';

/**
 * Member export options
 */
export interface MemberExportOptions {
  format: MemberExportFormat;
  includeRoles: boolean;
  includePermissions: boolean;
  includeActivity: boolean;
  includeInfractions: boolean;
  dateRange?: {
    start: Date;
    end: Date;
  };
  filters?: MemberSearchOptions;
}

/**
 * Member import result
 */
export interface MemberImportResult {
  processed: number;
  successful: number;
  failed: number;
  skipped: number;
  errors: Array<{
    row: number;
    error: string;
    data: Record<string, any>;
  }>;
  warnings: string[];
  summary: Record<string, any>;
}

// ====== UTILITY FUNCTIONS TYPE DEFINITIONS ======

/**
 * Member comparison function type
 */
export type MemberComparator = (a: MemberInfo, b: MemberInfo) => number;

/**
 * Member filter function type
 */
export type MemberFilter = (member: MemberInfo) => boolean;

/**
 * Member transformer function type
 */
export type MemberTransformer<T> = (member: MemberInfo) => T;

/**
 * Permission calculator function type
 */
export type PermissionCalculator = (
  member: MemberInfo, 
  channel?: Snowflake
) => MemberPermissions;