import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

// Generic API Response Interface
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
  timestamp: string;
}

// Pagination interface
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Health check interfaces
export interface HealthCheck {
  name: string;
  status: 'healthy' | 'degraded' | 'unhealthy' | 'critical';
  responseTime: number | null;
  message: string;
  error?: string;
  details?: Record<string, any>;
}

export interface SystemHealth {
  status: 'healthy' | 'degraded' | 'unhealthy' | 'critical';
  timestamp: Date;
  responseTime: number;
  uptime: number;
  version: string;
  environment: string;
  checks: {
    database: HealthCheck;
    redis: HealthCheck;
    memory: HealthCheck;
    disk: HealthCheck;
  };
  error?: string;
}

// Metrics interfaces
export interface MemoryMetrics {
  rss: number;
  heapTotal: number;
  heapUsed: number;
  external: number;
  arrayBuffers: number;
}

export interface CpuMetrics {
  user: number;
  system: number;
}

export interface ProcessMetrics {
  pid: number;
  version: string;
  platform: string;
  arch: string;
}

export interface SystemMetrics {
  timestamp: Date;
  uptime: number;
  memory: MemoryMetrics;
  cpu: CpuMetrics;
  process: ProcessMetrics;
}

// Session interfaces
export interface SessionInfo {
  sessionId: string;
  userId: string;
  username: string;
  createdAt: Date;
  lastActivity: Date;
  ipAddress: string;
}

// Statistics interfaces
export interface UserStats {
  totalUsers: number;
  activeUsers: number;
  premiumUsers: number;
}

export interface GuildStats {
  totalGuilds: number;
  activeGuilds: number;
  premiumGuilds: number;
}

// Analytics interfaces
export interface UserAnalytics {
  total: number;
  active24h: number;
  active7d: number;
  active30d: number;
  premium: number;
  new?: number;
  active?: number;
}

export interface GuildAnalytics {
  total: number;
  active24h: number;
  active7d: number;
  active30d: number;
  premium: number;
  new?: number;
  active?: number;
}

export interface CommandAnalytics {
  total24h: number;
  total7d: number;
  total30d: number;
}

export interface AIAnalytics {
  interactions24h: number;
  interactions7d: number;
  interactions30d: number;
  tokensUsed30d: number;
}

export interface ActivityAnalytics {
  commands: number;
  aiInteractions: number;
  messages: number;
}

export interface Analytics {
  users: UserAnalytics;
  guilds: GuildAnalytics;
  commands: CommandAnalytics;
  ai: AIAnalytics;
}

export interface PeriodAnalytics {
  period: string;
  startDate: Date;
  endDate: Date;
  users: UserAnalytics;
  guilds: GuildAnalytics;
  activity: ActivityAnalytics;
}

// System log interfaces
export interface SystemLog {
  timestamp: Date;
  level: string;
  message: string;
  source: string;
  data?: Record<string, any>;
}

// Configuration interfaces
export interface SystemFeatures {
  maintenance: boolean;
  ai_enabled: boolean;
  premium_enabled: boolean;
}

export interface SystemLimits {
  max_users_per_guild: number;
  max_channels_per_guild: number;
  rate_limit: number;
}

export interface SystemConfig {
  environment: string;
  version: string;
  features: SystemFeatures;
  limits: SystemLimits;
}

// Error response interfaces
export interface ErrorResponse {
  success: false;
  message: string;
  error: string;
  statusCode: number;
  timestamp: string;
  path?: string;
}

// Success response interfaces
export interface SuccessResponse<T = any> {
  success: true;
  message: string;
  data: T;
  timestamp: string;
}

// Operation response interfaces
export interface OperationResult {
  success: boolean;
  message: string;
  timestamp: Date;
}