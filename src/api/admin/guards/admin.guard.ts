import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class AdminGuard implements CanActivate {
  private readonly adminUserIds: string[]

  constructor(private readonly configService: ConfigService) {this.adminUserIds = this.configService.get<string>('ADMIN_USER_IDS')?.split(',') || []}

  canActivate(context: ExecutionContext): boolean {const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user || !this.adminUserIds.includes(user.userId)) {
      throw new ForbiddenException('Admin permissions required')}

    return true};
};