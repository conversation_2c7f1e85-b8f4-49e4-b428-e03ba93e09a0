import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsBoolean, IsOptional, IsNumber, IsEnum, IsDateString, ValidateNested, IsObject, Min, Max } from 'class-validator';
import { Type } from 'class-transformer';

// Base response types
export class BaseResponse {
  @ApiProperty({ description: 'Timestamp of the response' })
  timestamp: Date;
}

export class OperationResponse extends BaseResponse {
  @ApiProperty({ description: 'Whether the operation was successful' })
  success: boolean;

  @ApiProperty({ description: 'Operation message' })
  message: string;
}

// Session DTOs
export class SessionDto {
  @ApiProperty({ description: 'Session ID' })
  sessionId: string;

  @ApiProperty({ description: 'User ID' })
  userId: string;

  @ApiProperty({ description: 'Username' })
  username: string;

  @ApiProperty({ description: 'Session creation timestamp' })
  createdAt: Date;

  @ApiProperty({ description: 'Last activity timestamp' })
  lastActivity: Date;

  @ApiProperty({ description: 'IP address' })
  ipAddress: string;
}

export class SessionsResponse extends BaseResponse {
  @ApiProperty({ type: [SessionDto], description: 'Active sessions list' })
  sessions: SessionDto[];

  @ApiProperty({ description: 'Total number of sessions' })
  total: number;
}

// Stats DTOs
export class UserStatsDto {
  @ApiProperty({ description: 'Total number of users' })
  totalUsers: number;

  @ApiProperty({ description: 'Number of active users (24h)' })
  activeUsers: number;

  @ApiProperty({ description: 'Number of premium users' })
  premiumUsers: number;
}

export class UserStatsResponse extends BaseResponse {
  @ApiProperty({ type: UserStatsDto })
  stats: UserStatsDto;
}

export class GuildStatsDto {
  @ApiProperty({ description: 'Total number of guilds' })
  totalGuilds: number;

  @ApiProperty({ description: 'Number of active guilds (24h)' })
  activeGuilds: number;

  @ApiProperty({ description: 'Number of premium guilds' })
  premiumGuilds: number;
}

export class GuildStatsResponse extends BaseResponse {
  @ApiProperty({ type: GuildStatsDto })
  stats: GuildStatsDto;
}

// Health check DTOs
export class HealthCheckDto {
  @ApiProperty({ description: 'Service name' })
  name: string;

  @ApiProperty({ enum: ['healthy', 'degraded', 'unhealthy', 'critical'], description: 'Health status' })
  status: 'healthy' | 'degraded' | 'unhealthy' | 'critical';

  @ApiProperty({ description: 'Response time in milliseconds' })
  responseTime: number | null;

  @ApiProperty({ description: 'Health check message' })
  message: string;

  @ApiPropertyOptional({ description: 'Error message if unhealthy' })
  error?: string;

  @ApiPropertyOptional({ description: 'Additional details' })
  details?: Record<string, any>;
}

export class SystemHealthDto {
  @ApiProperty({ enum: ['healthy', 'degraded', 'unhealthy', 'critical'], description: 'Overall system status' })
  status: 'healthy' | 'degraded' | 'unhealthy' | 'critical';

  @ApiProperty({ description: 'Health check timestamp' })
  timestamp: Date;

  @ApiProperty({ description: 'Response time in milliseconds' })
  responseTime: number;

  @ApiProperty({ description: 'System uptime in seconds' })
  uptime: number;

  @ApiProperty({ description: 'Application version' })
  version: string;

  @ApiProperty({ description: 'Environment' })
  environment: string;

  @ApiProperty({ description: 'Individual health checks' })
  checks: {
    database: HealthCheckDto;
    redis: HealthCheckDto;
    memory: HealthCheckDto;
    disk: HealthCheckDto;
  };

  @ApiPropertyOptional({ description: 'Error message if system is unhealthy' })
  error?: string;
}

export class SystemHealthResponse extends BaseResponse {
  @ApiProperty({ type: SystemHealthDto })
  health: SystemHealthDto;
}

// Metrics DTOs
export class MemoryMetricsDto {
  @ApiProperty({ description: 'RSS memory usage in bytes' })
  rss: number;

  @ApiProperty({ description: 'Total heap size in bytes' })
  heapTotal: number;

  @ApiProperty({ description: 'Used heap size in bytes' })
  heapUsed: number;

  @ApiProperty({ description: 'External memory usage in bytes' })
  external: number;

  @ApiProperty({ description: 'Array buffers memory usage in bytes' })
  arrayBuffers: number;
}

export class CpuMetricsDto {
  @ApiProperty({ description: 'User CPU time in microseconds' })
  user: number;

  @ApiProperty({ description: 'System CPU time in microseconds' })
  system: number;
}

export class ProcessMetricsDto {
  @ApiProperty({ description: 'Process ID' })
  pid: number;

  @ApiProperty({ description: 'Node.js version' })
  version: string;

  @ApiProperty({ description: 'Operating system platform' })
  platform: string;

  @ApiProperty({ description: 'CPU architecture' })
  arch: string;
}

export class SystemMetricsDto {
  @ApiProperty({ description: 'Metrics timestamp' })
  timestamp: Date;

  @ApiProperty({ description: 'System uptime in seconds' })
  uptime: number;

  @ApiProperty({ type: MemoryMetricsDto, description: 'Memory usage metrics' })
  memory: MemoryMetricsDto;

  @ApiProperty({ type: CpuMetricsDto, description: 'CPU usage metrics' })
  cpu: CpuMetricsDto;

  @ApiProperty({ type: ProcessMetricsDto, description: 'Process information' })
  process: ProcessMetricsDto;
}

export class SystemMetricsResponse extends BaseResponse {
  @ApiProperty({ type: SystemMetricsDto })
  metrics: SystemMetricsDto;
}

// Analytics DTOs
export class UserAnalyticsDto {
  @ApiProperty({ description: 'Total users' })
  total: number;

  @ApiProperty({ description: 'Active users (24h)' })
  active24h: number;

  @ApiProperty({ description: 'Active users (7d)' })
  active7d: number;

  @ApiProperty({ description: 'Active users (30d)' })
  active30d: number;

  @ApiProperty({ description: 'Premium users' })
  premium: number;

  @ApiPropertyOptional({ description: 'New users in period' })
  new?: number;

  @ApiPropertyOptional({ description: 'Active users in custom period' })
  active?: number;
}

export class GuildAnalyticsDto {
  @ApiProperty({ description: 'Total guilds' })
  total: number;

  @ApiProperty({ description: 'Active guilds (24h)' })
  active24h: number;

  @ApiProperty({ description: 'Active guilds (7d)' })
  active7d: number;

  @ApiProperty({ description: 'Active guilds (30d)' })
  active30d: number;

  @ApiProperty({ description: 'Premium guilds' })
  premium: number;

  @ApiPropertyOptional({ description: 'New guilds in period' })
  new?: number;

  @ApiPropertyOptional({ description: 'Active guilds in custom period' })
  active?: number;
}

export class CommandAnalyticsDto {
  @ApiProperty({ description: 'Commands in last 24 hours' })
  total24h: number;

  @ApiProperty({ description: 'Commands in last 7 days' })
  total7d: number;

  @ApiProperty({ description: 'Commands in last 30 days' })
  total30d: number;
}

export class AIAnalyticsDto {
  @ApiProperty({ description: 'AI interactions in last 24 hours' })
  interactions24h: number;

  @ApiProperty({ description: 'AI interactions in last 7 days' })
  interactions7d: number;

  @ApiProperty({ description: 'AI interactions in last 30 days' })
  interactions30d: number;

  @ApiProperty({ description: 'Tokens used in last 30 days' })
  tokensUsed30d: number;
}

export class ActivityAnalyticsDto {
  @ApiProperty({ description: 'Command count' })
  commands: number;

  @ApiProperty({ description: 'AI interaction count' })
  aiInteractions: number;

  @ApiProperty({ description: 'Message count' })
  messages: number;
}

export class AnalyticsDto {
  @ApiProperty({ type: UserAnalyticsDto, description: 'User analytics' })
  users: UserAnalyticsDto;

  @ApiProperty({ type: GuildAnalyticsDto, description: 'Guild analytics' })
  guilds: GuildAnalyticsDto;

  @ApiProperty({ type: CommandAnalyticsDto, description: 'Command analytics' })
  commands: CommandAnalyticsDto;

  @ApiProperty({ type: AIAnalyticsDto, description: 'AI analytics' })
  ai: AIAnalyticsDto;
}

export class AnalyticsResponse extends BaseResponse {
  @ApiProperty({ type: AnalyticsDto })
  analytics: AnalyticsDto;
}

export class PeriodAnalyticsDto {
  @ApiProperty({ description: 'Analytics period' })
  period: string;

  @ApiProperty({ description: 'Start date of the period' })
  startDate: Date;

  @ApiProperty({ description: 'End date of the period' })
  endDate: Date;

  @ApiProperty({ type: UserAnalyticsDto, description: 'User analytics for period' })
  users: UserAnalyticsDto;

  @ApiProperty({ type: GuildAnalyticsDto, description: 'Guild analytics for period' })
  guilds: GuildAnalyticsDto;

  @ApiProperty({ type: ActivityAnalyticsDto, description: 'Activity analytics for period' })
  activity: ActivityAnalyticsDto;
}

export class PeriodAnalyticsResponse extends BaseResponse {
  @ApiProperty({ type: PeriodAnalyticsDto })
  analytics: PeriodAnalyticsDto;
}

// System Log DTOs
export class SystemLogDto {
  @ApiProperty({ description: 'Log timestamp' })
  timestamp: Date;

  @ApiProperty({ description: 'Log level' })
  level: string;

  @ApiProperty({ description: 'Log message' })
  message: string;

  @ApiProperty({ description: 'Log source' })
  source: string;

  @ApiPropertyOptional({ description: 'Additional log data' })
  data?: Record<string, any>;
}

export class SystemLogsResponse extends BaseResponse {
  @ApiProperty({ type: [SystemLogDto], description: 'System logs' })
  logs: SystemLogDto[];

  @ApiProperty({ description: 'Total number of logs' })
  total: number;
}

// Config DTOs
export class SystemFeaturesDto {
  @ApiProperty({ description: 'Maintenance mode status' })
  maintenance: boolean;

  @ApiProperty({ description: 'AI features enabled' })
  ai_enabled: boolean;

  @ApiProperty({ description: 'Premium features enabled' })
  premium_enabled: boolean;
}

export class SystemLimitsDto {
  @ApiProperty({ description: 'Maximum users per guild' })
  max_users_per_guild: number;

  @ApiProperty({ description: 'Maximum channels per guild' })
  max_channels_per_guild: number;

  @ApiProperty({ description: 'Rate limit per minute' })
  rate_limit: number;
}

export class SystemConfigDto {
  @ApiProperty({ description: 'Application environment' })
  environment: string;

  @ApiProperty({ description: 'Application version' })
  version: string;

  @ApiProperty({ type: SystemFeaturesDto, description: 'Feature flags' })
  features: SystemFeaturesDto;

  @ApiProperty({ type: SystemLimitsDto, description: 'System limits' })
  limits: SystemLimitsDto;
}

export class SystemConfigResponse extends BaseResponse {
  @ApiProperty({ type: SystemConfigDto })
  config: SystemConfigDto;
}

// Request DTOs
export enum AnalyticsPeriod {
  HOUR = 'hour',
  DAY = 'day',
  WEEK = 'week',
  MONTH = 'month',
  YEAR = 'year'
}

export class UpdateUserStatusDto {
  @ApiProperty({ description: 'New user status' })
  @IsString()
  status: string;

  @ApiPropertyOptional({ description: 'Reason for status change' })
  @IsString()
  @IsOptional()
  reason?: string;
}

export class UpdateSystemConfigDto {
  @ApiPropertyOptional({ type: SystemFeaturesDto, description: 'Feature configuration updates' })
  @ValidateNested()
  @Type(() => SystemFeaturesDto)
  @IsOptional()
  features?: Partial<SystemFeaturesDto>;

  @ApiPropertyOptional({ type: SystemLimitsDto, description: 'Limits configuration updates' })
  @ValidateNested()
  @Type(() => SystemLimitsDto)
  @IsOptional()
  limits?: Partial<SystemLimitsDto>;
}

export class LogQueryDto {
  @ApiPropertyOptional({ description: 'Log level filter' })
  @IsString()
  @IsOptional()
  level?: string;

  @ApiPropertyOptional({ description: 'Log source filter' })
  @IsString()
  @IsOptional()
  source?: string;

  @ApiPropertyOptional({ description: 'Start date for logs' })
  @IsDateString()
  @IsOptional()
  startDate?: string;

  @ApiPropertyOptional({ description: 'End date for logs' })
  @IsDateString()
  @IsOptional()
  endDate?: string;

  @ApiPropertyOptional({ description: 'Maximum number of logs to return', minimum: 1, maximum: 1000 })
  @IsNumber()
  @Min(1)
  @Max(1000)
  @IsOptional()
  limit?: number;
}

// User interface from JWT payload
export interface AuthenticatedUser {
  id: string;
  userId: string;
  username: string;
  discriminator?: string;
  avatar?: string;
  roles: string[];
  guilds?: string[];
  permissions?: string[];
  iat?: number;
  exp?: number;
}

// Express request with user
export interface AuthenticatedRequest extends Request {
  user: AuthenticatedUser;
}