import { Injectable, Logger, ForbiddenException, InternalServerErrorException, BadRequestException } from '@nestjs/common';
import { DatabaseService } from '../../core/database/database.service';
import { CacheService } from '../../core/cache/cache.service';
import { SessionService } from '../../core/security/session.service';
import { DiscordService } from '../../core/services/discord.service';
import { UserRepository } from '../../core/database/repositories/user.repository';
import { GuildRepository } from '../../core/database/repositories/guild.repository';
import { SessionRepository } from '../../core/database/repositories/session.repository';
import { User } from '../../core/database/entities/user.entity';
import { Guild } from '../../core/database/entities/guild.entity';
import { Session } from '../../core/database/entities/session.entity';
import { RedisOperationResult } from '../../core/database/types/redis.types';

/**
 * Admin-specific interfaces for type safety
 */
export interface AdminUser extends User {
  roles: string[];
  permissions?: string[];
  lastAccessedAt?: Date;
}

export interface SessionSummary {
  sessionId: string;
  userId: string;
  username: string;
  createdAt: Date;
  lastActivity?: Date;
  ipAddress?: string;
  deviceType?: string;
  isActive: boolean;
}

export interface UserStatistics {
  totalUsers: number;
  activeUsers: number;
  premiumUsers: number;
  newUsersToday: number;
  timestamp: Date;
}

export interface GuildStatistics {
  totalGuilds: number;
  activeGuilds: number;
  premiumGuilds: number;
  newGuildsToday: number;
  timestamp: Date;
}

export interface SystemLogEntry {
  id: string;
  timestamp: Date;
  level: 'error' | 'warn' | 'info' | 'debug';
  message: string;
  source: string;
  metadata?: Record<string, any>;
}

export interface SystemConfiguration {
  environment: string;
  version: string;
  features: {
    maintenance: boolean;
    aiEnabled: boolean;
    premiumEnabled: boolean;
    debugMode: boolean;
  };
  limits: {
    maxUsersPerGuild: number;
    maxChannelsPerGuild: number;
    rateLimit: number;
    maxSessionDuration: number;
  };
  security: {
    sessionTimeout: number;
    maxFailedAttempts: number;
    requireMfa: boolean;
  };
}

export interface UserStatusUpdate {
  status: 'active' | 'suspended' | 'banned' | 'inactive';
  reason?: string;
  expiresAt?: Date;
}

export interface CacheStatistics {
  totalKeys: number;
  memoryUsage: number;
  hitRate: number;
  missRate: number;
  evictions: number;
}

@Injectable()
export class AdminService {
  private readonly logger = new Logger(AdminService.name);

  constructor(
    private readonly databaseService: DatabaseService,
    private readonly cacheService: CacheService,
    private readonly sessionService: SessionService,
    private readonly discordService: DiscordService,
    private readonly userRepository: UserRepository,
    private readonly guildRepository: GuildRepository,
    private readonly sessionRepository: SessionRepository,
  ) {}

  async getActiveSessions(adminUser: AdminUser): Promise<SessionSummary[]> {
    this.checkAdminPermissions(adminUser);
    
    try {
      // Get active sessions with pagination
      const sessions = await this.sessionRepository.findByField('isRevoked', false, { limit: 100 });
      const sessionSummaries: SessionSummary[] = [];
      
      for (const session of sessions) {
        const user = await this.userRepository.findById(session.userId);
        const deviceType = session.metadata?.deviceType || 'unknown';
        
        sessionSummaries.push({
          sessionId: session.sessionId,
          userId: session.userId,
          username: user?.username || 'Unknown',
          createdAt: session.createdAt,
          lastActivity: session.lastAccessedAt,
          ipAddress: session.ipAddress,
          deviceType,
          isActive: !session.isRevoked && session.expiresAt > new Date(),
        });
      }
      
      return sessionSummaries.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
    } catch (error) {
      this.logger.error('Error getting active sessions:', error);
      throw new InternalServerErrorException('Failed to retrieve active sessions');
    }
  }

  async getUserStats(adminUser: AdminUser): Promise<UserStatistics> {
    this.checkAdminPermissions(adminUser);
    
    try {
      const now = new Date();
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      
      // Get all users for analysis
      const allUsers = await this.userRepository.findAll({ limit: 10000 });
      
      const totalUsers = allUsers.length;
      const activeUsers = allUsers.filter(user => 
        user.lastActivityAt && user.lastActivityAt > oneDayAgo
      ).length;
      const premiumUsers = allUsers.filter(user => 
        user.preferences?.balance && user.preferences.balance > 0
      ).length;
      const newUsersToday = allUsers.filter(user => 
        user.createdAt > oneDayAgo
      ).length;
      
      const stats: UserStatistics = {
        totalUsers,
        activeUsers,
        premiumUsers,
        newUsersToday,
        timestamp: now,
      };
      
      // Cache stats for 5 minutes
      await this.cacheService.set(
        'admin:user_stats',
        JSON.stringify(stats),
        300
      );
      
      return stats;
    } catch (error) {
      this.logger.error('Error getting user stats:', error);
      throw new InternalServerErrorException('Failed to retrieve user statistics');
    }
  }

  async getGuildStats(adminUser: AdminUser): Promise<GuildStatistics> {
    this.checkAdminPermissions(adminUser);
    
    try {
      const now = new Date();
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      
      // Get all guilds for analysis
      const allGuilds = await this.guildRepository.findAll({ limit: 5000 });
      
      const totalGuilds = allGuilds.length;
      const activeGuilds = allGuilds.filter(guild => 
        guild.lastActivityAt && guild.lastActivityAt > oneDayAgo
      ).length;
      const premiumGuilds = allGuilds.filter(guild => 
        guild.features?.premium?.enabled === true
      ).length;
      const newGuildsToday = allGuilds.filter(guild => 
        guild.createdAt > oneDayAgo
      ).length;
      
      const stats: GuildStatistics = {
        totalGuilds,
        activeGuilds,
        premiumGuilds,
        newGuildsToday,
        timestamp: now,
      };
      
      // Cache stats for 5 minutes
      await this.cacheService.set(
        'admin:guild_stats',
        JSON.stringify(stats),
        300
      );
      
      return stats;
    } catch (error) {
      this.logger.error('Error getting guild stats:', error);
      throw new InternalServerErrorException('Failed to retrieve guild statistics');
    }
  }

  async terminateSession(sessionId: string, adminUser: AdminUser, reason?: string): Promise<void> {
    this.checkAdminPermissions(adminUser);
    
    if (!sessionId || typeof sessionId !== 'string') {
      throw new BadRequestException('Valid session ID is required');
    }
    
    try {
      // Verify session exists
      const session = await this.sessionRepository.findByField('sessionId', sessionId);
      if (!session.length) {
        throw new BadRequestException('Session not found');
      }
      
      // Revoke session
      await this.sessionService.revokeSession(sessionId);
      
      // Log admin action
      this.logger.log(
        `Admin ${adminUser.username} (${adminUser.id}) terminated session ${sessionId}${reason ? ` - Reason: ${reason}` : ''}`
      );
      
      // Audit log
      await this.logAdminAction(adminUser, 'session_terminate', {
        sessionId,
        targetSession: session[0],
        reason,
      });
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      this.logger.error('Error terminating session:', error);
      throw new InternalServerErrorException('Failed to terminate session');
    }
  }

  async clearCache(adminUser: AdminUser, cacheKeys?: string[]): Promise<CacheStatistics> {
    this.checkAdminPermissions(adminUser);
    
    try {
      let clearedKeys = 0;
      
      if (cacheKeys && cacheKeys.length > 0) {
        // Clear specific cache keys
        for (const key of cacheKeys) {
          const result = await this.cacheService.del(key);
          if (result) clearedKeys++;
        }
      } else {
        // Get cache stats before clearing
        const beforeStats = await this.getCacheStatistics();
        
        // Clear all cache
        await this.cacheService.reset();
        clearedKeys = beforeStats.totalKeys;
      }
      
      // Get updated cache stats
      const afterStats = await this.getCacheStatistics();
      
      this.logger.log(
        `Admin ${adminUser.username} (${adminUser.id}) cleared ${clearedKeys} cache keys`
      );
      
      // Audit log
      await this.logAdminAction(adminUser, 'cache_clear', {
        clearedKeys,
        specificKeys: cacheKeys,
        beforeStats: cacheKeys ? null : afterStats, // Don't log full stats for partial clears
      });
      
      return afterStats;
    } catch (error) {
      this.logger.error('Error clearing cache:', error);
      throw new InternalServerErrorException('Failed to clear cache');
    }
  }

  async startMaintenance(adminUser: AdminUser, duration?: number, message?: string): Promise<void> {
    this.checkAdminPermissions(adminUser);
    
    try {
      const maintenanceData = {
        enabled: true,
        startedAt: new Date().toISOString(),
        startedBy: adminUser.id,
        duration: duration || 0, // 0 means indefinite
        message: message || 'System maintenance in progress',
        expiresAt: duration ? new Date(Date.now() + duration * 1000).toISOString() : null,
      };
      
      await this.cacheService.set(
        'system:maintenance',
        JSON.stringify(maintenanceData),
        duration || 0
      );
      
      this.logger.warn(
        `Admin ${adminUser.username} (${adminUser.id}) started maintenance mode${duration ? ` for ${duration} seconds` : ' (indefinite)'}`
      );
      
      // Audit log
      await this.logAdminAction(adminUser, 'maintenance_start', maintenanceData);
    } catch (error) {
      this.logger.error('Error starting maintenance:', error);
      throw new InternalServerErrorException('Failed to start maintenance mode');
    }
  }

  async stopMaintenance(adminUser: AdminUser): Promise<void> {
    this.checkAdminPermissions(adminUser);
    
    try {
      // Get current maintenance data before removing
      const maintenanceDataStr = await this.cacheService.get('system:maintenance');
      const maintenanceData = maintenanceDataStr ? JSON.parse(maintenanceDataStr) : null;
      
      await this.cacheService.del('system:maintenance');
      
      this.logger.log(
        `Admin ${adminUser.username} (${adminUser.id}) stopped maintenance mode`
      );
      
      // Audit log
      await this.logAdminAction(adminUser, 'maintenance_stop', {
        stoppedAt: new Date().toISOString(),
        previousMaintenanceData: maintenanceData,
      });
    } catch (error) {
      this.logger.error('Error stopping maintenance:', error);
      throw new InternalServerErrorException('Failed to stop maintenance mode');
    }
  }

  async updateUserStatus(userId: string, statusUpdate: UserStatusUpdate, adminUser: AdminUser): Promise<User> {
    this.checkAdminPermissions(adminUser);
    
    if (!userId || typeof userId !== 'string') {
      throw new BadRequestException('Valid user ID is required');
    }
    
    if (!statusUpdate.status || !['active', 'suspended', 'banned', 'inactive'].includes(statusUpdate.status)) {
      throw new BadRequestException('Valid status is required: active, suspended, banned, or inactive');
    }
    
    try {
      // Get current user
      const user = await this.userRepository.findById(userId);
      if (!user) {
        throw new BadRequestException('User not found');
      }
      
      // Create update data with proper typing
      const updateData = {
        preferences: {
          ...user.preferences,
          adminStatus: {
            status: statusUpdate.status,
            reason: statusUpdate.reason,
            updatedBy: adminUser.id,
            updatedAt: new Date().toISOString(),
            expiresAt: statusUpdate.expiresAt?.toISOString(),
          },
        },
        updatedAt: new Date(),
      };
      
      const updatedUser = await this.userRepository.update(userId, updateData);
      
      this.logger.log(
        `Admin ${adminUser.username} (${adminUser.id}) updated status for user ${user.username} (${userId}) to ${statusUpdate.status}${statusUpdate.reason ? ` - Reason: ${statusUpdate.reason}` : ''}`
      );
      
      // Audit log
      await this.logAdminAction(adminUser, 'user_status_update', {
        targetUserId: userId,
        targetUsername: user.username,
        previousStatus: user.preferences?.adminStatus?.status || 'active',
        newStatus: statusUpdate.status,
        reason: statusUpdate.reason,
        expiresAt: statusUpdate.expiresAt,
      });
      
      return updatedUser;
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      this.logger.error('Error updating user status:', error);
      throw new InternalServerErrorException('Failed to update user status');
    }
  }

  async getSystemLogs(
    adminUser: AdminUser,
    query?: {
      level?: string;
      source?: string;
      startDate?: string;
      endDate?: string;
      limit?: number;
    }
  ): Promise<SystemLogEntry[]> {
    this.checkAdminPermissions(adminUser);
    
    try {
      const { level, source, startDate, endDate, limit = 100 } = query || {};
      
      // Get system logs from cache/storage
      const logsKey = 'system:logs';
      const logsData = await this.cacheService.get(logsKey);
      let logs: SystemLogEntry[] = logsData ? JSON.parse(logsData) : [];
      
      // Filter logs based on criteria
      logs = logs.filter(log => {
        if (level && log.level !== level) return false;
        if (source && log.source !== source) return false;
        if (startDate && new Date(log.timestamp) < new Date(startDate)) return false;
        if (endDate && new Date(log.timestamp) > new Date(endDate)) return false;
        return true;
      });
      
      // Sort by timestamp (newest first) and limit
      logs = logs
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
        .slice(0, limit);
      
      // Add current access log
      const accessLog: SystemLogEntry = {
        id: `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date(),
        level: 'info',
        message: `System logs accessed by admin ${adminUser.username}`,
        source: 'AdminService',
        metadata: {
          adminId: adminUser.id,
          filters: { level, source, startDate, endDate },
          resultCount: logs.length,
        },
      };
      
      logs.unshift(accessLog);
      
      // Update logs cache
      await this.updateSystemLogs(logs.slice(0, 1000)); // Keep last 1000 logs
      
      return logs;
    } catch (error) {
      this.logger.error('Error getting system logs:', error);
      throw new InternalServerErrorException('Failed to retrieve system logs');
    }
  }

  async getSystemConfig(adminUser: AdminUser): Promise<SystemConfiguration> {
    this.checkAdminPermissions(adminUser);
    
    try {
      const maintenanceData = await this.cacheService.get('system:maintenance');
      const maintenanceInfo = maintenanceData ? JSON.parse(maintenanceData) : null;
      
      const config: SystemConfiguration = {
        environment: process.env.NODE_ENV || 'development',
        version: process.env.APP_VERSION || '1.0.0',
        features: {
          maintenance: !!maintenanceInfo?.enabled,
          aiEnabled: process.env.AI_ENABLED !== 'false',
          premiumEnabled: process.env.PREMIUM_ENABLED !== 'false',
          debugMode: process.env.DEBUG_MODE === 'true',
        },
        limits: {
          maxUsersPerGuild: parseInt(process.env.MAX_USERS_PER_GUILD || '1000'),
          maxChannelsPerGuild: parseInt(process.env.MAX_CHANNELS_PER_GUILD || '500'),
          rateLimit: parseInt(process.env.RATE_LIMIT || '100'),
          maxSessionDuration: parseInt(process.env.SESSION_DURATION || '86400'), // 24 hours
        },
        security: {
          sessionTimeout: parseInt(process.env.SESSION_TIMEOUT || '3600'), // 1 hour
          maxFailedAttempts: parseInt(process.env.MAX_FAILED_ATTEMPTS || '5'),
          requireMfa: process.env.REQUIRE_MFA === 'true',
        },
      };
      
      // Log access
      this.logger.log(
        `Admin ${adminUser.username} (${adminUser.id}) accessed system configuration`
      );
      
      return config;
    } catch (error) {
      this.logger.error('Error getting system config:', error);
      throw new InternalServerErrorException('Failed to retrieve system configuration');
    }
  }

  async updateSystemConfig(
    configUpdate: Partial<SystemConfiguration>,
    adminUser: AdminUser
  ): Promise<SystemConfiguration> {
    this.checkAdminPermissions(adminUser);
    
    try {
      const updatedFields: string[] = [];
      
      // Update maintenance mode
      if (configUpdate.features?.maintenance !== undefined) {
        if (configUpdate.features.maintenance) {
          await this.startMaintenance(adminUser, 0, 'Maintenance enabled via system config');
        } else {
          await this.stopMaintenance(adminUser);
        }
        updatedFields.push('maintenance');
      }
      
      // Update other configuration values in cache
      if (configUpdate.limits) {
        await this.cacheService.set(
          'system:config:limits',
          JSON.stringify(configUpdate.limits),
          0
        );
        updatedFields.push('limits');
      }
      
      if (configUpdate.security) {
        await this.cacheService.set(
          'system:config:security',
          JSON.stringify(configUpdate.security),
          0
        );
        updatedFields.push('security');
      }
      
      this.logger.log(
        `Admin ${adminUser.username} (${adminUser.id}) updated system configuration: ${updatedFields.join(', ')}`
      );
      
      // Audit log
      await this.logAdminAction(adminUser, 'config_update', {
        updatedFields,
        configUpdate,
      });
      
      // Return updated config
      return await this.getSystemConfig(adminUser);
    } catch (error) {
      this.logger.error('Error updating system config:', error);
      throw new InternalServerErrorException('Failed to update system configuration');
    }
  }

  /**
   * Get cache statistics
   */
  private async getCacheStatistics(): Promise<CacheStatistics> {
    try {
      const redis = this.cacheService.redis?.getClient();
      if (!redis) {
        throw new Error('Redis client not available');
      }
      
      const info = await redis.info('stats');
      const dbsize = await redis.dbsize();
      const memoryInfo = await redis.info('memory');
      
      const statsLines = info.split('\r\n');
      const memoryLines = memoryInfo.split('\r\n');
      
      const parseValue = (lines: string[], key: string): number => {
        const line = lines.find(l => l.startsWith(key + ':'));
        return line ? parseInt(line.split(':')[1]) || 0 : 0;
      };
      
      const hits = parseValue(statsLines, 'keyspace_hits');
      const misses = parseValue(statsLines, 'keyspace_misses');
      const total = hits + misses;
      
      return {
        totalKeys: dbsize,
        memoryUsage: parseValue(memoryLines, 'used_memory'),
        hitRate: total > 0 ? (hits / total) * 100 : 0,
        missRate: total > 0 ? (misses / total) * 100 : 0,
        evictions: parseValue(statsLines, 'evicted_keys'),
      };
    } catch (error) {
      this.logger.error('Error getting cache statistics:', error);
      return {
        totalKeys: 0,
        memoryUsage: 0,
        hitRate: 0,
        missRate: 0,
        evictions: 0,
      };
    }
  }
  
  /**
   * Update system logs in cache
   */
  private async updateSystemLogs(logs: SystemLogEntry[]): Promise<void> {
    try {
      await this.cacheService.set(
        'system:logs',
        JSON.stringify(logs),
        86400 // 24 hours
      );
    } catch (error) {
      this.logger.error('Error updating system logs cache:', error);
    }
  }
  
  /**
   * Log admin actions for audit trail
   */
  private async logAdminAction(
    adminUser: AdminUser,
    action: string,
    metadata: Record<string, any>
  ): Promise<void> {
    try {
      const auditLog = {
        id: `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date(),
        adminId: adminUser.id,
        adminUsername: adminUser.username,
        action,
        metadata,
      };
      
      // Store in audit logs
      const auditLogsKey = 'system:audit_logs';
      const existingLogsStr = await this.cacheService.get(auditLogsKey);
      const existingLogs = existingLogsStr ? JSON.parse(existingLogsStr) : [];
      
      existingLogs.unshift(auditLog);
      
      // Keep last 1000 audit logs
      const trimmedLogs = existingLogs.slice(0, 1000);
      
      await this.cacheService.set(
        auditLogsKey,
        JSON.stringify(trimmedLogs),
        2592000 // 30 days
      );
    } catch (error) {
      this.logger.error('Error logging admin action:', error);
    }
  }
  
  /**
   * Check admin permissions with enhanced validation
   */
  private checkAdminPermissions(user: AdminUser): void {
    if (!user) {
      throw new ForbiddenException('Authentication required');
    }
    
    if (!user.roles || !Array.isArray(user.roles)) {
      throw new ForbiddenException('Invalid user role structure');
    }
    
    if (!user.roles.includes('admin') && !user.roles.includes('super_admin')) {
      throw new ForbiddenException('Admin access required');
    }
    
    // Check if user is active
    const adminStatus = user.preferences?.adminStatus;
    if (adminStatus && adminStatus.status !== 'active') {
      throw new ForbiddenException(`Admin access suspended: ${adminStatus.reason || 'Unknown reason'}`);
    }
  }
}