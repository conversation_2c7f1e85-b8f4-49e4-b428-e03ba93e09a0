import {
  Injectable,
  NestInterceptor,
  Execution<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Request } from 'express';
import { SuccessResponse } from '../interfaces/admin-response.interface';

@Injectable()
export class AdminResponseInterceptor implements NestInterceptor {
  private readonly logger = new Logger(AdminResponseInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<Request>();
    const startTime = Date.now();
    const user = (request as any).user;

    return next.handle().pipe(
      map((data) => {
        const responseTime = Date.now() - startTime;
        
        // Log admin API access
        this.logger.log(
          `Admin API accessed: ${request.method} ${request.url} - ${responseTime}ms`,
          {
            method: request.method,
            url: request.url,
            user: user?.id,
            username: user?.username,
            responseTime,
            dataSize: JSON.stringify(data).length,
          }
        );

        // If data is already in the expected format, return it
        if (data && typeof data === 'object' && 'success' in data && 'timestamp' in data) {
          return data;
        }

        // Transform raw data into standard response format
        const transformedResponse: SuccessResponse = {
          success: true,
          message: this.getDefaultMessage(request.method, request.url),
          data,
          timestamp: new Date().toISOString(),
        };

        return transformedResponse;
      })
    );
  }

  private getDefaultMessage(method: string, url: string): string {
    const resource = this.extractResourceFromUrl(url);
    
    switch (method) {
      case 'GET':
        return `${resource} retrieved successfully`;
      case 'POST':
        return `${resource} operation completed successfully`;
      case 'PUT':
        return `${resource} updated successfully`;
      case 'DELETE':
        return `${resource} deleted successfully`;
      default:
        return 'Operation completed successfully';
    }
  }

  private extractResourceFromUrl(url: string): string {
    const pathParts = url.split('/').filter(part => part && part !== 'api' && part !== 'admin');
    
    if (pathParts.length === 0) return 'Admin resource';
    
    const resource = pathParts[0];
    
    // Convert common endpoints to readable names
    const resourceMap: Record<string, string> = {
      'sessions': 'Sessions',
      'users': 'User statistics',
      'guilds': 'Guild statistics', 
      'analytics': 'Analytics data',
      'logs': 'System logs',
      'config': 'System configuration',
      'cache': 'Cache operation',
      'maintenance': 'Maintenance mode',
      'system': 'System information',
    };

    return resourceMap[resource] || `${resource.charAt(0).toUpperCase()}${resource.slice(1)}`;
  }
}