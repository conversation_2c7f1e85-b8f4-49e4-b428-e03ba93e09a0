import { Test, TestingModule } from '@nestjs/testing';
import { AdminController } from './admin.controller';
import { AdminService } from './admin.service';
import { SystemHealthService } from './components/system-health.service';
import { AnalyticsService } from './components/analytics.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AdminGuard } from './guards/admin.guard';
import {
  AnalyticsPeriod,
  UpdateUserStatusDto,
  UpdateSystemConfigDto,
  LogQueryDto,
  AuthenticatedRequest,
} from './dto/admin.dto';
import {
  SuccessResponse,
  SessionsResponse,
  UserStatsResponse,
  GuildStatsResponse,
} from './interfaces/admin-response.interface';
import { AdminResponseInterceptor } from './interceptors/admin-response.interceptor';
import { AdminExceptionFilter } from './filters/admin-exception.filter';

describe('AdminController', () => {
  let controller: AdminController;
  let adminService: AdminService;
  let systemHealthService: SystemHealthService;
  let analyticsService: AnalyticsService;

  const mockAuthenticatedRequest = {
    user: {
      id: 'admin-123',
      userId: 'admin-123',
      username: 'testadmin',
      roles: ['admin'],
      permissions: ['admin:read', 'admin:write'],
    },
  } as AuthenticatedRequest;

  const mockAdminService = {
    getActiveSessions: jest.fn(),
    getUserStats: jest.fn(),
    getGuildStats: jest.fn(),
    clearCache: jest.fn(),
    startMaintenance: jest.fn(),
    stopMaintenance: jest.fn(),
    terminateSession: jest.fn(),
    updateUserStatus: jest.fn(),
    getSystemLogs: jest.fn(),
    getSystemConfig: jest.fn(),
    updateSystemConfig: jest.fn(),
  };

  const mockSystemHealthService = {
    getHealthStatus: jest.fn(),
    getSystemMetrics: jest.fn(),
  };

  const mockAnalyticsService = {
    getAnalytics: jest.fn(),
    getAnalyticsByPeriod: jest.fn(),
  };

  const mockJwtAuthGuard = {
    canActivate: jest.fn().mockReturnValue(true),
  };

  const mockAdminGuard = {
    canActivate: jest.fn().mockReturnValue(true),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AdminController],
      providers: [
        {
          provide: AdminService,
          useValue: mockAdminService,
        },
        {
          provide: SystemHealthService,
          useValue: mockSystemHealthService,
        },
        {
          provide: AnalyticsService,
          useValue: mockAnalyticsService,
        },
        AdminResponseInterceptor,
        AdminExceptionFilter,
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue(mockJwtAuthGuard)
      .overrideGuard(AdminGuard)
      .useValue(mockAdminGuard)
      .compile();

    controller = module.get<AdminController>(AdminController);
    adminService = module.get<AdminService>(AdminService);
    systemHealthService = module.get<SystemHealthService>(SystemHealthService);
    analyticsService = module.get<AnalyticsService>(AnalyticsService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Type Safety Tests', () => {
    it('should be defined with proper types', () => {
      expect(controller).toBeDefined();
      expect(controller).toBeInstanceOf(AdminController);
    });

    it('should have proper return types for getSessions', async () => {
      const mockSessions = [
        {
          sessionId: 'sess-123',
          userId: 'user-123',
          username: 'testuser',
          createdAt: new Date(),
          lastActivity: new Date(),
          ipAddress: '127.0.0.1',
        },
      ];

      mockAdminService.getActiveSessions.mockResolvedValue(mockSessions);

      const result = await controller.getSessions(mockAuthenticatedRequest);

      // Type assertion to verify return type structure
      expect(result).toMatchObject({
        success: true,
        message: expect.any(String),
        data: {
          sessions: mockSessions,
          total: mockSessions.length,
          timestamp: expect.any(Date),
        },
        timestamp: expect.any(String),
      } satisfies SuccessResponse<SessionsResponse>);
    });

    it('should handle proper enum types for analytics periods', async () => {
      const mockAnalytics = {
        period: 'day',
        startDate: new Date(),
        endDate: new Date(),
        users: { active: 100, new: 10 },
        guilds: { active: 50, new: 5 },
        activity: { commands: 1000, aiInteractions: 500, messages: 2000 },
      };

      mockAnalyticsService.getAnalyticsByPeriod.mockResolvedValue(mockAnalytics);

      // Test with valid enum value
      const result = await controller.getAnalyticsByPeriod(
        AnalyticsPeriod.DAY,
        mockAuthenticatedRequest
      );

      expect(result.success).toBe(true);
      expect(result.data.analytics).toEqual(mockAnalytics);
      expect(mockAnalyticsService.getAnalyticsByPeriod).toHaveBeenCalledWith(AnalyticsPeriod.DAY);
    });

    it('should validate UpdateUserStatusDto properly', async () => {
      const statusUpdate: UpdateUserStatusDto = {
        status: 'suspended',
        reason: 'Policy violation',
      };

      const mockUpdatedUser = {
        id: 'user-123',
        username: 'testuser',
        status: 'suspended',
      };

      mockAdminService.updateUserStatus.mockResolvedValue(mockUpdatedUser);

      const result = await controller.updateUserStatus(
        'user-123',
        statusUpdate,
        mockAuthenticatedRequest
      );

      expect(result.success).toBe(true);
      expect(mockAdminService.updateUserStatus).toHaveBeenCalledWith(
        'user-123',
        statusUpdate,
        mockAuthenticatedRequest.user
      );
    });

    it('should handle LogQueryDto with proper optional fields', async () => {
      const query: LogQueryDto = {
        level: 'error',
        source: 'AdminService',
        limit: 50,
      };

      const mockLogs = [
        {
          timestamp: new Date(),
          level: 'error',
          message: 'Test error',
          source: 'AdminService',
        },
      ];

      mockAdminService.getSystemLogs.mockResolvedValue(mockLogs);

      const result = await controller.getSystemLogs(query, mockAuthenticatedRequest);

      expect(result.success).toBe(true);
      expect(result.data.logs).toEqual(mockLogs);
      expect(mockAdminService.getSystemLogs).toHaveBeenCalledWith(
        mockAuthenticatedRequest.user,
        query
      );
    });

    it('should properly type UpdateSystemConfigDto', async () => {
      const configUpdate: UpdateSystemConfigDto = {
        features: {
          maintenance: true,
        },
        limits: {
          maxUsersPerGuild: 2000,
        },
      };

      mockAdminService.updateSystemConfig.mockResolvedValue(undefined);

      const result = await controller.updateSystemConfig(
        configUpdate,
        mockAuthenticatedRequest
      );

      expect(result.success).toBe(true);
      expect(mockAdminService.updateSystemConfig).toHaveBeenCalledWith(
        configUpdate,
        mockAuthenticatedRequest.user
      );
    });
  });

  describe('HTTP Response Types', () => {
    it('should return proper success response structure', async () => {
      const mockStats = {
        totalUsers: 1000,
        activeUsers: 500,
        premiumUsers: 100,
        timestamp: new Date(),
      };

      mockAdminService.getUserStats.mockResolvedValue(mockStats);

      const result = await controller.getUserStats(mockAuthenticatedRequest);

      // Verify complete response structure
      expect(result).toHaveProperty('success', true);
      expect(result).toHaveProperty('message');
      expect(result).toHaveProperty('data');
      expect(result).toHaveProperty('timestamp');
      expect(result.data).toHaveProperty('stats', mockStats);
      expect(result.data).toHaveProperty('timestamp');
    });

    it('should handle system health response types correctly', async () => {
      const mockHealth = {
        status: 'healthy' as const,
        timestamp: new Date(),
        responseTime: 50,
        uptime: 86400,
        version: '1.0.0',
        environment: 'test',
        checks: {
          database: { name: 'database', status: 'healthy' as const, responseTime: 10, message: 'OK' },
          redis: { name: 'redis', status: 'healthy' as const, responseTime: 5, message: 'OK' },
          memory: { name: 'memory', status: 'healthy' as const, responseTime: 0, message: 'OK' },
          disk: { name: 'disk', status: 'healthy' as const, responseTime: 0, message: 'OK' },
        },
      };

      mockSystemHealthService.getHealthStatus.mockResolvedValue(mockHealth);

      const result = await controller.getSystemHealth(mockAuthenticatedRequest);

      expect(result.success).toBe(true);
      expect(result.data.health).toEqual(mockHealth);
    });
  });

  describe('Authentication and Authorization Types', () => {
    it('should properly type AuthenticatedRequest', async () => {
      const request: AuthenticatedRequest = {
        user: {
          id: 'admin-456',
          userId: 'admin-456',
          username: 'superadmin',
          roles: ['admin', 'super_admin'],
          permissions: ['admin:read', 'admin:write', 'system:manage'],
          avatar: 'avatar-url',
          discriminator: '0001',
        },
      } as AuthenticatedRequest;

      mockAdminService.clearCache.mockResolvedValue(undefined);

      const result = await controller.clearCache(request);

      expect(result.success).toBe(true);
      expect(mockAdminService.clearCache).toHaveBeenCalledWith(request.user);
    });

    it('should handle optional user properties correctly', () => {
      // Test that optional properties are handled properly
      const minimalUser = {
        id: 'admin-789',
        userId: 'admin-789',
        username: 'basicadmin',
        roles: ['admin'],
      };

      const request = { user: minimalUser } as AuthenticatedRequest;

      expect(request.user.avatar).toBeUndefined();
      expect(request.user.permissions).toBeUndefined();
      expect(request.user.discriminator).toBeUndefined();
      expect(request.user.roles).toBeDefined();
    });
  });

  describe('Error Handling Types', () => {
    it('should handle service errors properly', async () => {
      const errorMessage = 'Database connection failed';
      mockAdminService.getUserStats.mockRejectedValue(new Error(errorMessage));

      await expect(controller.getUserStats(mockAuthenticatedRequest)).rejects.toThrow(
        errorMessage
      );
    });

    it('should validate required parameters', async () => {
      // This would be caught by NestJS validation pipes
      const invalidStatusUpdate = {} as UpdateUserStatusDto;

      mockAdminService.updateUserStatus.mockRejectedValue(
        new Error('Validation failed')
      );

      await expect(
        controller.updateUserStatus('user-123', invalidStatusUpdate, mockAuthenticatedRequest)
      ).rejects.toThrow();
    });
  });
});