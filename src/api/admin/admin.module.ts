import { Modu<PERSON> } from '@nestjs/common';
import { AdminController } from './admin.controller';
import { AdminService } from './admin.service';
import { SystemHealthService } from './components/system-health.service';
import { AnalyticsService } from './components/analytics.service';
import { AdminGuard } from './guards/admin.guard';
import { SecurityModule } from '../../core/security/security.module';
import { DatabaseModule } from '../../core/database/database.module';
import { DiscordModule } from '../../discord/discord.module';
import { CacheModule } from '../../core/cache/cache.module';
import { UserRepository } from '../../core/database/repositories/user.repository';
import { GuildRepository } from '../../core/database/repositories/guild.repository';
import { SessionRepository } from '../../core/database/repositories/session.repository';

@Module({
  imports: [SecurityModule, DatabaseModule, DiscordModule, CacheModule],
  controllers: [AdminController],
  providers: [
    AdminService,
    SystemHealthService,
    AnalyticsService,
    AdminGuard,
    UserRepository,
    GuildRepository,
    SessionRepository,
  ],
  exports: [AdminService, SystemHealthService, AnalyticsService],
})
export class AdminModule {}