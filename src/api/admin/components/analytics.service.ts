import { Injectable, Logger, InternalServerErrorException } from '@nestjs/common';
import { DatabaseService } from '../../../core/database/database.service';
import { CacheService } from '../../../core/cache/cache.service';
import {
  AnalyticsPeriod,
  DashboardStatistics,
  PeriodicAnalyticsStatistics,
  AnalyticsQueryOptions,
  DatabaseQueryResult,
  PerformanceAnalyticsMetrics,
  PanelAnalyticsMetrics,
  RealTimeAnalyticsMetrics,
  StatisticalAnalysis,
  AnalyticsExportData
} from '../types/analytics.types';

@Injectable()
export class AnalyticsService {
  private readonly logger = new Logger(AnalyticsService.name);

  constructor(
    private readonly databaseService: DatabaseService,
    private readonly redisDatabaseService: CacheService,
  ) {}

  /**
   * Get comprehensive dashboard statistics with full type safety
   * @returns Promise<DashboardStatistics> - Complete dashboard metrics
   */
  async getDashboardStats(): Promise<DashboardStatistics> {
    try {
      const now = new Date();
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

      const stats: DashboardStatistics = {
        users: {
          total: await this.getUserCount(),
          active24h: await this.getActiveUserCount(oneDayAgo),
          active7d: await this.getActiveUserCount(oneWeekAgo),
          active30d: await this.getActiveUserCount(oneMonthAgo),
          premium: await this.getPremiumUserCount(),
        },
        guilds: {
          total: await this.getGuildCount(),
          active24h: await this.getActiveGuildCount(oneDayAgo),
          active7d: await this.getActiveGuildCount(oneWeekAgo),
          active30d: await this.getActiveGuildCount(oneMonthAgo),
          premium: await this.getPremiumGuildCount(),
        },
        commands: {
          total24h: await this.getCommandCount(oneDayAgo),
          total7d: await this.getCommandCount(oneWeekAgo),
          total30d: await this.getCommandCount(oneMonthAgo),
          topCommands: await this.getTopCommands(oneMonthAgo),
        },
        ai: {
          interactions24h: await this.getAIInteractionCount(oneDayAgo),
          interactions7d: await this.getAIInteractionCount(oneWeekAgo),
          interactions30d: await this.getAIInteractionCount(oneMonthAgo),
          tokensUsed30d: await this.getTokensUsed(oneMonthAgo),
          averageResponseTime: await this.getAverageResponseTime(oneMonthAgo),
          successRate: await this.getSuccessRate(oneMonthAgo),
          sentimentDistribution: await this.getSentimentDistribution(oneMonthAgo),
          byAgentType: await this.getInteractionsByAgentType(oneMonthAgo),
        },
        performance: await this.getPerformanceMetrics(),
        panels: await this.getPanelAnalytics(oneMonthAgo),
        timestamp: now,
      };

      return stats;
    } catch (error) {
      this.logger.error('Error getting dashboard stats:', error);
      throw new InternalServerErrorException('Failed to retrieve dashboard statistics');
    }
  }

  /**
   * Get analytics data - alias for dashboard stats
   * @returns Promise<DashboardStatistics> - Dashboard statistics
   */
  async getAnalytics(): Promise<DashboardStatistics> {
    return this.getDashboardStats();
  }

  /**
   * Get analytics data for a specific time period
   * @param period - Time period for analytics
   * @param options - Additional query options
   * @returns Promise<PeriodicAnalyticsStatistics> - Period-specific analytics
   */
  async getAnalyticsByPeriod(
    period: AnalyticsPeriod,
    options: AnalyticsQueryOptions = {}
  ): Promise<PeriodicAnalyticsStatistics> {
    try {
      let startDate: Date;
      const now = new Date();

      if (period === 'custom' && options.dateRange) {
        startDate = options.dateRange.startDate;
      } else {
        switch (period) {
          case 'hour':
            startDate = new Date(now.getTime() - 60 * 60 * 1000);
            break;
          case 'day':
            startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
            break;
          case 'week':
            startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            break;
          case 'month':
            startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            break;
          case 'year':
            startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
            break;
          default:
            startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        }
      }

      const stats: PeriodicAnalyticsStatistics = {
        period,
        startDate,
        endDate: now,
        users: {
          active: await this.getActiveUserCount(startDate),
          new: await this.getNewUserCount(startDate),
        },
        guilds: {
          active: await this.getActiveGuildCount(startDate),
          new: await this.getNewGuildCount(startDate),
        },
        activity: {
          commands: await this.getCommandCount(startDate),
          aiInteractions: await this.getAIInteractionCount(startDate),
          messages: await this.getMessageCount(startDate),
          panelInteractions: await this.getPanelInteractionCount(startDate),
        },
      };

      // Add performance metrics if requested
      if (options.includePerformance) {
        stats.performance = await this.getPerformanceMetrics();
      }

      // Add growth metrics if requested
      if (options.includeGrowthMetrics) {
        stats.growth = await this.calculateGrowthMetrics(period, startDate);
      }

      return stats;
    } catch (error) {
      this.logger.error(`Error getting analytics for period ${period}:`, error);
      throw new InternalServerErrorException(`Failed to retrieve analytics for period ${period}`);
    }
  }

  /**
   * Get real-time analytics metrics
   * @returns Promise<RealTimeAnalyticsMetrics> - Real-time metrics
   */
  async getRealTimeMetrics(): Promise<RealTimeAnalyticsMetrics> {
    try {
      const now = new Date();
      const oneMinuteAgo = new Date(now.getTime() - 60 * 1000);

      return {
        currentActiveUsers: await this.getCurrentActiveUsers(),
        currentActiveGuilds: await this.getCurrentActiveGuilds(),
        messagesPerMinute: await this.getMessageCount(oneMinuteAgo),
        commandsPerMinute: await this.getCommandCount(oneMinuteAgo),
        aiInteractionsPerMinute: await this.getAIInteractionCount(oneMinuteAgo),
        averageResponseTime: await this.getAverageResponseTime(oneMinuteAgo),
        systemLoad: await this.getSystemLoad(),
        timestamp: now,
      };
    } catch (error) {
      this.logger.error('Error getting real-time metrics:', error);
      throw new InternalServerErrorException('Failed to retrieve real-time metrics');
    }
  }

  /**
   * Export analytics data in specified format
   * @param format - Export format
   * @param period - Time period
   * @param options - Query options
   * @returns Promise<AnalyticsExportData> - Export data
   */
  async exportAnalytics(
    format: 'csv' | 'json' | 'excel',
    period: AnalyticsPeriod,
    options: AnalyticsQueryOptions = {}
  ): Promise<AnalyticsExportData> {
    try {
      const data = await this.getAnalyticsByPeriod(period, options);

      return {
        exportType: format,
        data,
        generatedAt: new Date(),
        period,
        filters: options,
      };
    } catch (error) {
      this.logger.error('Error exporting analytics:', error);
      throw new InternalServerErrorException('Failed to export analytics');
    }
  }

  /**
   * Perform statistical analysis on metrics
   * @param values - Array of numeric values
   * @returns Promise<StatisticalAnalysis> - Statistical analysis
   */
  async performStatisticalAnalysis(values: number[]): Promise<StatisticalAnalysis> {
    try {
      if (values.length === 0) {
        return {
          mean: 0,
          median: 0,
          standardDeviation: 0,
          percentiles: { p25: 0, p50: 0, p75: 0, p90: 0, p95: 0, p99: 0 }
        };
      }

      const sorted = [...values].sort((a, b) => a - b);
      const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
      const median = this.calculatePercentile(sorted, 50);
      const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
      const standardDeviation = Math.sqrt(variance);

      return {
        mean,
        median,
        standardDeviation,
        percentiles: {
          p25: this.calculatePercentile(sorted, 25),
          p50: median,
          p75: this.calculatePercentile(sorted, 75),
          p90: this.calculatePercentile(sorted, 90),
          p95: this.calculatePercentile(sorted, 95),
          p99: this.calculatePercentile(sorted, 99),
        },
      };
    } catch (error) {
      this.logger.error('Error performing statistical analysis:', error);
      throw new InternalServerErrorException('Failed to perform statistical analysis');
    }
  }

  // Private Helper Methods

  /**
   * Get total user count
   * @returns Promise<number> - Total number of users
   */
  private async getUserCount(): Promise<number> {
    try {
      const result: DatabaseQueryResult[] = await this.databaseService.query(
        'SELECT COUNT(*) as count FROM users'
      );
      return Number(result[0]?.count) || 0;
    } catch (error) {
      this.logger.error('Error getting user count:', error);
      return 0;
    }
  }

  /**
   * Get active user count since a specific date
   * @param since - Date to count active users from
   * @returns Promise<number> - Number of active users
   */
  private async getActiveUserCount(since: Date): Promise<number> {
    try {
      const result: DatabaseQueryResult[] = await this.databaseService.query(
        'SELECT COUNT(*) as count FROM users WHERE last_seen >= ?',
        [since]
      );
      return Number(result[0]?.count) || 0;
    } catch (error) {
      this.logger.error('Error getting active user count:', error);
      return 0;
    }
  }

  /**
   * Get new user count since a specific date
   * @param since - Date to count new users from
   * @returns Promise<number> - Number of new users
   */
  private async getNewUserCount(since: Date): Promise<number> {
    try {
      const result: DatabaseQueryResult[] = await this.databaseService.query(
        'SELECT COUNT(*) as count FROM users WHERE created_at >= ?',
        [since]
      );
      return Number(result[0]?.count) || 0;
    } catch (error) {
      this.logger.error('Error getting new user count:', error);
      return 0;
    }
  }

  /**
   * Get premium user count
   * @returns Promise<number> - Number of premium users
   */
  private async getPremiumUserCount(): Promise<number> {
    try {
      const result: DatabaseQueryResult[] = await this.databaseService.query(
        'SELECT COUNT(*) as count FROM users WHERE is_premium = true'
      );
      return Number(result[0]?.count) || 0;
    } catch (error) {
      this.logger.error('Error getting premium user count:', error);
      return 0;
    }
  }

  /**
   * Get total guild count
   * @returns Promise<number> - Total number of guilds
   */
  private async getGuildCount(): Promise<number> {
    try {
      const result: DatabaseQueryResult[] = await this.databaseService.query(
        'SELECT COUNT(*) as count FROM guilds'
      );
      return Number(result[0]?.count) || 0;
    } catch (error) {
      this.logger.error('Error getting guild count:', error);
      return 0;
    }
  }

  /**
   * Get active guild count since a specific date
   * @param since - Date to count active guilds from
   * @returns Promise<number> - Number of active guilds
   */
  private async getActiveGuildCount(since: Date): Promise<number> {
    try {
      const result: DatabaseQueryResult[] = await this.databaseService.query(
        'SELECT COUNT(*) as count FROM guilds WHERE last_activity >= ?',
        [since]
      );
      return Number(result[0]?.count) || 0;
    } catch (error) {
      this.logger.error('Error getting active guild count:', error);
      return 0;
    }
  }

  /**
   * Get new guild count since a specific date
   * @param since - Date to count new guilds from
   * @returns Promise<number> - Number of new guilds
   */
  private async getNewGuildCount(since: Date): Promise<number> {
    try {
      const result: DatabaseQueryResult[] = await this.databaseService.query(
        'SELECT COUNT(*) as count FROM guilds WHERE created_at >= ?',
        [since]
      );
      return Number(result[0]?.count) || 0;
    } catch (error) {
      this.logger.error('Error getting new guild count:', error);
      return 0;
    }
  }

  /**
   * Get premium guild count
   * @returns Promise<number> - Number of premium guilds
   */
  private async getPremiumGuildCount(): Promise<number> {
    try {
      const result: DatabaseQueryResult[] = await this.databaseService.query(
        'SELECT COUNT(*) as count FROM guilds WHERE is_premium = true'
      );
      return Number(result[0]?.count) || 0;
    } catch (error) {
      this.logger.error('Error getting premium guild count:', error);
      return 0;
    }
  }

  /**
   * Get command count since a specific date
   * @param since - Date to count commands from
   * @returns Promise<number> - Number of commands
   */
  private async getCommandCount(since: Date): Promise<number> {
    try {
      const result: DatabaseQueryResult[] = await this.databaseService.query(
        'SELECT COUNT(*) as count FROM agent_interactions WHERE created_at >= ? AND interaction_type = ?',
        [since, 'command']
      );
      return Number(result[0]?.count) || 0;
    } catch (error) {
      this.logger.error('Error getting command count:', error);
      return 0;
    }
  }

  /**
   * Get AI interaction count since a specific date
   * @param since - Date to count interactions from
   * @returns Promise<number> - Number of AI interactions
   */
  private async getAIInteractionCount(since: Date): Promise<number> {
    try {
      const result: DatabaseQueryResult[] = await this.databaseService.query(
        'SELECT COUNT(*) as count FROM agent_interactions WHERE created_at >= ?',
        [since]
      );
      return Number(result[0]?.count) || 0;
    } catch (error) {
      this.logger.error('Error getting AI interaction count:', error);
      return 0;
    }
  }

  /**
   * Get tokens used since a specific date
   * @param since - Date to count tokens from
   * @returns Promise<number> - Total tokens used
   */
  private async getTokensUsed(since: Date): Promise<number> {
    try {
      const result: DatabaseQueryResult[] = await this.databaseService.query(
        'SELECT COALESCE(SUM(CAST(metadata->>\'tokens\' AS INTEGER)), 0) as total FROM agent_interactions WHERE created_at >= ? AND metadata->>\'tokens\' IS NOT NULL',
        [since]
      );
      return Number(result[0]?.total) || 0;
    } catch (error) {
      this.logger.error('Error getting tokens used:', error);
      return 0;
    }
  }

  /**
   * Get message count since a specific date
   * @param since - Date to count messages from
   * @returns Promise<number> - Number of messages
   */
  private async getMessageCount(since: Date): Promise<number> {
    try {
      const result: DatabaseQueryResult[] = await this.databaseService.query(
        'SELECT COUNT(*) as count FROM agent_interactions WHERE created_at >= ? AND interaction_type = ?',
        [since, 'message']
      );
      return Number(result[0]?.count) || 0;
    } catch (error) {
      this.logger.error('Error getting message count:', error);
      return 0;
    }
  }

  /**
   * Get top commands since a specific date
   * @param since - Date to get commands from
   * @returns Promise<Array<{command: string, count: number}>> - Top commands
   */
  private async getTopCommands(since: Date): Promise<Array<{ command: string; count: number }>> {
    try {
      const result: DatabaseQueryResult[] = await this.databaseService.query(
        `SELECT content as command, COUNT(*) as count 
         FROM agent_interactions 
         WHERE created_at >= ? AND interaction_type = 'command'
         GROUP BY content 
         ORDER BY count DESC 
         LIMIT 10`,
        [since]
      );
      return result.map(row => ({
        command: String(row.command || 'unknown'),
        count: Number(row.count) || 0
      }));
    } catch (error) {
      this.logger.error('Error getting top commands:', error);
      return [];
    }
  }

  /**
   * Get average response time for AI interactions
   * @param since - Date to calculate from
   * @returns Promise<number> - Average response time in ms
   */
  private async getAverageResponseTime(since: Date): Promise<number> {
    try {
      const result: DatabaseQueryResult[] = await this.databaseService.query(
        'SELECT AVG(CAST(metadata->>\'processingTime\' AS FLOAT)) as avg_response_time FROM agent_interactions WHERE created_at >= ? AND metadata->>\'processingTime\' IS NOT NULL',
        [since]
      );
      return Number(result[0]?.avg_response_time) || 0;
    } catch (error) {
      this.logger.error('Error getting average response time:', error);
      return 0;
    }
  }

  /**
   * Get success rate for AI interactions
   * @param since - Date to calculate from
   * @returns Promise<number> - Success rate as percentage
   */
  private async getSuccessRate(since: Date): Promise<number> {
    try {
      const result: DatabaseQueryResult[] = await this.databaseService.query(
        `SELECT 
           COUNT(CASE WHEN status = 'completed' THEN 1 END) * 100.0 / COUNT(*) as success_rate
         FROM agent_interactions 
         WHERE created_at >= ?`,
        [since]
      );
      return Number(result[0]?.success_rate) || 0;
    } catch (error) {
      this.logger.error('Error getting success rate:', error);
      return 0;
    }
  }

  /**
   * Get sentiment distribution for AI interactions
   * @param since - Date to calculate from
   * @returns Promise<{positive: number, neutral: number, negative: number}> - Sentiment distribution
   */
  private async getSentimentDistribution(since: Date): Promise<{ positive: number; neutral: number; negative: number }> {
    try {
      const result: DatabaseQueryResult[] = await this.databaseService.query(
        `SELECT 
           COUNT(CASE WHEN sentiment_score > 0.1 THEN 1 END) as positive,
           COUNT(CASE WHEN sentiment_score BETWEEN -0.1 AND 0.1 THEN 1 END) as neutral,
           COUNT(CASE WHEN sentiment_score < -0.1 THEN 1 END) as negative
         FROM agent_interactions 
         WHERE created_at >= ?`,
        [since]
      );
      
      const row = result[0] || {};
      return {
        positive: Number(row.positive) || 0,
        neutral: Number(row.neutral) || 0,
        negative: Number(row.negative) || 0,
      };
    } catch (error) {
      this.logger.error('Error getting sentiment distribution:', error);
      return { positive: 0, neutral: 0, negative: 0 };
    }
  }

  /**
   * Get interactions by agent type
   * @param since - Date to calculate from
   * @returns Promise<Record<string, number>> - Interactions by agent type
   */
  private async getInteractionsByAgentType(since: Date): Promise<Record<string, number>> {
    try {
      const result: DatabaseQueryResult[] = await this.databaseService.query(
        'SELECT agent_type, COUNT(*) as count FROM agent_interactions WHERE created_at >= ? GROUP BY agent_type',
        [since]
      );
      
      const interactions: Record<string, number> = {};
      result.forEach(row => {
        if (row.agent_type) {
          interactions[String(row.agent_type)] = Number(row.count) || 0;
        }
      });
      
      return interactions;
    } catch (error) {
      this.logger.error('Error getting interactions by agent type:', error);
      return {};
    }
  }

  /**
   * Get performance metrics
   * @returns Promise<PerformanceAnalyticsMetrics> - Performance metrics
   */
  private async getPerformanceMetrics(): Promise<PerformanceAnalyticsMetrics> {
    try {
      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

      const [responseTimeResult, errorRateResult] = await Promise.all([
        this.databaseService.query(
          'SELECT AVG(CAST(metadata->>\'processingTime\' AS FLOAT)) as avg_response_time FROM agent_interactions WHERE created_at >= ? AND metadata->>\'processingTime\' IS NOT NULL',
          [oneHourAgo]
        ),
        this.databaseService.query(
          `SELECT 
             COUNT(CASE WHEN status = 'failed' THEN 1 END) * 100.0 / COUNT(*) as error_rate
           FROM agent_interactions 
           WHERE created_at >= ?`,
          [oneHourAgo]
        ),
      ]);

      return {
        averageResponseTime: Number(responseTimeResult[0]?.avg_response_time) || 0,
        uptime: 99.5, // This would be calculated from system metrics
        errorRate: Number(errorRateResult[0]?.error_rate) || 0,
        throughput: await this.getAIInteractionCount(oneHourAgo),
      };
    } catch (error) {
      this.logger.error('Error getting performance metrics:', error);
      return {
        averageResponseTime: 0,
        uptime: 0,
        errorRate: 0,
        throughput: 0,
      };
    }
  }

  /**
   * Get panel analytics
   * @param since - Date to calculate from
   * @returns Promise<PanelAnalyticsMetrics> - Panel analytics
   */
  private async getPanelAnalytics(since: Date): Promise<PanelAnalyticsMetrics> {
    try {
      const result: DatabaseQueryResult[] = await this.databaseService.query(
        `SELECT 
           COUNT(*) as total_views,
           COUNT(DISTINCT user_id) as unique_users,
           COUNT(CASE WHEN event_type = 'interaction' THEN 1 END) as total_interactions,
           AVG(CAST(event_data->>'duration' AS FLOAT)) as avg_engagement_time
         FROM panel_analytics 
         WHERE timestamp >= ?`,
        [since]
      );

      const topPanelsResult: DatabaseQueryResult[] = await this.databaseService.query(
        'SELECT panel_id, COUNT(*) as views FROM panel_analytics WHERE timestamp >= ? GROUP BY panel_id ORDER BY views DESC LIMIT 10',
        [since]
      );

      const row = result[0] || {};
      const totalViews = Number(row.total_views) || 0;
      const uniqueUsers = Number(row.unique_users) || 0;

      return {
        totalViews,
        uniqueUsers,
        totalInteractions: Number(row.total_interactions) || 0,
        averageEngagementTime: Number(row.avg_engagement_time) || 0,
        bounceRate: totalViews > 0 ? ((totalViews - uniqueUsers) / totalViews) * 100 : 0,
        topPanels: topPanelsResult.map(panel => ({
          panelId: String(panel.panel_id || 'unknown'),
          views: Number(panel.views) || 0
        })),
      };
    } catch (error) {
      this.logger.error('Error getting panel analytics:', error);
      return {
        totalViews: 0,
        uniqueUsers: 0,
        totalInteractions: 0,
        averageEngagementTime: 0,
        bounceRate: 0,
        topPanels: [],
      };
    }
  }

  /**
   * Get panel interaction count
   * @param since - Date to count from
   * @returns Promise<number> - Panel interaction count
   */
  private async getPanelInteractionCount(since: Date): Promise<number> {
    try {
      const result: DatabaseQueryResult[] = await this.databaseService.query(
        'SELECT COUNT(*) as count FROM panel_analytics WHERE timestamp >= ? AND event_type = ?',
        [since, 'interaction']
      );
      return Number(result[0]?.count) || 0;
    } catch (error) {
      this.logger.error('Error getting panel interaction count:', error);
      return 0;
    }
  }

  /**
   * Calculate growth metrics
   * @param period - Time period
   * @param startDate - Start date
   * @returns Promise<{userGrowthRate: number, guildGrowthRate: number, activityGrowthRate: number}> - Growth metrics
   */
  private async calculateGrowthMetrics(period: AnalyticsPeriod, startDate: Date): Promise<{ userGrowthRate: number; guildGrowthRate: number; activityGrowthRate: number }> {
    try {
      const previousPeriodStart = this.getPreviousPeriodStart(period, startDate);

      const [currentUsers, previousUsers, currentGuilds, previousGuilds, currentActivity, previousActivity] = await Promise.all([
        this.getNewUserCount(startDate),
        this.getNewUserCount(previousPeriodStart),
        this.getNewGuildCount(startDate),
        this.getNewGuildCount(previousPeriodStart),
        this.getAIInteractionCount(startDate),
        this.getAIInteractionCount(previousPeriodStart),
      ]);

      return {
        userGrowthRate: this.calculateGrowthRate(currentUsers, previousUsers),
        guildGrowthRate: this.calculateGrowthRate(currentGuilds, previousGuilds),
        activityGrowthRate: this.calculateGrowthRate(currentActivity, previousActivity),
      };
    } catch (error) {
      this.logger.error('Error calculating growth metrics:', error);
      return { userGrowthRate: 0, guildGrowthRate: 0, activityGrowthRate: 0 };
    }
  }

  /**
   * Get current active users (online now)
   * @returns Promise<number> - Current active users
   */
  private async getCurrentActiveUsers(): Promise<number> {
    try {
      const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
      const result: DatabaseQueryResult[] = await this.databaseService.query(
        'SELECT COUNT(*) as count FROM users WHERE last_seen >= ?',
        [fiveMinutesAgo]
      );
      return Number(result[0]?.count) || 0;
    } catch (error) {
      this.logger.error('Error getting current active users:', error);
      return 0;
    }
  }

  /**
   * Get current active guilds
   * @returns Promise<number> - Current active guilds
   */
  private async getCurrentActiveGuilds(): Promise<number> {
    try {
      const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
      const result: DatabaseQueryResult[] = await this.databaseService.query(
        'SELECT COUNT(*) as count FROM guilds WHERE last_activity >= ?',
        [fiveMinutesAgo]
      );
      return Number(result[0]?.count) || 0;
    } catch (error) {
      this.logger.error('Error getting current active guilds:', error);
      return 0;
    }
  }

  /**
   * Get system load metrics
   * @returns Promise<{cpu: number, memory: number, disk: number}> - System load
   */
  private async getSystemLoad(): Promise<{ cpu: number; memory: number; disk: number }> {
    try {
      // This would typically come from system monitoring
      // For now, return mock data
      return {
        cpu: 45.2,
        memory: 62.1,
        disk: 78.3,
      };
    } catch (error) {
      this.logger.error('Error getting system load:', error);
      return { cpu: 0, memory: 0, disk: 0 };
    }
  }

  // Utility Methods

  /**
   * Calculate percentile value
   * @param sortedValues - Sorted array of values
   * @param percentile - Percentile to calculate (0-100)
   * @returns number - Percentile value
   */
  private calculatePercentile(sortedValues: number[], percentile: number): number {
    if (sortedValues.length === 0) return 0;
    
    const index = (percentile / 100) * (sortedValues.length - 1);
    const lower = Math.floor(index);
    const upper = Math.ceil(index);
    
    if (lower === upper) {
      return sortedValues[lower];
    }
    
    const weight = index - lower;
    return sortedValues[lower] * (1 - weight) + sortedValues[upper] * weight;
  }

  private getPreviousPeriodStart(period: AnalyticsPeriod, currentStart: Date): Date {
    const diff = Date.now() - currentStart.getTime();
    return new Date(currentStart.getTime() - diff);
  }

  private calculateGrowthRate(current: number, previous: number): number {
    if (previous === 0) return current > 0 ? 100 : 0;
    return ((current - previous) / previous) * 100;
  }
}