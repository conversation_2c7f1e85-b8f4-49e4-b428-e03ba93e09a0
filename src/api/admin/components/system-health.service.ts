import { Injectable, Logger } from '@nestjs/common';
import { DatabaseService } from '../../../core/database/database.service';
import { CacheService } from '../../../core/cache/cache.service';
import { HealthCheck } from '../../../common/interfaces/enterprise.interfaces';

// Health status enums
export enum HealthStatus {
  HEALTHY = 'healthy',
  DEGRADED = 'degraded',
  CRITICAL = 'critical',
  UNHEALTHY = 'unhealthy'
}

export enum ServiceStatus {
  UP = 'up',
  DOWN = 'down',
  DEGRADED = 'degraded',
  UNKNOWN = 'unknown'
}

// System health interfaces
export interface SystemHealthCheck {
  name: string;
  status: HealthStatus;
  responseTime: number | null;
  message: string;
  error?: string;
  details?: Record<string, any>;
  timestamp: Date;
}

export interface SystemHealthResponse {
  status: HealthStatus;
  timestamp: Date;
  responseTime: number;
  checks: {
    database: SystemHealthCheck;
    redis: SystemHealthCheck;
    memory: SystemHealthCheck;
    disk: SystemHealthCheck;
  };
  uptime: number;
  version: string;
  environment: string;
  error?: string;
}

// System metrics interfaces
export interface MemoryMetrics {
  rss: number;
  heapTotal: number;
  heapUsed: number;
  external: number;
  arrayBuffers: number;
  percentage?: number;
}

export interface CpuMetrics {
  user: number;
  system: number;
  percentage?: number;
}

export interface ProcessMetrics {
  pid: number;
  version: string;
  platform: string;
  arch: string;
  nodeVersion: string;
}

export interface SystemMetrics {
  timestamp: Date;
  uptime: number;
  memory: MemoryMetrics;
  cpu: CpuMetrics;
  process: ProcessMetrics;
}

// Alert threshold types
export interface AlertThresholds {
  memory: {
    warning: number; // percentage
    critical: number; // percentage
  };
  cpu: {
    warning: number; // percentage
    critical: number; // percentage
  };
  responseTime: {
    warning: number; // milliseconds
    critical: number; // milliseconds
  };
  disk: {
    warning: number; // percentage
    critical: number; // percentage
  };
}

// Monitoring data types
export interface MonitoringData {
  service: string;
  status: HealthStatus;
  metrics: SystemMetrics;
  checks: SystemHealthCheck[];
  alerts: Alert[];
  timestamp: Date;
}

export interface Alert {
  id: string;
  type: AlertType;
  severity: AlertSeverity;
  message: string;
  details: Record<string, any>;
  timestamp: Date;
  acknowledged: boolean;
  resolvedAt?: Date;
}

export enum AlertType {
  MEMORY_HIGH = 'memory_high',
  CPU_HIGH = 'cpu_high',
  DISK_FULL = 'disk_full',
  SERVICE_DOWN = 'service_down',
  RESPONSE_TIME_HIGH = 'response_time_high',
  DATABASE_CONNECTION_FAILED = 'database_connection_failed',
  REDIS_CONNECTION_FAILED = 'redis_connection_failed'
}

export enum AlertSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

@Injectable()
export class SystemHealthService {
  private readonly logger = new Logger(SystemHealthService.name);
  private readonly alerts: Alert[] = [];
  private readonly thresholds: AlertThresholds = {
    memory: { warning: 75, critical: 90 },
    cpu: { warning: 70, critical: 85 },
    responseTime: { warning: 1000, critical: 5000 },
    disk: { warning: 80, critical: 95 }
  };

  constructor(
    private readonly databaseService: DatabaseService,
    private readonly redisDatabaseService: CacheService,
  ) {}

  async getHealthStatus(): Promise<SystemHealthResponse> {
    const startTime = Date.now();
    
    try {
      const checks = await Promise.allSettled([
        this.checkDatabase(),
        this.checkRedis(),
        this.checkMemory(),
        this.checkDisk(),
      ]);

      const [database, redis, memory, disk] = checks.map((check, index) => {
        const names = ['database', 'redis', 'memory', 'disk'] as const;
        const name = names[index];
        if (check.status === 'fulfilled') {
          return { ...check.value, name };
        } else {
          const failedCheck: SystemHealthCheck = {
            name,
            status: HealthStatus.UNHEALTHY,
            error: check.reason?.message || 'Unknown error',
            responseTime: null,
            message: name + ' check failed',
            timestamp: new Date(),
          };
          return failedCheck;
        }
      });

      const overallStatus = this.determineOverallStatus([database, redis, memory, disk]);
      const responseTime = Date.now() - startTime;

      // Check for alerts based on health status
      await this.processHealthAlerts([database, redis, memory, disk]);

      const result: SystemHealthResponse = {
        status: overallStatus,
        timestamp: new Date(),
        responseTime,
        checks: {
          database,
          redis,
          memory,
          disk,
        },
        uptime: process.uptime(),
        version: process.env.npm_package_version || '1.0.0',
        environment: process.env.NODE_ENV || 'development',
      };

      return result;
    } catch (error) {
      this.logger.error('Error getting health status:', error);
      const errorResponse: SystemHealthResponse = {
        status: HealthStatus.UNHEALTHY,
        timestamp: new Date(),
        responseTime: Date.now() - startTime,
        error: (error as Error).message,
        uptime: process.uptime(),
        version: process.env.npm_package_version || '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        checks: {
          database: this.createErrorHealthCheck('database', error as Error),
          redis: this.createErrorHealthCheck('redis', error as Error),
          memory: this.createErrorHealthCheck('memory', error as Error),
          disk: this.createErrorHealthCheck('disk', error as Error),
        },
      };
      return errorResponse;
    }
  }

  async getSystemMetrics(): Promise<SystemMetrics> {
    try {
      const memoryUsage = process.memoryUsage();
      const cpuUsage = process.cpuUsage();
      
      // Calculate memory percentage
      const heapUsedMB = memoryUsage.heapUsed / 1024 / 1024;
      const heapTotalMB = memoryUsage.heapTotal / 1024 / 1024;
      const memoryPercentage = (heapUsedMB / heapTotalMB) * 100;

      const metrics: SystemMetrics = {
        timestamp: new Date(),
        uptime: process.uptime(),
        memory: {
          rss: memoryUsage.rss,
          heapTotal: memoryUsage.heapTotal,
          heapUsed: memoryUsage.heapUsed,
          external: memoryUsage.external,
          arrayBuffers: memoryUsage.arrayBuffers,
          percentage: memoryPercentage,
        },
        cpu: {
          user: cpuUsage.user,
          system: cpuUsage.system,
          // Note: CPU percentage calculation would require additional monitoring
        },
        process: {
          pid: process.pid,
          version: process.version,
          platform: process.platform,
          arch: process.arch,
          nodeVersion: process.version,
        },
      };

      return metrics;
    } catch (error) {
      this.logger.error('Error getting system metrics:', error);
      throw new Error(`Failed to get system metrics: ${(error as Error).message}`);
    }
  }

  private async checkDatabase(): Promise<SystemHealthCheck> {
    const startTime = Date.now();
    const name = 'database';
    
    try {
      await this.databaseService.query('SELECT 1');
      const responseTime = Date.now() - startTime;
      
      const status = responseTime > this.thresholds.responseTime.critical 
        ? HealthStatus.CRITICAL
        : responseTime > this.thresholds.responseTime.warning
        ? HealthStatus.DEGRADED
        : HealthStatus.HEALTHY;

      const check: SystemHealthCheck = {
        name,
        status,
        responseTime,
        message: status === HealthStatus.HEALTHY 
          ? 'Database connection successful'
          : `Database response time is ${responseTime}ms`,
        timestamp: new Date(),
        details: {
          responseTime: `${responseTime}ms`,
          query: 'SELECT 1',
        },
      };

      return check;
    } catch (error) {
      const responseTime = Date.now() - startTime;
      const check: SystemHealthCheck = {
        name,
        status: HealthStatus.UNHEALTHY,
        responseTime,
        message: 'Database connection failed',
        error: (error as Error).message,
        timestamp: new Date(),
        details: {
          errorType: (error as Error).constructor.name,
          query: 'SELECT 1',
        },
      };
      
      return check;
    }
  }

  private async checkRedis(): Promise<SystemHealthCheck> {
    const startTime = Date.now();
    const name = 'redis';
    
    try {
      const redis = this.redisDatabaseService.redis?.getClient();
      if (!redis) {
        throw new Error('Redis client not available');
      }
      
      const pingResult = await redis.ping();
      const responseTime = Date.now() - startTime;
      
      const status = responseTime > this.thresholds.responseTime.critical
        ? HealthStatus.CRITICAL
        : responseTime > this.thresholds.responseTime.warning
        ? HealthStatus.DEGRADED
        : HealthStatus.HEALTHY;

      const check: SystemHealthCheck = {
        name,
        status,
        responseTime,
        message: status === HealthStatus.HEALTHY
          ? 'Redis connection successful'
          : `Redis response time is ${responseTime}ms`,
        timestamp: new Date(),
        details: {
          responseTime: `${responseTime}ms`,
          pingResult,
          command: 'PING',
        },
      };

      return check;
    } catch (error) {
      const responseTime = Date.now() - startTime;
      const check: SystemHealthCheck = {
        name,
        status: HealthStatus.UNHEALTHY,
        responseTime,
        message: 'Redis connection failed',
        error: (error as Error).message,
        timestamp: new Date(),
        details: {
          errorType: (error as Error).constructor.name,
          command: 'PING',
        },
      };
      
      return check;
    }
  }

  private async checkMemory(): Promise<SystemHealthCheck> {
    const name = 'memory';
    
    try {
      const memoryUsage = process.memoryUsage();
      const heapUsedMB = memoryUsage.heapUsed / 1024 / 1024;
      const heapTotalMB = memoryUsage.heapTotal / 1024 / 1024;
      const rssMB = memoryUsage.rss / 1024 / 1024;
      const usagePercentage = (heapUsedMB / heapTotalMB) * 100;

      let status = HealthStatus.HEALTHY;
      if (usagePercentage > this.thresholds.memory.critical) {
        status = HealthStatus.CRITICAL;
      } else if (usagePercentage > this.thresholds.memory.warning) {
        status = HealthStatus.DEGRADED;
      }

      const check: SystemHealthCheck = {
        name,
        status,
        responseTime: 0,
        message: `Memory usage: ${heapUsedMB.toFixed(2)} MB (${usagePercentage.toFixed(1)}%)`,
        timestamp: new Date(),
        details: {
          heapUsed: `${heapUsedMB.toFixed(2)} MB`,
          heapTotal: `${heapTotalMB.toFixed(2)} MB`,
          rss: `${rssMB.toFixed(2)} MB`,
          external: `${(memoryUsage.external / 1024 / 1024).toFixed(2)} MB`,
          percentage: Number(usagePercentage.toFixed(1)),
          thresholds: {
            warning: this.thresholds.memory.warning,
            critical: this.thresholds.memory.critical,
          },
        },
      };

      return check;
    } catch (error) {
      const check: SystemHealthCheck = {
        name,
        status: HealthStatus.UNHEALTHY,
        responseTime: 0,
        message: 'Memory check failed',
        error: (error as Error).message,
        timestamp: new Date(),
        details: {
          errorType: (error as Error).constructor.name,
        },
      };
      
      return check;
    }
  }

  private async checkDisk(): Promise<SystemHealthCheck> {
    const name = 'disk';
    
    try {
      const stats = await this.getBasicDiskStats();
      
      let status = HealthStatus.HEALTHY;
      const usagePercentage = stats.usagePercentage || 0;
      
      if (usagePercentage > this.thresholds.disk.critical) {
        status = HealthStatus.CRITICAL;
      } else if (usagePercentage > this.thresholds.disk.warning) {
        status = HealthStatus.DEGRADED;
      }

      const check: SystemHealthCheck = {
        name,
        status,
        responseTime: 0,
        message: stats.available 
          ? `Disk usage: ${usagePercentage.toFixed(1)}%`
          : 'Disk monitoring available but stats not implemented',
        timestamp: new Date(),
        details: {
          ...stats,
          thresholds: {
            warning: this.thresholds.disk.warning,
            critical: this.thresholds.disk.critical,
          },
          note: 'Basic disk monitoring - consider implementing full disk stats for production',
        },
      };

      return check;
    } catch (error) {
      const check: SystemHealthCheck = {
        name,
        status: HealthStatus.UNHEALTHY,
        responseTime: 0,
        message: 'Disk check failed',
        error: (error as Error).message,
        timestamp: new Date(),
        details: {
          errorType: (error as Error).constructor.name,
        },
      };
      
      return check;
    }
  }

  private determineOverallStatus(checks: SystemHealthCheck[]): HealthStatus {
    const statuses = checks.map(check => check.status);
    
    if (statuses.some(status => status === HealthStatus.UNHEALTHY)) {
      return HealthStatus.UNHEALTHY;
    }
    
    if (statuses.some(status => status === HealthStatus.CRITICAL)) {
      return HealthStatus.CRITICAL;
    }
    
    if (statuses.some(status => status === HealthStatus.DEGRADED)) {
      return HealthStatus.DEGRADED;
    }
    
    return HealthStatus.HEALTHY;
  }

  private createErrorHealthCheck(name: string, error: Error): SystemHealthCheck {
    const capitalizedName = name.charAt(0).toUpperCase() + name.slice(1);
    return {
      name,
      status: HealthStatus.UNHEALTHY,
      responseTime: null,
      message: `${capitalizedName} check failed`,
      error: error.message,
      timestamp: new Date(),
      details: {
        errorType: error.constructor.name,
      },
    };
  }

  private async getBasicDiskStats(): Promise<Record<string, any>> {
    try {
      // Basic implementation - in production use proper disk monitoring libraries
      return {
        available: false,
        usagePercentage: 0,
        totalSpace: 'unknown',
        freeSpace: 'unknown',
        usedSpace: 'unknown',
      };
    } catch (error) {
      return {
        available: false,
        error: (error as Error).message,
      };
    }
  }

  private async processHealthAlerts(checks: SystemHealthCheck[]): Promise<void> {
    for (const check of checks) {
      if (check.status === HealthStatus.CRITICAL || check.status === HealthStatus.UNHEALTHY) {
        await this.createAlert(check);
      }
    }
  }

  private async createAlert(check: SystemHealthCheck): Promise<void> {
    const alertId = `${check.name}_${Date.now()}`;
    const alert: Alert = {
      id: alertId,
      type: this.mapHealthCheckToAlertType(check.name),
      severity: check.status === HealthStatus.CRITICAL 
        ? AlertSeverity.CRITICAL 
        : AlertSeverity.HIGH,
      message: check.message,
      details: {
        service: check.name,
        status: check.status,
        responseTime: check.responseTime,
        error: check.error,
        ...check.details,
      },
      timestamp: new Date(),
      acknowledged: false,
    };

    this.alerts.push(alert);
    this.logger.warn(`Health alert created: ${alert.message}`, {
      alertId: alert.id,
      type: alert.type,
      severity: alert.severity,
    });
  }

  private mapHealthCheckToAlertType(checkName: string): AlertType {
    switch (checkName) {
      case 'database':
        return AlertType.DATABASE_CONNECTION_FAILED;
      case 'redis':
        return AlertType.REDIS_CONNECTION_FAILED;
      case 'memory':
        return AlertType.MEMORY_HIGH;
      case 'disk':
        return AlertType.DISK_FULL;
      default:
        return AlertType.SERVICE_DOWN;
    }
  }

  // Performance metrics collection
  async getPerformanceMetrics(): Promise<MonitoringData> {
    const healthStatus = await this.getHealthStatus();
    const systemMetrics = await this.getSystemMetrics();
    
    const monitoring: MonitoringData = {
      service: 'system-health',
      status: healthStatus.status,
      metrics: systemMetrics,
      checks: Object.values(healthStatus.checks),
      alerts: this.getActiveAlerts(),
      timestamp: new Date(),
    };

    return monitoring;
  }

  // Alert management
  getActiveAlerts(): Alert[] {
    return this.alerts.filter(alert => !alert.resolvedAt);
  }

  async acknowledgeAlert(alertId: string): Promise<boolean> {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.acknowledged = true;
      this.logger.info(`Alert acknowledged: ${alertId}`);
      return true;
    }
    return false;
  }

  async resolveAlert(alertId: string): Promise<boolean> {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.resolvedAt = new Date();
      this.logger.info(`Alert resolved: ${alertId}`);
      return true;
    }
    return false;
  }

  // Health check patterns for async operations
  async performHealthCheck(): Promise<HealthCheck> {
    const healthStatus = await this.getHealthStatus();
    
    const enterpriseHealthCheck: HealthCheck = {
      service: 'discord-bot-energex',
      status: healthStatus.status,
      timestamp: healthStatus.timestamp,
      responseTime: healthStatus.responseTime,
      details: {
        environment: healthStatus.environment,
        version: healthStatus.version,
        uptime: healthStatus.uptime,
        checks: healthStatus.checks,
      },
      dependencies: await this.getDependencyHealth(),
    };

    return enterpriseHealthCheck;
  }

  private async getDependencyHealth(): Promise<HealthCheck[]> {
    const dependencies: HealthCheck[] = [];
    const healthStatus = await this.getHealthStatus();

    // Convert system health checks to enterprise health checks
    Object.entries(healthStatus.checks).forEach(([service, check]) => {
      const dependency: HealthCheck = {
        service,
        status: check.status,
        timestamp: check.timestamp,
        responseTime: check.responseTime || 0,
        details: {
          message: check.message,
          error: check.error,
          ...check.details,
        },
      };
      dependencies.push(dependency);
    });

    return dependencies;
  }

  // Utility methods for alert threshold management
  updateAlertThresholds(newThresholds: Partial<AlertThresholds>): void {
    Object.assign(this.thresholds, newThresholds);
    this.logger.info('Alert thresholds updated', { thresholds: this.thresholds });
  }

  getAlertThresholds(): AlertThresholds {
    return { ...this.thresholds };
  }
}