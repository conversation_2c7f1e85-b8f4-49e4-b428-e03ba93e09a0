import { Controller, Get, UseGuards, Param, Post, Body } from "@nestjs/common";
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from "@nestjs/swagger";
import { AdminGuard } from "../guards/admin.guard";
import { 
  SystemHealthService,
  SystemHealthResponse,
  SystemMetrics,
  MonitoringData,
  Alert,
  AlertThresholds,
  HealthStatus
} from "./system-health.service";

@ApiTags("admin/system-health")
@Controller("admin/system-health")
@UseGuards(AdminGuard)
@ApiBearerAuth()
export class SystemHealthController {
  constructor(private readonly systemHealthService: SystemHealthService) {}

  @Get()
  @ApiOperation({ 
    summary: "Get comprehensive system health status",
    description: "Returns overall health status including database, Redis, memory, and disk checks"
  })
  @ApiResponse({ 
    status: 200, 
    description: "System health status retrieved successfully"
  })
  async getSystemHealth(): Promise<SystemHealthResponse> {
    return this.systemHealthService.getHealthStatus();
  }

  @Get("metrics")
  @ApiOperation({ 
    summary: "Get detailed system metrics",
    description: "Returns system performance metrics including CPU, memory, and process information"
  })
  @ApiResponse({ 
    status: 200, 
    description: "System metrics retrieved successfully"
  })
  async getSystemMetrics(): Promise<SystemMetrics> {
    return this.systemHealthService.getSystemMetrics();
  }

  @Get("performance")
  @ApiOperation({ 
    summary: "Get comprehensive monitoring data",
    description: "Returns complete monitoring data including health checks, metrics, and alerts"
  })
  @ApiResponse({ 
    status: 200, 
    description: "Performance monitoring data retrieved successfully"
  })
  async getPerformanceMetrics(): Promise<MonitoringData> {
    return this.systemHealthService.getPerformanceMetrics();
  }

  @Get("alerts")
  @ApiOperation({ 
    summary: "Get active system alerts",
    description: "Returns all active alerts that have not been resolved"
  })
  @ApiResponse({ 
    status: 200, 
    description: "Active alerts retrieved successfully"
  })
  async getActiveAlerts(): Promise<Alert[]> {
    return this.systemHealthService.getActiveAlerts();
  }

  @Post("alerts/:alertId/acknowledge")
  @ApiOperation({ 
    summary: "Acknowledge a system alert",
    description: "Mark an alert as acknowledged by an administrator"
  })
  @ApiResponse({ 
    status: 200, 
    description: "Alert acknowledged successfully"
  })
  @ApiResponse({ 
    status: 404, 
    description: "Alert not found"
  })
  async acknowledgeAlert(@Param("alertId") alertId: string): Promise<{ success: boolean; message: string }> {
    const success = await this.systemHealthService.acknowledgeAlert(alertId);
    return {
      success,
      message: success ? "Alert acknowledged successfully" : "Alert not found"
    };
  }

  @Post("alerts/:alertId/resolve")
  @ApiOperation({ 
    summary: "Resolve a system alert",
    description: "Mark an alert as resolved, removing it from active alerts"
  })
  @ApiResponse({ 
    status: 200, 
    description: "Alert resolved successfully"
  })
  @ApiResponse({ 
    status: 404, 
    description: "Alert not found"
  })
  async resolveAlert(@Param("alertId") alertId: string): Promise<{ success: boolean; message: string }> {
    const success = await this.systemHealthService.resolveAlert(alertId);
    return {
      success,
      message: success ? "Alert resolved successfully" : "Alert not found"
    };
  }

  @Get("thresholds")
  @ApiOperation({ 
    summary: "Get alert thresholds configuration",
    description: "Returns current alert threshold settings for all monitoring categories"
  })
  @ApiResponse({ 
    status: 200, 
    description: "Alert thresholds retrieved successfully"
  })
  async getAlertThresholds(): Promise<AlertThresholds> {
    return this.systemHealthService.getAlertThresholds();
  }

  @Post("thresholds")
  @ApiOperation({ 
    summary: "Update alert thresholds",
    description: "Update alert threshold settings for monitoring categories"
  })
  @ApiResponse({ 
    status: 200, 
    description: "Alert thresholds updated successfully"
  })
  async updateAlertThresholds(
    @Body() thresholds: Partial<AlertThresholds>
  ): Promise<{ success: boolean; message: string; thresholds: AlertThresholds }> {
    this.systemHealthService.updateAlertThresholds(thresholds);
    const updatedThresholds = this.systemHealthService.getAlertThresholds();
    
    return {
      success: true,
      message: "Alert thresholds updated successfully",
      thresholds: updatedThresholds
    };
  }

  @Get("check")
  @ApiOperation({ 
    summary: "Perform enterprise health check",
    description: "Execute comprehensive health check compatible with enterprise monitoring systems"
  })
  @ApiResponse({ 
    status: 200, 
    description: "Enterprise health check completed successfully"
  })
  async performHealthCheck() {
    return this.systemHealthService.performHealthCheck();
  }

  @Get("status")
  @ApiOperation({ 
    summary: "Get simple health status",
    description: "Returns a simple health status indicator for load balancer health checks"
  })
  @ApiResponse({ 
    status: 200, 
    description: "Health status OK"
  })
  @ApiResponse({ 
    status: 503, 
    description: "Service unhealthy"
  })
  async getHealthStatus(): Promise<{ status: string; timestamp: Date }> {
    const health = await this.systemHealthService.getHealthStatus();
    
    return {
      status: health.status,
      timestamp: health.timestamp
    };
  }
}
