import { Injectable, Logger, InternalServerErrorException } from '@nestjs/common';
import { DatabaseService } from '../../../core/database/database.service';
import { CacheService } from '../../../core/cache/cache.service';

@Injectable()
export class AnalyticsService {
  private readonly logger = new Logger(AnalyticsService.name);

  constructor(
    private readonly databaseService: DatabaseService,
    private readonly redisDatabaseService: CacheService,
  ) {}

  async getDashboardStats(): Promise<any> {
    try {
      const now = new Date();
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

      const stats = {
        users: {
          total: await this.getUserCount(),
          active24h: await this.getActiveUserCount(oneDayAgo),
          active7d: await this.getActiveUserCount(oneWeekAgo),
          active30d: await this.getActiveUserCount(oneMonthAgo),
          premium: await this.getPremiumUserCount(),
        },
        guilds: {
          total: await this.getGuildCount(),
          active24h: await this.getActiveGuildCount(oneDayAgo),
          active7d: await this.getActiveGuildCount(oneWeekAgo),
          active30d: await this.getActiveGuildCount(oneMonthAgo),
          premium: await this.getPremiumGuildCount(),
        },
        commands: {
          total24h: await this.getCommandCount(oneDayAgo),
          total7d: await this.getCommandCount(oneWeekAgo),
          total30d: await this.getCommandCount(oneMonthAgo),
        },
        ai: {
          interactions24h: await this.getAIInteractionCount(oneDayAgo),
          interactions7d: await this.getAIInteractionCount(oneWeekAgo),
          interactions30d: await this.getAIInteractionCount(oneMonthAgo),
          tokensUsed30d: await this.getTokensUsed(oneMonthAgo),
        },
        timestamp: now,
      };

      return stats;
    } catch (error) {
      this.logger.error('Error getting dashboard stats:', error);
      throw new InternalServerErrorException('Failed to retrieve dashboard statistics');
    }
  }

  async getAnalytics(): Promise<any> {
    return this.getDashboardStats();
  }

  async getAnalyticsByPeriod(period: string): Promise<any> {
    try {
      let startDate: Date;
      const now = new Date();

      switch (period) {
        case 'hour':
          startDate = new Date(now.getTime() - 60 * 60 * 1000);
          break;
        case 'day':
          startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          break;
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'month':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        case 'year':
          startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
          break;
        default:
          startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      }

      const stats = {
        period,
        startDate,
        endDate: now,
        users: {
          active: await this.getActiveUserCount(startDate),
          new: await this.getNewUserCount(startDate),
        },
        guilds: {
          active: await this.getActiveGuildCount(startDate),
          new: await this.getNewGuildCount(startDate),
        },
        activity: {
          commands: await this.getCommandCount(startDate),
          aiInteractions: await this.getAIInteractionCount(startDate),
          messages: await this.getMessageCount(startDate),
        },
      };

      return stats;
    } catch (error) {
      this.logger.error(`Error getting analytics for period ${period}:`, error);
      throw new InternalServerErrorException(`Failed to retrieve analytics for period ${period}`);
    }
  }

  private async getUserCount(): Promise<number> {
    try {
      const result = await this.databaseService.query('SELECT COUNT(*) as count FROM users');
      return result[0]?.count || 0;
    } catch (error) {
      this.logger.error('Error getting user count:', error);
      return 0;
    }
  }

  private async getActiveUserCount(since: Date): Promise<number> {
    try {
      const result = await this.databaseService.query(
        'SELECT COUNT(*) as count FROM users WHERE last_seen >= ?',
        [since]
      );
      return result[0]?.count || 0;
    } catch (error) {
      this.logger.error('Error getting active user count:', error);
      return 0;
    }
  }

  private async getNewUserCount(since: Date): Promise<number> {
    try {
      const result = await this.databaseService.query(
        'SELECT COUNT(*) as count FROM users WHERE created_at >= ?',
        [since]
      );
      return result[0]?.count || 0;
    } catch (error) {
      this.logger.error('Error getting new user count:', error);
      return 0;
    }
  }

  private async getPremiumUserCount(): Promise<number> {
    try {
      const result = await this.databaseService.query(
        'SELECT COUNT(*) as count FROM users WHERE is_premium = true'
      );
      return result[0]?.count || 0;
    } catch (error) {
      this.logger.error('Error getting premium user count:', error);
      return 0;
    }
  }

  private async getGuildCount(): Promise<number> {
    try {
      const result = await this.databaseService.query('SELECT COUNT(*) as count FROM guilds');
      return result[0]?.count || 0;
    } catch (error) {
      this.logger.error('Error getting guild count:', error);
      return 0;
    }
  }

  private async getActiveGuildCount(since: Date): Promise<number> {
    try {
      const result = await this.databaseService.query(
        'SELECT COUNT(*) as count FROM guilds WHERE last_activity >= ?',
        [since]
      );
      return result[0]?.count || 0;
    } catch (error) {
      this.logger.error('Error getting active guild count:', error);
      return 0;
    }
  }

  private async getNewGuildCount(since: Date): Promise<number> {
    try {
      const result = await this.databaseService.query(
        'SELECT COUNT(*) as count FROM guilds WHERE created_at >= ?',
        [since]
      );
      return result[0]?.count || 0;
    } catch (error) {
      this.logger.error('Error getting new guild count:', error);
      return 0;
    }
  }

  private async getPremiumGuildCount(): Promise<number> {
    try {
      const result = await this.databaseService.query(
        'SELECT COUNT(*) as count FROM guilds WHERE is_premium = true'
      );
      return result[0]?.count || 0;
    } catch (error) {
      this.logger.error('Error getting premium guild count:', error);
      return 0;
    }
  }

  private async getCommandCount(since: Date): Promise<number> {
    try {
      // This would typically query a commands/interactions table
      const result = await this.databaseService.query(
        'SELECT COUNT(*) as count FROM agent_interactions WHERE created_at >= ?',
        [since]
      );
      return result[0]?.count || 0;
    } catch (error) {
      this.logger.error('Error getting command count:', error);
      return 0;
    }
  }

  private async getAIInteractionCount(since: Date): Promise<number> {
    try {
      const result = await this.databaseService.query(
        'SELECT COUNT(*) as count FROM agent_interactions WHERE created_at >= ?',
        [since]
      );
      return result[0]?.count || 0;
    } catch (error) {
      this.logger.error('Error getting AI interaction count:', error);
      return 0;
    }
  }

  private async getTokensUsed(since: Date): Promise<number> {
    try {
      const result = await this.databaseService.query(
        'SELECT SUM(tokens_used) as total FROM agent_interactions WHERE created_at >= ?',
        [since]
      );
      return result[0]?.total || 0;
    } catch (error) {
      this.logger.error('Error getting tokens used:', error);
      return 0;
    }
  }

  private async getMessageCount(since: Date): Promise<number> {
    try {
      // This would typically query a messages table if it exists
      const result = await this.databaseService.query(
        'SELECT COUNT(*) as count FROM agent_interactions WHERE created_at >= ?',
        [since]
      );
      return result[0]?.count || 0;
    } catch (error) {
      this.logger.error('Error getting message count:', error);
      return 0;
    }
  }
}