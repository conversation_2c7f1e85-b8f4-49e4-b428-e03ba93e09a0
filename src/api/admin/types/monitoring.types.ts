/**
 * Comprehensive monitoring and health check type definitions
 * Enterprise-grade monitoring system types
 */

// Core health monitoring enums
export enum HealthStatus {
  HEALTHY = 'healthy',
  DEGRADED = 'degraded',
  CRITICAL = 'critical',
  UNHEALTHY = 'unhealthy'
}

export enum ServiceStatus {
  UP = 'up',
  DOWN = 'down',
  DEGRADED = 'degraded',
  MAINTENANCE = 'maintenance',
  UNKNOWN = 'unknown'
}

export enum AlertSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export enum AlertType {
  // System resource alerts
  MEMORY_HIGH = 'memory_high',
  MEMORY_CRITICAL = 'memory_critical',
  CPU_HIGH = 'cpu_high',
  CPU_CRITICAL = 'cpu_critical',
  DISK_FULL = 'disk_full',
  DISK_WARNING = 'disk_warning',
  
  // Service connectivity alerts
  DATABASE_CONNECTION_FAILED = 'database_connection_failed',
  DATABASE_SLOW_RESPONSE = 'database_slow_response',
  REDIS_CONNECTION_FAILED = 'redis_connection_failed',
  REDIS_SLOW_RESPONSE = 'redis_slow_response',
  
  // Performance alerts
  RESPONSE_TIME_HIGH = 'response_time_high',
  THROUGHPUT_LOW = 'throughput_low',
  ERROR_RATE_HIGH = 'error_rate_high',
  
  // Application alerts
  SERVICE_DOWN = 'service_down',
  SERVICE_DEGRADED = 'service_degraded',
  HEALTH_CHECK_FAILED = 'health_check_failed',
  
  // Security alerts
  SECURITY_BREACH_ATTEMPT = 'security_breach_attempt',
  RATE_LIMIT_EXCEEDED = 'rate_limit_exceeded',
  UNAUTHORIZED_ACCESS = 'unauthorized_access'
}

// Health check interfaces
export interface HealthCheckResult {
  name: string;
  status: HealthStatus;
  responseTime: number | null;
  message: string;
  error?: string;
  details?: HealthCheckDetails;
  timestamp: Date;
}

export interface HealthCheckDetails {
  [key: string]: any;
  thresholds?: AlertThresholds;
  metadata?: HealthCheckMetadata;
}

export interface HealthCheckMetadata {
  version?: string;
  component?: string;
  checkType?: string;
  dependencies?: string[];
  tags?: string[];
}

export interface ComprehensiveHealthCheck extends HealthCheckResult {
  dependencies?: HealthCheckResult[];
  metrics?: PerformanceMetrics;
  uptime?: number;
  environment?: string;
  version?: string;
}

// System metrics interfaces
export interface MemoryMetrics {
  rss: number;           // Resident Set Size
  heapTotal: number;     // Total heap memory
  heapUsed: number;      // Used heap memory
  external: number;      // External memory (C++ objects bound to JS objects)
  arrayBuffers: number;  // ArrayBuffer memory
  percentage?: number;   // Memory usage percentage
  
  // Additional calculated metrics
  heapUtilization?: number;
  memoryGrowthRate?: number;
  gcFrequency?: number;
}

export interface CpuMetrics {
  user: number;          // User CPU time
  system: number;        // System CPU time
  percentage?: number;   // CPU usage percentage
  
  // Additional calculated metrics
  loadAverage?: number[];
  cores?: number;
  utilization?: CpuUtilization;
}

export interface CpuUtilization {
  idle: number;
  user: number;
  system: number;
  nice?: number;
  irq?: number;
  softirq?: number;
}

export interface DiskMetrics {
  totalSpace: number;
  freeSpace: number;
  usedSpace: number;
  percentage: number;
  
  // Additional metrics
  inodes?: DiskInodeMetrics;
  io?: DiskIOMetrics;
}

export interface DiskInodeMetrics {
  total: number;
  free: number;
  used: number;
  percentage: number;
}

export interface DiskIOMetrics {
  readOps: number;
  writeOps: number;
  readBytes: number;
  writeBytes: number;
}

export interface NetworkMetrics {
  bytesReceived: number;
  bytesSent: number;
  packetsReceived: number;
  packetsSent: number;
  errors: number;
  dropped: number;
  
  // Connection metrics
  activeConnections: number;
  establishedConnections: number;
  timeWaitConnections: number;
}

export interface ProcessMetrics {
  pid: number;
  ppid?: number;
  version: string;
  platform: string;
  arch: string;
  nodeVersion: string;
  uptime: number;
  
  // Process specific metrics
  threads?: number;
  handles?: number;
  fileDescriptors?: number;
}

export interface PerformanceMetrics {
  timestamp: Date;
  uptime: number;
  memory: MemoryMetrics;
  cpu: CpuMetrics;
  disk?: DiskMetrics;
  network?: NetworkMetrics;
  process: ProcessMetrics;
  
  // Application specific metrics
  requests?: RequestMetrics;
  database?: DatabaseMetrics;
  cache?: CacheMetrics;
}

export interface RequestMetrics {
  total: number;
  successful: number;
  failed: number;
  averageResponseTime: number;
  p95ResponseTime: number;
  p99ResponseTime: number;
  throughput: number; // requests per second
}

export interface DatabaseMetrics {
  connectionPoolSize: number;
  activeConnections: number;
  idleConnections: number;
  averageQueryTime: number;
  slowQueries: number;
  failedQueries: number;
}

export interface CacheMetrics {
  hitRate: number;
  missRate: number;
  evictions: number;
  memoryUsage: number;
  keyCount: number;
}

// Alert threshold configuration
export interface AlertThresholds {
  memory: ResourceThreshold;
  cpu: ResourceThreshold;
  disk: ResourceThreshold;
  responseTime: ResponseTimeThreshold;
  throughput: ThroughputThreshold;
  errorRate: ErrorRateThreshold;
}

export interface ResourceThreshold {
  warning: number;   // percentage
  critical: number;  // percentage
  duration?: number; // seconds - how long before alerting
}

export interface ResponseTimeThreshold {
  warning: number;   // milliseconds
  critical: number;  // milliseconds
  p95Warning?: number;
  p95Critical?: number;
}

export interface ThroughputThreshold {
  minWarning: number;   // requests per second
  minCritical: number;  // requests per second
}

export interface ErrorRateThreshold {
  warning: number;   // percentage
  critical: number;  // percentage
  window: number;    // time window in seconds
}

// Alert management
export interface Alert {
  id: string;
  type: AlertType;
  severity: AlertSeverity;
  status: AlertStatus;
  title: string;
  message: string;
  details: AlertDetails;
  source: AlertSource;
  timestamp: Date;
  acknowledged: boolean;
  acknowledgedBy?: string;
  acknowledgedAt?: Date;
  resolvedAt?: Date;
  resolvedBy?: string;
  escalated?: boolean;
  escalatedAt?: Date;
  
  // Alert lifecycle
  firstOccurrence: Date;
  lastOccurrence: Date;
  occurrenceCount: number;
  
  // Notification tracking
  notificationsSent: NotificationRecord[];
}

export interface AlertDetails {
  service: string;
  component?: string;
  metric?: string;
  currentValue?: number | string;
  thresholdValue?: number | string;
  trend?: 'increasing' | 'decreasing' | 'stable';
  impact?: AlertImpact;
  recommendation?: string;
  runbookUrl?: string;
  
  // Context
  environment: string;
  region?: string;
  datacenter?: string;
  
  // Additional metadata
  [key: string]: any;
}

export interface AlertSource {
  service: string;
  instance?: string;
  host: string;
  region?: string;
  environment: string;
}

export interface AlertImpact {
  severity: 'low' | 'medium' | 'high' | 'critical';
  affectedUsers?: number;
  affectedServices?: string[];
  businessImpact?: string;
}

export enum AlertStatus {
  ACTIVE = 'active',
  ACKNOWLEDGED = 'acknowledged',
  RESOLVED = 'resolved',
  SUPPRESSED = 'suppressed',
  ESCALATED = 'escalated'
}

export interface NotificationRecord {
  channel: NotificationChannel;
  recipient: string;
  sentAt: Date;
  status: 'sent' | 'delivered' | 'failed' | 'bounced';
  error?: string;
}

export enum NotificationChannel {
  EMAIL = 'email',
  SMS = 'sms',
  SLACK = 'slack',
  DISCORD = 'discord',
  WEBHOOK = 'webhook',
  PAGERDUTY = 'pagerduty'
}

// Comprehensive monitoring data
export interface MonitoringData {
  service: string;
  instance?: string;
  environment: string;
  status: HealthStatus;
  timestamp: Date;
  
  // Core monitoring data
  healthChecks: HealthCheckResult[];
  metrics: PerformanceMetrics;
  alerts: Alert[];
  
  // Additional context
  uptime: number;
  version: string;
  dependencies?: DependencyStatus[];
  configuration?: MonitoringConfiguration;
}

export interface DependencyStatus {
  name: string;
  type: DependencyType;
  status: HealthStatus;
  responseTime?: number;
  lastCheck: Date;
  version?: string;
  endpoint?: string;
}

export enum DependencyType {
  DATABASE = 'database',
  CACHE = 'cache',
  MESSAGE_QUEUE = 'message_queue',
  EXTERNAL_API = 'external_api',
  MICROSERVICE = 'microservice',
  FILE_SYSTEM = 'file_system',
  NETWORK_RESOURCE = 'network_resource'
}

export interface MonitoringConfiguration {
  checkInterval: number;        // seconds
  alertingEnabled: boolean;
  thresholds: AlertThresholds;
  retentionPeriod: number;      // days
  
  // Feature flags
  enabledChecks: string[];
  disabledAlerts: AlertType[];
  
  // Notification settings
  notificationChannels: NotificationChannel[];
  escalationRules: EscalationRule[];
}

export interface EscalationRule {
  alertTypes: AlertType[];
  severities: AlertSeverity[];
  delay: number;                // minutes
  recipients: EscalationRecipient[];
}

export interface EscalationRecipient {
  type: 'user' | 'team' | 'external';
  identifier: string;
  channels: NotificationChannel[];
}

// Health check registry for tracking all health checks
export interface HealthCheckRegistry {
  checks: Map<string, RegisteredHealthCheck>;
  lastUpdate: Date;
}

export interface RegisteredHealthCheck {
  name: string;
  description: string;
  category: HealthCheckCategory;
  handler: HealthCheckHandler;
  schedule: HealthCheckSchedule;
  timeout: number;
  enabled: boolean;
  
  // Status tracking
  lastRun?: Date;
  lastResult?: HealthCheckResult;
  consecutiveFailures: number;
}

export enum HealthCheckCategory {
  INFRASTRUCTURE = 'infrastructure',
  DATABASE = 'database',
  CACHE = 'cache',
  EXTERNAL_SERVICE = 'external_service',
  APPLICATION = 'application',
  SECURITY = 'security',
  BUSINESS_LOGIC = 'business_logic'
}

export type HealthCheckHandler = () => Promise<HealthCheckResult>;

export interface HealthCheckSchedule {
  interval: number;    // seconds
  cron?: string;       // cron expression for complex scheduling
  timezone?: string;
}

// System health dashboard data
export interface HealthDashboard {
  overview: HealthOverview;
  services: ServiceHealthStatus[];
  metrics: DashboardMetrics;
  alerts: AlertSummary;
  trends: HealthTrends;
  lastUpdated: Date;
}

export interface HealthOverview {
  overallStatus: HealthStatus;
  totalServices: number;
  healthyServices: number;
  degradedServices: number;
  unhealthyServices: number;
  uptime: number;
  availability: number; // percentage
}

export interface ServiceHealthStatus {
  name: string;
  status: HealthStatus;
  responseTime: number;
  uptime: number;
  lastCheck: Date;
  issues?: string[];
}

export interface DashboardMetrics {
  systemLoad: number;
  memoryUsage: number;
  diskUsage: number;
  networkTraffic: NetworkTraffic;
  requestRate: number;
  errorRate: number;
  avgResponseTime: number;
}

export interface NetworkTraffic {
  inbound: number;    // bytes per second
  outbound: number;   // bytes per second
}

export interface AlertSummary {
  total: number;
  critical: number;
  high: number;
  medium: number;
  low: number;
  acknowledged: number;
  unacknowledged: number;
  recentAlerts: Alert[];
}

export interface HealthTrends {
  period: string;           // '1h', '24h', '7d', '30d'
  dataPoints: TrendDataPoint[];
  predictions?: HealthPrediction[];
}

export interface TrendDataPoint {
  timestamp: Date;
  overallHealth: number;    // 0-100 health score
  availability: number;     // percentage
  responseTime: number;     // average
  errorRate: number;        // percentage
}

export interface HealthPrediction {
  metric: string;
  predictedValue: number;
  confidence: number;       // percentage
  timeHorizon: number;      // hours
  trend: 'improving' | 'degrading' | 'stable';
}

// SRE and reliability metrics
export interface SLIMetrics {
  availability: AvailabilitySLI;
  latency: LatencySLI;
  errorRate: ErrorRateSLI;
  throughput: ThroughputSLI;
}

export interface AvailabilitySLI {
  target: number;           // percentage (e.g., 99.9)
  current: number;          // current availability
  period: string;           // measurement period
  uptime: number;           // seconds
  downtime: number;         // seconds
  incidents: number;        // count of incidents
}

export interface LatencySLI {
  target: number;           // milliseconds (e.g., p95 < 200ms)
  current: number;          // current p95 latency
  percentile: number;       // 50, 90, 95, 99
  period: string;
}

export interface ErrorRateSLI {
  target: number;           // percentage (e.g., < 0.1%)
  current: number;          // current error rate
  period: string;
  totalRequests: number;
  errorRequests: number;
}

export interface ThroughputSLI {
  target: number;           // requests per second
  current: number;          // current throughput
  period: string;
  peak: number;             // peak throughput in period
}

// Export utility types
export type HealthCheckName = string;
export type AlertId = string;
export type ServiceName = string;

// Type guards
export function isHealthy(status: HealthStatus): boolean {
  return status === HealthStatus.HEALTHY;
}

export function isCriticalAlert(alert: Alert): boolean {
  return alert.severity === AlertSeverity.CRITICAL;
}

export function isActiveAlert(alert: Alert): boolean {
  return alert.status === AlertStatus.ACTIVE;
}

// Metric calculation utilities
export interface MetricCalculations {
  calculateAvailability(uptime: number, totalTime: number): number;
  calculateErrorRate(errors: number, total: number): number;
  calculateHealthScore(checks: HealthCheckResult[]): number;
  calculateTrend(dataPoints: number[]): 'increasing' | 'decreasing' | 'stable';
}