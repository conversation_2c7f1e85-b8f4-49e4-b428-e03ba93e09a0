/**
 * Analytics Type Definitions
 * Complete type safety for analytics data collection and reporting
 */

/**
 * Time period enumeration for analytics queries
 */
export type AnalyticsPeriod = 'hour' | 'day' | 'week' | 'month' | 'year' | 'custom';

/**
 * Date range interface for custom analytics periods
 */
export interface AnalyticsDateRange {
  startDate: Date;
  endDate: Date;
}

/**
 * User analytics metrics interface
 */
export interface UserAnalyticsMetrics {
  total: number;
  active24h: number;
  active7d: number;
  active30d: number;
  premium: number;
  new?: number;
}

/**
 * Guild analytics metrics interface
 */
export interface GuildAnalyticsMetrics {
  total: number;
  active24h: number;
  active7d: number;
  active30d: number;
  premium: number;
  new?: number;
}

/**
 * Command analytics metrics interface
 */
export interface CommandAnalyticsMetrics {
  total24h: number;
  total7d: number;
  total30d: number;
  byType?: Record<string, number>;
  topCommands?: Array<{ command: string; count: number }>;
}

/**
 * AI analytics metrics interface
 */
export interface AIAnalyticsMetrics {
  interactions24h: number;
  interactions7d: number;
  interactions30d: number;
  tokensUsed30d: number;
  averageResponseTime?: number;
  successRate?: number;
  byAgentType?: Record<string, number>;
  sentimentDistribution?: {
    positive: number;
    neutral: number;
    negative: number;
  };
}

/**
 * Activity analytics metrics interface
 */
export interface ActivityAnalyticsMetrics {
  commands: number;
  aiInteractions: number;
  messages: number;
  panelInteractions?: number;
}

/**
 * Performance analytics metrics interface
 */
export interface PerformanceAnalyticsMetrics {
  averageResponseTime: number;
  uptime: number;
  errorRate: number;
  throughput: number;
  memoryUsage?: number;
  cpuUsage?: number;
}

/**
 * Panel analytics metrics interface
 */
export interface PanelAnalyticsMetrics {
  totalViews: number;
  uniqueUsers: number;
  totalInteractions: number;
  averageEngagementTime: number;
  bounceRate: number;
  conversionRate?: number;
  topPanels?: Array<{ panelId: string; views: number }>;
}

/**
 * Dashboard statistics interface with complete type safety
 */
export interface DashboardStatistics {
  users: UserAnalyticsMetrics;
  guilds: GuildAnalyticsMetrics;
  commands: CommandAnalyticsMetrics;
  ai: AIAnalyticsMetrics;
  performance?: PerformanceAnalyticsMetrics;
  panels?: PanelAnalyticsMetrics;
  timestamp: Date;
}

/**
 * Periodic analytics statistics interface
 */
export interface PeriodicAnalyticsStatistics {
  period: AnalyticsPeriod;
  startDate: Date;
  endDate: Date;
  users: {
    active: number;
    new: number;
  };
  guilds: {
    active: number;
    new: number;
  };
  activity: ActivityAnalyticsMetrics;
  performance?: PerformanceAnalyticsMetrics;
  growth?: {
    userGrowthRate: number;
    guildGrowthRate: number;
    activityGrowthRate: number;
  };
}

/**
 * Analytics query options interface
 */
export interface AnalyticsQueryOptions {
  period?: AnalyticsPeriod;
  dateRange?: AnalyticsDateRange;
  includePerformance?: boolean;
  includePanels?: boolean;
  includeGrowthMetrics?: boolean;
  guildId?: string;
  channelId?: string;
  limit?: number;
  offset?: number;
}

/**
 * Database query result interface
 */
export interface DatabaseQueryResult {
  count?: number;
  total?: number;
  avg_response_time?: number;
  success_rate?: number;
  sentiment_score?: number;
  agent_type?: string;
  interaction_type?: string;
  panel_id?: string;
  views?: number;
  command?: string;
  positive?: number;
  neutral?: number;
  negative?: number;
  [key: string]: any;
}

/**
 * Analytics export data interface
 */
export interface AnalyticsExportData {
  exportType: 'csv' | 'json' | 'excel';
  data: DashboardStatistics | PeriodicAnalyticsStatistics;
  generatedAt: Date;
  period: AnalyticsPeriod;
  filters?: AnalyticsQueryOptions;
}

/**
 * Real-time analytics metrics interface
 */
export interface RealTimeAnalyticsMetrics {
  currentActiveUsers: number;
  currentActiveGuilds: number;
  messagesPerMinute: number;
  commandsPerMinute: number;
  aiInteractionsPerMinute: number;
  averageResponseTime: number;
  systemLoad: {
    cpu: number;
    memory: number;
    disk: number;
  };
  timestamp: Date;
}

/**
 * Statistical analysis interface
 */
export interface StatisticalAnalysis {
  mean: number;
  median: number;
  standardDeviation: number;
  percentiles: {
    p25: number;
    p50: number;
    p75: number;
    p90: number;
    p95: number;
    p99: number;
  };
  correlation?: number;
}

/**
 * Cohort analysis interface
 */
export interface CohortAnalysis {
  cohortDate: Date;
  totalUsers: number;
  retentionRates: {
    week1: number;
    week2: number;
    week4: number;
    week8: number;
    week12: number;
  };
}

/**
 * Funnel analysis interface
 */
export interface FunnelAnalysis {
  name: string;
  steps: Array<{
    step: string;
    users: number;
    conversionRate: number;
    dropoffRate: number;
  }>;
  overallConversionRate: number;
}

/**
 * Aggregated analytics data interface
 */
export interface AggregatedAnalyticsData {
  dailyMetrics: PeriodicAnalyticsStatistics[];
  weeklyMetrics: PeriodicAnalyticsStatistics[];
  monthlyMetrics: PeriodicAnalyticsStatistics[];
  trends: {
    userTrend: 'increasing' | 'decreasing' | 'stable';
    activityTrend: 'increasing' | 'decreasing' | 'stable';
    performanceTrend: 'improving' | 'degrading' | 'stable';
  };
}

/**
 * Metrics calculation configuration
 */
export interface MetricsConfig {
  includeCache: boolean;
  cacheTimeout: number;
  precision: number;
  timeZone: string;
}

/**
 * Analytics data validation schema
 */
export interface AnalyticsValidationSchema {
  required: string[];
  optional: string[];
  types: Record<string, string>;
  ranges: Record<string, { min?: number; max?: number }>;
}