import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  UseGuards,
  Req,
  HttpCode,
  HttpStatus,
  Query,
  ValidationPipe,
  UsePipes,
  ParseEnumPipe,
  UseInterceptors,
  UseFilters,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiBody,
  ApiQuery,
  ApiOkResponse,
  ApiBadRequestResponse,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
} from '@nestjs/swagger';
import { AdminService, AdminUser } from './admin.service';
import { SystemHealthService } from './components/system-health.service';
import { AnalyticsService } from './components/analytics.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AdminGuard } from './guards/admin.guard';
import {
  SessionsResponse,
  UserStatsResponse,
  GuildStatsResponse,
  SystemHealthResponse,
  SystemMetricsResponse,
  AnalyticsResponse,
  PeriodAnalyticsResponse,
  OperationResponse,
  SystemLogsResponse,
  SystemConfigResponse,
  UpdateUserStatusDto,
  UpdateSystemConfigDto,
  LogQueryDto,
  AnalyticsPeriod,
  AuthenticatedRequest,
} from './dto/admin.dto';
import {
  ApiResponse as ApiResponseInterface,
  ErrorResponse,
  SuccessResponse,
} from './interfaces/admin-response.interface';
import { AdminResponseInterceptor } from './interceptors/admin-response.interceptor';
import { AdminExceptionFilter } from './filters/admin-exception.filter';

@ApiTags('admin')
@Controller('admin')
@UseGuards(JwtAuthGuard, AdminGuard)
@UseInterceptors(AdminResponseInterceptor)
@UseFilters(AdminExceptionFilter)
@ApiBearerAuth()
@UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
export class AdminController {
  constructor(
    private readonly adminService: AdminService,
    private readonly systemHealthService: SystemHealthService,
    private readonly analyticsService: AnalyticsService,
  ) {}

  @Get('sessions')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'Get active user sessions',
    description: 'Retrieve all currently active user sessions. Admin only.' 
  })
  @ApiOkResponse({
    description: 'Active sessions retrieved successfully',
    type: SessionsResponse,
  })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  @ApiForbiddenResponse({ description: 'Admin access required' })
  @ApiInternalServerErrorResponse({ description: 'Internal server error' })
  async getSessions(
    @Req() req: AuthenticatedRequest
  ): Promise<SuccessResponse<SessionsResponse>> {
    const sessions = await this.adminService.getActiveSessions(req.user as AdminUser);
    return {
      success: true,
      message: 'Active sessions retrieved successfully',
      data: {
        sessions,
        total: sessions.length,
        timestamp: new Date(),
      },
      timestamp: new Date().toISOString(),
    };
  }

  @Get('users')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'Get user statistics',
    description: 'Retrieve comprehensive user statistics including total, active, and premium users. Admin only.' 
  })
  @ApiOkResponse({
    description: 'User statistics retrieved successfully',
    type: UserStatsResponse,
  })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  @ApiForbiddenResponse({ description: 'Admin access required' })
  @ApiInternalServerErrorResponse({ description: 'Internal server error' })
  async getUserStats(
    @Req() req: AuthenticatedRequest
  ): Promise<SuccessResponse<UserStatsResponse>> {
    const stats = await this.adminService.getUserStats(req.user as AdminUser);
    return {
      success: true,
      message: 'User statistics retrieved successfully',
      data: {
        stats,
        timestamp: new Date(),
      },
      timestamp: new Date().toISOString(),
    };
  }

  @Get('guilds')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'Get guild statistics',
    description: 'Retrieve comprehensive guild statistics including total, active, and premium guilds. Admin only.' 
  })
  @ApiOkResponse({
    description: 'Guild statistics retrieved successfully',
    type: GuildStatsResponse,
  })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  @ApiForbiddenResponse({ description: 'Admin access required' })
  @ApiInternalServerErrorResponse({ description: 'Internal server error' })
  async getGuildStats(
    @Req() req: AuthenticatedRequest
  ): Promise<SuccessResponse<GuildStatsResponse>> {
    const stats = await this.adminService.getGuildStats(req.user as AdminUser);
    return {
      success: true,
      message: 'Guild statistics retrieved successfully',
      data: {
        stats,
        timestamp: new Date(),
      },
      timestamp: new Date().toISOString(),
    };
  }

  @Get('system/health')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'Get system health status',
    description: 'Retrieve comprehensive system health including database, Redis, memory, and disk status. Admin only.' 
  })
  @ApiOkResponse({
    description: 'System health status retrieved successfully',
    type: SystemHealthResponse,
  })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  @ApiForbiddenResponse({ description: 'Admin access required' })
  @ApiInternalServerErrorResponse({ description: 'Internal server error' })
  async getSystemHealth(
    @Req() req: AuthenticatedRequest
  ): Promise<SuccessResponse<SystemHealthResponse>> {
    const health = await this.systemHealthService.getHealthStatus();
    return {
      success: true,
      message: 'System health status retrieved successfully',
      data: {
        health,
        timestamp: new Date(),
      },
      timestamp: new Date().toISOString(),
    };
  }

  @Get('system/metrics')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'Get system metrics',
    description: 'Retrieve detailed system performance metrics including CPU, memory, and process information. Admin only.' 
  })
  @ApiOkResponse({
    description: 'System metrics retrieved successfully',
    type: SystemMetricsResponse,
  })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  @ApiForbiddenResponse({ description: 'Admin access required' })
  @ApiInternalServerErrorResponse({ description: 'Internal server error' })
  async getSystemMetrics(
    @Req() req: AuthenticatedRequest
  ): Promise<SuccessResponse<SystemMetricsResponse>> {
    const metrics = await this.systemHealthService.getSystemMetrics();
    return {
      success: true,
      message: 'System metrics retrieved successfully',
      data: {
        metrics,
        timestamp: new Date(),
      },
      timestamp: new Date().toISOString(),
    };
  }

  @Get('analytics')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'Get analytics dashboard data',
    description: 'Retrieve comprehensive analytics including user, guild, command, and AI interaction statistics. Admin only.' 
  })
  @ApiOkResponse({
    description: 'Analytics data retrieved successfully',
    type: AnalyticsResponse,
  })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  @ApiForbiddenResponse({ description: 'Admin access required' })
  @ApiInternalServerErrorResponse({ description: 'Internal server error' })
  async getAnalytics(
    @Req() req: AuthenticatedRequest
  ): Promise<SuccessResponse<AnalyticsResponse>> {
    const analytics = await this.analyticsService.getAnalytics();
    return {
      success: true,
      message: 'Analytics data retrieved successfully',
      data: {
        analytics,
        timestamp: new Date(),
      },
      timestamp: new Date().toISOString(),
    };
  }

  @Get('analytics/:period')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'Get analytics data for specific time period',
    description: 'Retrieve analytics data filtered by time period (hour, day, week, month, year). Admin only.' 
  })
  @ApiParam({
    name: 'period',
    enum: AnalyticsPeriod,
    description: 'Time period for analytics data',
    example: 'day',
  })
  @ApiOkResponse({
    description: 'Analytics data for period retrieved successfully',
    type: PeriodAnalyticsResponse,
  })
  @ApiBadRequestResponse({ description: 'Invalid period parameter' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  @ApiForbiddenResponse({ description: 'Admin access required' })
  @ApiInternalServerErrorResponse({ description: 'Internal server error' })
  async getAnalyticsByPeriod(
    @Param('period', new ParseEnumPipe(AnalyticsPeriod)) period: AnalyticsPeriod,
    @Req() req: AuthenticatedRequest,
  ): Promise<SuccessResponse<PeriodAnalyticsResponse>> {
    const analytics = await this.analyticsService.getAnalyticsByPeriod(period);
    return {
      success: true,
      message: `Analytics data for ${period} period retrieved successfully`,
      data: {
        analytics,
        timestamp: new Date(),
      },
      timestamp: new Date().toISOString(),
    };
  }

  @Post('cache/clear')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'Clear system cache',
    description: 'Clear all cached data from Redis. This operation may temporarily impact performance. Admin only.' 
  })
  @ApiOkResponse({
    description: 'Cache cleared successfully',
    type: OperationResponse,
  })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  @ApiForbiddenResponse({ description: 'Admin access required' })
  @ApiInternalServerErrorResponse({ description: 'Internal server error' })
  async clearCache(
    @Req() req: AuthenticatedRequest
  ): Promise<SuccessResponse<OperationResponse>> {
    const cacheStats = await this.adminService.clearCache(req.user as AdminUser);
    return {
      success: true,
      message: 'System cache cleared successfully',
      data: {
        success: true,
        message: `Cache cleared successfully. ${cacheStats.totalKeys} keys processed.`,
        metadata: cacheStats,
        timestamp: new Date(),
      },
      timestamp: new Date().toISOString(),
    };
  }

  @Post('maintenance/start')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'Start maintenance mode',
    description: 'Enable maintenance mode to prevent normal user operations. Admin only.' 
  })
  @ApiOkResponse({
    description: 'Maintenance mode started successfully',
    type: OperationResponse,
  })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  @ApiForbiddenResponse({ description: 'Admin access required' })
  @ApiInternalServerErrorResponse({ description: 'Internal server error' })
  async startMaintenance(
    @Req() req: AuthenticatedRequest,
    @Body() body?: { duration?: number; message?: string }
  ): Promise<SuccessResponse<OperationResponse>> {
    await this.adminService.startMaintenance(
      req.user as AdminUser,
      body?.duration,
      body?.message
    );
    return {
      success: true,
      message: 'Maintenance mode started successfully',
      data: {
        success: true,
        message: 'Maintenance mode is now active',
        timestamp: new Date(),
      },
      timestamp: new Date().toISOString(),
    };
  }

  @Post('maintenance/stop')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'Stop maintenance mode',
    description: 'Disable maintenance mode to restore normal user operations. Admin only.' 
  })
  @ApiOkResponse({
    description: 'Maintenance mode stopped successfully',
    type: OperationResponse,
  })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  @ApiForbiddenResponse({ description: 'Admin access required' })
  @ApiInternalServerErrorResponse({ description: 'Internal server error' })
  async stopMaintenance(
    @Req() req: AuthenticatedRequest
  ): Promise<SuccessResponse<OperationResponse>> {
    await this.adminService.stopMaintenance(req.user as AdminUser);
    return {
      success: true,
      message: 'Maintenance mode stopped successfully',
      data: {
        success: true,
        message: 'Maintenance mode has been disabled',
        timestamp: new Date(),
      },
      timestamp: new Date().toISOString(),
    };
  }

  @Delete('sessions/:sessionId')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'Terminate a user session',
    description: 'Forcefully terminate a specific user session by session ID. Admin only.' 
  })
  @ApiParam({
    name: 'sessionId',
    description: 'Unique session identifier',
    example: 'sess_abc123def456',
  })
  @ApiOkResponse({
    description: 'Session terminated successfully',
    type: OperationResponse,
  })
  @ApiBadRequestResponse({ description: 'Invalid session ID' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  @ApiForbiddenResponse({ description: 'Admin access required' })
  @ApiInternalServerErrorResponse({ description: 'Internal server error' })
  async terminateSession(
    @Param('sessionId') sessionId: string,
    @Req() req: AuthenticatedRequest,
    @Body() body?: { reason?: string }
  ): Promise<SuccessResponse<OperationResponse>> {
    await this.adminService.terminateSession(
      sessionId,
      req.user as AdminUser,
      body?.reason
    );
    return {
      success: true,
      message: 'Session terminated successfully',
      data: {
        success: true,
        message: `Session ${sessionId} has been terminated`,
        timestamp: new Date(),
      },
      timestamp: new Date().toISOString(),
    };
  }

  @Put('users/:userId/status')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'Update user account status',
    description: 'Update the status of a user account (active, suspended, banned, etc.). Admin only.' 
  })
  @ApiParam({
    name: 'userId',
    description: 'Unique user identifier',
    example: '12345678901234567890',
  })
  @ApiBody({
    description: 'User status update data',
    type: UpdateUserStatusDto,
  })
  @ApiOkResponse({
    description: 'User status updated successfully',
    type: OperationResponse,
  })
  @ApiBadRequestResponse({ description: 'Invalid user ID or status data' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  @ApiForbiddenResponse({ description: 'Admin access required' })
  @ApiInternalServerErrorResponse({ description: 'Internal server error' })
  async updateUserStatus(
    @Param('userId') userId: string,
    @Body() statusUpdate: UpdateUserStatusDto,
    @Req() req: AuthenticatedRequest,
  ): Promise<SuccessResponse<OperationResponse>> {
    const updatedUser = await this.adminService.updateUserStatus(
      userId,
      statusUpdate,
      req.user as AdminUser
    );
    return {
      success: true,
      message: 'User status updated successfully',
      data: {
        success: true,
        message: `User ${updatedUser.username} (${userId}) status updated to ${statusUpdate.status}`,
        metadata: {
          userId: updatedUser.id,
          username: updatedUser.username,
          newStatus: statusUpdate.status,
        },
        timestamp: new Date(),
      },
      timestamp: new Date().toISOString(),
    };
  }

  @Get('logs')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'Get system logs',
    description: 'Retrieve system logs with optional filtering by level, source, or date range. Admin only.' 
  })
  @ApiQuery({
    name: 'level',
    required: false,
    description: 'Filter logs by level (error, warn, info, debug)',
    example: 'error',
  })
  @ApiQuery({
    name: 'source',
    required: false,
    description: 'Filter logs by source service',
    example: 'AdminService',
  })
  @ApiQuery({
    name: 'startDate',
    required: false,
    description: 'Start date for log filtering (ISO 8601)',
    example: '2023-01-01T00:00:00Z',
  })
  @ApiQuery({
    name: 'endDate',
    required: false,
    description: 'End date for log filtering (ISO 8601)',
    example: '2023-12-31T23:59:59Z',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Maximum number of logs to return (1-1000)',
    example: 100,
  })
  @ApiOkResponse({
    description: 'System logs retrieved successfully',
    type: SystemLogsResponse,
  })
  @ApiBadRequestResponse({ description: 'Invalid query parameters' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  @ApiForbiddenResponse({ description: 'Admin access required' })
  @ApiInternalServerErrorResponse({ description: 'Internal server error' })
  async getSystemLogs(
    @Query() query: LogQueryDto,
    @Req() req: AuthenticatedRequest
  ): Promise<SuccessResponse<SystemLogsResponse>> {
    const logs = await this.adminService.getSystemLogs(req.user as AdminUser, {
      level: query.level as 'error' | 'warn' | 'info' | 'debug',
      source: query.source,
      limit: query.limit,
      since: query.startDate ? new Date(query.startDate) : undefined,
    });
    return {
      success: true,
      message: 'System logs retrieved successfully',
      data: {
        logs,
        total: logs.length,
        filters: query,
        timestamp: new Date(),
      },
      timestamp: new Date().toISOString(),
    };
  }

  @Get('config')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'Get system configuration',
    description: 'Retrieve current system configuration including features, limits, and environment settings. Admin only.' 
  })
  @ApiOkResponse({
    description: 'System configuration retrieved successfully',
    type: SystemConfigResponse,
  })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  @ApiForbiddenResponse({ description: 'Admin access required' })
  @ApiInternalServerErrorResponse({ description: 'Internal server error' })
  async getSystemConfig(
    @Req() req: AuthenticatedRequest
  ): Promise<SuccessResponse<SystemConfigResponse>> {
    const config = await this.adminService.getSystemConfig(req.user as AdminUser);
    return {
      success: true,
      message: 'System configuration retrieved successfully',
      data: {
        config,
        timestamp: new Date(),
      },
      timestamp: new Date().toISOString(),
    };
  }

  @Put('config')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'Update system configuration',
    description: 'Update system configuration settings including features and limits. Admin only.' 
  })
  @ApiBody({
    description: 'System configuration update data',
    type: UpdateSystemConfigDto,
  })
  @ApiOkResponse({
    description: 'System configuration updated successfully',
    type: OperationResponse,
  })
  @ApiBadRequestResponse({ description: 'Invalid configuration data' })
  @ApiUnauthorizedResponse({ description: 'Authentication required' })
  @ApiForbiddenResponse({ description: 'Admin access required' })
  @ApiInternalServerErrorResponse({ description: 'Internal server error' })
  async updateSystemConfig(
    @Body() configUpdate: UpdateSystemConfigDto,
    @Req() req: AuthenticatedRequest,
  ): Promise<SuccessResponse<OperationResponse>> {
    const updatedConfig = await this.adminService.updateSystemConfig(
      configUpdate,
      req.user as AdminUser
    );
    return {
      success: true,
      message: 'System configuration updated successfully',
      data: {
        success: true,
        message: 'Configuration changes have been applied',
        updatedConfig,
        timestamp: new Date(),
      },
      timestamp: new Date().toISOString(),
    };
  }
}