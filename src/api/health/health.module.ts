import { Module } from '@nestjs/common';
import { TerminusModule } from '@nestjs/terminus';
import { DatabaseModule } from '../../core/database/database.module';
import { DiscordModule } from '../../discord/discord.module';
import { HealthController } from './health.controller';

@Module({
  imports: [TerminusModule, DatabaseModule, DiscordModule],
  controllers: [HealthController]
})
export class HealthModule {}