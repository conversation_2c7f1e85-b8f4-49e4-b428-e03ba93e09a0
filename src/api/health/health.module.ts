import { Module } from '@nestjs/common';
import { TerminusModule } from '@nestjs/terminus';
import { HealthController } from './health.controller';
import { DatabaseModule } from '../../core/database/database.module';
import { DiscordModule } from '../../discord/discord.module';

@Module({
  imports: [TerminusModule, DatabaseModule, DiscordModule],
  controllers: [HealthController])
});
export class HealthModule {}