import { Module } from '@nestjs/common';
import { DatabaseModule } from '../../core/database/database.module';
import { SecurityModule } from '../../core/security/security.module';
import { WhopSdkService } from './whop-sdk.service';
import { Whop<PERSON>ontroller } from './whop.controller';
import { WhopService } from './whop.service';

@Module({
  imports: [DatabaseModule, SecurityModule],
  controllers: [WhopController],
  providers: [WhopService, WhopSdkService],
  exports: [WhopService, WhopSdkService]
})
export class WhopModule {}