import { Modu<PERSON> } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>roller } from './whop.controller';
import { WhopService } from './whop.service';
import { WhopSdkService } from './whop-sdk.service';
import { DatabaseModule } from '../../core/database/database.module';
import { SecurityModule } from '../../core/security/security.module';

@Module({
  imports: [DatabaseModule, SecurityModule],
  controllers: [WhopController],
  providers: [WhopService, WhopSdkService],
  exports: [WhopService, WhopSdkService])
});
export class WhopModule {}