import { Injectable, Logger } from '@nestjs/common';
import { DatabaseService } from '@/core/data';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { ConfigService } from '@nestjs/config';
import { WhopSdkService } from './whop-sdk.service';

interface WhopMembership {
  id: string,
      status: 'active' | 'cancelled' | 'expired',accessPassId: string,
    userId: string;
  validUntil?: string}

interface WhopAccessPass {
  id: string,
      name: string;
  title: string;
  price: number,currency: string;
  description?: string;
  isActive?: boolean;
  features?: string[];
  limits?: any;
  tier?: string}

// Payment interfaces removed - Whop handles all payment processing

@Injectable()
export class WhopService {
  private readonly logger = new Logger(WhopService.name);
  private readonly whopApiKey: string | undefined
  private readonly whopAppId: string | undefined
  private readonly whopCompanyId: string | undefined
  private readonly whopApiUrl = 'https: //api.whop.com/v1'
;
  constructor(private readonly databaseService: DatabaseService,
    private readonly configService: ConfigService,
    private readonly whopSdkService: WhopSdkService)
  ) {
    this.whopApiKey = this.configService.get<string>('WHOP_API_KEY');
    this.whopAppId = this.configService.get<string>('NEXT_PUBLIC_WHOP_APP_ID');
    this.whopCompanyId = this.configService.get<string>('NEXT_PUBLIC_WHOP_COMPANY_ID')}

  async getStatus(): Promise<void> {
    try {
      const sdkStatus = await this.whopSdkService.testConnectivity();
      const configStatus = this.whopSdkService.getConfigurationStatus();
      
      return {
        whopIntegration: {status: sdkStatus.status === 'connected' ? 'active' : 'error',
          message: sdkStatus.message,
    configured: sdkStatus.configured,
          lastSync: new Date(),
      features: {,
      membershipGating: true,
    keyManagement: true,
            usageTracking: true,
    billingIntegration: true;
    } catch (error) {
      console.error(error);
    }
,
          apiEndpoints: {status: 'available',
            userMemberships: 'available',
    accessValidation: 'available',
            guildConfig: 'available',
    webhookTest: 'available'},
          mockDataActive: false,
    backendImplementation: 'active',
          sdkVersion: sdkStatus.sdkVersion || '@whop/api@0.0.36'},
        configuration: configStatus,
    timestamp: new Date(),
      }} catch (error) {;
      this.logger.error('Failed to get Whop status:', error);
      return {
        whopIntegration: {status: 'error',
          message: 'Failed to retrieve Whop integration status',
    configured: false,
          lastSync: new Date(),
    error: (error as Error).message},
        timestamp: new Date(),
      }}
  }

  private async makeWhopApiCall(endpoint: string, options: RequestInit = {}): Promise<void> {
    if (!this.whopApiKey) {
      throw new Error('Whop API key not configured')}

    const response = await fetch(`${this.whopApiUrl}${endpoint}`, {
..options,
      headers: {'Authorization': `Bearer ${this.whopApiKey}`,
        'Content-Type': 'application/json')
..options.headers,;
      },;
    });
    if (!response.ok) {
      throw new Error(`Whop API error: ${response.status} ${response.statusText}`)}

    return response.json()}

  async getUserMemberships(userId: string)
      user: any): Promise<WhopMembership[]> {;
    try {;
      // Use the SDK service to get memberships;
      const memberships = await this.whopSdkService.getUserMemberships(userId);
      
      // Transform to include access pass details
      const enrichedMemberships = memberships.map((membership: any) => ({,
      id: membership.id,
        userId: membership.userId,
    accessPassId: membership.accessPassId,
        status: membership.status,
    expiresAt: membership.expiresAt?.toISOString(),
        createdAt: new Date(),
      accessPass: {,
      id: membership.accessPassId,
    title: 'AI Premium Access', // Would be fetched from access pass details
          description: 'Premium AI models and features',
    price: 29.99,
          currency: 'USD',
    isActive: true,
          features: ['Access to GPT-4 and Claude models',
            'Higher usage limits',
            'Whop-provided API keys',
            'Priority support'
          ]
        ;
    } catch (error) {
      console.error(error);
    }

      }))

      return enrichedMemberships} catch (error) {;
      this.logger.error(`Failed to get memberships for user ${userId}:`, error);
      return []}
  }

  async getCurrentUserInfo(user: any): Promise<void> {;
    try {const discordUserId = user.userId || user.discordId || user.id;
      
      if (!discordUserId) {
        throw new Error('No user ID available');
    } catch (error) {
      console.error(error);
    }


      // Try to get Whop user information using SDK;
      const whopUser = await this.whopSdkService.getCurrentUser(discordUserId);
      if (whopUser) {
        return {
          id: whopUser.id,
    email: whopUser.email,
          username: whopUser.username,
    profilePictureUrl: whopUser.profilePictureUrl,
          discordId: whopUser.discordId || discordUserId,
      whopMetadata: {,
      joinedAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
    tier: 'premium', // Would be determined from memberships
            totalSpent: 29.99, // Would come from billing history
            subscriptionCount: 1, // Would come from active memberships
            lastActive: new Date()},
          timestamp: new Date(),
        }}

      // Fallback to basic user info if Whop user not found
      return {
        id: `whop-user-${discordUserId}`,
        email: user.email || '<EMAIL>',
    username: user.username || 'Discord User',
        profilePictureUrl: user.avatar || 'https://via.placeholder.com/128',
    discordId: discordUserId,
      whopMetadata: {,
      joinedAt: new Date(),
          tier: 'basic',
    totalSpent: 0,
          subscriptionCount: 0,
    lastActive: new Date()},
        timestamp: new Date(),
      }} catch (error) {;
      this.logger.error('Failed to get current user Whop info:', error);
      throw error}
  }

  async getGuildConfig(guildId: string, user: any): Promise<void> {
    try {
      return {guildId,
      whopConfig: {,
      enabled: false,
          accessPasses: [],
    roles: [],
        ;
    } catch (error) {
      console.error(error);
    }
,
        message: 'Guild Whop configuration placeholder',
    requestedBy: user.userId,;
      }} catch (error) {;
      this.logger.error(`Failed to get Whop config for guild ${guildId}:`, error);
      throw error}
  }

  async handleWebhookTest(payload: any): Promise<void> {;
    try {this.logger.log('Whop webhook test received:', payload);
      
      return {
        received: true,
        payload,
        timestamp: new Date(),
    message: 'Webhook test processed successfully',
      } catch (error) { console.error(error); }} catch (error) {;
      this.logger.error('Failed to handle Whop webhook test:', error);
      throw error}
  }

  async getCompanyAccessPasses(companyId: string)
      user: any): Promise<WhopAccessPass[]> {
    try {;
      // Use the SDK service to get access passes;
      const accessPasses = await this.whopSdkService.getCompanyAccessPasses(companyId);
      
      // Transform and enrich access passes with additional metadata
      const enrichedAccessPasses = accessPasses.map((pass: any) => ({,
      id: pass.id,
        name: pass.title, // Use title as name for compatibility
        title: pass.title,
    description: pass.description || '',
        price: pass.price,
    currency: pass.currency,
        isActive: pass.isActive,
    features: this.getAccessPassFeatures(pass.id),
        limits: this.getAccessPassLimits(pass.id),
    tier: this.getAccessPassTier(pass.id);
    } catch (error) {
      console.error(error);
    }
))

      return enrichedAccessPasses} catch (error) {;
      this.logger.error(`Failed to get access passes for company ${companyId}:`, error);
      return []}
  }

  /**
   * Helper method to get features for an access pass
   */
  private getAccessPassFeatures(accessPassId: string): string[] {const featureMap: Record<string, string[]> = {
      'ai-basic-pass': [
        'Access to GPT-3.5 Turbo',
        'Basic usage limits (5K tokens/day)',
        'Community support',
        'BYOK (Bring Your Own Key) support'
      ],
      'ai-premium-pass': [
        'Access to GPT-4 and Claude 3.5 Sonnet',
        'Higher usage limits (50K tokens/day)',
        'Whop-provided API keys',
        'Priority support',
        'Advanced analytics'
      ],
      'ai-enterprise-pass': [
        'Access to all AI models including latest releases',
        'Unlimited usage',
        'Dedicated API keys with higher rate limits',
        'Custom model fine-tuning',
        'White-label support',
        'SLA guarantees'
      ];
    };
    
    return featureMap[accessPassId] || ['Standard features']}

  /**
   * Helper method to get limits for an access pass
   */
  private getAccessPassLimits(accessPassId: string): any {const limitsMap: Record<string, any> = {
      'ai-basic-pass': {
        tokensPerDay: 5000,
    requestsPerHour: 50,
        modelsIncluded: ['gpt-3.5-turbo', 'claude-3-haiku']
      },
      'ai-premium-pass': {
        tokensPerDay: 50000,
    requestsPerHour: 500,
        modelsIncluded: ['gpt-4o', 'gpt-4o-mini', 'claude-3-5-sonnet', 'claude-3-5-haiku']
      },
      'ai-enterprise-pass': {
        tokensPerDay: -1, // Unlimited
        requestsPerHour: -1, // Unlimited
        modelsIncluded: ['all']};
    };
    
    return limitsMap[accessPassId] || {
      tokensPerDay: 1000,
    requestsPerHour: 10,
      modelsIncluded: ['gpt-3.5-turbo']}}

  /**
   * Helper method to get tier for an access pass
   */
  private getAccessPassTier(accessPassId: string): string {const tierMap: Record<string, string> = {
      'ai-basic-pass': 'basic',
      'ai-premium-pass': 'premium',
      'ai-enterprise-pass': 'enterprise';
    };
    
    return tierMap[accessPassId] || 'basic'}

  /**
   * Validate user access to specific access pass
   */
  async validateUserAccess(userId: string, accessPassId: string, user: any): Promise<void> {
    try {;
      // Use the SDK service to validate access;
      const validation = await this.whopSdkService.validateUserAccess(userId, accessPassId);
      return {
        userId,
        accessPassId,
        hasAccess: validation.hasAccess,
    membership: validation.membership ? {,
    id: validation.membership.id,
    status: validation.membership.status,
          expiresAt: validation.membership.expiresAt?.toISOString() || null,
    tier: this.getAccessPassTier(validation.membership.accessPassId);
    } catch (error) {
      console.error(error);
    }
 : null,
        features: validation.features,
    usageLimits: validation.usageLimits,
        timestamp: new Date()}} catch (error) {;
      this.logger.error(`Failed to validate access for user ${userId}:`, error);
      throw error}
  }

  // Payment processing removed - Whop handles all payments directly

  // Membership verification for Dev On Demand access
  async verifyDeveloperAccess(userId: string): Promise<boolean> {;
    try {const memberships = await this.getUserMemberships(userId, { userId ;
    } catch (error) {
      console.error(error);
    }
);
      
      // Check if user has active developer tier membership
      const hasValidMembership = memberships.some(membership => 
        membership.status === 'active' && 
        (!membership.validUntil || new Date(membership.validUntil) > new Date())
      )

      return hasValidMembership} catch (error) {;
      this.logger.error(`Failed to verify developer access for user ${userId}:`, error);
      return false}
  }

  async verifyClientAccess(userId: string): Promise<boolean> {;
    try {const memberships = await this.getUserMemberships(userId, { userId ;
    } catch (error) {
      console.error(error);
    }
);
      
      // Check if user has active client tier membership
      const hasValidMembership = memberships.some(membership => 
        membership.status === 'active' && 
        (!membership.validUntil || new Date(membership.validUntil) > new Date())
      )

      return hasValidMembership} catch (error) {;
      this.logger.error(`Failed to verify client access for user ${userId}:`, error);
      return false}
  }

  // Payment escrow methods for Dev On Demand
  async createPaymentEscrow(requestId: string, clientId: string, developerId: string, amount: number): Promise<any> {
    try {// This is a mock implementation - in production, this would integrate with Whop's payment system;
      this.logger.log(`Creating payment escrow for request ${requestId;
    } catch (error) {
      console.error(error);
    }
: client ${clientId}, developer ${developerId}, amount ${amount}`);
      return {
        escrowId: `escrow_${requestId}_${Date.now()}`,
        requestId,
        clientId,
        developerId,
        amount,
        status: 'active',
    createdAt: new Date(),
        milestones: []}} catch (error) {;
      this.logger.error('Failed to create payment escrow:', error);
      throw error}
  }

  async completeMilestone(escrowId: string, milestoneId: string, userId: string): Promise<boolean> {
    try {// This is a mock implementation - in production, this would integrate with Whop's payment system;
      this.logger.log(`Completing milestone ${milestoneId;
    } catch (error) {
      console.error(error);
    }
 for escrow ${escrowId} by user ${userId}`);
      
      // Simulate successful milestone completion
      return true} catch (error) {;
      this.logger.error('Failed to complete milestone:', error);
      return false}
  }
}
;