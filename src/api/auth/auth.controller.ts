import {
  <PERSON>,
  Get,
  HttpStatus,
  Post,
  Req,
  <PERSON>s,
  UseGuards,
  <PERSON>gger,
  UseInterceptors,
  BadRequestException,
  UnauthorizedException,
  ForbiddenException,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiCookieAuth,
  ApiSecurity,
} from '@nestjs/swagger';
import { Request, Response } from 'express';
import { ThrottlerGuard } from '@nestjs/throttler';
import { AuthService } from './auth.service';
import {
  AuthUser,
  OAuthCallbackRequest,
  SessionResponse,
  LoginResponse,
  AuthConfigResponse,
  BotInviteInfo,
  AuthHealthCheck,
  CSRFToken,
  AUTH_CONSTANTS,
} from './types/auth.types';
import {
  SecurityContext,
  SecurityError,
  EnhancedRequest,
} from './types/security.types';
import { AuditInterceptor } from '../../core/security/interceptors/audit.interceptor';
import { SecurityInterceptor } from '../../core/security/interceptors/security.interceptor';

@ApiTags('auth')
@Controller('auth')
@UseInterceptors(SecurityInterceptor, AuditInterceptor)
export class AuthController {
  private readonly logger = new Logger(AuthController.name);

  constructor(private readonly authService: AuthService) {}

  @Get('login')
  @ApiOperation({ 
    summary: 'Initiate Discord OAuth login',
    description: 'Redirects to Discord OAuth with secure state validation'
  })
  @ApiResponse({ 
    status: 302, 
    description: 'Redirect to Discord OAuth authorization endpoint' 
  })
  @ApiResponse({ 
    status: 429, 
    description: 'Rate limit exceeded' 
  })
  @UseGuards(ThrottlerGuard, AuthGuard('discord'))
  async login(@Req() req: EnhancedRequest): Promise<void> {
    this.logger.log(`OAuth login initiated from IP: ${req.ip}`);
    // OAuth state validation is handled by Discord strategy
    // Redirect will be performed automatically by PassportJS
  }

  @Get('callback')
  @ApiOperation({ 
    summary: 'Discord OAuth callback handler',
    description: 'Handles Discord OAuth callback with secure session creation'
  })
  @ApiResponse({ 
    status: 302, 
    description: 'Redirect to frontend with authentication result' 
  })
  @ApiResponse({ 
    status: 400, 
    description: 'Invalid OAuth state or missing parameters' 
  })
  @ApiResponse({ 
    status: 401, 
    description: 'Authentication failed' 
  })
  @UseGuards(AuthGuard('discord'))
  async callback(
    @Req() req: OAuthCallbackRequest,
    @Res() res: Response
  ): Promise<void> {
    try {
      // Validate OAuth state parameter
      if (!req.query.state) {
        this.logger.warn('OAuth callback missing state parameter');
        throw new BadRequestException('OAuth state parameter missing');
      }

      // Handle OAuth errors
      if (req.query.error) {
        this.logger.warn(`OAuth error: ${req.query.error}`);
        throw new UnauthorizedException(`OAuth error: ${req.query.error_description}`);
      }

      if (!req.user) {
        this.logger.error('OAuth callback missing user data');
        throw new UnauthorizedException('Authentication failed - no user data');
      }

      const loginResult: LoginResponse = await this.authService.login(req.user, req);

      // Set secure authentication cookies
      const cookieOptions = this.getSecureCookieOptions();

      res.cookie(AUTH_CONSTANTS.COOKIE_NAMES.SESSION_TOKEN, loginResult.tokens.sessionToken, cookieOptions);
      res.cookie(AUTH_CONSTANTS.COOKIE_NAMES.ACCESS_TOKEN, loginResult.tokens.accessToken, cookieOptions);

      this.logger.log(`User ${loginResult.user.username} authenticated successfully`);

      // Redirect to frontend with success
      const frontendUrl = this.getFrontendUrl();
      res.redirect(`${frontendUrl}/auth/success?session=${loginResult.session.id}`);
    } catch (error) {
      this.logger.error('OAuth callback error:', error);
      
      const frontendUrl = this.getFrontendUrl();
      const errorMessage = error instanceof Error ? error.message : 'Authentication failed';
      res.redirect(`${frontendUrl}/auth/error?message=${encodeURIComponent(errorMessage)}`);
    }
  }

  // Bot OAuth callback handled by frontend at /api/auth/bot-callback

  @Get('session')
  @ApiOperation({ 
    summary: 'Get current session information',
    description: 'Validates and returns current authentication session details'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Session information with user details',
    type: 'object'
  })
  @ApiResponse({ 
    status: 401, 
    description: 'Invalid or expired session' 
  })
  @ApiBearerAuth()
  @ApiCookieAuth('session_token')
  @UseGuards(ThrottlerGuard)
  async getSession(@Req() req: EnhancedRequest): Promise<SessionResponse> {
    try {
      // Check for Discord token in Authorization header
      const authHeader = req.get(AUTH_CONSTANTS.HEADER_NAMES.AUTHORIZATION);
      if (authHeader?.startsWith(`${AUTH_CONSTANTS.TOKEN_TYPES.BEARER} `)) {
        const token = authHeader.replace(`${AUTH_CONSTANTS.TOKEN_TYPES.BEARER} `, '');
        
        // Validate Discord token if detected
        if (this.isDiscordToken(token)) {
          this.logger.debug('Validating Discord token for session');
          const discordSession = await this.validateDiscordToken(token);
          if (discordSession) {
            return discordSession;
          }
        }
        
        // Validate JWT token
        const jwtSession = await this.authService.validateJwtToken(token);
        if (jwtSession) {
          return jwtSession;
        }
      }
      
      // Fallback to cookie-based session
      const sessionToken = req.cookies?.[AUTH_CONSTANTS.COOKIE_NAMES.SESSION_TOKEN];
      if (sessionToken) {
        this.logger.debug('Validating cookie-based session');
        const session = await this.authService.validateSession(sessionToken, req);
        if (session) {
          return {
            authenticated: true,
            user: session.user,
            session: {
              id: session.sessionId,
              expiresAt: session.expiresAt,
              lastAccessedAt: session.lastAccessedAt,
              type: 'session-cookie'
            },
            permissions: session.user.permissions
          };
        }
      }
      
      this.logger.debug('No valid session found');
      return { authenticated: false };
    } catch (error) {
      this.logger.error('Session validation error:', error);
      return { authenticated: false };
    }
  }

  @Post('signout')
  @ApiOperation({ 
    summary: 'Sign out and invalidate session',
    description: 'Revokes current session and clears authentication cookies'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Successfully signed out' 
  })
  @ApiResponse({ 
    status: 400, 
    description: 'No active session to sign out' 
  })
  @UseGuards(ThrottlerGuard)
  async signout(
    @Req() req: EnhancedRequest,
    @Res() res: Response
  ): Promise<Response> {
    try {
      const sessionToken = req.cookies?.[AUTH_CONSTANTS.COOKIE_NAMES.SESSION_TOKEN];
      const userId = req.user?.id;
      
      if (sessionToken) {
        await this.authService.logout(sessionToken);
        this.logger.log(`User ${userId || 'unknown'} signed out successfully`);
      }
      
      // Clear all authentication cookies with secure options
      const cookieOptions = this.getSecureCookieOptions();
      res.clearCookie(AUTH_CONSTANTS.COOKIE_NAMES.SESSION_TOKEN, cookieOptions);
      res.clearCookie(AUTH_CONSTANTS.COOKIE_NAMES.ACCESS_TOKEN, cookieOptions);
      res.clearCookie(AUTH_CONSTANTS.COOKIE_NAMES.CSRF_TOKEN, cookieOptions);
      
      return res.status(HttpStatus.OK).json({ 
        success: true,
        message: 'Signed out successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      this.logger.error('Signout error:', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: 'Failed to sign out',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  @Get('csrf-token')
  @ApiOperation({ 
    summary: 'Generate CSRF protection token',
    description: 'Returns a secure CSRF token for form submissions'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'CSRF token generated successfully' 
  })
  @ApiResponse({ 
    status: 429, 
    description: 'Rate limit exceeded' 
  })
  @UseGuards(ThrottlerGuard)
  async getCsrfToken(@Req() req: EnhancedRequest): Promise<CSRFToken> {
    try {
      const sessionId = req.session?.sessionId;
      const token = await this.authService.generateCsrfToken(sessionId);
      
      this.logger.debug('CSRF token generated');
      
      return {
        token,
        expiresAt: new Date(Date.now() + 4 * 60 * 60 * 1000), // 4 hours
        sessionId
      };
    } catch (error) {
      this.logger.error('Failed to generate CSRF token:', error);
      throw new BadRequestException('Failed to generate CSRF token');
    }
  }

  @Get('config')
  @ApiOperation({ 
    summary: 'Get API configuration and authentication status',
    description: 'Returns public API configuration and current auth status'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'API configuration and authentication information' 
  })
  async getConfig(@Req() req: EnhancedRequest): Promise<AuthConfigResponse> {
    const isDevelopment = process.env.NODE_ENV !== 'production';
    const port = process.env.PORT || 8080;
    const clientId = process.env.DISCORD_CLIENT_ID || process.env.BOT_CLIENT_ID;

    return {
      apiBaseUrl: isDevelopment
        ? `http://localhost:${port}`
        : `https://discordbot-energex-backend-nqzv2.sevalla.app`,
      environment: process.env.NODE_ENV || 'development',
      discord: {
        clientId: clientId || '',
        botInviteUrl: clientId
          ? `https://discord.com/oauth2/authorize?client_id=${clientId}&permissions=8&scope=bot+applications.commands&response_type=code&redirect_uri=${encodeURIComponent(this.getFrontendUrl() + '/api/auth/bot-callback')}`
          : '',
        scopes: ['identify', 'guilds', 'email']
      },
      auth: {
        hasSessionToken: !!req.cookies?.[AUTH_CONSTANTS.COOKIE_NAMES.SESSION_TOKEN],
        hasAccessToken: !!req.cookies?.[AUTH_CONSTANTS.COOKIE_NAMES.ACCESS_TOKEN],
        authHeader: !!req.get(AUTH_CONSTANTS.HEADER_NAMES.AUTHORIZATION),
        csrfEnabled: true
      },
      features: {
        oauthOnly: !process.env.DISCORD_TOKEN,
        aiAgents: !!process.env.ANTHROPIC_API_KEY,
        analytics: !!process.env.ANALYTICS_ENABLED
      },
    };
  }

  @Get('bot-invite')
  @ApiOperation({ 
    summary: 'Get Discord bot invite information',
    description: 'Returns Discord bot invitation URL and configuration'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Bot invite information' 
  })
  @ApiResponse({ 
    status: 500, 
    description: 'Discord client not configured' 
  })
  async getBotInvite(): Promise<BotInviteInfo> {
    const clientId = process.env.DISCORD_CLIENT_ID || process.env.BOT_CLIENT_ID;

    if (!clientId) {
      this.logger.error('Discord client ID not configured');
      throw new BadRequestException('Discord client ID not configured');
    }

    const redirectUri = `${this.getFrontendUrl()}/api/auth/bot-callback`;
    const permissions = '8'; // Administrator permissions
    const scopes = 'bot+applications.commands';
    
    const inviteUrl = `https://discord.com/oauth2/authorize?client_id=${clientId}&permissions=${permissions}&scope=${scopes}&response_type=code&redirect_uri=${encodeURIComponent(redirectUri)}`;

    return {
      clientId,
      inviteUrl,
      redirectUri,
      permissions,
      scopes: scopes.split('+')
    };
  }

  @Get('health')
  @ApiOperation({ 
    summary: 'Authentication service health check',
    description: 'Returns health status of authentication components'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Authentication service health information' 
  })
  async healthCheck(): Promise<AuthHealthCheck> {
    try {
      const redisConnected = await this.authService.checkRedisConnection();
      const databaseConnected = await this.authService.checkDatabaseConnection();
      
      const allHealthy = redisConnected && databaseConnected && 
                        !!process.env.DISCORD_CLIENT_ID && 
                        !!process.env.JWT_SECRET;

      return {
        status: allHealthy ? 'ok' : 'degraded',
        service: 'auth',
        timestamp: new Date().toISOString(),
        discord_configured: !!process.env.DISCORD_CLIENT_ID,
        jwt_configured: !!process.env.JWT_SECRET,
        redis_connected: redisConnected,
        database_connected: databaseConnected
      };
    } catch (error) {
      this.logger.error('Health check failed:', error);
      return {
        status: 'down',
        service: 'auth',
        timestamp: new Date().toISOString(),
        discord_configured: !!process.env.DISCORD_CLIENT_ID,
        jwt_configured: !!process.env.JWT_SECRET,
        redis_connected: false,
        database_connected: false
      };
    }
  }

  // ========================
  // Private Helper Methods
  // ========================

  private getSecureCookieOptions() {
    const isProduction = process.env.NODE_ENV === 'production';
    return {
      httpOnly: true,
      secure: isProduction,
      sameSite: isProduction ? 'strict' as const : 'lax' as const,
      maxAge: AUTH_CONSTANTS.SESSION.DEFAULT_EXPIRY,
      domain: isProduction ? '.sevalla.app' : undefined,
      path: '/'
    };
  }

  private getFrontendUrl(): string {
    return process.env.WEB_URL || 'http://localhost:3000';
  }

  private isDiscordToken(token: string): boolean {
    // Discord tokens have different format than JWT
    return token.includes('.') && token.length > 50 && !token.startsWith('eyJ');
  }

  private async validateDiscordToken(token: string): Promise<SessionResponse | null> {
    try {
      const response = await fetch('https://discord.com/api/v10/users/@me', {
        headers: { [AUTH_CONSTANTS.HEADER_NAMES.AUTHORIZATION]: `${AUTH_CONSTANTS.TOKEN_TYPES.BEARER} ${token}` },
      });
      
      if (!response.ok) {
        return null;
      }

      const discordUser = await response.json();
      const userOrganization = await this.authService.getUserOrganization(discordUser.id);
      
      return {
        authenticated: true,
        user: {
          id: discordUser.id,
          discordId: discordUser.id,
          username: discordUser.username,
          discriminator: discordUser.discriminator,
          avatar: discordUser.avatar,
          email: discordUser.email,
          tier: userOrganization?.organization?.tier || 'free',
          organization: userOrganization?.organization || null,
          role: userOrganization?.role || null,
          permissions: userOrganization?.permissions || [],
          verified: discordUser.verified || false,
          createdAt: new Date(),
          updatedAt: new Date()
        } as AuthUser,
        session: {
          id: 'discord-session',
          type: 'discord-token',
          expiresAt: new Date(Date.now() + AUTH_CONSTANTS.SESSION.DEFAULT_EXPIRY),
          lastAccessedAt: new Date()
        },
        permissions: userOrganization?.permissions || []
      };
    } catch (error) {
      this.logger.error('Discord token validation failed:', error);
      return null;
    }
  }
}