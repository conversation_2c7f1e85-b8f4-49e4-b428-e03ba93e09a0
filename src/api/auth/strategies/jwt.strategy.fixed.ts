import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { JwtPayload, AuthUser } from '../types/auth.types';
import { UserService } from '../../../core/security/user.service';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy, 'jwt') {
  private readonly logger = new Logger(JwtStrategy.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly userService: UserService
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('JWT_SECRET') || 'default-secret-change-in-production',
      issuer: configService.get<string>('JWT_ISSUER') || 'energex-auth',
      audience: configService.get<string>('JWT_AUDIENCE') || 'energex-api',
      algorithms: ['HS256']
    });
    
    this.logger.log('JWT strategy initialized');
  }

  async validate(payload: JwtPayload): Promise<AuthUser> {
    try {
      this.logger.debug(`Validating JWT payload for user: ${payload.sub}`);

      // Validate payload structure
      if (!payload.sub || !payload.username || !payload.sessionId) {
        this.logger.warn('Invalid JWT payload structure');
        throw new UnauthorizedException('Invalid token payload');
      }

      // Validate token timestamps
      const now = Math.floor(Date.now() / 1000);
      if (payload.exp < now) {
        this.logger.warn(`JWT token expired for user: ${payload.sub}`);
        throw new UnauthorizedException('Token expired');
      }

      if (payload.iat > now) {
        this.logger.warn(`JWT token issued in future for user: ${payload.sub}`);
        throw new UnauthorizedException('Invalid token');
      }

      // Validate issuer and audience
      const expectedIss = this.configService.get<string>('JWT_ISSUER') || 'energex-auth';
      const expectedAud = this.configService.get<string>('JWT_AUDIENCE') || 'energex-api';
      
      if (payload.iss !== expectedIss) {
        this.logger.warn(`Invalid JWT issuer: ${payload.iss}, expected: ${expectedIss}`);
        throw new UnauthorizedException('Invalid token issuer');
      }

      if (payload.aud !== expectedAud) {
        this.logger.warn(`Invalid JWT audience: ${payload.aud}, expected: ${expectedAud}`);
        throw new UnauthorizedException('Invalid token audience');
      }

      // Get user information from database
      const user = await this.userService.findByDiscordId(payload.sub);
      if (!user) {
        this.logger.warn(`User not found for JWT: ${payload.sub}`);
        throw new UnauthorizedException('User not found');
      }

      // Check if user is active/not suspended
      if (user.suspended) {
        this.logger.warn(`Suspended user attempted access: ${payload.sub}`);
        throw new UnauthorizedException('User account suspended');
      }

      // Get organization information
      const userOrganization = await this.userService.getUserOrganization(user.discordId);

      // Create comprehensive user object for request context
      const authUser: AuthUser = {
        id: user.discordId,
        discordId: user.discordId,
        username: user.username,
        discriminator: user.discriminator,
        avatar: user.avatar,
        email: user.email,
        tier: userOrganization?.organization?.tier || payload.tier || 'free',
        organization: userOrganization?.organization || null,
        role: userOrganization?.role || (payload.roles?.[0] as any) || null,
        permissions: payload.permissions || [],
        verified: user.verified || false,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      };

      this.logger.debug(`JWT validation successful for user: ${user.username}`);
      return authUser;

    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      
      this.logger.error('JWT validation error:', error);
      throw new UnauthorizedException('Token validation failed');
    }
  }

  // Additional method for custom token validation
  async validateTokenClaims(payload: JwtPayload): Promise<boolean> {
    try {
      // Custom business logic validation
      // Check if session is still valid
      // Validate permissions are current
      // Check rate limits, etc.
      
      return true;
    } catch (error) {
      this.logger.error('Token claims validation failed:', error);
      return false;
    }
  }
}