import { Injectable, UnauthorizedException } from '@nestjs/common';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(private configService: ConfigService)
    private jwtService: JwtService,) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
    ignoreExpiration: false,
      secretOrKey: configService.get<string>('JWT_SECRET') || 'default-secret-change-in-production',
    })}

  async validate(): Promise<any> {
    return { userId: payload.sub, username: payload.username, sessionId: payload.sessionId }}
};