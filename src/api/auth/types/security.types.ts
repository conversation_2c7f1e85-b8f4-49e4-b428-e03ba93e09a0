/**
 * Security-focused type definitions for authentication middleware
 * Implements comprehensive security patterns and validation types
 */

import { Request, Response } from 'express';
import { AuthUser, Permission, AuthSession } from './auth.types';

// ========================
// Security Context Types
// ========================

export interface SecurityContext {
  readonly user?: AuthUser;
  readonly session?: AuthSession;
  readonly permissions: Permission[];
  readonly ipAddress: string;
  readonly userAgent: string;
  readonly deviceFingerprint: string;
  readonly riskScore: number;
  readonly threatLevel: ThreatLevel;
  readonly geoLocation?: GeoLocation;
}

export type ThreatLevel = 'low' | 'medium' | 'high' | 'critical';

export interface GeoLocation {
  readonly country: string;
  readonly region: string;
  readonly city: string;
  readonly timezone: string;
  readonly isp?: string;
  readonly isVPN?: boolean;
  readonly isTor?: boolean;
}

// ========================
// Authentication Middleware Types
// ========================

export interface AuthMiddlewareOptions {
  readonly required?: boolean;
  readonly permissions?: Permission[];
  readonly roles?: string[];
  readonly skipCSRF?: boolean;
  readonly allowDiscordToken?: boolean;
  readonly rateLimit?: RateLimitOptions;
}

export interface RateLimitOptions {
  readonly windowMs: number;
  readonly max: number;
  readonly message?: string;
  readonly skipSuccessfulRequests?: boolean;
  readonly keyGenerator?: (req: Request) => string;
}

export interface AuthMiddlewareResult {
  readonly success: boolean;
  readonly context?: SecurityContext;
  readonly error?: SecurityError;
  readonly metadata?: Record<string, unknown>;
}

// ========================
// Security Validation Types
// ========================

export interface TokenValidationOptions {
  readonly validateExpiry?: boolean;
  readonly validateSignature?: boolean;
  readonly validateAudience?: boolean;
  readonly validateIssuer?: boolean;
  readonly validateSession?: boolean;
  readonly allowDiscordTokens?: boolean;
}

export interface SessionValidationOptions {
  readonly validateFingerprint?: boolean;
  readonly updateLastAccessed?: boolean;
  readonly checkDeviceConsistency?: boolean;
  readonly validateGeoLocation?: boolean;
  readonly maxInactiveDuration?: number;
}

export interface PermissionValidationOptions {
  readonly strictMode?: boolean;
  readonly inheritFromRoles?: boolean;
  readonly checkResourceAccess?: boolean;
  readonly validateScope?: boolean;
}

// ========================
// Security Error Types
// ========================

export interface SecurityError {
  readonly code: SecurityErrorCode;
  readonly message: string;
  readonly severity: SecuritySeverity;
  readonly context?: SecurityErrorContext;
  readonly timestamp: Date;
  readonly requestId?: string;
  readonly userId?: string;
  readonly sessionId?: string;
}

export type SecurityErrorCode =
  | 'AUTH_TOKEN_INVALID'
  | 'AUTH_TOKEN_EXPIRED'
  | 'AUTH_SESSION_INVALID'
  | 'AUTH_SESSION_EXPIRED'
  | 'AUTH_INSUFFICIENT_PERMISSIONS'
  | 'AUTH_RATE_LIMIT_EXCEEDED'
  | 'AUTH_CSRF_TOKEN_INVALID'
  | 'AUTH_SUSPICIOUS_ACTIVITY'
  | 'AUTH_GEOLOCATION_BLOCKED'
  | 'AUTH_DEVICE_FINGERPRINT_MISMATCH'
  | 'AUTH_CONCURRENT_SESSION_LIMIT'
  | 'AUTH_ACCOUNT_LOCKED'
  | 'AUTH_ACCOUNT_SUSPENDED'
  | 'AUTH_OAUTH_ERROR'
  | 'AUTH_DISCORD_API_ERROR';

export type SecuritySeverity = 'info' | 'warning' | 'error' | 'critical';

export interface SecurityErrorContext {
  readonly ipAddress?: string;
  readonly userAgent?: string;
  readonly endpoint?: string;
  readonly method?: string;
  readonly headers?: Record<string, string>;
  readonly payload?: Record<string, unknown>;
}

// ========================
// Security Audit Types
// ========================

export interface SecurityEvent {
  readonly id: string;
  readonly type: SecurityEventType;
  readonly severity: SecuritySeverity;
  readonly userId?: string;
  readonly sessionId?: string;
  readonly ipAddress: string;
  readonly userAgent: string;
  readonly timestamp: Date;
  readonly details: SecurityEventDetails;
  readonly resolved: boolean;
  readonly resolvedAt?: Date;
  readonly resolvedBy?: string;
}

export type SecurityEventType =
  | 'LOGIN_SUCCESS'
  | 'LOGIN_FAILURE'
  | 'LOGOUT'
  | 'SESSION_CREATED'
  | 'SESSION_EXPIRED'
  | 'SESSION_REVOKED'
  | 'TOKEN_REFRESH'
  | 'PERMISSION_DENIED'
  | 'RATE_LIMIT_EXCEEDED'
  | 'SUSPICIOUS_ACTIVITY'
  | 'GEOLOCATION_ANOMALY'
  | 'DEVICE_FINGERPRINT_MISMATCH'
  | 'CONCURRENT_SESSION_DETECTED'
  | 'PASSWORD_CHANGE'
  | 'EMAIL_CHANGE'
  | 'ACCOUNT_LOCKED'
  | 'ACCOUNT_UNLOCKED'
  | 'OAUTH_ERROR';

export interface SecurityEventDetails {
  readonly action: string;
  readonly resource?: string;
  readonly metadata?: Record<string, unknown>;
  readonly riskScore?: number;
  readonly threatLevel?: ThreatLevel;
  readonly geoLocation?: GeoLocation;
  readonly deviceInfo?: DeviceInfo;
}

export interface DeviceInfo {
  readonly fingerprint: string;
  readonly browser?: string;
  readonly os?: string;
  readonly device?: string;
  readonly screen?: string;
  readonly timezone?: string;
  readonly language?: string;
}

// ========================
// CSRF Protection Types
// ========================

export interface CSRFConfig {
  readonly enabled: boolean;
  readonly tokenLength: number;
  readonly headerName: string;
  readonly cookieName: string;
  readonly secure: boolean;
  readonly sameSite: 'strict' | 'lax' | 'none';
  readonly httpOnly: boolean;
  readonly maxAge: number;
}

export interface CSRFValidationResult {
  readonly valid: boolean;
  readonly token?: string;
  readonly error?: string;
  readonly regenerated?: boolean;
}

// ========================
// OAuth Security Types
// ========================

export interface OAuthSecurityConfig {
  readonly stateValidation: boolean;
  readonly stateExpiry: number;
  readonly nonceValidation: boolean;
  readonly pkceRequired: boolean;
  readonly allowedRedirectUris: string[];
  readonly maxStateAge: number;
}

export interface OAuthState {
  readonly state: string;
  readonly nonce?: string;
  readonly codeVerifier?: string;
  readonly redirectUri: string;
  readonly createdAt: Date;
  readonly expiresAt: Date;
  readonly clientId: string;
  readonly scopes: string[];
}

export interface OAuthValidationResult {
  readonly valid: boolean;
  readonly state?: OAuthState;
  readonly error?: OAuthError;
  readonly metadata?: Record<string, unknown>;
}

export interface OAuthError {
  readonly code: OAuthErrorCode;
  readonly description: string;
  readonly uri?: string;
}

export type OAuthErrorCode =
  | 'invalid_request'
  | 'invalid_client'
  | 'invalid_grant'
  | 'unauthorized_client'
  | 'unsupported_grant_type'
  | 'invalid_scope'
  | 'server_error'
  | 'temporarily_unavailable'
  | 'interaction_required'
  | 'login_required'
  | 'consent_required'
  | 'invalid_state'
  | 'access_denied';

// ========================
// Security Headers Types
// ========================

export interface SecurityHeadersConfig {
  readonly contentSecurityPolicy: CSPConfig;
  readonly httpStrictTransportSecurity: HSTSConfig;
  readonly xFrameOptions: XFrameOptionsConfig;
  readonly xContentTypeOptions: boolean;
  readonly xXSSProtection: boolean;
  readonly referrerPolicy: ReferrerPolicyConfig;
  readonly permissionsPolicy: PermissionsPolicyConfig;
}

export interface CSPConfig {
  readonly enabled: boolean;
  readonly directives: CSPDirectives;
  readonly reportOnly: boolean;
  readonly reportUri?: string;
}

export interface CSPDirectives {
  readonly defaultSrc: string[];
  readonly scriptSrc: string[];
  readonly styleSrc: string[];
  readonly imgSrc: string[];
  readonly connectSrc: string[];
  readonly fontSrc: string[];
  readonly frameSrc: string[];
  readonly mediaSrc: string[];
  readonly objectSrc: string[];
  readonly childSrc: string[];
  readonly workerSrc: string[];
  readonly manifestSrc: string[];
  readonly formAction: string[];
  readonly frameAncestors: string[];
  readonly baseUri: string[];
  readonly upgradeInsecureRequests: boolean;
}

export interface HSTSConfig {
  readonly enabled: boolean;
  readonly maxAge: number;
  readonly includeSubDomains: boolean;
  readonly preload: boolean;
}

export interface XFrameOptionsConfig {
  readonly enabled: boolean;
  readonly policy: 'DENY' | 'SAMEORIGIN' | 'ALLOW-FROM';
  readonly allowFrom?: string;
}

export interface ReferrerPolicyConfig {
  readonly enabled: boolean;
  readonly policy: 
    | 'no-referrer'
    | 'no-referrer-when-downgrade'
    | 'origin'
    | 'origin-when-cross-origin'
    | 'same-origin'
    | 'strict-origin'
    | 'strict-origin-when-cross-origin'
    | 'unsafe-url';
}

export interface PermissionsPolicyConfig {
  readonly enabled: boolean;
  readonly directives: Record<string, string[]>;
}

// ========================
// Security Monitoring Types
// ========================

export interface SecurityMetrics {
  readonly authenticationAttempts: number;
  readonly successfulAuthentications: number;
  readonly failedAuthentications: number;
  readonly rateLimitViolations: number;
  readonly csrfViolations: number;
  readonly suspiciousActivities: number;
  readonly activeSession: number;
  readonly revokedSessions: number;
  readonly tokenRefreshes: number;
  readonly permissionDenials: number;
}

export interface SecurityAlert {
  readonly id: string;
  readonly type: SecurityAlertType;
  readonly severity: SecuritySeverity;
  readonly message: string;
  readonly details: SecurityAlertDetails;
  readonly timestamp: Date;
  readonly acknowledged: boolean;
  readonly acknowledgedBy?: string;
  readonly acknowledgedAt?: Date;
  readonly resolved: boolean;
  readonly resolvedAt?: Date;
  readonly resolvedBy?: string;
}

export type SecurityAlertType =
  | 'BRUTE_FORCE_ATTACK'
  | 'CREDENTIAL_STUFFING'
  | 'SUSPICIOUS_LOGIN_PATTERN'
  | 'GEOLOCATION_ANOMALY'
  | 'DEVICE_FINGERPRINT_ANOMALY'
  | 'RATE_LIMIT_ABUSE'
  | 'CSRF_ATTACK'
  | 'SESSION_HIJACKING'
  | 'TOKEN_THEFT'
  | 'PRIVILEGE_ESCALATION'
  | 'DATA_EXFILTRATION'
  | 'API_ABUSE';

export interface SecurityAlertDetails {
  readonly userId?: string;
  readonly ipAddress?: string;
  readonly userAgent?: string;
  readonly endpoint?: string;
  readonly method?: string;
  readonly count?: number;
  readonly timeWindow?: number;
  readonly riskScore?: number;
  readonly threatLevel?: ThreatLevel;
  readonly mitigationActions?: string[];
}

// ========================
// Request Enhancement Types
// ========================

export interface EnhancedRequest extends Request {
  readonly securityContext?: SecurityContext;
  readonly csrfToken?: string;
  readonly rateLimitInfo?: RateLimitInfo;
  readonly deviceFingerprint?: string;
  readonly geoLocation?: GeoLocation;
  readonly riskScore?: number;
  readonly threatLevel?: ThreatLevel;
}

export interface RateLimitInfo {
  readonly limit: number;
  readonly remaining: number;
  readonly resetTime: Date;
  readonly retryAfter?: number;
}

// ========================
// Security Utility Types
// ========================

export interface SecurityUtilities {
  readonly generateSecureToken: (length?: number) => string;
  readonly hashPassword: (password: string) => Promise<string>;
  readonly verifyPassword: (password: string, hash: string) => Promise<boolean>;
  readonly generateDeviceFingerprint: (userAgent: string, ipAddress: string) => string;
  readonly calculateRiskScore: (context: SecurityContext) => number;
  readonly detectThreatLevel: (riskScore: number) => ThreatLevel;
  readonly validateCSRFToken: (token: string, session?: string) => boolean;
  readonly encryptSensitiveData: (data: string) => string;
  readonly decryptSensitiveData: (encryptedData: string) => string;
}

// ========================
// Type Guards for Security
// ========================

export function isSecurityError(error: unknown): error is SecurityError {
  return (
    typeof error === 'object' &&
    error !== null &&
    'code' in error &&
    'message' in error &&
    'severity' in error
  );
}

export function isSecurityEvent(event: unknown): event is SecurityEvent {
  return (
    typeof event === 'object' &&
    event !== null &&
    'id' in event &&
    'type' in event &&
    'severity' in event &&
    'timestamp' in event
  );
}

export function hasEnhancedSecurity(req: Request): req is EnhancedRequest {
  return 'securityContext' in req || 'deviceFingerprint' in req;
}

// ========================
// Constants
// ========================

export const SECURITY_CONSTANTS = {
  RISK_SCORES: {
    LOW: 0,
    MEDIUM: 25,
    HIGH: 75,
    CRITICAL: 90,
  },
  TOKEN_LENGTHS: {
    CSRF: 32,
    SESSION: 64,
    API_KEY: 48,
    STATE: 32,
  },
  TIMEOUTS: {
    OAUTH_STATE: 10 * 60 * 1000, // 10 minutes
    CSRF_TOKEN: 4 * 60 * 60 * 1000, // 4 hours
    SESSION_RENEWAL: 30 * 60 * 1000, // 30 minutes
  },
  RATE_LIMITS: {
    AUTH_ATTEMPTS: 5, // per 15 minutes
    CSRF_VIOLATIONS: 3, // per hour
    API_CALLS: 1000, // per hour
  },
  HEADERS: {
    SECURITY: {
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
    },
  },
} as const;