/**
 * Comprehensive Authentication Type Definitions
 * Security-focused type system for authentication flows
 */

import { Request } from 'express';

// ========================
// Core Authentication Types
// ========================

export interface AuthUser {
  readonly id: string;
  readonly discordId: string;
  readonly username: string;
  readonly discriminator?: string;
  readonly avatar?: string;
  readonly email?: string;
  readonly tier: UserTier;
  readonly organization?: UserOrganization | null;
  readonly role?: OrganizationRole | null;
  readonly permissions: Permission[];
  readonly verified: boolean;
  readonly createdAt: Date;
  readonly updatedAt: Date;
}

export interface UserOrganization {
  readonly id: string;
  readonly name: string;
  readonly tier: OrganizationTier;
  readonly features: OrganizationFeature[];
  readonly memberCount: number;
  readonly createdAt: Date;
  readonly settings: OrganizationSettings;
}

export type UserTier = 'free' | 'premium' | 'enterprise';
export type OrganizationTier = 'starter' | 'professional' | 'enterprise';
export type OrganizationRole = 'owner' | 'admin' | 'member' | 'viewer';

export interface Permission {
  readonly scope: PermissionScope;
  readonly action: PermissionAction;
  readonly resource?: string;
}

export type PermissionScope = 'organization' | 'guild' | 'channel' | 'user' | 'system';
export type PermissionAction = 'create' | 'read' | 'update' | 'delete' | 'manage' | 'admin';

// ========================
// OAuth & Discord Types
// ========================

export interface DiscordOAuthProfile {
  readonly id: string;
  readonly username: string;
  readonly discriminator: string;
  readonly avatar?: string;
  readonly email?: string;
  readonly verified: boolean;
  readonly locale: string;
  readonly flags: number;
  readonly premium_type?: number;
  readonly public_flags: number;
}

export interface DiscordTokenResponse {
  readonly access_token: string;
  readonly token_type: 'Bearer';
  readonly expires_in: number;
  readonly refresh_token: string;
  readonly scope: string;
}

export interface DiscordOAuthState {
  readonly state: string;
  readonly redirectUri: string;
  readonly timestamp: number;
  readonly clientId: string;
  readonly expiresAt: Date;
}

export interface OAuthCallbackRequest extends Request {
  readonly user: DiscordOAuthProfile;
  readonly query: {
    readonly code?: string;
    readonly state?: string;
    readonly error?: string;
    readonly error_description?: string;
  };
}

// ========================
// JWT & Token Types
// ========================

export interface JwtPayload {
  readonly sub: string; // Subject (user ID)
  readonly username: string;
  readonly sessionId: string;
  readonly organizationId?: string;
  readonly roles: OrganizationRole[];
  readonly permissions: Permission[];
  readonly tier: UserTier;
  readonly iat: number; // Issued at
  readonly exp: number; // Expires at
  readonly iss: string; // Issuer
  readonly aud: string; // Audience
}

export interface TokenPair {
  readonly accessToken: string;
  readonly refreshToken?: string;
  readonly sessionToken: string;
  readonly expiresAt: Date;
  readonly tokenType: 'Bearer';
}

export interface TokenValidationResult {
  readonly valid: boolean;
  readonly payload?: JwtPayload;
  readonly error?: TokenError;
  readonly expiresAt?: Date;
}

export type TokenError = 
  | 'INVALID_SIGNATURE'
  | 'EXPIRED_TOKEN' 
  | 'MALFORMED_TOKEN'
  | 'INSUFFICIENT_SCOPE'
  | 'REVOKED_TOKEN'
  | 'UNKNOWN_ERROR';

// ========================
// Session Management Types
// ========================

export interface AuthSession {
  readonly sessionId: string;
  readonly userId: string;
  readonly ipAddress: string;
  readonly userAgent: string;
  readonly deviceFingerprint: string;
  readonly createdAt: Date;
  readonly expiresAt: Date;
  readonly lastAccessedAt: Date;
  readonly isRevoked: boolean;
  readonly revokedAt?: Date;
  readonly revokedReason?: SessionRevokeReason;
}

export interface SessionWithUser extends AuthSession {
  readonly user: AuthUser;
}

export type SessionRevokeReason = 
  | 'USER_LOGOUT'
  | 'SECURITY_BREACH'
  | 'ADMIN_REVOKE'
  | 'SESSION_EXPIRED'
  | 'DEVICE_CHANGED'
  | 'SUSPICIOUS_ACTIVITY';

export interface SessionValidationOptions {
  readonly checkExpiry?: boolean;
  readonly updateLastAccessed?: boolean;
  readonly validateFingerprint?: boolean;
  readonly requireActiveUser?: boolean;
}

// ========================
// Authentication Request Types
// ========================

export interface AuthenticatedRequest extends Request {
  readonly user: AuthUser;
  readonly session: AuthSession;
  readonly permissions: Permission[];
}

export interface LoginRequest {
  readonly discordUser: DiscordOAuthProfile;
  readonly accessToken: string;
  readonly refreshToken?: string;
  readonly state?: string;
}

export interface LoginResponse {
  readonly success: boolean;
  readonly tokens: TokenPair;
  readonly user: AuthUser;
  readonly session: {
    readonly id: string;
    readonly expiresAt: Date;
  };
}

export interface SessionResponse {
  readonly authenticated: boolean;
  readonly user?: AuthUser;
  readonly session?: {
    readonly id: string;
    readonly expiresAt: Date;
    readonly lastAccessedAt: Date;
    readonly type: SessionType;
  };
  readonly permissions?: Permission[];
}

export type SessionType = 'jwt' | 'discord-token' | 'session-cookie';

// ========================
// Security & Validation Types
// ========================

export interface SecurityHeaders {
  readonly 'X-Frame-Options': string;
  readonly 'X-Content-Type-Options': string;
  readonly 'X-XSS-Protection': string;
  readonly 'Strict-Transport-Security': string;
  readonly 'Content-Security-Policy': string;
  readonly 'X-CSRF-Token'?: string;
}

export interface CSRFToken {
  readonly token: string;
  readonly expiresAt: Date;
  readonly sessionId?: string;
}

export interface RateLimitInfo {
  readonly limit: number;
  readonly remaining: number;
  readonly resetAt: Date;
  readonly retryAfter?: number;
}

export interface AuthError {
  readonly code: AuthErrorCode;
  readonly message: string;
  readonly details?: Record<string, unknown>;
  readonly timestamp: Date;
  readonly requestId?: string;
}

export type AuthErrorCode =
  | 'INVALID_CREDENTIALS'
  | 'OAUTH_ERROR'
  | 'SESSION_EXPIRED'
  | 'INSUFFICIENT_PERMISSIONS'
  | 'RATE_LIMITED'
  | 'CSRF_INVALID'
  | 'TOKEN_EXPIRED'
  | 'USER_DISABLED'
  | 'ORGANIZATION_SUSPENDED';

// ========================
// Configuration Types
// ========================

export interface AuthConfig {
  readonly jwt: {
    readonly secret: string;
    readonly expiresIn: string;
    readonly issuer: string;
    readonly audience: string;
  };
  readonly session: {
    readonly maxAge: number;
    readonly renewThreshold: number;
    readonly secureCookies: boolean;
  };
  readonly discord: {
    readonly clientId: string;
    readonly clientSecret: string;
    readonly callbackUrl: string;
    readonly scopes: string[];
  };
  readonly security: {
    readonly enableCSRF: boolean;
    readonly rateLimit: RateLimitConfig;
    readonly allowedOrigins: string[];
    readonly secureHeaders: boolean;
  };
}

export interface RateLimitConfig {
  readonly windowMs: number;
  readonly maxRequests: number;
  readonly skipSuccessfulRequests: boolean;
  readonly keyGenerator?: (req: Request) => string;
}

// ========================
// Organization Types
// ========================

export interface OrganizationSettings {
  readonly allowPublicJoin: boolean;
  readonly requireEmailVerification: boolean;
  readonly enableSSO: boolean;
  readonly maxMembers: number;
  readonly features: OrganizationFeature[];
}

export type OrganizationFeature =
  | 'AI_AGENTS'
  | 'ANALYTICS'
  | 'CUSTOM_BRANDING'
  | 'API_ACCESS'
  | 'PRIORITY_SUPPORT'
  | 'ADVANCED_SECURITY'
  | 'AUDIT_LOGS'
  | 'WEBHOOK_MANAGEMENT';

// ========================
// API Response Types
// ========================

export interface AuthHealthCheck {
  readonly status: 'ok' | 'degraded' | 'down';
  readonly service: 'auth';
  readonly timestamp: string;
  readonly discord_configured: boolean;
  readonly jwt_configured: boolean;
  readonly redis_connected: boolean;
  readonly database_connected: boolean;
}

export interface BotInviteInfo {
  readonly clientId: string;
  readonly inviteUrl: string;
  readonly redirectUri: string;
  readonly permissions: string;
  readonly scopes: string[];
}

export interface AuthConfigResponse {
  readonly apiBaseUrl: string;
  readonly environment: string;
  readonly discord: {
    readonly clientId: string;
    readonly botInviteUrl: string;
    readonly scopes: string[];
  };
  readonly auth: {
    readonly hasSessionToken: boolean;
    readonly hasAccessToken: boolean;
    readonly authHeader: boolean;
    readonly csrfEnabled: boolean;
  };
  readonly features: {
    readonly oauthOnly: boolean;
    readonly aiAgents: boolean;
    readonly analytics: boolean;
  };
}

// ========================
// Type Guards
// ========================

export function isAuthenticatedRequest(req: Request): req is AuthenticatedRequest {
  return 'user' in req && 'session' in req && 'permissions' in req;
}

export function isDiscordToken(token: string): boolean {
  return token.includes('.') && token.length > 50 && !token.startsWith('eyJ');
}

export function isValidJwtPayload(payload: unknown): payload is JwtPayload {
  return (
    typeof payload === 'object' &&
    payload !== null &&
    'sub' in payload &&
    'username' in payload &&
    'sessionId' in payload &&
    'iat' in payload &&
    'exp' in payload
  );
}

export function hasPermission(
  userPermissions: Permission[],
  requiredScope: PermissionScope,
  requiredAction: PermissionAction,
  resource?: string
): boolean {
  return userPermissions.some(
    perm =>
      perm.scope === requiredScope &&
      perm.action === requiredAction &&
      (resource ? perm.resource === resource : true)
  );
}

// ========================
// Constants
// ========================

export const AUTH_CONSTANTS = {
  COOKIE_NAMES: {
    SESSION_TOKEN: 'session_token',
    ACCESS_TOKEN: 'access_token',
    CSRF_TOKEN: 'csrf_token',
  },
  HEADER_NAMES: {
    AUTHORIZATION: 'Authorization',
    CSRF_TOKEN: 'X-CSRF-Token',
    SESSION_ID: 'X-Session-ID',
  },
  TOKEN_TYPES: {
    BEARER: 'Bearer',
    DISCORD: 'Discord',
  },
  SESSION: {
    DEFAULT_EXPIRY: 24 * 60 * 60 * 1000, // 24 hours
    RENEWAL_THRESHOLD: 2 * 60 * 60 * 1000, // 2 hours
    MAX_INACTIVE: 7 * 24 * 60 * 60 * 1000, // 7 days
  },
  RATE_LIMITS: {
    LOGIN: 5, // per minute
    CSRF: 10, // per minute
    SESSION_CHECK: 100, // per minute
  },
} as const;