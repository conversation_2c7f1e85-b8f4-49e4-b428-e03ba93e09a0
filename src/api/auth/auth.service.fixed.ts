import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';
import { SessionService } from '../../core/security/session.service';
import { UserService } from '../../core/security/user.service';
import { EncryptionService } from '../../core/security/encryption.service';
import {
  AuthUser,
  DiscordOAuthProfile,
  LoginResponse,
  SessionResponse,
  TokenPair,
  JwtPayload,
  TokenValidationResult,
  SessionWithUser,
  AUTH_CONSTANTS,
} from './types/auth.types';
import {
  SecurityContext,
  SecurityError,
} from './types/security.types';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private readonly jwtService: JwtService,
    private readonly sessionService: SessionService,
    private readonly userService: UserService,
    private readonly encryptionService: EncryptionService
  ) {}

  // ========================
  // Authentication Methods
  // ========================

  async login(
    discordUser: DiscordOAuthProfile,
    req: Request
  ): Promise<LoginResponse> {
    try {
      // Create or update user
      const user = await this.userService.upsertUser({
        discordId: discordUser.id,
        username: discordUser.username,
        email: discordUser.email,
        avatar: discordUser.avatar,
        verified: discordUser.verified
      });

      // Get user's organization information
      const userOrganization = await this.userService.getUserOrganization(user.discordId);

      // Create secure session
      const session = await this.sessionService.createSession(user.discordId, req);

      // Generate JWT token with comprehensive payload
      const jwtPayload: JwtPayload = {
        sub: user.discordId,
        username: user.username,
        sessionId: session.sessionId,
        organizationId: userOrganization?.organization?.id || undefined,
        roles: userOrganization ? [userOrganization.role] : [],
        permissions: userOrganization?.permissions || [],
        tier: userOrganization?.organization?.tier || 'free',
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor((Date.now() + AUTH_CONSTANTS.SESSION.DEFAULT_EXPIRY) / 1000),
        iss: process.env.JWT_ISSUER || 'energex-auth',
        aud: process.env.JWT_AUDIENCE || 'energex-api'
      };

      const accessToken = this.jwtService.sign(jwtPayload);
      const refreshToken = this.generateRefreshToken(user.discordId, session.sessionId);

      const tokens: TokenPair = {
        accessToken,
        refreshToken,
        sessionToken: session.sessionId,
        expiresAt: session.expiresAt,
        tokenType: 'Bearer'
      };

      const authUser: AuthUser = {
        id: user.discordId,
        discordId: user.discordId,
        username: user.username,
        discriminator: discordUser.discriminator,
        avatar: discordUser.avatar,
        email: user.email,
        tier: userOrganization?.organization?.tier || 'free',
        organization: userOrganization?.organization || null,
        role: userOrganization?.role || null,
        permissions: userOrganization?.permissions || [],
        verified: user.verified || false,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      };

      this.logger.log(`User ${user.username} logged in successfully`);

      return {
        success: true,
        tokens,
        user: authUser,
        session: {
          id: session.sessionId,
          expiresAt: session.expiresAt
        }
      };
    } catch (error) {
      this.logger.error('Login failed:', error);
      throw new UnauthorizedException('Authentication failed');
    }
  }

  async validateSession(
    sessionToken: string,
    req: Request
  ): Promise<SessionWithUser | null> {
    try {
      const session = await this.sessionService.validateSession(sessionToken, req);
      
      if (!session || !session.user) {
        return null;
      }

      // Get organization information for the user
      const userOrganization = await this.userService.getUserOrganization(session.user.discordId);
      
      // Enhance user object with organization data
      const enhancedUser: AuthUser = {
        ...session.user,
        tier: userOrganization?.organization?.tier || 'free',
        organization: userOrganization?.organization || null,
        role: userOrganization?.role || null,
        permissions: userOrganization?.permissions || []
      };

      return {
        ...session,
        user: enhancedUser
      };
    } catch (error) {
      this.logger.error('Session validation failed:', error);
      return null;
    }
  }

  async logout(sessionToken: string): Promise<void> {
    try {
      await this.sessionService.revokeSession(sessionToken);
      this.logger.log('User session revoked successfully');
    } catch (error) {
      this.logger.error('Logout failed:', error);
      throw error;
    }
  }

  // ========================
  // CSRF Protection Methods
  // ========================

  async generateCsrfToken(sessionId?: string): Promise<string> {
    try {
      return this.encryptionService.generateCSRFToken(sessionId);
    } catch (error) {
      this.logger.error('CSRF token generation failed:', error);
      throw error;
    }
  }

  async validateCsrfToken(token: string, sessionId?: string): Promise<boolean> {
    try {
      return this.encryptionService.validateCSRFToken(token, sessionId);
    } catch (error) {
      this.logger.error('CSRF token validation failed:', error);
      return false;
    }
  }

  // ========================
  // Organization Methods
  // ========================

  async getUserOrganization(discordId: string) {
    try {
      return this.userService.getUserOrganization(discordId);
    } catch (error) {
      this.logger.error('Failed to get user organization:', error);
      return null;
    }
  }

  // ========================
  // JWT Token Methods
  // ========================

  async validateJwtToken(token: string): Promise<SessionResponse | null> {
    try {
      const payload = this.jwtService.verify<JwtPayload>(token);
      
      if (!payload || !payload.sub) {
        return null;
      }

      // Get user information
      const user = await this.userService.findByDiscordId(payload.sub);
      if (!user) {
        return null;
      }

      // Get organization information
      const userOrganization = await this.getUserOrganization(user.discordId);

      const authUser: AuthUser = {
        id: user.discordId,
        discordId: user.discordId,
        username: user.username,
        email: user.email,
        tier: userOrganization?.organization?.tier || 'free',
        organization: userOrganization?.organization || null,
        role: userOrganization?.role || null,
        permissions: payload.permissions || [],
        verified: user.verified || false,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        discriminator: user.discriminator,
        avatar: user.avatar
      };

      return {
        authenticated: true,
        user: authUser,
        session: {
          id: payload.sessionId,
          type: 'jwt',
          expiresAt: new Date(payload.exp * 1000),
          lastAccessedAt: new Date()
        },
        permissions: payload.permissions || []
      };
    } catch (error) {
      this.logger.debug('JWT validation failed:', error.message);
      return null;
    }
  }

  async validateTokenSecurity(token: string): Promise<TokenValidationResult> {
    try {
      const payload = this.jwtService.verify<JwtPayload>(token);
      
      // Comprehensive token validation
      const now = Math.floor(Date.now() / 1000);
      
      if (payload.exp < now) {
        return {
          valid: false,
          error: 'EXPIRED_TOKEN'
        };
      }

      if (payload.iat > now) {
        return {
          valid: false,
          error: 'INVALID_SIGNATURE'
        };
      }

      // Validate issuer and audience
      const expectedIss = process.env.JWT_ISSUER || 'energex-auth';
      const expectedAud = process.env.JWT_AUDIENCE || 'energex-api';
      
      if (payload.iss !== expectedIss || payload.aud !== expectedAud) {
        return {
          valid: false,
          error: 'INSUFFICIENT_SCOPE'
        };
      }

      return {
        valid: true,
        payload,
        expiresAt: new Date(payload.exp * 1000)
      };
    } catch (error) {
      this.logger.error('Token security validation failed:', error);
      return {
        valid: false,
        error: 'MALFORMED_TOKEN'
      };
    }
  }

  async refreshTokens(refreshToken: string): Promise<TokenPair | null> {
    try {
      const payload = this.jwtService.verify(refreshToken, {
        secret: process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET
      });

      if (payload.type !== 'refresh') {
        return null;
      }

      // Get user and create new tokens
      const user = await this.userService.findByDiscordId(payload.sub);
      if (!user) {
        return null;
      }

      const userOrganization = await this.getUserOrganization(user.discordId);

      const newJwtPayload: JwtPayload = {
        sub: user.discordId,
        username: user.username,
        sessionId: payload.sessionId,
        organizationId: userOrganization?.organization?.id,
        roles: userOrganization ? [userOrganization.role] : [],
        permissions: userOrganization?.permissions || [],
        tier: userOrganization?.organization?.tier || 'free',
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor((Date.now() + AUTH_CONSTANTS.SESSION.DEFAULT_EXPIRY) / 1000),
        iss: process.env.JWT_ISSUER || 'energex-auth',
        aud: process.env.JWT_AUDIENCE || 'energex-api'
      };

      const accessToken = this.jwtService.sign(newJwtPayload);
      const newRefreshToken = this.generateRefreshToken(user.discordId, payload.sessionId);

      return {
        accessToken,
        refreshToken: newRefreshToken,
        sessionToken: payload.sessionId,
        expiresAt: new Date(newJwtPayload.exp * 1000),
        tokenType: 'Bearer'
      };
    } catch (error) {
      this.logger.error('Token refresh failed:', error);
      return null;
    }
  }

  // ========================
  // Utility Methods
  // ========================

  private generateRefreshToken(userId: string, sessionId: string): string {
    const payload = {
      sub: userId,
      sessionId,
      type: 'refresh',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor((Date.now() + 30 * 24 * 60 * 60 * 1000) / 1000) // 30 days
    };
    
    return this.jwtService.sign(payload, {
      secret: process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET
    });
  }

  async checkRedisConnection(): Promise<boolean> {
    try {
      // TODO: Implement actual Redis health check
      return true;
    } catch {
      return false;
    }
  }

  async checkDatabaseConnection(): Promise<boolean> {
    try {
      // TODO: Implement actual database health check  
      return true;
    } catch {
      return false;
    }
  }

  // ========================
  // Security Methods
  // ========================

  async createSecurityContext(
    req: Request,
    user?: AuthUser,
    session?: any
  ): Promise<SecurityContext> {
    const ipAddress = req.ip || req.connection?.remoteAddress || 'unknown';
    const userAgent = req.get('User-Agent') || 'unknown';
    const deviceFingerprint = this.encryptionService.generateDeviceFingerprint(
      userAgent,
      ipAddress
    );

    // Calculate risk score based on various factors
    const riskScore = this.calculateRiskScore({
      ipAddress,
      userAgent,
      user,
      session
    });

    return {
      user,
      session,
      permissions: user?.permissions || [],
      ipAddress,
      userAgent,
      deviceFingerprint,
      riskScore,
      threatLevel: this.determineThreatLevel(riskScore)
    };
  }

  private calculateRiskScore(context: {
    ipAddress: string;
    userAgent: string;
    user?: AuthUser;
    session?: any;
  }): number {
    let score = 0;

    // IP-based risk factors
    if (context.ipAddress === 'unknown' || context.ipAddress.includes('127.0.0.1')) {
      score += 10;
    }

    // User-Agent risk factors
    if (context.userAgent === 'unknown' || !context.userAgent.includes('Mozilla')) {
      score += 5;
    }

    // User verification status
    if (context.user && !context.user.verified) {
      score += 15;
    }

    return Math.min(score, 100);
  }

  private determineThreatLevel(riskScore: number): 'low' | 'medium' | 'high' | 'critical' {
    if (riskScore >= 90) return 'critical';
    if (riskScore >= 75) return 'high';
    if (riskScore >= 25) return 'medium';
    return 'low';
  }

  async logSecurityEvent(
    type: string,
    severity: 'info' | 'warning' | 'error' | 'critical',
    context: any
  ): Promise<void> {
    // TODO: Implement comprehensive security event logging
    this.logger.log(`Security Event: ${type}`, { severity, context });
  }

  async detectSuspiciousActivity(
    req: Request,
    user?: AuthUser
  ): Promise<boolean> {
    // TODO: Implement machine learning-based suspicious activity detection
    // This could include:
    // - Unusual login times
    // - Geographic anomalies
    // - Device fingerprint changes
    // - Rate limiting violations
    // - Multiple failed attempts
    return false;
  }

  async enforceAccountSecurity(user: AuthUser): Promise<void> {
    // TODO: Implement account security enforcement
    // - Check for compromised passwords
    // - Enforce 2FA requirements
    // - Validate email verification
    // - Check for suspended accounts
  }

  // ========================
  // Password Security (Future Implementation)
  // ========================

  async hashPassword(password: string): Promise<string> {
    // TODO: Implement with bcrypt or argon2
    return this.encryptionService.hashPassword(password);
  }

  async verifyPassword(password: string, hash: string): Promise<boolean> {
    // TODO: Implement password verification
    return this.encryptionService.verifyPassword(password, hash);
  }

  // ========================
  // Rate Limiting Support
  // ========================

  async checkRateLimit(
    identifier: string,
    action: string,
    limit: number,
    windowMs: number
  ): Promise<{ allowed: boolean; remaining: number; resetTime: Date }> {
    // TODO: Implement Redis-based rate limiting
    return {
      allowed: true,
      remaining: limit - 1,
      resetTime: new Date(Date.now() + windowMs)
    };
  }

  async recordSecurityViolation(
    type: string,
    context: SecurityContext,
    details: any
  ): Promise<void> {
    // TODO: Record security violations for analysis
    await this.logSecurityEvent(type, 'warning', { context, details });
  }
}