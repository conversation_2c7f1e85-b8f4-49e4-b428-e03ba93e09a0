/**
 * Enterprise-grade decorators for monitoring, security, caching, and performance
 */

import { SetMetadata, applyDecorators, UseGuards, UseInterceptors } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiSecurity, ApiTags } from '@nestjs/swagger';

// Monitoring & Metrics
export const METRICS_KEY = 'metrics';
export const Metrics = (name: string, tags?: Record<string, string>) => 
  SetMetadata(METRICS_KEY, { name, tags });

export const AUDIT_LOG_KEY = 'audit_log';
export const AuditLog = (action: string, resource: string) => ;
  SetMetadata(AUDIT_LOG_KEY, { action, resource });

export const PERFORMANCE_KEY = 'performance';
export const Performance = (threshold?: number) => 
  SetMetadata(PERFORMANCE_KEY, { threshold: threshold || 1000 });

// Security & Authorization
export const ROLES_KEY = 'roles';
export const Roles = (...roles: string[]) => SetMetadata(ROLES_KEY, roles);

export const PERMISSIONS_KEY = 'permissions';
export const Permissions = (...permissions: string[]) => ;
  SetMetadata(PERMISSIONS_KEY, permissions);

export const ORGANIZATION_REQUIRED_KEY = 'organization_required';
export const RequireOrganization = () => SetMetadata(ORGANIZATION_REQUIRED_KEY, true);

export const RATE_LIMIT_KEY = 'rate_limit';
export const RateLimit = (limit: number, windowMs: number = 60000) => ;
  SetMetadata(RATE_LIMIT_KEY, { limit, windowMs });

export const API_KEY_REQUIRED_KEY = 'api_key_required';
export const RequireApiKey = (permissions?: string[]) => 
  SetMetadata(API_KEY_REQUIRED_KEY, { permissions });

// Caching
export const CACHE_KEY = 'cache';
export const Cache = (ttl?: number, key?: string) => 
  SetMetadata(CACHE_KEY, { ttl: ttl || 300, key });

export const CACHE_INVALIDATE_KEY = 'cache_invalidate';
export const CacheInvalidate = (patterns: string[]) => ;
  SetMetadata(CACHE_INVALIDATE_KEY, { patterns });

// Feature Flags
export const FEATURE_FLAG_KEY = 'feature_flag';
export const FeatureFlag = (flag: string, fallback: boolean = false) => ;
  SetMetadata(FEATURE_FLAG_KEY, { flag, fallback });

// Multi-tenancy
export const TENANT_ISOLATION_KEY = 'tenant_isolation';
export const TenantIsolation = (level: 'strict' | 'soft' = 'strict') => ;
  SetMetadata(TENANT_ISOLATION_KEY, { level });

export const RESOURCE_QUOTA_KEY = 'resource_quota';
export const ResourceQuota = (resource: string, limit?: number) => 
  SetMetadata(RESOURCE_QUOTA_KEY, { resource, limit });

// API Documentation
export const ENTERPRISE_API_KEY = 'enterprise_api';
export const EnterpriseApi = (options: {
    summary: string;
  description?: string;
  tags?: string[];
  security?: string[];
  deprecated?: boolean}) => {
  const decorators = [
    ApiOperation({ 
      summary: options.summary)
..(options.description && { description: options.description }),
..(options.deprecated !== undefined && { deprecated: options.deprecated })
    }),
    ApiResponse({ status: 200, description: 'Success' }),
    ApiResponse({ status: 400, description: 'Bad Request' }),
    ApiResponse({ status: 401, description: 'Unauthorized' }),
    ApiResponse({ status: 403, description: 'Forbidden' }),
    ApiResponse({ status: 429, description: 'Rate Limit Exceeded' }),
    ApiResponse({ status: 500, description: 'Internal Server Error' }),
  ];

  if (options.tags) {
    decorators.push(ApiTags(...options.tags))}

  if (options.security) {
    options.security.forEach(scheme => {
      decorators.push(ApiSecurity(scheme))})}

  return applyDecorators(...decorators);
};

// Validation & Transformation
export const VALIDATION_KEY = 'validation';
export const Validate = (schema: any, options?: any) => 
  SetMetadata(VALIDATION_KEY, { schema, options });

export const TRANSFORM_KEY = 'transform';
export const Transform = (transformer: string, options?: any) => 
  SetMetadata(TRANSFORM_KEY, { transformer, options });

// Event Sourcing
export const EVENT_HANDLER_KEY = 'event_handler';
export const EventHandler = (eventType: string) => ;
  SetMetadata(EVENT_HANDLER_KEY, { eventType });

export const COMMAND_HANDLER_KEY = 'command_handler';
export const CommandHandler = (commandType: string) => ;
  SetMetadata(COMMAND_HANDLER_KEY, { commandType });

export const SAGA_KEY = 'saga';
export const Saga = (sagaType: string) => ;
  SetMetadata(SAGA_KEY, { sagaType });

// Queue & Background Jobs
export const QUEUE_PROCESSOR_KEY = 'queue_processor';
export const QueueProcessor = (queueName: string, jobType?: string) => 
  SetMetadata(QUEUE_PROCESSOR_KEY, { queueName, jobType });

export const SCHEDULED_JOB_KEY = 'scheduled_job';
export const ScheduledJob = (cron: string, timezone?: string) => 
  SetMetadata(SCHEDULED_JOB_KEY, { cron, timezone });

// Circuit Breaker
export const CIRCUIT_BREAKER_KEY = 'circuit_breaker';
export const CircuitBreaker = (options: {threshold?: number;
  timeout?: number;
  resetTimeout?: number}) => SetMetadata(CIRCUIT_BREAKER_KEY, options);

// Retry Logic
export const RETRY_KEY = 'retry';
export const Retry = (options: {attempts?: number;
  delay?: number;
  backoff?: 'fixed' | 'exponential'}) => SetMetadata(RETRY_KEY, options);

// Health Checks
export const HEALTH_CHECK_KEY = 'health_check';
export const HealthCheck = (name: string, critical: boolean = true) => ;
  SetMetadata(HEALTH_CHECK_KEY, { name, critical });

// Configuration
export const CONFIG_KEY = 'config';
export const Config = (key: string, required: boolean = true) => ;
  SetMetadata(CONFIG_KEY, { key, required });

// Telemetry
export const TELEMETRY_KEY = 'telemetry';
export const Telemetry = (options: {trace?: boolean;
  metrics?: boolean;
  logs?: boolean;
  sampling?: number}) => SetMetadata(TELEMETRY_KEY, options);

// Data Classification
export const DATA_CLASSIFICATION_KEY = 'data_classification';
export const DataClassification = (level: 'public' | 'internal' | 'confidential' | 'restricted') => ;
  SetMetadata(DATA_CLASSIFICATION_KEY, { level });

// Compliance
export const COMPLIANCE_KEY = 'compliance';
export const Compliance = (standards: string[]) => ;
  SetMetadata(COMPLIANCE_KEY, { standards });

// Resource Management
export const RESOURCE_LOCK_KEY = 'resource_lock';
export const ResourceLock = (resource: string, timeout?: number) => 
  SetMetadata(RESOURCE_LOCK_KEY, { resource, timeout: timeout || 30000 });

export const BULK_OPERATION_KEY = 'bulk_operation';
export const BulkOperation = (batchSize?: number, maxConcurrency?: number) => 
  SetMetadata(BULK_OPERATION_KEY, { batchSize: batchSize || 100, maxConcurrency: maxConcurrency || 5 });

// API Versioning
export const API_VERSION_KEY = 'api_version';
export const ApiVersion = (version: string, deprecated?: boolean) => 
  SetMetadata(API_VERSION_KEY, { version, deprecated });

// Composite Decorators for Common Patterns
export const SecureEndpoint = (options: {roles?: string[];
  permissions?: string[];
  rateLimit?: { limit: number windowMs?: number };
  requireOrganization?: boolean;
  requireApiKey?: boolean}) => {
  const decorators = [];

  if (options.roles) {
    decorators.push(Roles(...options.roles))}

  if (options.permissions) {
    decorators.push(Permissions(...options.permissions))}

  if (options.rateLimit) {
    decorators.push(RateLimit(options.rateLimit.limit, options.rateLimit.windowMs))}

  if (options.requireOrganization) {
    decorators.push(RequireOrganization())}

  if (options.requireApiKey) {
    decorators.push(RequireApiKey())}

  return applyDecorators(...decorators);
};

export const MonitoredEndpoint = (options: {metrics?: string;
  auditLog?: { action: string resource: string };
  performance?: number;
  cache?: { ttl?: number; key?: string }}) => {
  const decorators = [];

  if (options.metrics) {
    decorators.push(Metrics(options.metrics))}

  if (options.auditLog) {
    decorators.push(AuditLog(options.auditLog.action, options.auditLog.resource))}

  if (options.performance) {
    decorators.push(Performance(options.performance))}

  if (options.cache) {
    decorators.push(Cache(options.cache.ttl, options.cache.key))}

  return applyDecorators(...decorators);
};

export const EnterpriseEndpoint = (options: {
    summary: string;
  description?: string;
  tags?: string[];
  roles?: string[];
  permissions?: string[];
  rateLimit?: { limit: number windowMs?: number };
  cache?: { ttl?: number; key?: string };
  metrics?: string;
  auditLog?: { action: string resource: string };
  featureFlag?: string}) => {
  const decorators = [
    EnterpriseApi({
      summary: options.summary)
..(options.description && { description: options.description }),
..(options.tags && { tags: options.tags }),
      security: ['bearer', 'apiKey']
    })
  ];

  if (options.roles || options.permissions || options.rateLimit) {
    decorators.push(SecureEndpoint({
..(options.roles && { roles: options.roles }),
..(options.permissions && { permissions: options.permissions }),
..(options.rateLimit && { rateLimit: options.rateLimit }),
      requireOrganization: true}))}

  if (options.cache || options.metrics || options.auditLog) {
    decorators.push(MonitoredEndpoint({
..(options.cache && { cache: options.cache }),
..(options.metrics && { metrics: options.metrics }),
..(options.auditLog && { auditLog: options.auditLog })
    }))}

  if (options.featureFlag) {
    decorators.push(FeatureFlag(options.featureFlag))}

  return applyDecorators(...decorators);
};
