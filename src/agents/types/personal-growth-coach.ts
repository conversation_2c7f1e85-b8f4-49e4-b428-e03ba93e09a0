import { Injectable, Logger } from '@nestjs/common';
import { AgentsService } from '../agents.service';

/**
 * Defines the personality traits and characteristics of the personal growth coach
 */
interface CoachPersonality {
  /** The communication tone used by the coach */
  readonly tone: string;
  /** The interaction style of the coach */
  readonly style: string;
  /** Areas of expertise the coach specializes in */
  readonly expertise: readonly string[];
}

/**
 * Pre-defined response templates for different interaction types
 */
interface ResponseTemplates {
  /** Welcome messages for new interactions */
  readonly greeting: readonly string[];
  /** Motivational messages to encourage users */
  readonly motivation: readonly string[];
  /** Goal-setting assistance responses */
  readonly goal_setting: readonly string[];
  /** Check-in and progress review messages */
  readonly check_in: readonly string[];
}

/**
 * User's coaching context and history
 */
interface UserContext {
  /** Recent goals set by the user */
  recentGoals?: GoalData | null;
  /** Coaching interaction history */
  history?: CoachingHistory | null;
  /** User's coaching preferences */
  preferences?: CoachingPreferences | null;
  /** Timestamp of the last interaction */
  lastInteraction?: Date | null;
  /** Recent challenges the user has faced */
  recentChallenges?: Challenge[] | null;
  /** Recent achievements or wins */
  recentWins?: Achievement[] | null;
}

/**
 * Data structure for user goals
 */
interface GoalData {
  /** List of active goals */
  goals: Goal[];
  /** Total number of goals set */
  totalGoals: number;
  /** Number of completed goals */
  completedGoals: number;
}

/**
 * Individual goal structure
 */
interface Goal {
  /** Unique goal identifier */
  id: string;
  /** Goal description */
  description: string;
  /** Target completion date */
  targetDate?: Date;
  /** Current progress (0-100) */
  progress: number;
  /** Goal priority level */
  priority: 'low' | 'medium' | 'high';
  /** Goal status */
  status: 'active' | 'completed' | 'paused';
}

/**
 * Coaching history data
 */
interface CoachingHistory {
  /** Total number of interactions */
  totalInteractions: number;
  /** Most recent interaction types */
  recentTypes: string[];
  /** Last coaching session date */
  lastSession: Date;
  /** Common topics discussed */
  commonTopics: string[];
}

/**
 * User's coaching preferences
 */
interface CoachingPreferences {
  /** Preferred communication style */
  communicationStyle: 'formal' | 'casual' | 'motivational';
  /** Focus areas of interest */
  focusAreas: string[];
  /** Reminder frequency preference */
  reminderFrequency: 'daily' | 'weekly' | 'monthly' | 'none';
  /** Preferred feedback type */
  feedbackType: 'direct' | 'gentle' | 'detailed';
}

/**
 * Challenge or obstacle data
 */
interface Challenge {
  /** Challenge description */
  description: string;
  /** When the challenge was encountered */
  date: Date;
  /** Severity level */
  severity: 'low' | 'medium' | 'high';
  /** Current status */
  status: 'active' | 'resolved' | 'ongoing';
}

/**
 * Achievement or win data
 */
interface Achievement {
  /** Achievement description */
  description: string;
  /** When the achievement occurred */
  date: Date;
  /** Category of achievement */
  category: string;
  /** Impact level */
  impact: 'small' | 'medium' | 'large';
}

/**
 * Memory entry for storing interactions
 */
interface InteractionMemory {
  /** Type of interaction */
  type: string;
  /** User's message content */
  userMessage: string;
  /** Coach's response */
  response: string;
  /** Interaction timestamp */
  timestamp: Date;
}

/**
 * Coach statistics and capabilities
 */
interface CoachStats {
  /** Type of coach */
  coachType: string;
  /** Personality configuration */
  personality: CoachPersonality;
  /** List of capabilities */
  capabilities: string[];
  /** Available response types */
  responseTypes: string[];
  /** Total number of response templates */
  totalTemplates: number;
}

/**
 * Interaction type definitions
 */
type InteractionType = 'greeting' | 'motivation' | 'goal_setting' | 'check_in' | 'message';

/**
 * Response template keys
 */
type TemplateType = keyof ResponseTemplates;

/**
 * Importance level for storing interactions
 */
type ImportanceLevel = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10;

@Injectable()
export class PersonalGrowthCoach {
  private readonly logger = new Logger(PersonalGrowthCoach.name);
  
  private readonly coachPersonality: CoachPersonality = {
    tone: 'supportive',
    style: 'encouraging',
    expertise: ['personal development', 'goal setting', 'motivation', 'habit formation'],
  };

  private readonly responseTemplates: ResponseTemplates = {
    greeting: [
      "Hello! I'm excited to be your personal growth coach. What area of your life would you like to focus on improving today?",
      "Welcome! As your personal growth coach, I'm here to help you unlock your potential. What's on your mind?",
      "Hi there! I'm here to support your journey of personal growth. What would you like to work on together?"
    ],
    motivation: [
      "Remember, every small step forward is progress. You're capable of amazing things! 🌟",
      "Your commitment to growth is inspiring. Keep pushing forward - you've got this! 💪",
      "Progress isn't always linear, but you're moving in the right direction. Stay focused on your goals! 🎯",
      "Each day is a new opportunity to become the person you want to be. What will you choose today? 🚀"
    ],
    goal_setting: [
      "Let's break that goal down into smaller, actionable steps. What's the first thing you can do today?",
      "Great goal! Now, let's make it SMART: Specific, Measurable, Achievable, Relevant, and Time-bound.",
      "I love your ambition! Let's create a roadmap to get you there. What does success look like to you?"
    ],
    check_in: [
      "How are you feeling about your progress today? What's one thing you're proud of?",
      "It's check-in time! What challenges are you facing, and how can we tackle them together?",
      "Let's reflect on your recent wins. What's working well for you right now?"
    ],
  };

  constructor(private readonly agentsService: AgentsService) {}

  /**
   * Handles user interactions and generates appropriate responses
   * @param userId - Unique identifier for the user
   * @param interactionType - Type of interaction (greeting, motivation, etc.)
   * @param content - User's message content
   * @param context - Additional context information
   * @returns Promise resolving to the coach's response
   */
  public async handleInteraction(
    userId: string,
    interactionType: InteractionType | string,
    content: string,
    context?: Record<string, unknown> | null
  ): Promise<string> {
    try {
      // Get user's coaching history and context
      const userContext = await this.getUserContext(userId);
      
      // Generate personalized response based on interaction type
      const response = await this.generateResponse(
        interactionType,
        content,
        userContext,
        context
      );

      // Store the interaction for future context
      await this.storeInteractionContext(userId, interactionType, content, response);

      return response;
    } catch (error) {
      this.logger.error(`Failed to handle interaction for user ${userId}:`, error);
      return "I apologize, but I'm having trouble processing your request right now. Please try again in a moment.";
    }
  }

  /**
   * Generates personalized responses based on interaction type and context
   * @param interactionType - Type of interaction
   * @param content - User's message content
   * @param userContext - User's coaching context and history
   * @param context - Additional context information
   * @returns Promise resolving to the generated response
   */
  private async generateResponse(
    interactionType: string,
    content: string,
    userContext: UserContext,
    context?: Record<string, unknown> | null
  ): Promise<string> {
    switch (interactionType) {
      case 'greeting':
        return this.getRandomTemplate('greeting');
      
      case 'motivation':
        return this.getMotivationalResponse(userContext);
      
      case 'goal_setting':
        return this.getGoalSettingResponse(content, userContext);
      
      case 'check_in':
        return this.getCheckinResponse(userContext, context?.isScheduled as boolean);
      
      case 'message':
        return await this.getContextualResponse(content, userContext);
      
      default:
        return this.getDefaultResponse(content, userContext);
    }
  }
  /**
   * Retrieves a random template from the specified category
   * @param type - Template category to select from
   * @returns Random template string
   */
  private getRandomTemplate(type: TemplateType): string {
    const templates = this.responseTemplates[type] || [];
    if (templates.length === 0) {
      return "I'm here to help you grow and achieve your goals!";
    }
    const randomIndex = Math.floor(Math.random() * templates.length);
    const template = templates[randomIndex];
    return template || "I'm here to help you grow and achieve your goals!";
  }
  /**
   * Generates a motivational response personalized to the user's context
   * @param userContext - User's coaching context
   * @returns Personalized motivational message
   */
  private getMotivationalResponse(userContext: UserContext): string {
    const baseMotivation = this.getRandomTemplate('motivation');
    // Personalize based on user context
    if (userContext.recentChallenges && userContext.recentChallenges.length > 0) {
      return `${baseMotivation}\n\nI know you've been working through some challenges lately. Remember, obstacles are opportunities in disguise.`;
    }
    
    if (userContext.recentWins && userContext.recentWins.length > 0) {
      return `${baseMotivation}\n\nI'm particularly proud of your recent progress. Keep building on that momentum!`;
    }
    
    return baseMotivation;
  }
  /**
   * Generates goal-setting advice based on user input
   * @param content - User's goal-related message
   * @param userContext - User's coaching context
   * @returns Goal-setting guidance response
   */
  private getGoalSettingResponse(content: string, userContext: UserContext): string {
    const baseResponse = this.getRandomTemplate('goal_setting');
    
    // Analyze the goal mentioned in the content
    const isSpecific = content.length > 50;
    const hasTimeframe = /\b(day|week|month|year|by\s+\w+)\b/i.test(content);
    
    let advice = baseResponse;
    
    if (!isSpecific) {
      advice += '\n\nTry to be more specific about what exactly you want to achieve.';
    }
    
    if (!hasTimeframe) {
      advice += '\n\nConsider adding a timeline - when would you like to achieve this?';
    }
    
    return advice;
  }

  /**
   * Generates check-in responses for progress reviews
   * @param userContext - User's coaching context
   * @param isScheduled - Whether this is a scheduled check-in
   * @returns Check-in response message
   */
  private getCheckinResponse(userContext: UserContext, isScheduled?: boolean): string {
    const baseCheckin = this.getRandomTemplate('check_in');
    if (isScheduled === true) {
      return `Good morning! ☀️ ${baseCheckin}\n\nTake a moment to reflect on yesterday and set your intention for today.`;
    }
    
    return baseCheckin;
  }
  /**
   * Generates contextual responses based on message content analysis
   * @param content - User's message content
   * @param userContext - User's coaching context
   * @returns Promise resolving to contextual response
   */
  private async getContextualResponse(content: string, userContext: UserContext): Promise<string> {
    const contentLower = content.toLowerCase();
    
    // Detect emotional state
    if (this.isNegativeEmotion(contentLower)) {
      return this.getEmpatheticResponse(content, userContext);
    }
    
    if (this.isPositiveEmotion(contentLower)) {
      return this.getCelebrativeResponse(content, userContext);
    }
    
    // Detect question
    if (content.includes('?')) {
      return this.getInquisitiveResponse(content, userContext);
    }
    
    // Detect goal or aspiration
    if (this.isGoalRelated(contentLower)) {
      return this.getGoalSupportResponse(content, userContext);
    }
    
    return this.getGeneralSupportResponse(content, userContext);
  }

  /**
   * Detects negative emotional indicators in user messages
   * @param content - Message content to analyze
   * @returns True if negative emotion detected
   */
  private isNegativeEmotion(content: string): boolean {
    const negativeWords = ['frustrated', 'stuck', 'overwhelmed', 'difficult', 'hard', 'struggle', 'fail', "can't"];
    return negativeWords.some(word => content.includes(word));
  }

  /**
   * Detects positive emotional indicators in user messages
   * @param content - Message content to analyze
   * @returns True if positive emotion detected
   */
  private isPositiveEmotion(content: string): boolean {
    const positiveWords = ['excited', 'happy', 'achieved', 'success', 'accomplished', 'proud', 'great', 'amazing'];
    return positiveWords.some(word => content.includes(word));
  }

  /**
   * Detects goal-related keywords in user messages
   * @param content - Message content to analyze
   * @returns True if goal-related content detected
   */
  private isGoalRelated(content: string): boolean {
    const goalWords = ['want to', 'goal', 'achieve', 'improve', 'get better', 'work on', 'focus on'];
    return goalWords.some(phrase => content.includes(phrase));
  }

  /**
   * Generates empathetic responses for users facing challenges
   * @param content - User's message content
   * @param userContext - User's coaching context
   * @returns Empathetic and supportive response
   */
  private getEmpatheticResponse(content: string, userContext: UserContext): string {
    return `I hear that you're facing some challenges right now, and that's completely normal. Everyone goes through difficult moments on their growth journey.\n\nWhat's one small step you could take today to move forward? Remember, progress doesn't have to be perfect - it just has to be consistent.`;
  }

  /**
   * Generates celebratory responses for user achievements
   * @param content - User's message content
   * @param userContext - User's coaching context
   * @returns Celebratory and encouraging response
   */
  private getCelebrativeResponse(content: string, userContext: UserContext): string {
    return `That's fantastic! 🎉 I'm so proud of your progress. Celebrating these wins is just as important as working toward your goals.\n\nHow can we build on this momentum? What's the next step in your journey?`;
  }

  /**
   * Generates responses to user questions
   * @param content - User's question
   * @param userContext - User's coaching context
   * @returns Thoughtful response encouraging reflection
   */
  private getInquisitiveResponse(content: string, userContext: UserContext): string {
    return `That's a great question! Let me help you think through this.\n\nBased on what you've shared, I'd suggest starting with some self-reflection. What does your intuition tell you? Sometimes the answers we seek are already within us.\n\nWould you like to explore this together step by step?`;
  }

  /**
   * Generates supportive responses for goal-related messages
   * @param content - User's goal-related message
   * @param userContext - User's coaching context
   * @returns Supportive goal-focused response
   */
  private getGoalSupportResponse(content: string, userContext: UserContext): string {
    return `I love your commitment to growth! 🌱 The fact that you're thinking about improvement shows real self-awareness.\n\nLet's break this down together:\n1. What specifically do you want to achieve?\n2. Why is this important to you?\n3. What's one action you can take this week?\n\nI'm here to support you every step of the way.`;
  }

  /**
   * Generates general supportive responses
   * @param content - User's message content
   * @param userContext - User's coaching context
   * @returns General supportive response
   */
  private getGeneralSupportResponse(content: string, userContext: UserContext): string {
    return `Thank you for sharing that with me. I'm here to support you on your personal growth journey.\n\nWhat's the most important thing you'd like to focus on right now? Whether it's building new habits, overcoming challenges, or reaching specific goals, we can work on it together.`;
  }

  /**
   * Generates default responses when no specific pattern is detected
   * @param content - User's message content
   * @param userContext - User's coaching context
   * @returns Default supportive response
   */
  private getDefaultResponse(content: string, userContext: UserContext): string {
    return `I'm here to help you grow and achieve your potential! Whether you want to set new goals, build better habits, or overcome challenges, I'm here to support you.\n\nWhat would you like to work on today?`;
  }

  /**
   * Retrieves user's coaching context from stored memories
   * @param userId - Unique user identifier
   * @returns Promise resolving to user context
   */
  private async getUserContext(userId: string): Promise<UserContext> {
    try {
      // Get user's recent interactions and stored memories
      const recentGoals = await this.agentsService.getMemory(userId, 'recent_goals');
      const coachingHistory = await this.agentsService.getMemory(userId, 'coaching_history');
      const preferences = await this.agentsService.getMemory(userId, 'coaching_preferences');
      
      return {
        recentGoals: (recentGoals?.value as unknown as GoalData) || null,
        history: (coachingHistory?.value as unknown as CoachingHistory) || null,
        preferences: (preferences?.value as unknown as CoachingPreferences) || null,
        lastInteraction: new Date(),
        recentChallenges: null,
        recentWins: null,
      };
    } catch (error) {
      this.logger.error(`Failed to get user context for ${userId}:`, error);
      return {
        recentGoals: null,
        history: null,
        preferences: null,
        lastInteraction: null,
        recentChallenges: null,
        recentWins: null,
      };
    }
  }

  /**
   * Stores interaction context for future personalization
   * @param userId - Unique user identifier
   * @param interactionType - Type of interaction
   * @param content - User's message content
   * @param response - Coach's response
   */
  private async storeInteractionContext(
    userId: string,
    interactionType: string,
    content: string,
    response: string
  ): Promise<void> {
    try {
      // Store the interaction for future personalization
      await this.agentsService.storeMemory({
        userId,
        memoryType: 'interaction',
        key: `coaching_${Date.now()}`,
        value: {
          type: interactionType,
          userMessage: content.substring(0, 200),
          response: response.substring(0, 200),
          timestamp: new Date(),
        } as InteractionMemory,
        importance: this.getInteractionImportance(interactionType),
      });
    } catch (error) {
      this.logger.error('Failed to store interaction context:', error);
    }
  }

  /**
   * Calculates the importance level of different interaction types
   * @param interactionType - Type of interaction
   * @returns Importance level (1-10)
   */
  private getInteractionImportance(interactionType: string): ImportanceLevel {
    const importanceMap: Record<string, ImportanceLevel> = {
      goal_setting: 8,
      check_in: 6,
      motivation: 4,
      greeting: 2,
      message: 5,
    };
    
    return importanceMap[interactionType] || 3;
  }

  /**
   * Retrieves comprehensive statistics about the coach's capabilities
   * @returns Promise resolving to coach statistics
   */
  public async getCoachStats(): Promise<CoachStats> {
    return {
      coachType: 'Personal Growth Coach',
      personality: this.coachPersonality,
      capabilities: [
        'Goal setting and planning',
        'Motivational support',
        'Habit formation guidance',
        'Progress tracking',
        'Overcoming obstacles',
        'Personal development advice'
      ],
      responseTypes: Object.keys(this.responseTemplates),
      totalTemplates: Object.values(this.responseTemplates).flat().length,
    };
  }
}