import { Injectable, Logger } from '@nestjs/common';
import { AgentsService } from '../agents.service';

// Branded types for type safety
type GoalId = string & { readonly __brand: 'GoalId' };
type UserId = string & { readonly __brand: 'UserId' };
type MilestoneId = string & { readonly __brand: 'MilestoneId' };

// Progress state discriminated union
type ProgressState = 
  | { readonly status: 'not_started'; readonly progress: 0 }
  | { readonly status: 'in_progress'; readonly progress: number }
  | { readonly status: 'completed'; readonly progress: 100 }
  | { readonly status: 'paused'; readonly progress: number };

// Template literal types for progress messages
type ProgressLevel = 'low' | 'medium' | 'high' | 'excellent';
type ProgressMessage<T extends ProgressLevel> = `Progress is ${T} - ${string}`;

// Conditional types for milestone validation
type MilestoneType = 'daily' | 'weekly' | 'monthly' | 'yearly';
type MilestoneValidation<T extends MilestoneType> = T extends 'daily' 
  ? { readonly maxDuration: 24 }
  : T extends 'weekly'
  ? { readonly maxDuration: 168 }
  : T extends 'monthly'
  ? { readonly maxDuration: 744 }
  : { readonly maxDuration: 8760 };

// Strict interfaces with proper typing
interface TrackerPersonality {
  readonly tone: 'analytical' | 'encouraging' | 'data-driven' | 'motivational';
  readonly style: 'formal' | 'casual' | 'data-driven' | 'supportive';
  readonly expertise: readonly string[];
}

type ResponseTemplateType = 'progress_check' | 'milestone_celebration' | 'goal_review' | 'trend_analysis';

interface ResponseTemplates {
  readonly progress_check: readonly string[];
  readonly milestone_celebration: readonly string[];
  readonly goal_review: readonly string[];
  readonly trend_analysis: readonly string[];
}

interface ProgressMetrics {
  readonly daily: readonly string[];
  readonly weekly: readonly string[];
  readonly monthly: readonly string[];
}

// Strict goal interface
interface Goal {
  readonly id: GoalId;
  readonly title: string;
  readonly description: string;
  readonly status: 'active' | 'completed' | 'paused' | 'cancelled';
  readonly category: string;
  readonly targetDate?: Date;
  readonly createdAt: Date;
  readonly updatedAt: Date;
  readonly progress: ProgressState;
  readonly priority: 1 | 2 | 3 | 4 | 5;
}

// Strict milestone interface
interface Milestone {
  readonly id: MilestoneId;
  readonly goalId: GoalId;
  readonly title: string;
  readonly description: string;
  readonly type: MilestoneType;
  readonly achievedAt?: Date;
  readonly targetDate: Date;
  readonly isCompleted: boolean;
  readonly validationRules: MilestoneValidation<MilestoneType>;
}

// Strict daily metrics interface
interface DailyMetric {
  readonly date: string;
  readonly checkedIn: boolean;
  readonly habitsCompleted: number;
  readonly timeSpent: number;
  readonly moodRating: 1 | 2 | 3 | 4 | 5;
  readonly energyLevel: 1 | 2 | 3 | 4 | 5;
  readonly notes?: string;
  readonly timestamp: Date;
}

// Strict weekly metrics interface
interface WeeklyMetric {
  readonly weekStart: string;
  readonly goalsAdvanced: number;
  readonly challengesOvercome: string[];
  readonly newSkillsLearned: string[];
  readonly totalTimeSpent: number;
  readonly avgMoodRating: number;
  readonly avgEnergyLevel: number;
}

// Strict monthly metrics interface
interface MonthlyMetric {
  readonly month: string;
  readonly majorMilestones: MilestoneId[];
  readonly habitConsistency: number;
  readonly goalCompletionRate: number;
  readonly totalGoalsCompleted: number;
  readonly avgWeeklyProgress: number;
}

// Trend analysis interface
interface TrendData {
  readonly date: Date;
  readonly metric: string;
  readonly value: number;
  readonly category: string;
  readonly userId: UserId;
}

interface ProgressData {
  readonly userId: UserId;
  readonly dailyMetrics: Record<string, DailyMetric>;
  readonly weeklyMetrics: Record<string, WeeklyMetric>;
  readonly monthlyMetrics: Record<string, MonthlyMetric>;
  readonly goals: readonly Goal[];
  readonly milestones: readonly Milestone[];
  readonly trends: readonly TrendData[];
  readonly lastUpdate: Date;
}

type TrendDirection = 'up' | 'down' | 'stable';

interface ProgressAnalysis {
  readonly completionRate: number;
  readonly trendDirection: TrendDirection;
  readonly streakCount: number;
  readonly recommendations: readonly string[];
  readonly confidence: number;
  readonly dataQuality: 'excellent' | 'good' | 'fair' | 'poor';
}

@Injectable()
export class ProgressTracker {
  private readonly logger = new Logger(ProgressTracker.name);
  
  private readonly trackerPersonality: TrackerPersonality = {
    tone: 'analytical',
    style: 'data-driven',
    expertise: ['goal tracking', 'progress measurement', 'milestone celebration', 'trend analysis'],
  };

  private readonly responseTemplates: ResponseTemplates = {
    progress_check: [
      'Let\'s review your progress! I\'ve been tracking your journey and I have some insights to share.',
      'Time for a progress check! I\'ve analyzed your recent activity and goal advancement.',
      'Progress update time! I\'ve compiled your latest achievements and areas for improvement.',
    ],
    milestone_celebration: [
      'Congratulations! 🎉 You\'ve reached an important milestone. Let\'s celebrate this achievement!',
      'Amazing work! 🌟 You\'ve hit a significant goal marker. This is worth celebrating!',
      'Fantastic progress! 🎯 You\'ve achieved another milestone on your journey!',
    ],
    goal_review: [
      'Let\'s examine your current goals and see how they align with your progress.',
      'Time to review your goals! I\'ll help you assess what\'s working and what needs adjustment.',
      'Goal review session! Let\'s analyze your targets and optimize your path forward.',
    ],
    trend_analysis: [
      'I\'ve noticed some interesting patterns in your progress. Let me share what the data shows.',
      'Based on your activity trends, I have some insights that might help you optimize your approach.',
      'The numbers tell a story! Here\'s what your progress data reveals about your journey.',
    ],
  };

  private readonly progressMetrics: ProgressMetrics = {
    daily: ['habits_completed', 'time_spent', 'mood_rating', 'energy_level'],
    weekly: ['goals_advanced', 'challenges_overcome', 'new_skills_learned'],
    monthly: ['major_milestones', 'habit_consistency', 'goal_completion_rate'],
  };

  constructor(private readonly agentsService: AgentsService) {}

  async handleInteraction(
    userId: string,
    interactionType: ResponseTemplateType | 'daily_checkin' | 'default',
    content: string,
    context?: Record<string, unknown>
  ): Promise<string> {
    try {
      // Get user's progress data and history
      const progressData = await this.getProgressData(userId);
      
      // Generate response based on interaction type
      const response = await this.generateResponse(
        interactionType,
        content,
        progressData,
        context
      );

      // Update progress tracking
      await this.updateProgressTracking(userId, interactionType, content, response);

      return response;
    } catch (error) {
      this.logger.error(`Failed to handle progress interaction for user ${userId}:`, error);
      return 'I\'m having trouble accessing your progress data right now. Please try again in a moment.';
    }
  }

  private async generateResponse(
    interactionType: ResponseTemplateType | 'daily_checkin' | 'default',
    content: string,
    progressData: ProgressData,
    context?: Record<string, unknown>
  ): Promise<string> {
    switch (interactionType) {
      case 'progress_check':
        return this.generateProgressCheck(progressData);
      
      case 'milestone_celebration':
        return this.generateMilestoneCelebration(progressData, content);
      
      case 'goal_review':
        return this.generateGoalReview(progressData);
      
      case 'trend_analysis':
        return this.generateTrendAnalysis(progressData);
      
      case 'daily_checkin':
        return this.generateDailyCheckin(progressData);
      
      default:
        return this.generateDefaultResponse(progressData);
    }
  }

  private getRandomTemplate(type: ResponseTemplateType): string {
    const templates = this.responseTemplates[type];
    if (templates.length === 0) {
      return 'I\'m here to help track your progress and celebrate your achievements!';
    }
    const randomIndex = Math.floor(Math.random() * templates.length);
    const selectedTemplate = templates[randomIndex];
    return selectedTemplate ?? 'I\'m here to help track your progress!';
  }

  private generateProgressCheck(progressData: ProgressData): string {
    const baseMessage = this.getRandomTemplate('progress_check');
    
    if (progressData.goals.length === 0) {
      return `${baseMessage}\n\nIt looks like you haven\'t set any goals yet. Would you like to start by setting some goals to track?`;
    }

    const analysis = this.analyzeProgress(progressData);
    
    return `${baseMessage}\n\n📊 **Your Progress Summary:**\n• Completion Rate: ${analysis.completionRate}%\n• Current Trend: ${analysis.trendDirection}\n• Streak: ${analysis.streakCount} days\n• Data Quality: ${analysis.dataQuality}\n• Confidence: ${Math.round(analysis.confidence * 100)}%\n\n💡 **Recommendations:**\n${analysis.recommendations.join('\n')}`;
  }

  private generateMilestoneCelebration(progressData: ProgressData, content: string): string {
    const baseMessage = this.getRandomTemplate('milestone_celebration');
    
    const recentMilestones = progressData.milestones.filter(m => 
      m.achievedAt && 
      new Date().getTime() - m.achievedAt.getTime() < 7 * 24 * 60 * 60 * 1000
    );

    if (recentMilestones.length > 0) {
      const milestone = recentMilestones[0];
      const achievedDate = milestone.achievedAt;
      if (achievedDate) {
        return `${baseMessage}\n\n🏆 **Milestone Achieved:** ${milestone.title}\n📅 **Date:** ${achievedDate.toLocaleDateString()}\n🎯 **Type:** ${milestone.type}\n\nThis achievement represents real progress in your journey. How does it feel to have reached this goal?`;
      }
    }

    return `${baseMessage}\n\nEvery step forward is worth celebrating! What recent achievement would you like to acknowledge?`;
  }

  private generateGoalReview(progressData: ProgressData): string {
    const baseMessage = this.getRandomTemplate('goal_review');
    
    if (progressData.goals.length === 0) {
      return `${baseMessage}\n\n🎯 It looks like you haven\'t set any goals yet. Let\'s start by identifying what you\'d like to achieve!`;
    }

    const activeGoals = progressData.goals.filter(g => g.status === 'active');
    const completedGoals = progressData.goals.filter(g => g.status === 'completed');
    const pausedGoals = progressData.goals.filter(g => g.status === 'paused');
    
    return `${baseMessage}\n\n🎯 **Goal Status:**\n• Active Goals: ${activeGoals.length}\n• Completed Goals: ${completedGoals.length}\n• Paused Goals: ${pausedGoals.length}\n• Success Rate: ${Math.round((completedGoals.length / progressData.goals.length) * 100)}%\n\nWhich goal would you like to focus on adjusting or optimizing?`;
  }

  private generateTrendAnalysis(progressData: ProgressData): string {
    const baseMessage = this.getRandomTemplate('trend_analysis');
    
    if (progressData.trends.length === 0) {
      return `${baseMessage}\n\n📈 I need more data points to analyze your trends. Keep logging your progress and I\'ll provide insights soon!`;
    }

    const trendInsights = this.analyzeTrends(progressData.trends);
    
    return `${baseMessage}\n\n📈 **Trend Analysis:**\n${trendInsights.map(insight => `• ${insight}`).join('\n')}\n\nBased on these patterns, I recommend focusing on consistency rather than perfection.`;
  }

  private generateDailyCheckin(progressData: ProgressData): string {
    const today = new Date().toDateString();
    const hasCheckedToday = progressData.dailyMetrics[today];

    if (hasCheckedToday) {
      const todayMetrics = hasCheckedToday;
      return `Great to see you again today! 📊 You\'ve already logged progress today.\n\n**Today\'s Metrics:**\n• Habits Completed: ${todayMetrics.habitsCompleted}\n• Mood: ${todayMetrics.moodRating}/5\n• Energy: ${todayMetrics.energyLevel}/5\n\nHow are you feeling about today\'s progress so far?`;
    }

    return `Welcome to your daily check-in! 📝\n\nLet\'s track today\'s progress:\n• What habits did you complete?\n• How was your energy level?\n• Any challenges you overcame?\n\nEven small steps count toward your bigger goals!`;
  }

  private generateDefaultResponse(progressData: ProgressData): string {
    return `I\'m your progress tracker! 📊 I\'m here to help you:\n\n• Monitor your goal advancement\n• Celebrate your milestones\n• Analyze your progress trends\n• Provide data-driven insights\n\nWhat aspect of your progress would you like to explore today?`;
  }

  private analyzeProgress(progressData: ProgressData): ProgressAnalysis {
    if (progressData.goals.length === 0) {
      return {
        completionRate: 0,
        trendDirection: 'stable',
        streakCount: 0,
        recommendations: ['Start by setting clear, measurable goals'],
        confidence: 0,
        dataQuality: 'poor',
      };
    }

    const completedGoals = progressData.goals.filter(g => g.status === 'completed').length;
    const totalGoals = progressData.goals.length;
    const completionRate = Math.round((completedGoals / totalGoals) * 100);

    // Enhanced trend analysis with data quality assessment
    const trendDirection = this.calculateTrendDirection(progressData);
    const streakCount = this.calculateStreak(progressData);
    const confidence = this.calculateConfidence(progressData);
    const dataQuality = this.assessDataQuality(progressData);
    const recommendations = this.generateRecommendations(completionRate, trendDirection);

    return {
      completionRate,
      trendDirection,
      streakCount,
      recommendations,
      confidence,
      dataQuality,
    };
  }

  private calculateStreak(progressData: ProgressData): number {
    let streak = 0;
    const today = new Date();
    
    for (let i = 0; i < 30; i++) {
      const date = new Date(today.getTime() - i * 24 * 60 * 60 * 1000);
      const dateString = date.toDateString();
      
      if (progressData.dailyMetrics[dateString]?.checkedIn) {
        streak++;
      } else if (i === 0) {
        // If today has no activity, streak is broken
        break;
      }
    }
    
    return streak;
  }

  private generateRecommendations(completionRate: number, trend: TrendDirection): readonly string[] {
    const recommendations: string[] = [];

    if (completionRate < 30) {
      recommendations.push('Consider breaking your goals into smaller, more manageable tasks');
      recommendations.push('Focus on building consistency with just 1-2 key habits');
    } else if (completionRate >= 70) {
      recommendations.push('Excellent progress! Consider setting more ambitious goals');
      recommendations.push('Share your success strategies with others');
    } else {
      recommendations.push('You\'re making steady progress! Stay consistent');
      recommendations.push('Identify what\'s working well and do more of that');
    }

    if (trend === 'down') {
      recommendations.push('Recent trend shows a dip - consider what obstacles you\'re facing');
    } else if (trend === 'up') {
      recommendations.push('Great momentum! Keep doing what\'s working');
    }

    return recommendations as readonly string[];
  }

  private analyzeTrends(trends: readonly TrendData[]): string[] {
    if (trends.length === 0) {
      return ['Not enough data for trend analysis yet'];
    }

    const insights: string[] = [];
    
    // Add basic trend insights
    insights.push(`You\'ve been most active on ${this.getMostActiveDay(trends)}`);
    insights.push(`Your best performing goal category is ${this.getTopCategory(trends)}`);
    
    if (trends.length >= 7) {
      const recentTrends = trends.slice(-7);
      const avgValue = recentTrends.reduce((sum, t) => sum + t.value, 0) / recentTrends.length;
      insights.push(`Your average performance this week: ${avgValue.toFixed(1)}`);
    }

    return insights;
  }

  private getMostActiveDay(trends: readonly TrendData[]): string {
    const dayActivity = new Map<string, number>();
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    
    trends.forEach(trend => {
      const dayName = days[trend.date.getDay()];
      if (dayName) {
        dayActivity.set(dayName, (dayActivity.get(dayName) ?? 0) + trend.value);
      }
    });
    
    let mostActiveDay = 'Monday';
    let maxActivity = 0;
    
    dayActivity.forEach((activity, day) => {
      if (activity > maxActivity) {
        maxActivity = activity;
        mostActiveDay = day;
      }
    });
    
    return mostActiveDay;
  }

  private getTopCategory(trends: readonly TrendData[]): string {
    const categoryPerformance = new Map<string, number>();
    
    trends.forEach(trend => {
      const category = trend.category;
      categoryPerformance.set(category, (categoryPerformance.get(category) ?? 0) + trend.value);
    });
    
    let topCategory = 'Personal Growth';
    let maxPerformance = 0;
    
    categoryPerformance.forEach((performance, category) => {
      if (performance > maxPerformance) {
        maxPerformance = performance;
        topCategory = category;
      }
    });
    
    return topCategory;
  }

  private async getProgressData(userId: string): Promise<ProgressData> {
    try {
      const [goals, milestones, dailyMetrics, weeklyMetrics, monthlyMetrics, trends] = await Promise.all([
        this.agentsService.getMemory(userId, 'user_goals'),
        this.agentsService.getMemory(userId, 'milestones'),
        this.agentsService.getMemory(userId, 'daily_metrics'),
        this.agentsService.getMemory(userId, 'weekly_metrics'),
        this.agentsService.getMemory(userId, 'monthly_metrics'),
        this.agentsService.getMemory(userId, 'progress_trends')
      ]);

      return {
        userId: userId as UserId,
        goals: this.validateGoals(goals?.value ?? []),
        milestones: this.validateMilestones(milestones?.value ?? []),
        dailyMetrics: this.validateDailyMetrics(dailyMetrics?.value ?? {}),
        weeklyMetrics: this.validateWeeklyMetrics(weeklyMetrics?.value ?? {}),
        monthlyMetrics: this.validateMonthlyMetrics(monthlyMetrics?.value ?? {}),
        trends: this.validateTrends(trends?.value ?? []),
        lastUpdate: new Date(),
      };
    } catch (error) {
      this.logger.error(`Failed to get progress data for user ${userId}:`, error);
      return this.createEmptyProgressData(userId as UserId);
    }
  }

  private async updateProgressTracking(
    userId: string,
    interactionType: ResponseTemplateType | 'daily_checkin' | 'default',
    content: string,
    response: string
  ): Promise<void> {
    try {
      const timestamp = new Date();
      
      // Store the interaction
      await this.agentsService.storeMemory({
        userId,
        memoryType: 'progress_interaction',
        key: `progress_${timestamp.getTime()}`,
        value: {
          type: interactionType,
          userMessage: content.substring(0, 200),
          response: response.substring(0, 200),
          timestamp,
        },
        importance: this.getInteractionImportance(interactionType),
      });

      // Update progress metrics if applicable
      if (interactionType === 'daily_checkin') {
        await this.updateDailyMetrics(userId, content);
      }
      
    } catch (error) {
      this.logger.error('Failed to update progress tracking:', error);
    }
  }

  private async updateDailyMetrics(userId: string, content: string): Promise<void> {
    try {
      const today = new Date().toDateString();
      const existingMetrics = await this.agentsService.getMemory(userId, 'daily_metrics');
      const metrics = existingMetrics?.value || {};
      
      metrics[today] = {
        checkedIn: true,
        content: content.substring(0, 100),
        timestamp: new Date(),
      };

      await this.agentsService.storeMemory({
        userId,
        memoryType: 'daily_metrics',
        key: 'daily_metrics',
        value: metrics,
        importance: 7,
      });
    } catch (error) {
      this.logger.error('Failed to update daily metrics:', error);
    }
  }

  private getInteractionImportance(interactionType: string): number {
    const importanceMap: Record<string, number> = {
      progress_check: 8,
      milestone_celebration: 9,
      goal_review: 8,
      trend_analysis: 7,
      daily_checkin: 6,
    };
    
    return importanceMap[interactionType] ?? 5;
  }

  // Type guards for data validation
  private isValidGoal(goal: unknown): goal is Goal {
    return (
      typeof goal === 'object' &&
      goal !== null &&
      'id' in goal &&
      'title' in goal &&
      'status' in goal &&
      typeof (goal as any).id === 'string' &&
      typeof (goal as any).title === 'string' &&
      ['active', 'completed', 'paused', 'cancelled'].includes((goal as any).status)
    );
  }

  private isValidMilestone(milestone: unknown): milestone is Milestone {
    return (
      typeof milestone === 'object' &&
      milestone !== null &&
      'id' in milestone &&
      'title' in milestone &&
      'type' in milestone &&
      typeof (milestone as any).id === 'string' &&
      typeof (milestone as any).title === 'string' &&
      ['daily', 'weekly', 'monthly', 'yearly'].includes((milestone as any).type)
    );
  }

  private isValidDailyMetric(metric: unknown): metric is DailyMetric {
    return (
      typeof metric === 'object' &&
      metric !== null &&
      'date' in metric &&
      'checkedIn' in metric &&
      typeof (metric as any).date === 'string' &&
      typeof (metric as any).checkedIn === 'boolean'
    );
  }

  // Data validation helpers
  private validateGoals(goals: unknown[]): readonly Goal[] {
    return goals.filter(this.isValidGoal);
  }

  private validateMilestones(milestones: unknown[]): readonly Milestone[] {
    return milestones.filter(this.isValidMilestone);
  }

  private validateDailyMetrics(metrics: Record<string, unknown>): Record<string, DailyMetric> {
    const validMetrics: Record<string, DailyMetric> = {};
    for (const [date, metric] of Object.entries(metrics)) {
      if (this.isValidDailyMetric(metric)) {
        validMetrics[date] = metric;
      }
    }
    return validMetrics;
  }

  private validateWeeklyMetrics(metrics: Record<string, unknown>): Record<string, WeeklyMetric> {
    // Implementation for weekly metrics validation
    return metrics as Record<string, WeeklyMetric>;
  }

  private validateMonthlyMetrics(metrics: Record<string, unknown>): Record<string, MonthlyMetric> {
    // Implementation for monthly metrics validation
    return metrics as Record<string, MonthlyMetric>;
  }

  private validateTrends(trends: unknown[]): readonly TrendData[] {
    return trends.filter((trend): trend is TrendData => {
      return (
        typeof trend === 'object' &&
        trend !== null &&
        'date' in trend &&
        'value' in trend &&
        'category' in trend &&
        trend.date instanceof Date &&
        typeof (trend as any).value === 'number' &&
        typeof (trend as any).category === 'string'
      );
    });
  }

  private createEmptyProgressData(userId: UserId): ProgressData {
    return {
      userId,
      goals: [],
      milestones: [],
      dailyMetrics: {},
      weeklyMetrics: {},
      monthlyMetrics: {},
      trends: [],
      lastUpdate: new Date(),
    };
  }

  // Enhanced analysis methods
  private calculateTrendDirection(progressData: ProgressData): TrendDirection {
    if (progressData.trends.length < 2) {
      return 'stable';
    }

    const recentTrends = progressData.trends.slice(-7);
    const olderTrends = progressData.trends.slice(-14, -7);

    if (recentTrends.length === 0 || olderTrends.length === 0) {
      return 'stable';
    }

    const recentAvg = recentTrends.reduce((sum, t) => sum + t.value, 0) / recentTrends.length;
    const olderAvg = olderTrends.reduce((sum, t) => sum + t.value, 0) / olderTrends.length;

    if (recentAvg > olderAvg * 1.1) return 'up';
    if (recentAvg < olderAvg * 0.9) return 'down';
    return 'stable';
  }

  private calculateConfidence(progressData: ProgressData): number {
    let confidence = 0;

    // Data completeness factors
    if (progressData.goals.length > 0) confidence += 0.3;
    if (progressData.trends.length >= 7) confidence += 0.3;
    if (Object.keys(progressData.dailyMetrics).length >= 7) confidence += 0.4;

    return Math.min(confidence, 1.0);
  }

  private assessDataQuality(progressData: ProgressData): 'excellent' | 'good' | 'fair' | 'poor' {
    const dataPoints = progressData.goals.length + 
                      progressData.trends.length + 
                      Object.keys(progressData.dailyMetrics).length;

    if (dataPoints >= 30) return 'excellent';
    if (dataPoints >= 15) return 'good';
    if (dataPoints >= 5) return 'fair';
    return 'poor';
  }

  // Branded type helpers
  private createGoalId(id: string): GoalId {
    return id as GoalId;
  }

  private createUserId(id: string): UserId {
    return id as UserId;
  }

  private createMilestoneId(id: string): MilestoneId {
    return id as MilestoneId;
  }

  async getTrackerStats(): Promise<{
    readonly trackerType: 'Progress Tracker';
    readonly personality: TrackerPersonality;
    readonly capabilities: readonly string[];
    readonly metrics: ProgressMetrics;
    readonly responseTypes: readonly ResponseTemplateType[];
  }> {
    return {
      trackerType: 'Progress Tracker',
      personality: this.trackerPersonality,
      capabilities: [
        'Goal progress monitoring',
        'Milestone celebration',
        'Trend analysis',
        'Daily check-ins',
        'Data-driven insights',
        'Progress recommendations',
        'Advanced type safety',
        'Branded type validation',
        'Discriminated union handling'
      ] as const,
      metrics: this.progressMetrics,
      responseTypes: ['progress_check', 'milestone_celebration', 'goal_review', 'trend_analysis'] as const,
    };
  }
}