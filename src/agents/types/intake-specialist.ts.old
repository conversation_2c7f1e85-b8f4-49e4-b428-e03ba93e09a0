import { Injectable, Logger } from '@nestjs/common';
import { AgentsService } from '../agents.service';
import {
  AgentType,
  InteractionType,
  MemoryType,
  AgentMemory,
  CreateAgentMemory,
  InteractionContext,
  MemoryValue,
  UserPreferences,
  UserProfile
} from '../../core/database/schema-types';
import {
  CommandInteraction,
  ButtonInteraction,
  SelectMenuInteraction,
  ModalSubmitInteraction,
  Message,
  User
} from 'discord.js';

// Strict type definitions for intake specialist
export interface IntakeFormData {
  readonly userId: string;
  readonly stage: AssessmentStage;
  readonly response: string;
  readonly timestamp: Date;
  readonly isComplete: boolean;
}

export type AssessmentStage = 'initial' | 'experience' | 'goals' | 'challenges' | 'preferences' | 'completion';

export interface AssessmentState {
  readonly userId: string;
  readonly currentStage: AssessmentStage | null;
  readonly completed: boolean;
  readonly startedAt?: Date;
  readonly lastInteraction?: Date;
  readonly responses: Record<string, string | null>;
}

export interface UserIntakeProfile {
  experienceLevel: 'beginner' | 'intermediate' | 'advanced';
  needsMotivation: boolean;
  hasSpecificGoals: boolean;
  preferredStyle: 'accountability' | 'encouragement' | 'practical' | 'balanced';
  primaryFocus: 'general_growth' | 'career' | 'health' | 'relationships' | 'skills' | 'mindset';
  completedAt: Date;
  version: string;
}

export interface IntakeValidationResult {
  readonly isValid: boolean;
  readonly errors: readonly string[];
  readonly warnings: readonly string[];
}

export interface IntakeInteractionContext extends InteractionContext {
  readonly interactionType: InteractionType;
  readonly userId: string;
  readonly content: string;
  readonly assessmentState: AssessmentState;
}

export interface IntakeResponse {
  readonly content: string;
  readonly nextStage: AssessmentStage | null;
  readonly isComplete: boolean;
  readonly recommendations: readonly string[];
  readonly metadata: Readonly<Record<string, unknown>>;
}

export interface IntakeMemoryData {
  readonly userId: string;
  readonly memoryType: MemoryType;
  readonly key: string;
  readonly value: unknown;
  readonly importance: number;
  readonly tags?: readonly string[];
}

export interface AssessmentQuestions {
  [K in AssessmentStage]: readonly string[];
}

export interface IntakeError {
  readonly code: string;
  readonly message: string;
  readonly stage?: AssessmentStage;
  readonly userId?: string;
  readonly timestamp: Date;
}

export interface IntakeStats {
  readonly specialistType: string;
  readonly personality: Readonly<Record<string, unknown>>;
  readonly capabilities: readonly string[];
  readonly assessmentStages: readonly AssessmentStage[];
  readonly questionCategories: readonly string[];
  readonly totalQuestions: number;
}

// Type guards for validation
export function isValidAssessmentStage(stage: string): stage is AssessmentStage {
  return ['initial', 'experience', 'goals', 'challenges', 'preferences', 'completion'].includes(stage);
}

export function isValidInteractionType(type: string): type is InteractionType {
  return ['text', 'voice', 'slash_command', 'button', 'select_menu', 'modal', 'context_menu'].includes(type);
}

export function isValidAssessmentState(state: unknown): state is AssessmentState {
  if (!state || typeof state !== 'object') return false;
  const s = state as Record<string, unknown>;
  return (
    typeof s.userId === 'string' &&
    (s.currentStage === null || isValidAssessmentStage(s.currentStage as string)) &&
    typeof s.completed === 'boolean' &&
    (s.responses === undefined || typeof s.responses === 'object')
  );
}

export function isValidUserIntakeProfile(profile: unknown): profile is UserIntakeProfile {
  if (!profile || typeof profile !== 'object') return false;
  const p = profile as Record<string, unknown>;
  return (
    ['beginner', 'intermediate', 'advanced'].includes(p.experienceLevel as string) &&
    typeof p.needsMotivation === 'boolean' &&
    typeof p.hasSpecificGoals === 'boolean' &&
    ['accountability', 'encouragement', 'practical', 'balanced'].includes(p.preferredStyle as string) &&
    typeof p.completedAt === 'object' &&
    typeof p.version === 'string'
  );
}

// Error handling types
export class IntakeValidationError extends Error {
  constructor(
    public readonly code: string,
    message: string,
    public readonly stage?: AssessmentStage,
    public readonly userId?: string
  ) {
    super(message);
    this.name = 'IntakeValidationError';
  }
}

export class IntakeProcessingError extends Error {
  constructor(
    message: string,
    public readonly userId: string,
    public readonly stage: AssessmentStage,
    public readonly cause?: Error
  ) {
    super(message);
    this.name = 'IntakeProcessingError';
  }
}

// Result types for safer error handling
export type IntakeResult<T> = 
  | { readonly success: true; readonly data: T }
  | { readonly success: false; readonly error: IntakeError };

export type AsyncIntakeResult<T> = Promise<IntakeResult<T>>;

// Helper function to check if result is error
export function isIntakeError<T>(result: IntakeResult<T>): result is { success: false; error: IntakeError } {
  return !result.success;
}

@Injectable()
export class IntakeSpecialist {
  private readonly logger = new Logger(IntakeSpecialist.name);
  
  // Intake specialist personality and assessment templates
  private readonly specialistPersonality: Readonly<{
    tone: string;
    style: string;
    expertise: readonly string[];
  }> = {
    tone: 'professional',
    style: 'thorough',
    expertise: ['user onboarding', 'needs assessment', 'goal identification', 'service matching'] as const,
  } as const;

  private readonly assessmentQuestions: AssessmentQuestions = {
    initial: [
      'Welcome! I\'m here to help you get the most out of our community. Let\'s start with understanding your goals. What brings you here today?',
      'Hello! I\'m your intake specialist. My role is to understand your needs and connect you with the right resources. What are you hoping to achieve?',
      'Hi there! I\'ll be guiding you through our intake process. This helps us personalize your experience. What\'s your main area of interest?',
    ],
    experience: [
      'Tell me about your experience with personal development. Are you just starting out, or have you been working on yourself for a while?',
      'What\'s your background with growth-focused communities? Have you been part of similar groups before?',
      'Help me understand your journey so far. What personal development work have you done previously?',
    ],
    goals: [
      'What are your top 3 goals you\'d like to work on in the next 6 months?',
      'If you could change one thing about your life in the next year, what would it be?',
      'What does success look like to you? Paint me a picture of your ideal outcome.',
    ],
    challenges: [
      'What are the biggest obstacles you\'ve faced in reaching your goals?',
      'What typically gets in your way when you\'re trying to make positive changes?',
      'Tell me about a time you struggled to maintain momentum. What happened?',
    ],
    preferences: [
      'How do you prefer to receive support? Do you like daily check-ins, weekly reviews, or something else?',
      'Are you more motivated by accountability, encouragement, or practical advice?',
      'What time of day are you most likely to engage with personal development content?',
    ],
    completion: [
      'Perfect! Based on our conversation, I have a good understanding of your needs. Let me connect you with the right resources.',
      'Excellent! Your intake assessment is complete. I\'ll now set up your personalized experience based on what you\'ve shared.',
      'Thank you for those thoughtful responses! I\'m ready to recommend the best path forward for you.',
    ]
  };

  private readonly assessmentStages: readonly AssessmentStage[] = [
    'initial',
    'experience', 
    'goals',
    'challenges',
    'preferences',
    'completion'
  ] as const;

  constructor(private readonly agentsService: AgentsService) {}

  async handleInteraction(
    userId: string,
    interactionType: InteractionType,
    content: string,
    context?: Partial<InteractionContext>
  ): Promise<IntakeResult<string>> {
    try {
      // Validate inputs
      if (!userId.trim()) {
        return {
          success: false,
          error: {
            code: 'INVALID_USER_ID',
            message: 'User ID cannot be empty',
            timestamp: new Date()
          }
        };
      }

      if (!isValidInteractionType(interactionType)) {
        return {
          success: false,
          error: {
            code: 'INVALID_INTERACTION_TYPE',
            message: `Invalid interaction type: ${interactionType}`,
            userId,
            timestamp: new Date()
          }
        };
      }

      // Get user's assessment progress
      const assessmentStateResult = await this.getAssessmentState(userId);
      if (!assessmentStateResult.success) {
        return {
          success: false,
          error: assessmentStateResult.error
        };
      }

      const assessmentState = assessmentStateResult.data;
      
      // Generate response based on interaction type and assessment progress
      const responseResult = await this.generateResponse(
        interactionType,
        content,
        assessmentState,
        context
      );

      if (!responseResult.success) {
        return {
          success: false,
          error: responseResult.error
        };
      }

      // Update assessment state
      const updateResult = await this.updateAssessmentState(userId, interactionType, content, responseResult.data.content);
      if (!updateResult.success) {
        this.logger.warn(`Failed to update assessment state for user ${userId}`, updateResult.error);
      }

      return { success: true, data: responseResult.data.content };
    } catch (error) {
      this.logger.error(`Failed to handle intake interaction for user ${userId}:`, error);
      return {
        success: false,
        error: {
          code: 'INTERACTION_PROCESSING_ERROR',
          message: 'Failed to process interaction',
          userId,
          timestamp: new Date()
        }
      };
    }
  }

  private async generateResponse(
    interactionType: InteractionType,
    content: string,
    assessmentState: AssessmentState,
    context?: Partial<InteractionContext>
  ): Promise<IntakeResult<IntakeResponse>> {
    try {
      let response: IntakeResponse;
      
      switch (interactionType) {
        case 'slash_command':
        case 'text':
          if (content.toLowerCase().includes('restart') || content.toLowerCase().includes('start over')) {
            response = this.restartAssessment();
          } else if (!assessmentState.currentStage || assessmentState.currentStage === 'initial') {
            response = this.startAssessment(assessmentState);
          } else {
            const continueResult = await this.continueAssessment(content, assessmentState);
            if (!continueResult.success) {
              return continueResult;
            }
            response = continueResult.data;
          }
          break;
        case 'button':
        case 'select_menu':
        case 'modal':
          const continueResult = await this.continueAssessment(content, assessmentState);
          if (!continueResult.success) {
            return continueResult;
          }
          response = continueResult.data;
          break;
        default:
          response = this.getGeneralIntakeResponse(content, assessmentState);
      }
      
      return { success: true, data: response };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'RESPONSE_GENERATION_ERROR',
          message: 'Failed to generate response',
          userId: assessmentState.userId,
          stage: assessmentState.currentStage || undefined,
          timestamp: new Date()
        }
      };
    }
  }

  private startAssessment(assessmentState: AssessmentState): IntakeResponse {
    if (assessmentState.completed) {
      return {
        content: `Welcome back! I see you've already completed your intake assessment. Would you like to: \n\n1. Review your profile\n2. Update your goals\n3. Start a new assessment\n\nWhat would you prefer?`,
        nextStage: null,
        isComplete: true,
        recommendations: ['Review profile', 'Update goals', 'Start new assessment'],
        metadata: { assessmentCompleted: true }
      };
    }
    
    if (assessmentState.currentStage && assessmentState.currentStage !== 'initial') {
      return {
        content: `I see we were in the middle of your assessment. Let's continue where we left off.\n\n${this.getQuestionForStage(assessmentState.currentStage)}`,
        nextStage: assessmentState.currentStage,
        isComplete: false,
        recommendations: [],
        metadata: { resumingAssessment: true, stage: assessmentState.currentStage }
      };
    }
    
    return {
      content: this.getRandomQuestion('initial') + '\n\n*This assessment will take about 5-10 minutes and will help us personalize your experience.*',
      nextStage: 'experience',
      isComplete: false,
      recommendations: [],
      metadata: { startingAssessment: true }
    };
  }

  private async continueAssessment(content: string, assessmentState: AssessmentState): Promise<IntakeResult<IntakeResponse>> {
    try {
      // Validate content
      if (!content.trim()) {
        return {
          success: false,
          error: {
            code: 'EMPTY_RESPONSE',
            message: 'Response cannot be empty',
            userId: assessmentState.userId,
            stage: assessmentState.currentStage || 'initial',
            timestamp: new Date()
          }
        };
      }

      const currentStage = assessmentState.currentStage || 'initial';
      
      // Store the user's response
      const storeResult = await this.storeAssessmentResponse(
        assessmentState.userId,
        currentStage,
        content
      );
      
      if (!storeResult.success) {
        return storeResult;
      }
      
      // Move to next stage
      const nextStage = this.getNextStage(currentStage);
      
      if (!nextStage) {
        // Assessment complete
        const completionResult = await this.completeAssessment(assessmentState.userId);
        return completionResult;
      }
      
      // Continue with next question
      const acknowledgment = this.getAcknowledgment(content);
      const nextQuestion = this.getQuestionForStage(nextStage);
      
      return {
        success: true,
        data: {
          content: `${acknowledgment}\n\n${nextQuestion}`,
          nextStage,
          isComplete: false,
          recommendations: [],
          metadata: { 
            currentStage: nextStage,
            responseLength: content.length,
            acknowledgmentUsed: acknowledgment
          }
        }
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'ASSESSMENT_CONTINUATION_ERROR',
          message: 'Failed to continue assessment',
          userId: assessmentState.userId,
          stage: assessmentState.currentStage || 'initial',
          timestamp: new Date()
        }
      };
    }
  }

  private restartAssessment(): IntakeResponse {
    return {
      content: `No problem! Let\'s start fresh with your intake assessment.\n\n${this.getRandomQuestion('initial')}\n\n*This assessment will help us understand your needs and goals better.*`,
      nextStage: 'experience',
      isComplete: false,
      recommendations: [],
      metadata: { restarted: true, timestamp: new Date() }
    };
  }

  private getGeneralIntakeResponse(content: string, assessmentState: AssessmentState): IntakeResponse {
    if (!assessmentState.completed) {
      return {
        content: `I'd love to help you with that! First, let's complete your intake assessment so I can understand your needs better.\n\n${this.getQuestionForStage(assessmentState.currentStage || 'initial')}`,
        nextStage: assessmentState.currentStage || 'initial',
        isComplete: false,
        recommendations: ['Complete intake assessment'],
        metadata: { needsCompletion: true }
      };
    }
    
    return {
      content: `Based on your intake assessment, I can see you\'re focused on personal growth. How can I assist you today?\n\nWould you like me to: \n- Connect you with your personal growth coach\n- Review your assessment results\n- Update your profile`,
      nextStage: null,
      isComplete: true,
      recommendations: ['Connect with coach', 'Review results', 'Update profile'],
      metadata: { postAssessment: true }
    };
  }

  private getRandomQuestion(stage: AssessmentStage): string {
    const questions = this.assessmentQuestions[stage] || [];
    if (questions.length === 0) {
      return 'Tell me more about what you\'re looking for.';
    }
    const randomIndex = Math.floor(Math.random() * questions.length);
    const question = questions[randomIndex];
    return question || 'Tell me more about what you\'re looking for.';
  }

  private getQuestionForStage(stage: AssessmentStage): string {
    return this.getRandomQuestion(stage);
  }

  private getNextStage(currentStage: AssessmentStage): AssessmentStage | null {
    const currentIndex = this.assessmentStages.indexOf(currentStage);
    if (currentIndex === -1 || currentIndex >= this.assessmentStages.length - 1) {
      return null;
    }
    return this.assessmentStages[currentIndex + 1] || null;
  }

  private getAcknowledgment(content: string): string {
    const acknowledgments = [
      'Thank you for sharing that.',
      'I appreciate your openness.',
      'That\'s helpful to know.',
      'Great, that gives me good insight.',
      'Perfect, that information is very useful.',
    ];
    
    // Customize based on content length or sentiment
    if (content.length > 100) {
      return 'Thank you for that detailed response.';
    }
    
    const randomIndex = Math.floor(Math.random() * acknowledgments.length);
    const acknowledgment = acknowledgments[randomIndex];
    return acknowledgment || 'Thank you for that information.';
  }

  private async completeAssessment(userId: string): Promise<IntakeResult<IntakeResponse>> {
    try {
      // Get all assessment responses
      const responsesResult = await this.getAssessmentResponses(userId);
      if (!responsesResult.success) {
        return {
          success: false,
          error: responsesResult.error
        };
      }
      
      // Analyze responses to create profile
      const profile = this.analyzeAssessmentResponses(responsesResult.data);
      if (!isValidUserIntakeProfile(profile)) {
        return {
          success: false,
          error: {
            code: 'INVALID_PROFILE',
            message: 'Generated profile is invalid',
            userId,
            timestamp: new Date()
          }
        };
      }
      
      // Store completed profile
      const storeResult = await this.storeUserProfile(userId, profile);
      if (!storeResult.success) {
        return {
          success: false,
          error: storeResult.error
        };
      }
      
      // Generate completion response with recommendations
      const response = this.generateCompletionResponse(profile);
      return { success: true, data: response };
    } catch (error) {
      this.logger.error(`Failed to complete assessment for user ${userId}:`, error);
      return {
        success: false,
        error: {
          code: 'ASSESSMENT_COMPLETION_ERROR',
          message: 'Failed to complete assessment',
          userId,
          timestamp: new Date()
        }
      };
    }
  }

  private generateCompletionResponse(profile: UserIntakeProfile): IntakeResponse {
    const completion = this.getRandomQuestion('completion');
    const recommendationsList: string[] = [];
    
    let recommendationsText = '\n\n**Your Personalized Recommendations:**\n';
    
    if (profile.needsMotivation) {
      recommendationsText += '• I\'ll connect you with our Personal Growth Coach for regular motivation and support\n';
      recommendationsList.push('Personal Growth Coach');
    }
    
    if (profile.hasSpecificGoals) {
      recommendationsText += '• Our Progress Tracker will help you monitor your goal achievement\n';
      recommendationsList.push('Progress Tracker');
    }
    
    if (profile.experienceLevel === 'beginner') {
      recommendationsText += '• I\'ve set up a beginner-friendly resource library for you\n';
      recommendationsList.push('Beginner Resources');
    }
    
    recommendationsText += '\n**Next Steps:**\n';
    recommendationsText += '1. Check your DMs for your personalized welcome package\n';
    recommendationsText += '2. Join our community channels that match your interests\n';
    recommendationsText += '3. Schedule your first coaching session if desired\n';
    
    return {
      content: completion + recommendationsText + '\n\nWelcome to the community! 🎉',
      nextStage: null,
      isComplete: true,
      recommendations: recommendationsList,
      metadata: { 
        profile,
        completionTime: new Date(),
        assessmentVersion: profile.version
      }
    };
  }

  private analyzeAssessmentResponses(responses: Record<AssessmentStage, string>): UserIntakeProfile {
    // Simple analysis of assessment responses
    const profile: UserIntakeProfile = {
      experienceLevel: 'intermediate',
      needsMotivation: false,
      hasSpecificGoals: false,
      preferredStyle: 'balanced',
      primaryFocus: 'general_growth',
      completedAt: new Date(),
      version: '1.0'
    };
    
    // Analyze experience level
    const experienceResponse = responses.experience?.toLowerCase() || '';
    if (experienceResponse.includes('beginner') || experienceResponse.includes('new') || experienceResponse.includes('starting')) {
      profile.experienceLevel = 'beginner';
    } else if (experienceResponse.includes('advanced') || experienceResponse.includes('expert') || experienceResponse.includes('years')) {
      profile.experienceLevel = 'advanced';
    }
    
    // Analyze motivation needs
    const challengesResponse = responses.challenges?.toLowerCase() || '';
    if (challengesResponse.includes('motivation') || challengesResponse.includes('procrastination') || challengesResponse.includes('consistency')) {
      profile.needsMotivation = true;
    }
    
    // Analyze goal specificity
    const goalsResponse = responses.goals || '';
    if (goalsResponse.length > 50 && (goalsResponse.includes('specific') || goalsResponse.includes('by') || goalsResponse.includes('achieve'))) {
      profile.hasSpecificGoals = true;
    }
    
    // Analyze preferred style
    const preferencesResponse = responses.preferences?.toLowerCase() || '';
    if (preferencesResponse.includes('accountability')) {
      profile.preferredStyle = 'accountability';
    } else if (preferencesResponse.includes('encouragement') || preferencesResponse.includes('support')) {
      profile.preferredStyle = 'encouragement';
    } else if (preferencesResponse.includes('practical') || preferencesResponse.includes('advice')) {
      profile.preferredStyle = 'practical';
    }
    
    return profile;
  }

  private async getAssessmentState(userId: string): Promise<IntakeResult<AssessmentState>> {
    try {
      const memory = await this.agentsService.getMemory(userId, 'intake_assessment_state');
      const rawState = memory?.value || { userId, currentStage: null, completed: false, responses: {} };
      
      if (!isValidAssessmentState(rawState)) {
        this.logger.warn(`Invalid assessment state for user ${userId}, using default`);
        const defaultState: AssessmentState = {
          userId,
          currentStage: null,
          completed: false,
          responses: {
            initial: null,
            experience: null,
            goals: null,
            challenges: null,
            preferences: null,
            completion: null
          }
        };
        return { success: true, data: defaultState };
      }
      
      return { success: true, data: rawState as AssessmentState };
    } catch (error) {
      this.logger.error(`Failed to get assessment state for ${userId}:`, error);
      return {
        success: false,
        error: {
          code: 'STATE_RETRIEVAL_ERROR',
          message: 'Failed to retrieve assessment state',
          userId,
          timestamp: new Date()
        }
      };
    }
  }

  private async updateAssessmentState(
    userId: string,
    interactionType: InteractionType,
    content: string,
    response: string
  ): Promise<IntakeResult<void>> {
    try {
      const currentStateResult = await this.getAssessmentState(userId);
      if (!currentStateResult.success) {
        return {
          success: false,
          error: currentStateResult.error
        };
      }
      
      const currentState = currentStateResult.data;
      const nextStage = this.getNextStage(currentState.currentStage || 'initial');
      
      const newState: AssessmentState = {
        ...currentState,
        currentStage: nextStage,
        completed: !nextStage,
        lastInteraction: new Date(),
        responses: {
          ...currentState.responses,
          [currentState.currentStage || 'initial']: content
        }
      };
      
      await this.agentsService.storeMemory({
        userId,
        memoryType: 'assessment' as MemoryType,
        key: 'intake_assessment_state',
        value: newState,
        importance: 8,
      });
      
      return { success: true, data: undefined };
    } catch (error) {
      this.logger.error('Failed to update assessment state:', error);
      return {
        success: false,
        error: {
          code: 'STATE_UPDATE_ERROR',
          message: 'Failed to update assessment state',
          userId,
          timestamp: new Date()
        }
      };
    }
  }

  private async storeAssessmentResponse(userId: string, stage: AssessmentStage, response: string): Promise<IntakeResult<void>> {
    try {
      const trimmedResponse = response.substring(0, 500); // Limit response length
      
      const memoryData: IntakeMemoryData = {
        userId,
        memoryType: 'assessment' as MemoryType,
        key: `intake_${stage}`,
        value: {
          stage,
          response: trimmedResponse,
          timestamp: new Date(),
        },
        importance: 7,
        tags: ['intake', 'assessment', stage]
      };
      
      await this.agentsService.storeMemory({
        userId: memoryData.userId,
        memoryType: memoryData.memoryType,
        key: memoryData.key,
        value: memoryData.value,
        importance: memoryData.importance,
      });
      
      return { success: true, data: undefined };
    } catch (error) {
      this.logger.error('Failed to store assessment response:', error);
      return {
        success: false,
        error: {
          code: 'RESPONSE_STORAGE_ERROR',
          message: 'Failed to store assessment response',
          userId,
          stage,
          timestamp: new Date()
        }
      };
    }
  }

  private async getAssessmentResponses(userId: string): Promise<IntakeResult<Record<AssessmentStage, string>>> {
    try {
      // Get all assessment responses for analysis
      const responses: Record<AssessmentStage, string> = {} as Record<AssessmentStage, string>;
      
      for (const stage of this.assessmentStages.slice(0, -1)) { // Exclude 'completion'
        const memory = await this.agentsService.getMemory(userId, `intake_${stage}`);
        if (memory && memory.value && typeof memory.value === 'object') {
          const memoryValue = memory.value as { response?: string };
          responses[stage] = memoryValue.response || '';
        } else {
          responses[stage] = '';
        }
      }
      
      return { success: true, data: responses };
    } catch (error) {
      this.logger.error(`Failed to get assessment responses for ${userId}:`, error);
      return {
        success: false,
        error: {
          code: 'RESPONSES_RETRIEVAL_ERROR',
          message: 'Failed to retrieve assessment responses',
          userId,
          timestamp: new Date()
        }
      };
    }
  }

  private async storeUserProfile(userId: string, profile: UserIntakeProfile): Promise<IntakeResult<void>> {
    try {
      const profileData = {
        ...profile,
        completedAt: profile.completedAt,
        version: profile.version,
      };
      
      await this.agentsService.storeMemory({
        userId,
        memoryType: 'profile' as MemoryType,
        key: 'intake_profile',
        value: profileData,
        importance: 9,
      });
      
      return { success: true, data: undefined };
    } catch (error) {
      this.logger.error('Failed to store user profile:', error);
      return {
        success: false,
        error: {
          code: 'PROFILE_STORAGE_ERROR',
          message: 'Failed to store user profile',
          userId,
          timestamp: new Date()
        }
      };
    }
  }

  async getIntakeStats(): Promise<IntakeStats> {
    const capabilities = [
      'User onboarding',
      'Needs assessment',
      'Goal identification',
      'Service matching',
      'Profile creation',
      'Resource recommendations'
    ] as const;
    
    const questionCategories = Object.keys(this.assessmentQuestions) as AssessmentStage[];
    const totalQuestions = Object.values(this.assessmentQuestions).flat().length;
    
    return {
      specialistType: 'Intake Specialist',
      personality: this.specialistPersonality,
      capabilities: capabilities,
      assessmentStages: this.assessmentStages,
      questionCategories,
      totalQuestions,
    };
  }

  // Discord interaction handlers with full type safety
  async handleDiscordInteraction(
    interaction: CommandInteraction | ButtonInteraction | SelectMenuInteraction | ModalSubmitInteraction
  ): Promise<IntakeResult<void>> {
    try {
      const userId = interaction.user.id;
      const content = this.extractContentFromInteraction(interaction);
      const interactionType = this.mapDiscordInteractionType(interaction);
      
      const context: Partial<InteractionContext> = {
        guildId: interaction.guildId ?? undefined,
        channelId: interaction.channelId,
        messageId: 'message' in interaction ? (interaction as any).message?.id : undefined,
        timestamp: new Date()
      };

      const result = await this.handleInteraction(userId, interactionType, content, context);
      
      if (!result.success) {
        await this.handleInteractionError(interaction, result.error);
        return { success: false, error: result.error };
      }

      await this.sendDiscordResponse(interaction, result.data);
      return { success: true, data: undefined };
    } catch (error) {
      this.logger.error('Failed to handle Discord interaction:', error);
      return {
        success: false,
        error: {
          code: 'DISCORD_INTERACTION_ERROR',
          message: 'Failed to handle Discord interaction',
          timestamp: new Date()
        }
      };
    }
  }

  private extractContentFromInteraction(
    interaction: CommandInteraction | ButtonInteraction | SelectMenuInteraction | ModalSubmitInteraction
  ): string {
    if (interaction.isChatInputCommand()) {
      return interaction.options.getString('message') ?? 'start';
    } else if (interaction.isButton()) {
      return interaction.customId;
    } else if (interaction.isStringSelectMenu()) {
      return interaction.values.join(', ');
    } else if (interaction.isModalSubmit()) {
      const fields = interaction.fields.fields;
      return Array.from(fields.values()).map(field => field.value).join(' | ');
    }
    return '';
  }

  private mapDiscordInteractionType(
    interaction: CommandInteraction | ButtonInteraction | SelectMenuInteraction | ModalSubmitInteraction
  ): InteractionType {
    if (interaction.isChatInputCommand()) {
      return 'slash_command';
    } else if (interaction.isButton()) {
      return 'button';
    } else if (interaction.isStringSelectMenu()) {
      return 'select_menu';
    } else if (interaction.isModalSubmit()) {
      return 'modal';
    }
    return 'text'; // fallback
  }

  private async sendDiscordResponse(
    interaction: CommandInteraction | ButtonInteraction | SelectMenuInteraction | ModalSubmitInteraction,
    response: string
  ): Promise<void> {
    try {
      const replyOptions = {
        content: response,
        ephemeral: true
      };

      if (interaction.replied || interaction.deferred) {
        await interaction.editReply(replyOptions);
      } else {
        await interaction.reply(replyOptions);
      }
    } catch (error) {
      this.logger.error('Failed to send Discord response:', error);
      // Try to send a fallback message
      try {
        if (!interaction.replied && !interaction.deferred) {
          await interaction.reply({
            content: 'I apologize, but I encountered an error processing your request. Please try again.',
            ephemeral: true
          });
        }
      } catch (fallbackError) {
        this.logger.error('Failed to send fallback Discord response:', fallbackError);
      }
    }
  }

  private async handleInteractionError(
    interaction: CommandInteraction | ButtonInteraction | SelectMenuInteraction | ModalSubmitInteraction,
    error: IntakeError
  ): Promise<void> {
    const errorMessage = this.formatErrorMessage(error);
    
    try {
      const replyOptions = {
        content: errorMessage,
        ephemeral: true
      };

      if (interaction.replied || interaction.deferred) {
        await interaction.editReply(replyOptions);
      } else {
        await interaction.reply(replyOptions);
      }
    } catch (responseError) {
      this.logger.error('Failed to send error response:', responseError);
    }
  }

  private formatErrorMessage(error: IntakeError): string {
    switch (error.code) {
      case 'INVALID_USER_ID':
        return 'There was an issue with your user identification. Please try again.';
      case 'INVALID_INTERACTION_TYPE':
        return 'This interaction type is not supported. Please use the proper commands.';
      case 'EMPTY_RESPONSE':
        return 'Please provide a response to continue with the assessment.';
      case 'ASSESSMENT_COMPLETION_ERROR':
        return 'There was an issue completing your assessment. Your progress has been saved, and you can continue later.';
      default:
        return 'I apologize for the technical difficulty. Please try again or contact support if the issue persists.';
    }
  }

  // Validation helpers
  async validateUserAccess(userId: string, guildId?: string): Promise<IntakeResult<boolean>> {
    try {
      // Implement user access validation logic here
      // This could check premium status, guild membership, etc.
      
      if (!userId || userId.trim().length === 0) {
        return {
          success: false,
          error: {
            code: 'INVALID_USER_ID',
            message: 'Invalid user ID provided',
            userId,
            timestamp: new Date()
          }
        };
      }

      // Additional validation logic would go here
      return { success: true, data: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'ACCESS_VALIDATION_ERROR',
          message: 'Failed to validate user access',
          userId,
          timestamp: new Date()
        }
      };
    }
  }

  // Utility methods for data validation
  validateAssessmentResponse(response: string, stage: AssessmentStage): IntakeValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Basic validation
    if (!response || response.trim().length === 0) {
      errors.push('Response cannot be empty');
    }

    if (response.length < 10) {
      warnings.push('Response seems quite short. Consider providing more detail for better personalization.');
    }

    if (response.length > 500) {
      warnings.push('Response is very long. It will be truncated for storage.');
    }

    // Stage-specific validation
    switch (stage) {
      case 'goals':
        if (!response.toLowerCase().includes('goal') && !response.toLowerCase().includes('want') && !response.toLowerCase().includes('achieve')) {
          warnings.push('Consider mentioning specific goals you want to achieve.');
        }
        break;
      case 'experience':
        if (!response.toLowerCase().includes('experience') && !response.toLowerCase().includes('before') && !response.toLowerCase().includes('previous')) {
          warnings.push('Consider sharing more about your previous experience with personal development.');
        }
        break;
    }

    return {
      isValid: errors.length === 0,
      errors: errors as readonly string[],
      warnings: warnings as readonly string[]
    };
  }

  // Method for getting assessment progress
  async getAssessmentProgress(userId: string): Promise<IntakeResult<{ completed: number; total: number; percentage: number }>> {
    try {
      const stateResult = await this.getAssessmentState(userId);
      if (!stateResult.success) {
        return {
          success: false,
          error: stateResult.error
        };
      }

      const state = stateResult.data;
      const totalStages = this.assessmentStages.length - 1; // Exclude completion
      let completedStages = 0;

      if (state.completed) {
        completedStages = totalStages;
      } else if (state.currentStage) {
        completedStages = this.assessmentStages.indexOf(state.currentStage);
      }

      const percentage = Math.round((completedStages / totalStages) * 100);

      return {
        success: true,
        data: {
          completed: completedStages,
          total: totalStages,
          percentage
        }
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: 'PROGRESS_CALCULATION_ERROR',
          message: 'Failed to calculate assessment progress',
          userId,
          timestamp: new Date()
        }
      };
    }
  }
}