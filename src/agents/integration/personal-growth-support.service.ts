import { Injectable, Logger, Inject } from '@nestjs/common';
import { DatabaseService } from '@/core/data';
import { CacheService } from '../../core/cache/cache.service';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { Client, TextChannel, GuildMember, Message, EmbedBuilder, BaseGuildTextChannel } from 'discord.js';
import { ChannelRoutingService } from './channel-routing.service';
import { AgentsService } from '../agents.service';
import { PersonalGrowthCoach } from '../types/personal-growth-coach';
import { IntakeSpecialist } from '../types/intake-specialist';
import { ProgressTracker } from '../types/progress-tracker';

@Injectable()
export class PersonalGrowthSupportService {
  private readonly logger = new Logger(PersonalGrowthSupportService.name);

  // Keywords that indicate personal growth needs
  private readonly personalGrowthKeywords = [
    'motivation', 'motivated', 'consistency', 'consistent', 'productive', 'productivity',
    'goals', 'goal', 'habits', 'habit', 'discipline', 'focus', 'focused', 'struggling',
    'struggle', 'stuck', 'procrastination', 'procrastinating', 'overwhelmed', 'stressed',
    'anxiety', 'anxious', 'depression', 'depressed', 'burnout', 'tired', 'exhausted',
    'improvement', 'improve', 'better', 'growth', 'develop', 'development', 'progress',
    'achievement', 'achieve', 'success', 'successful', 'mindset', 'confidence', 'self-esteem',
    'relationship', 'relationships', 'communication', 'social', 'lonely', 'isolation',
    'support', 'help', 'guidance', 'advice', 'mentor', 'coaching', 'therapy'
  ];

  // Supportive response templates
  private readonly supportiveResponses = [
    "I hear you, and what you're going through is completely valid. You're not alone in this journey. 💙",
    "Thank you for sharing that with us. It takes courage to open up about your struggles. We're here to support you. 🤗",
    "Your feelings are important, and seeking help shows incredible strength. Let's work through this together. ✨",
    "I appreciate you trusting us with this. Everyone faces challenges, and you've taken the first step by reaching out. 🌟",
    "What you're experiencing is more common than you might think. You're brave for sharing, and we're here to help. 💪"
  ];

  constructor(
    private readonly databaseService: DatabaseService,
    @Inject(Client) private readonly client: Client,
    private readonly redisDatabaseService: CacheService,
    private readonly channelRoutingService: ChannelRoutingService,
    private readonly agentsService: AgentsService,
    private readonly personalGrowthCoach: PersonalGrowthCoach,
    private readonly intakeSpecialist: IntakeSpecialist,
    private readonly progressTracker: ProgressTracker,
  ) {}

  /**
   * Analyzes a message for personal growth indicators and provides appropriate support
   */
  async analyzeAndSupport(message: Message): Promise<boolean> {
    try {
      const content = message.content.toLowerCase();
      const hasPersonalGrowthKeywords = this.personalGrowthKeywords.some(keyword => 
        content.includes(keyword)
      );

      if (!hasPersonalGrowthKeywords) {
        return false;
      }

      // Log the interaction
      await this.logPersonalGrowthInteraction(message);

      // Get or create personal growth channels
      const channels = await this.getOrCreatePersonalGrowthChannels(message.guild!);
      
      if (!channels) {
        this.logger.warn(`Could not create personal growth channels for guild ${message.guild!.id}`);
        return false;
      }

      // Send supportive response in the original channel
      await this.sendSupportiveResponse(message);

      // Route to appropriate agent based on message content
      const routingResult = await this.channelRoutingService.routeMessage(message);
      
      if (routingResult) {
        // Send a follow-up message directing them to the personal growth channel
        await this.sendChannelRedirect(message, channels.mainChannel, routingResult);
        
        // Initiate conversation in the personal growth channel
        await this.initiatePersonalGrowthConversation(message, channels.mainChannel, routingResult);
      }

      return true;
    } catch (error) {
      this.logger.error('Error in analyzeAndSupport:', error);
      return false;
    }
  }

  /**
   * Handles new member welcome with personal growth introduction
   */
  async welcomeNewMember(member: GuildMember): Promise<void> {
    try {
      const channels = await this.getOrCreatePersonalGrowthChannels(member.guild);
      if (!channels) {
        this.logger.warn(`Could not create personal growth channels for guild ${member.guild.id}`);
        return;
      }

      // Send welcome message with personal growth introduction
      const welcomeEmbed = new EmbedBuilder()
        .setTitle('Welcome to Your Personal Growth Journey!')
        .setDescription('Ready to start tracking your personal development goals?')
        .setColor(0x00ff00)
        .setThumbnail('https://cdn.discordapp.com/attachments/placeholder.png')
        .setTimestamp();

      await channels.mainChannel.send({ embeds: [welcomeEmbed] });

      // Start intake process
      await this.initiateIntakeProcess(member);
    } catch (error) {
      this.logger.error('Error in welcomeNewMember:', error);
    }
  }

  /**
   * Gets or creates personal growth channels for a guild
   */
  private async getOrCreatePersonalGrowthChannels(guild: any): Promise<any> {
    try {
      // Look for existing channels
      let mainChannel = guild.channels.cache.find((ch: any) => 
        ch.name === 'personal-growth-support' && ch.type === 0
      );
      let progressChannel = guild.channels.cache.find((ch: any) => 
        ch.name === 'progress-wins' && ch.type === 0
      );
      let resourcesChannel = guild.channels.cache.find((ch: any) => 
        ch.name === 'growth-resources' && ch.type === 0
      );

      // Create category if it doesn't exist
      let category = guild.channels.cache.find((ch: any) => 
        ch.name === 'Personal Growth' && ch.type === 4
      );
      
      if (!category) {
        category = await guild.channels.create({
          name: 'Personal Growth',
          type: 4, // Category
          position: 1
        });
      }

      // Create main support channel
      if (!mainChannel) {
        mainChannel = await guild.channels.create({
          name: 'personal-growth-support',
          type: 0, // Text channel
          parent: category.id,
          topic: '🌟 Share your challenges, get support, and grow together! This is a safe space for personal development discussions.'
        });
      }

      // Create progress tracking channel
      if (!progressChannel) {
        progressChannel = await guild.channels.create({
          name: 'progress-wins',
          type: 0, // Text channel
          parent: category.id,
          topic: '🎉 Celebrate your wins, big and small! Share your progress and achievements here.'
        });
      }

      // Create resources channel
      if (!resourcesChannel) {
        resourcesChannel = await guild.channels.create({
          name: 'growth-resources',
          type: 0, // Text channel
          parent: category.id,
          topic: '📚 Helpful resources, tips, and tools for personal growth and development.'
        });

        // Add some initial resources
        await this.addInitialResources(resourcesChannel);
      }

      return {
        category,
        mainChannel,
        progressChannel,
        resourcesChannel
      };
    } catch (error) {
      this.logger.error('Error creating personal growth channels:', error);
      return null;
    }
  }

  /**
   * Sends a supportive response to the original message
   */
  private async sendSupportiveResponse(message: Message): Promise<void> {
    try {
      const randomIndex = Math.floor(Math.random() * this.supportiveResponses.length);
      const response = this.supportiveResponses[randomIndex];
      
      if (!response) {
        this.logger.warn('No supportive response available');
        return;
      }

      await message.react('❤️');
      
      setTimeout(async () => {
        await message.reply({
          content: response,
          allowedMentions: { repliedUser: false }
        });
      }, 1000);
    } catch (error) {
      this.logger.error('Error sending supportive response:', error);
    }
  }

  /**
   * Sends a message redirecting user to personal growth channel
   */
  private async sendChannelRedirect(message: Message, growthChannel: TextChannel, agentType: string): Promise<void> {
    try {
      const embed = new EmbedBuilder()
        .setTitle('Let\'s Continue in Our Growth Space')
        .setDescription(`I'd love to help you further with this! Please check ${growthChannel} where we can have a more focused conversation with your ${agentType.replace('_', ' ')} agent.`)
        .setColor(0x00ff00)
        .setTimestamp();

      await message.reply({ embeds: [embed], allowedMentions: { repliedUser: false } });
    } catch (error) {
      this.logger.error('Error sending channel redirect:', error);
    }
  }

  /**
   * Initiates a conversation in the personal growth channel
   */
  private async initiatePersonalGrowthConversation(message: Message, growthChannel: TextChannel, agentType: string): Promise<void> {
    try {
      const embed = new EmbedBuilder()
        .setTitle(`${message.author.username}'s Growth Session`)
        .setDescription(`Continuing from ${message.channel}: "${message.content.substring(0, 100)}..."`)
        .addFields({ name: 'Assigned Agent', value: agentType.replace('_', ' '), inline: true })
        .setColor(0x00ff00)
        .setTimestamp();

      await growthChannel.send({ content: `<@${message.author.id}>`, embeds: [embed] });
      
      // Process with the appropriate agent
      await this.processWithAgent(message, agentType, growthChannel);
    } catch (error) {
      this.logger.error('Error initiating personal growth conversation:', error);
    }
  }

  /**
   * Processes message with appropriate agent
   */
  private async processWithAgent(message: Message, agentType: string, channel: TextChannel): Promise<void> {
    try {
      let response: string;

      switch (agentType) {
        case 'personal_growth_coach':
          response = await this.personalGrowthCoach.handleInteraction(
            message.author.id,
            'message',
            message.content,
            { guildId: message.guild?.id, channelId: channel.id }
          );
          break;
        case 'intake_specialist':
          response = await this.intakeSpecialist.handleInteraction(
            message.author.id,
            'message',
            message.content,
            { guildId: message.guild?.id, channelId: channel.id }
          );
          break;
        case 'progress_tracker':
          response = await this.progressTracker.handleInteraction(
            message.author.id,
            'message',
            message.content,
            { guildId: message.guild?.id, channelId: channel.id }
          );
          break;
        default:
          this.logger.warn(`Unknown agent type: ${agentType}`);
          return;
      }

      if (response && response.trim()) {
        await channel.send(response);
      }
    } catch (error) {
      this.logger.error(`Error processing with agent ${agentType}:`, error);
    }
  }

  /**
   * Logs personal growth interaction for analytics
   */
  private async logPersonalGrowthInteraction(message: Message): Promise<void> {
    try {
      await this.agentsService.logInteraction({
        userId: message.author.id,
        agentType: 'personal_growth_support' as any,
        interactionType: 'support_trigger',
        content: message.content,
        ...(message.guild?.id && { guildId: message.guild.id }),
        channelId: message.channel.id,
        response: 'Personal growth support activated'
      });
    } catch (error) {
      this.logger.error('Error logging personal growth interaction:', error);
    }
  }

  /**
   * Initiates intake process for new member
   */
  private async initiateIntakeProcess(member: GuildMember): Promise<void> {
    try {
      await this.intakeSpecialist.handleInteraction(
        member.id,
        'welcome',
        'New member welcome',
        { guildId: member.guild.id }
      );
    } catch (error) {
      this.logger.error('Error initiating intake process:', error);
    }
  }

  /**
   * Adds initial resources to the resources channel
   */
  private async addInitialResources(channel: TextChannel): Promise<void> {
    try {
      const resourcesEmbed = new EmbedBuilder()
        .setTitle('📚 Personal Growth Resources')
        .setDescription('Here are some helpful resources to get you started on your personal growth journey:')
        .addFields(
          { name: '🎯 Goal Setting', value: '• SMART Goals framework\n• Goal tracking templates\n• Regular review processes', inline: true },
          { name: '🧠 Mindset', value: '• Growth vs Fixed mindset\n• Positive affirmations\n• Mindfulness practices', inline: true },
          { name: '⚡ Productivity', value: '• Time management techniques\n• Habit formation\n• Focus strategies', inline: true },
        )
        .setColor(0x00ff00)
        .setTimestamp();

      await channel.send({ embeds: [resourcesEmbed] });
    } catch (error) {
      this.logger.error('Error adding initial resources:', error);
    }
  }

  /**
   * Gets analytics for personal growth support
   */
  async getPersonalGrowthStats(): Promise<any> {
    try {
      return {
        totalSupportTriggers: 0, // Would be calculated from database
        uniqueUsersSupported: 0, // Would be calculated from database
        mostCommonKeywords: this.personalGrowthKeywords.slice(0, 5),
        responseTemplates: this.supportiveResponses.length,
        timestamp: new Date()
      };
    } catch (error) {
      this.logger.error('Error getting personal growth stats:', error);
      return { error: 'Failed to retrieve stats' };
    }
  }
}