import { Inject, Injectable, Logger, Optional } from '@nestjs/common';
import { DatabaseService } from '@/core/data';
import { CacheService } from '../../core/cache/cache.service';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { Cron, CronExpression } from '@nestjs/schedule';
import { Client } from 'discord.js';
import { AgentsService } from '../agents.service';
import { PersonalGrowthCoach } from '../types/personal-growth-coach';
import { ProgressTracker } from '../types/progress-tracker';

@Injectable()
export class SchedulerService {
  private readonly logger = new Logger(SchedulerService.name);
  private isInitialized = false;
  private scheduledTaskCount = 0;

  constructor(
    private readonly databaseService: DatabaseService,
    @Optional() @Inject(Client) private readonly client: Client | null,
    private readonly redisDatabaseService: CacheService,
    private readonly agentsService: AgentsService,
    private readonly personalGrowthCoach: PersonalGrowthCoach,
    private readonly progressTracker: ProgressTracker,
  ) {}

  async initialize(): Promise<void> {
    if (this.isInitialized) return;
    
    this.logger.log('🕐 Initializing AI Agent Scheduler...');
    this.isInitialized = true;
    this.logger.log('✅ AI Agent Scheduler initialized');
  }

  @Cron(CronExpression.EVERY_DAY_AT_9AM)
  async sendDailyCheckins(): Promise<void> {
    if (!this.isInitialized || !this.client || !this.client.isReady()) return;

    try {
      this.logger.log('🌅 Running daily check-ins...');
      
      // This would typically query the database for users who have
      // opted in for daily check-ins
      const usersForCheckIn = await this.getUsersForDailyCheckin();
      
      for (const user of usersForCheckIn) {
        await this.sendDailyCheckin(user.discordId);
        this.scheduledTaskCount++;
        
        // Add delay to avoid rate limits
        await this.delay(1000);
      }
      
      this.logger.log(`✅ Daily check-ins sent to ${usersForCheckIn.length} users`);
    } catch (error) {
      this.logger.error('❌ Failed to send daily check-ins:', error);
    }
  }

  @Cron(CronExpression.EVERY_WEEK)
  async sendWeeklyProgressReports(): Promise<void> {
    if (!this.isInitialized || !this.client || !this.client.isReady()) return;

    try {
      this.logger.log('📊 Running weekly progress reports...');
      
      const usersForReport = await this.getUsersForWeeklyReport();
      
      for (const user of usersForReport) {
        await this.sendWeeklyProgressReport(user.discordId);
        this.scheduledTaskCount++;
        
        // Add delay to avoid rate limits
        await this.delay(2000);
      }
      
      this.logger.log(`✅ Weekly progress reports sent to ${usersForReport.length} users`);
    } catch (error) {
      this.logger.error('❌ Failed to send weekly progress reports:', error);
    }
  }

  @Cron('0 */6 * * *') // Every 6 hours
  async sendProactiveMessages(): Promise<void> {
    if (!this.isInitialized || !this.client || !this.client.isReady()) return;

    try {
      this.logger.log('💬 Running proactive messaging...');
      
      const usersForProactiveMsg = await this.getUsersForProactiveMessaging();
      
      for (const user of usersForProactiveMsg) {
        await this.sendProactiveMessage(user.discordId);
        this.scheduledTaskCount++;
        
        // Add delay to avoid rate limits
        await this.delay(1500);
      }
      
      this.logger.log(`✅ Proactive messages sent to ${usersForProactiveMsg.length} users`);
    } catch (error) {
      this.logger.error('❌ Failed to send proactive messages:', error);
    }
  }

  private async sendDailyCheckin(userId: string): Promise<void> {
    try {
      if (!this.client) return;
      const user = await this.client.users.fetch(userId);
      if (!user) return;

      const message = await this.personalGrowthCoach.handleInteraction(
        userId,
        'check_in',
        'Daily check-in scheduled message',
        { isScheduled: true }
      );

      await user.send(message);
      await this.agentsService.logInteraction({
        userId,
        agentType: 'personal_growth_coach',
        interactionType: 'check_in',
        content: 'Daily scheduled check-in',
        response: message
      });
    } catch (error) {
      this.logger.error(`Failed to send daily check-in to user ${userId}:`, error);
    }
  }

  private async sendWeeklyProgressReport(userId: string): Promise<void> {
    try {
      if (!this.client) return;
      const user = await this.client.users.fetch(userId);
      if (!user) return;

      const report = await this.progressTracker.handleInteraction(
        userId,
        'weekly_report',
        'Weekly progress report scheduled message',
        { isScheduled: true }
      );

      await user.send(report);
      await this.agentsService.logInteraction({
        userId,
        agentType: 'progress_tracker',
        interactionType: 'weekly_report',
        content: 'Weekly scheduled progress report',
        response: report
      });
    } catch (error) {
      this.logger.error(`Failed to send weekly report to user ${userId}:`, error);
    }
  }

  private async sendProactiveMessage(userId: string): Promise<void> {
    try {
      if (!this.client) return;
      const user = await this.client.users.fetch(userId);
      if (!user) return;

      // Randomly choose between different types of proactive messages
      const messageType = ['motivation', 'progress_check', 'resource_share'][Math.floor(Math.random() * 3)];
      
      const message = await this.personalGrowthCoach.handleInteraction(
        userId,
        messageType,
        'Proactive motivational message',
        { isScheduled: true, isProactive: true }
      );

      await user.send(message);
      await this.agentsService.logInteraction({
        userId,
        agentType: 'personal_growth_coach',
        interactionType: 'proactive_message',
        content: `Proactive ${messageType} message`,
        response: message
      });
    } catch (error) {
      this.logger.error(`Failed to send proactive message to user ${userId}:`, error);
    }
  }

  private async getUsersForDailyCheckin(): Promise<any[]> {
    try {
      // This would query the database for users who have opted in for daily check-ins
      // For now, return empty array as placeholder
      return [];
    } catch (error) {
      this.logger.error('Failed to get users for daily check-in:', error);
      return [];
    }
  }

  private async getUsersForWeeklyReport(): Promise<any[]> {
    try {
      // This would query the database for users who have opted in for weekly reports
      // For now, return empty array as placeholder
      return [];
    } catch (error) {
      this.logger.error('Failed to get users for weekly report:', error);
      return [];
    }
  }

  private async getUsersForProactiveMessaging(): Promise<any[]> {
    try {
      // This would query the database for users who have opted in for proactive messaging
      // and haven't received a message recently
      return [];
    } catch (error) {
      this.logger.error('Failed to get users for proactive messaging:', error);
      return [];
    }
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async getScheduledTaskCount(): Promise<number> {
    return this.scheduledTaskCount;
  }

  async resetTaskCount(): Promise<void> {
    this.scheduledTaskCount = 0;
  }

  async getSchedulerStats(): Promise<any> {
    return {
      isInitialized: this.isInitialized,
      scheduledTaskCount: this.scheduledTaskCount,
      clientReady: this.client?.isReady() || false,
      timestamp: new Date()
    };
  }

  async setUserDailyCheckin(userId: string, enabled: boolean): Promise<void> {
    try {
      // This would update the database to set user's daily check-in preference
      this.logger.log(`Set daily check-in for user ${userId}: ${enabled}`);
    } catch (error) {
      this.logger.error(`Failed to set daily check-in for user ${userId}:`, error);
      throw error;
    }
  }

  async setUserWeeklyReport(userId: string, enabled: boolean): Promise<void> {
    try {
      // This would update the database to set user's weekly report preference
      this.logger.log(`Set weekly report for user ${userId}: ${enabled}`);
    } catch (error) {
      this.logger.error(`Failed to set weekly report for user ${userId}:`, error);
      throw error;
    }
  }

  async setUserProactiveMessaging(userId: string, enabled: boolean): Promise<void> {
    try {
      // This would update the database to set user's proactive messaging preference
      this.logger.log(`Set proactive messaging for user ${userId}: ${enabled}`);
    } catch (error) {
      this.logger.error(`Failed to set proactive messaging for user ${userId}:`, error);
      throw error;
    }
  }
}